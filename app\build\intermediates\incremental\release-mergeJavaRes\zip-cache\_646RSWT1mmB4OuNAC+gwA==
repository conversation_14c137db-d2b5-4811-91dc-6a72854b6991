[{"key": "androidx/core/os/BuildCompat$Api30Impl.class", "name": "androidx/core/os/BuildCompat$Api30Impl.class", "size": 1073, "crc": 1857026514}, {"key": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "name": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "size": 781, "crc": -1758429442}, {"key": "androidx/core/os/BuildCompat.class", "name": "androidx/core/os/BuildCompat.class", "size": 7579, "crc": 1955686922}, {"key": "android/support/v4/app/INotificationSideChannel$Default.class", "name": "android/support/v4/app/INotificationSideChannel$Default.class", "size": 1135, "crc": -395248935}, {"key": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "name": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "size": 2463, "crc": -1817265333}, {"key": "android/support/v4/app/INotificationSideChannel$Stub.class", "name": "android/support/v4/app/INotificationSideChannel$Stub.class", "size": 2803, "crc": -1087698977}, {"key": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "name": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "size": 1753, "crc": -2004194816}, {"key": "android/support/v4/app/INotificationSideChannel.class", "name": "android/support/v4/app/INotificationSideChannel.class", "size": 1205, "crc": -1604214337}, {"key": "android/support/v4/app/RemoteActionCompatParcelizer.class", "name": "android/support/v4/app/RemoteActionCompatParcelizer.class", "size": 1104, "crc": -587702565}, {"key": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "name": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "size": 1132, "crc": 1368227130}, {"key": "android/support/v4/os/IResultReceiver$Default.class", "name": "android/support/v4/os/IResultReceiver$Default.class", "size": 727, "crc": -1405212393}, {"key": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "size": 1698, "crc": 2064996755}, {"key": "android/support/v4/os/IResultReceiver$Stub.class", "name": "android/support/v4/os/IResultReceiver$Stub.class", "size": 2268, "crc": -1020262754}, {"key": "android/support/v4/os/IResultReceiver$_Parcel.class", "name": "android/support/v4/os/IResultReceiver$_Parcel.class", "size": 1714, "crc": 1241677238}, {"key": "android/support/v4/os/IResultReceiver.class", "name": "android/support/v4/os/IResultReceiver.class", "size": 977, "crc": 1325578816}, {"key": "android/support/v4/os/IResultReceiver2$Default.class", "name": "android/support/v4/os/IResultReceiver2$Default.class", "size": 731, "crc": -1958681237}, {"key": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "size": 1704, "crc": -471518}, {"key": "android/support/v4/os/IResultReceiver2$Stub.class", "name": "android/support/v4/os/IResultReceiver2$Stub.class", "size": 2275, "crc": -488450971}, {"key": "android/support/v4/os/IResultReceiver2$_Parcel.class", "name": "android/support/v4/os/IResultReceiver2$_Parcel.class", "size": 1718, "crc": -850731097}, {"key": "android/support/v4/os/IResultReceiver2.class", "name": "android/support/v4/os/IResultReceiver2.class", "size": 983, "crc": 1748309951}, {"key": "android/support/v4/os/ResultReceiver$1.class", "name": "android/support/v4/os/ResultReceiver$1.class", "size": 1177, "crc": 1291896286}, {"key": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "name": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "size": 1181, "crc": -989424411}, {"key": "android/support/v4/os/ResultReceiver$MyRunnable.class", "name": "android/support/v4/os/ResultReceiver$MyRunnable.class", "size": 886, "crc": -779159650}, {"key": "android/support/v4/os/ResultReceiver.class", "name": "android/support/v4/os/ResultReceiver.class", "size": 2978, "crc": 2067024131}, {"key": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "name": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "size": 3351, "crc": 74942830}, {"key": "androidx/core/app/ActivityCompat$1.class", "name": "androidx/core/app/ActivityCompat$1.class", "size": 1622, "crc": 463025258}, {"key": "androidx/core/app/ActivityCompat$Api16Impl.class", "name": "androidx/core/app/ActivityCompat$Api16Impl.class", "size": 1700, "crc": 718035142}, {"key": "androidx/core/app/ActivityCompat$Api21Impl.class", "name": "androidx/core/app/ActivityCompat$Api21Impl.class", "size": 1385, "crc": -27475877}, {"key": "androidx/core/app/ActivityCompat$Api22Impl.class", "name": "androidx/core/app/ActivityCompat$Api22Impl.class", "size": 765, "crc": -808803734}, {"key": "androidx/core/app/ActivityCompat$Api23Impl.class", "name": "androidx/core/app/ActivityCompat$Api23Impl.class", "size": 1442, "crc": 1578485729}, {"key": "androidx/core/app/ActivityCompat$Api28Impl.class", "name": "androidx/core/app/ActivityCompat$Api28Impl.class", "size": 867, "crc": 1657085898}, {"key": "androidx/core/app/ActivityCompat$Api30Impl.class", "name": "androidx/core/app/ActivityCompat$Api30Impl.class", "size": 1549, "crc": -1754908339}, {"key": "androidx/core/app/ActivityCompat$Api31Impl.class", "name": "androidx/core/app/ActivityCompat$Api31Impl.class", "size": 2075, "crc": -1870709849}, {"key": "androidx/core/app/ActivityCompat$Api32Impl.class", "name": "androidx/core/app/ActivityCompat$Api32Impl.class", "size": 839, "crc": 1133153515}, {"key": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "name": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "size": 425, "crc": 1567962088}, {"key": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "name": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "size": 621, "crc": -671588751}, {"key": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "name": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "size": 584, "crc": 780710089}, {"key": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "name": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "size": 4428, "crc": 351286491}, {"key": "androidx/core/app/ActivityCompat.class", "name": "androidx/core/app/ActivityCompat.class", "size": 10851, "crc": -261638075}, {"key": "androidx/core/app/ActivityManagerCompat.class", "name": "androidx/core/app/ActivityManagerCompat.class", "size": 780, "crc": -1698937269}, {"key": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "name": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "size": 2348, "crc": -825917519}, {"key": "androidx/core/app/ActivityOptionsCompat$Api16Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api16Impl.class", "size": 1398, "crc": -1114803006}, {"key": "androidx/core/app/ActivityOptionsCompat$Api21Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api21Impl.class", "size": 1582, "crc": 346520904}, {"key": "androidx/core/app/ActivityOptionsCompat$Api23Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api23Impl.class", "size": 1301, "crc": -1755345782}, {"key": "androidx/core/app/ActivityOptionsCompat$Api24Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api24Impl.class", "size": 1141, "crc": 1674674915}, {"key": "androidx/core/app/ActivityOptionsCompat.class", "name": "androidx/core/app/ActivityOptionsCompat.class", "size": 5395, "crc": 290241144}, {"key": "androidx/core/app/ActivityRecreator$1.class", "name": "androidx/core/app/ActivityRecreator$1.class", "size": 964, "crc": -1712743558}, {"key": "androidx/core/app/ActivityRecreator$2.class", "name": "androidx/core/app/ActivityRecreator$2.class", "size": 1176, "crc": 1115639159}, {"key": "androidx/core/app/ActivityRecreator$3.class", "name": "androidx/core/app/ActivityRecreator$3.class", "size": 1787, "crc": -1288801421}, {"key": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "name": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "size": 2055, "crc": -1497301040}, {"key": "androidx/core/app/ActivityRecreator.class", "name": "androidx/core/app/ActivityRecreator.class", "size": 6249, "crc": 1351327030}, {"key": "androidx/core/app/AlarmManagerCompat$Api19Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api19Impl.class", "size": 933, "crc": 311163197}, {"key": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "size": 1309, "crc": -730771635}, {"key": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "size": 1094, "crc": -298738575}, {"key": "androidx/core/app/AlarmManagerCompat.class", "name": "androidx/core/app/AlarmManagerCompat.class", "size": 2252, "crc": 1627972445}, {"key": "androidx/core/app/AppComponentFactory.class", "name": "androidx/core/app/AppComponentFactory.class", "size": 4319, "crc": 941336973}, {"key": "androidx/core/app/AppLaunchChecker.class", "name": "androidx/core/app/AppLaunchChecker.class", "size": 2271, "crc": -405740589}, {"key": "androidx/core/app/AppLocalesStorageHelper.class", "name": "androidx/core/app/AppLocalesStorageHelper.class", "size": 5093, "crc": 1815682277}, {"key": "androidx/core/app/AppOpsManagerCompat$Api19Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api19Impl.class", "size": 1057, "crc": -1170247109}, {"key": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "size": 1679, "crc": 1106233868}, {"key": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "size": 1581, "crc": -168458857}, {"key": "androidx/core/app/AppOpsManagerCompat.class", "name": "androidx/core/app/AppOpsManagerCompat.class", "size": 3230, "crc": -1987166702}, {"key": "androidx/core/app/BundleCompat.class", "name": "androidx/core/app/BundleCompat.class", "size": 1024, "crc": 742341568}, {"key": "androidx/core/app/ComponentActivity$ExtraData.class", "name": "androidx/core/app/ComponentActivity$ExtraData.class", "size": 753, "crc": 321062698}, {"key": "androidx/core/app/ComponentActivity.class", "name": "androidx/core/app/ComponentActivity.class", "size": 5123, "crc": -920581017}, {"key": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "name": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "size": 541, "crc": -1240361783}, {"key": "androidx/core/app/CoreComponentFactory.class", "name": "androidx/core/app/CoreComponentFactory.class", "size": 2957, "crc": -1634658571}, {"key": "androidx/core/app/DialogCompat$Api28Impl.class", "name": "androidx/core/app/DialogCompat$Api28Impl.class", "size": 849, "crc": -1446145350}, {"key": "androidx/core/app/DialogCompat.class", "name": "androidx/core/app/DialogCompat.class", "size": 1185, "crc": 1507368120}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "size": 1859, "crc": -1020155968}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "size": 4121, "crc": -1535527374}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "size": 981, "crc": -800692407}, {"key": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "name": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "size": 668, "crc": -1630363674}, {"key": "androidx/core/app/FrameMetricsAggregator.class", "name": "androidx/core/app/FrameMetricsAggregator.class", "size": 2570, "crc": -1426831642}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat$Api34Impl.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat$Api34Impl.class", "size": 1348, "crc": 1546297438}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat$GrammaticalGender.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat$GrammaticalGender.class", "size": 705, "crc": -768123921}, {"key": "androidx/core/app/GrammaticalInflectionManagerCompat.class", "name": "androidx/core/app/GrammaticalInflectionManagerCompat.class", "size": 1739, "crc": 1361943853}, {"key": "androidx/core/app/JobIntentService$CommandProcessor.class", "name": "androidx/core/app/JobIntentService$CommandProcessor.class", "size": 1825, "crc": 44568175}, {"key": "androidx/core/app/JobIntentService$CompatJobEngine.class", "name": "androidx/core/app/JobIntentService$CompatJobEngine.class", "size": 448, "crc": 1210592104}, {"key": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "size": 2923, "crc": 373299418}, {"key": "androidx/core/app/JobIntentService$CompatWorkItem.class", "name": "androidx/core/app/JobIntentService$CompatWorkItem.class", "size": 1021, "crc": -287917668}, {"key": "androidx/core/app/JobIntentService$GenericWorkItem.class", "name": "androidx/core/app/JobIntentService$GenericWorkItem.class", "size": 310, "crc": 683365043}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "size": 1478, "crc": 1773720750}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "size": 2592, "crc": 871330440}, {"key": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "size": 1852, "crc": -1663926146}, {"key": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "size": 1443, "crc": -862902624}, {"key": "androidx/core/app/JobIntentService.class", "name": "androidx/core/app/JobIntentService.class", "size": 7130, "crc": -1781495434}, {"key": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "size": 771, "crc": -1095698877}, {"key": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "size": 1056, "crc": -1768607198}, {"key": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "size": 1061, "crc": -1037654519}, {"key": "androidx/core/app/LocaleManagerCompat.class", "name": "androidx/core/app/LocaleManagerCompat.class", "size": 3026, "crc": -1458408911}, {"key": "androidx/core/app/MultiWindowModeChangedInfo.class", "name": "androidx/core/app/MultiWindowModeChangedInfo.class", "size": 1387, "crc": -1036727142}, {"key": "androidx/core/app/NavUtils$Api16Impl.class", "name": "androidx/core/app/NavUtils$Api16Impl.class", "size": 1128, "crc": -660975539}, {"key": "androidx/core/app/NavUtils.class", "name": "androidx/core/app/NavUtils.class", "size": 5971, "crc": 1952896136}, {"key": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "name": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "size": 597, "crc": -1985100633}, {"key": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "size": 4350, "crc": 1487899892}, {"key": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "size": 815, "crc": 1721686020}, {"key": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "size": 1417, "crc": -1496769729}, {"key": "androidx/core/app/NotificationChannelCompat$Builder.class", "name": "androidx/core/app/NotificationChannelCompat$Builder.class", "size": 3517, "crc": -1458413883}, {"key": "androidx/core/app/NotificationChannelCompat.class", "name": "androidx/core/app/NotificationChannelCompat.class", "size": 6989, "crc": -268500644}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "size": 1934, "crc": -347056883}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "size": 1275, "crc": 1629656438}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "size": 1411, "crc": 237511248}, {"key": "androidx/core/app/NotificationChannelGroupCompat.class", "name": "androidx/core/app/NotificationChannelGroupCompat.class", "size": 4884, "crc": -381039034}, {"key": "androidx/core/app/NotificationCompat$1.class", "name": "androidx/core/app/NotificationCompat$1.class", "size": 238, "crc": -598238096}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "size": 1215, "crc": 912469702}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "size": 1043, "crc": -1946575840}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "size": 998, "crc": 708279181}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "size": 991, "crc": -763749716}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "size": 986, "crc": -290492259}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "size": 998, "crc": -1517180708}, {"key": "androidx/core/app/NotificationCompat$Action$Builder.class", "name": "androidx/core/app/NotificationCompat$Action$Builder.class", "size": 9232, "crc": -1751296004}, {"key": "androidx/core/app/NotificationCompat$Action$Extender.class", "name": "androidx/core/app/NotificationCompat$Action$Extender.class", "size": 630, "crc": 258657630}, {"key": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "name": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "size": 492, "crc": 271952193}, {"key": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "size": 5051, "crc": 810417715}, {"key": "androidx/core/app/NotificationCompat$Action.class", "name": "androidx/core/app/NotificationCompat$Action.class", "size": 5711, "crc": 828707702}, {"key": "androidx/core/app/NotificationCompat$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Api20Impl.class", "size": 2205, "crc": -1785187051}, {"key": "androidx/core/app/NotificationCompat$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Api23Impl.class", "size": 881, "crc": -491954050}, {"key": "androidx/core/app/NotificationCompat$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Api24Impl.class", "size": 836, "crc": -222859606}, {"key": "androidx/core/app/NotificationCompat$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$Api26Impl.class", "size": 1466, "crc": 254158500}, {"key": "androidx/core/app/NotificationCompat$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Api28Impl.class", "size": 829, "crc": 1599306498}, {"key": "androidx/core/app/NotificationCompat$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Api29Impl.class", "size": 1720, "crc": 2135564006}, {"key": "androidx/core/app/NotificationCompat$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Api31Impl.class", "size": 836, "crc": -386336308}, {"key": "androidx/core/app/NotificationCompat$BadgeIconType.class", "name": "androidx/core/app/NotificationCompat$BadgeIconType.class", "size": 662, "crc": -1600074890}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api16Impl.class", "size": 2238, "crc": -1959361221}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "size": 1111, "crc": -1153890206}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "size": 1668, "crc": -726520465}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "size": 7557, "crc": -1740003453}, {"key": "androidx/core/app/NotificationCompat$BigTextStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$BigTextStyle$Api16Impl.class", "size": 1672, "crc": 39705078}, {"key": "androidx/core/app/NotificationCompat$BigTextStyle.class", "name": "androidx/core/app/NotificationCompat$BigTextStyle.class", "size": 3964, "crc": -418931129}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "size": 3541, "crc": 1961899461}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "size": 3618, "crc": 190638512}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "size": 4784, "crc": 60517543}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "size": 4276, "crc": 517707635}, {"key": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "size": 1599, "crc": 1656529507}, {"key": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "size": 1006, "crc": 1955791144}, {"key": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "size": 1440, "crc": -1882160241}, {"key": "androidx/core/app/NotificationCompat$Builder.class", "name": "androidx/core/app/NotificationCompat$Builder.class", "size": 28412, "crc": 1290186718}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api16Impl.class", "size": 1099, "crc": -1186758435}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "size": 2073, "crc": 422156270}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "size": 1181, "crc": -521179414}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "size": 1759, "crc": 717022035}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "size": 1133, "crc": 807645160}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "size": 1208, "crc": -1647262622}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "size": 3467, "crc": -1279856166}, {"key": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "name": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "size": 734, "crc": 2136238121}, {"key": "androidx/core/app/NotificationCompat$CallStyle.class", "name": "androidx/core/app/NotificationCompat$CallStyle.class", "size": 16407, "crc": 1987175753}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "size": 3348, "crc": -500987454}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "size": 873, "crc": -1468219676}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "size": 2896, "crc": -1192984677}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "size": 2343, "crc": -954710533}, {"key": "androidx/core/app/NotificationCompat$CarExtender.class", "name": "androidx/core/app/NotificationCompat$CarExtender.class", "size": 9177, "crc": 927000580}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api15Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api15Impl.class", "size": 1065, "crc": -993360036}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api16Impl.class", "size": 1188, "crc": -787797352}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "size": 949, "crc": -249520521}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "size": 7163, "crc": -1473878669}, {"key": "androidx/core/app/NotificationCompat$Extender.class", "name": "androidx/core/app/NotificationCompat$Extender.class", "size": 536, "crc": -1922350891}, {"key": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "name": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "size": 672, "crc": 900946520}, {"key": "androidx/core/app/NotificationCompat$InboxStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$InboxStyle$Api16Impl.class", "size": 1644, "crc": -980784688}, {"key": "androidx/core/app/NotificationCompat$InboxStyle.class", "name": "androidx/core/app/NotificationCompat$InboxStyle.class", "size": 4275, "crc": 1443821161}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api16Impl.class", "size": 1858, "crc": 1125091422}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "size": 1782, "crc": -613619705}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "size": 1287, "crc": 650008671}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "size": 1318, "crc": 973999471}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "size": 1723, "crc": -1829293019}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "size": 1414, "crc": -1701035620}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "size": 8027, "crc": 1382148671}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle.class", "size": 14323, "crc": 201326898}, {"key": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "name": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "size": 680, "crc": 1088614023}, {"key": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "name": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "size": 690, "crc": 1857766983}, {"key": "androidx/core/app/NotificationCompat$StreamType.class", "name": "androidx/core/app/NotificationCompat$StreamType.class", "size": 656, "crc": -1750087649}, {"key": "androidx/core/app/NotificationCompat$Style$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$Style$Api16Impl.class", "size": 1166, "crc": 1608129604}, {"key": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "size": 915, "crc": 1615801814}, {"key": "androidx/core/app/NotificationCompat$Style.class", "name": "androidx/core/app/NotificationCompat$Style.class", "size": 16242, "crc": -2021115927}, {"key": "androidx/core/app/NotificationCompat$TvExtender.class", "name": "androidx/core/app/NotificationCompat$TvExtender.class", "size": 4484, "crc": 1315857958}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "size": 2755, "crc": 1021840312}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "size": 1278, "crc": 2068019936}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "size": 1157, "crc": 1920400960}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "size": 1159, "crc": -1897159819}, {"key": "androidx/core/app/NotificationCompat$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$WearableExtender.class", "size": 18093, "crc": -788983209}, {"key": "androidx/core/app/NotificationCompat.class", "name": "androidx/core/app/NotificationCompat.class", "size": 24481, "crc": 513993004}, {"key": "androidx/core/app/NotificationCompatBuilder$Api16Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api16Impl.class", "size": 1670, "crc": 484482901}, {"key": "androidx/core/app/NotificationCompatBuilder$Api17Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api17Impl.class", "size": 950, "crc": -1327860806}, {"key": "androidx/core/app/NotificationCompatBuilder$Api19Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api19Impl.class", "size": 1000, "crc": -1773899453}, {"key": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "size": 3391, "crc": 1721066082}, {"key": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "size": 2192, "crc": 992763966}, {"key": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "size": 1824, "crc": 1198255011}, {"key": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "size": 2037, "crc": -426814245}, {"key": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "size": 2464, "crc": 955670683}, {"key": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "size": 1428, "crc": 1765474801}, {"key": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "size": 2143, "crc": 1185738732}, {"key": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "size": 1408, "crc": -2064621016}, {"key": "androidx/core/app/NotificationCompatBuilder.class", "name": "androidx/core/app/NotificationCompatBuilder.class", "size": 21111, "crc": 1822422092}, {"key": "androidx/core/app/NotificationCompatExtras.class", "name": "androidx/core/app/NotificationCompatExtras.class", "size": 780, "crc": 1230724101}, {"key": "androidx/core/app/NotificationCompatJellybean.class", "name": "androidx/core/app/NotificationCompatJellybean.class", "size": 12882, "crc": 80573459}, {"key": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "name": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "size": 2066, "crc": 1177375189}, {"key": "androidx/core/app/NotificationCompatSideChannelService.class", "name": "androidx/core/app/NotificationCompatSideChannelService.class", "size": 2134, "crc": -1284554455}, {"key": "androidx/core/app/NotificationManagerCompat$Api23Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api23Impl.class", "size": 1430, "crc": 1675840272}, {"key": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "size": 973, "crc": -830978995}, {"key": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "size": 3604, "crc": 1904468516}, {"key": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "size": 992, "crc": 250759468}, {"key": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "size": 1314, "crc": 1987504622}, {"key": "androidx/core/app/NotificationManagerCompat$Api34Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api34Impl.class", "size": 828, "crc": -1834148611}, {"key": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "name": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "size": 1902, "crc": -180483883}, {"key": "androidx/core/app/NotificationManagerCompat$InterruptionFilter.class", "name": "androidx/core/app/NotificationManagerCompat$InterruptionFilter.class", "size": 693, "crc": 1507578914}, {"key": "androidx/core/app/NotificationManagerCompat$NotificationWithIdAndTag.class", "name": "androidx/core/app/NotificationManagerCompat$NotificationWithIdAndTag.class", "size": 1039, "crc": -931731844}, {"key": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "name": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "size": 1712, "crc": 1203360300}, {"key": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "name": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "size": 702, "crc": -2048038003}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "size": 1146, "crc": -1354993597}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "size": 10593, "crc": 621262538}, {"key": "androidx/core/app/NotificationManagerCompat$Task.class", "name": "androidx/core/app/NotificationManagerCompat$Task.class", "size": 366, "crc": **********}, {"key": "androidx/core/app/NotificationManagerCompat.class", "name": "androidx/core/app/NotificationManagerCompat.class", "size": 18809, "crc": 384970419}, {"key": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "name": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "size": 501, "crc": **********}, {"key": "androidx/core/app/OnNewIntentProvider.class", "name": "androidx/core/app/OnNewIntentProvider.class", "size": 427, "crc": 152539972}, {"key": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "name": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "size": 526, "crc": **********}, {"key": "androidx/core/app/PendingIntentCompat$Api16Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api16Impl.class", "size": 1480, "crc": 925510753}, {"key": "androidx/core/app/PendingIntentCompat$Api23Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api23Impl.class", "size": 1701, "crc": -**********}, {"key": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "size": 909, "crc": **********}, {"key": "androidx/core/app/PendingIntentCompat$Flags.class", "name": "androidx/core/app/PendingIntentCompat$Flags.class", "size": 636, "crc": -**********}, {"key": "androidx/core/app/PendingIntentCompat$GatedCallback.class", "name": "androidx/core/app/PendingIntentCompat$GatedCallback.class", "size": 2756, "crc": -**********}, {"key": "androidx/core/app/PendingIntentCompat.class", "name": "androidx/core/app/PendingIntentCompat.class", "size": 5999, "crc": -996779470}, {"key": "androidx/core/app/Person$Api22Impl.class", "name": "androidx/core/app/Person$Api22Impl.class", "size": 2091, "crc": **********}, {"key": "androidx/core/app/Person$Api28Impl.class", "name": "androidx/core/app/Person$Api28Impl.class", "size": 2483, "crc": 87481841}, {"key": "androidx/core/app/Person$Builder.class", "name": "androidx/core/app/Person$Builder.class", "size": 2157, "crc": **********}, {"key": "androidx/core/app/Person.class", "name": "androidx/core/app/Person.class", "size": 5386, "crc": -226213189}, {"key": "androidx/core/app/PictureInPictureModeChangedInfo.class", "name": "androidx/core/app/PictureInPictureModeChangedInfo.class", "size": 1417, "crc": **********}, {"key": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "size": 2126, "crc": -**********}, {"key": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "size": 930, "crc": -235726249}, {"key": "androidx/core/app/RemoteActionCompat.class", "name": "androidx/core/app/RemoteActionCompat.class", "size": 4541, "crc": **********}, {"key": "androidx/core/app/RemoteActionCompatParcelizer.class", "name": "androidx/core/app/RemoteActionCompatParcelizer.class", "size": 2304, "crc": -505423669}, {"key": "androidx/core/app/RemoteInput$Api16Impl.class", "name": "androidx/core/app/RemoteInput$Api16Impl.class", "size": 1011, "crc": **********}, {"key": "androidx/core/app/RemoteInput$Api20Impl.class", "name": "androidx/core/app/RemoteInput$Api20Impl.class", "size": 4260, "crc": **********}, {"key": "androidx/core/app/RemoteInput$Api26Impl.class", "name": "androidx/core/app/RemoteInput$Api26Impl.class", "size": 2250, "crc": 1594028255}, {"key": "androidx/core/app/RemoteInput$Api28Impl.class", "name": "androidx/core/app/RemoteInput$Api28Impl.class", "size": 886, "crc": -1459103030}, {"key": "androidx/core/app/RemoteInput$Api29Impl.class", "name": "androidx/core/app/RemoteInput$Api29Impl.class", "size": 1105, "crc": -301845870}, {"key": "androidx/core/app/RemoteInput$Builder.class", "name": "androidx/core/app/RemoteInput$Builder.class", "size": 3002, "crc": 703901060}, {"key": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "name": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "size": 663, "crc": 137218255}, {"key": "androidx/core/app/RemoteInput$Source.class", "name": "androidx/core/app/RemoteInput$Source.class", "size": 627, "crc": 1225634699}, {"key": "androidx/core/app/RemoteInput.class", "name": "androidx/core/app/RemoteInput.class", "size": 10936, "crc": -2090754751}, {"key": "androidx/core/app/ServiceCompat$Api24Impl.class", "name": "androidx/core/app/ServiceCompat$Api24Impl.class", "size": 757, "crc": 6515986}, {"key": "androidx/core/app/ServiceCompat$Api29Impl.class", "name": "androidx/core/app/ServiceCompat$Api29Impl.class", "size": 993, "crc": 768644654}, {"key": "androidx/core/app/ServiceCompat$Api34Impl.class", "name": "androidx/core/app/ServiceCompat$Api34Impl.class", "size": 997, "crc": -1993685072}, {"key": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "name": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "size": 659, "crc": 655432038}, {"key": "androidx/core/app/ServiceCompat.class", "name": "androidx/core/app/ServiceCompat.class", "size": 1777, "crc": -921313391}, {"key": "androidx/core/app/ShareCompat$Api16Impl.class", "name": "androidx/core/app/ShareCompat$Api16Impl.class", "size": 2751, "crc": -976233903}, {"key": "androidx/core/app/ShareCompat$IntentBuilder.class", "name": "androidx/core/app/ShareCompat$IntentBuilder.class", "size": 8775, "crc": -591765183}, {"key": "androidx/core/app/ShareCompat$IntentReader.class", "name": "androidx/core/app/ShareCompat$IntentReader.class", "size": 7893, "crc": -407420263}, {"key": "androidx/core/app/ShareCompat.class", "name": "androidx/core/app/ShareCompat.class", "size": 4677, "crc": 1402677223}, {"key": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "name": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "size": 317, "crc": 1217450772}, {"key": "androidx/core/app/SharedElementCallback.class", "name": "androidx/core/app/SharedElementCallback.class", "size": 7188, "crc": 376381531}, {"key": "androidx/core/app/TaskStackBuilder$Api16Impl.class", "name": "androidx/core/app/TaskStackBuilder$Api16Impl.class", "size": 960, "crc": 13500967}, {"key": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "name": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "size": 385, "crc": 2113967134}, {"key": "androidx/core/app/TaskStackBuilder.class", "name": "androidx/core/app/TaskStackBuilder.class", "size": 6844, "crc": -334827036}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "size": 868, "crc": 1227855709}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "size": 1775, "crc": 2038111093}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "size": 2239, "crc": 1366924006}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "size": 1116, "crc": -1953749014}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "size": 992, "crc": -957843627}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "size": 1945, "crc": **********}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "size": 2617, "crc": -111289785}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "size": 1181, "crc": **********}, {"key": "androidx/core/content/ContentProviderCompat.class", "name": "androidx/core/content/ContentProviderCompat.class", "size": 986, "crc": -917373858}, {"key": "androidx/core/content/ContentResolverCompat$Api16Impl.class", "name": "androidx/core/content/ContentResolverCompat$Api16Impl.class", "size": 1345, "crc": 721940027}, {"key": "androidx/core/content/ContentResolverCompat.class", "name": "androidx/core/content/ContentResolverCompat.class", "size": 2209, "crc": 855935904}, {"key": "androidx/core/content/ContextCompat$Api16Impl.class", "name": "androidx/core/content/ContextCompat$Api16Impl.class", "size": 1217, "crc": 728892619}, {"key": "androidx/core/content/ContextCompat$Api17Impl.class", "name": "androidx/core/content/ContextCompat$Api17Impl.class", "size": 936, "crc": 938915157}, {"key": "androidx/core/content/ContextCompat$Api19Impl.class", "name": "androidx/core/content/ContextCompat$Api19Impl.class", "size": 1124, "crc": -223011922}, {"key": "androidx/core/content/ContextCompat$Api21Impl.class", "name": "androidx/core/content/ContextCompat$Api21Impl.class", "size": 1106, "crc": -779455471}, {"key": "androidx/core/content/ContextCompat$Api23Impl.class", "name": "androidx/core/content/ContextCompat$Api23Impl.class", "size": 1521, "crc": -1480214560}, {"key": "androidx/core/content/ContextCompat$Api24Impl.class", "name": "androidx/core/content/ContextCompat$Api24Impl.class", "size": 1123, "crc": 455663743}, {"key": "androidx/core/content/ContextCompat$Api26Impl.class", "name": "androidx/core/content/ContextCompat$Api26Impl.class", "size": 1971, "crc": -118745534}, {"key": "androidx/core/content/ContextCompat$Api28Impl.class", "name": "androidx/core/content/ContextCompat$Api28Impl.class", "size": 809, "crc": 148513440}, {"key": "androidx/core/content/ContextCompat$Api30Impl.class", "name": "androidx/core/content/ContextCompat$Api30Impl.class", "size": 1736, "crc": -1897489123}, {"key": "androidx/core/content/ContextCompat$Api33Impl.class", "name": "androidx/core/content/ContextCompat$Api33Impl.class", "size": 1337, "crc": 1406128839}, {"key": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "name": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "size": 4410, "crc": -**********}, {"key": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "name": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "size": 658, "crc": 794825751}, {"key": "androidx/core/content/ContextCompat.class", "name": "androidx/core/content/ContextCompat.class", "size": 13626, "crc": -**********}, {"key": "androidx/core/content/FileProvider$Api21Impl.class", "name": "androidx/core/content/FileProvider$Api21Impl.class", "size": 782, "crc": **********}, {"key": "androidx/core/content/FileProvider$PathStrategy.class", "name": "androidx/core/content/FileProvider$PathStrategy.class", "size": 346, "crc": 770878075}, {"key": "androidx/core/content/FileProvider$SimplePathStrategy.class", "name": "androidx/core/content/FileProvider$SimplePathStrategy.class", "size": 4336, "crc": 309084276}, {"key": "androidx/core/content/FileProvider.class", "name": "androidx/core/content/FileProvider.class", "size": 12786, "crc": 237188370}, {"key": "androidx/core/content/IntentCompat$Api15Impl.class", "name": "androidx/core/content/IntentCompat$Api15Impl.class", "size": 815, "crc": 78225125}, {"key": "androidx/core/content/IntentCompat$Api33Impl.class", "name": "androidx/core/content/IntentCompat$Api33Impl.class", "size": 2132, "crc": -160043687}, {"key": "androidx/core/content/IntentCompat.class", "name": "androidx/core/content/IntentCompat.class", "size": 5131, "crc": **********}, {"key": "androidx/core/content/IntentSanitizer$1.class", "name": "androidx/core/content/IntentSanitizer$1.class", "size": 237, "crc": -61702665}, {"key": "androidx/core/content/IntentSanitizer$Api15Impl.class", "name": "androidx/core/content/IntentSanitizer$Api15Impl.class", "size": 1006, "crc": **********}, {"key": "androidx/core/content/IntentSanitizer$Api16Impl$Api31Impl.class", "name": "androidx/core/content/IntentSanitizer$Api16Impl$Api31Impl.class", "size": 1834, "crc": **********}, {"key": "androidx/core/content/IntentSanitizer$Api16Impl.class", "name": "androidx/core/content/IntentSanitizer$Api16Impl.class", "size": 4375, "crc": -170329507}, {"key": "androidx/core/content/IntentSanitizer$Api29Impl.class", "name": "androidx/core/content/IntentSanitizer$Api29Impl.class", "size": 1051, "crc": 1195648172}, {"key": "androidx/core/content/IntentSanitizer$Builder.class", "name": "androidx/core/content/IntentSanitizer$Builder.class", "size": 13723, "crc": -959352565}, {"key": "androidx/core/content/IntentSanitizer.class", "name": "androidx/core/content/IntentSanitizer.class", "size": 10774, "crc": **********}, {"key": "androidx/core/content/LocusIdCompat$Api29Impl.class", "name": "androidx/core/content/LocusIdCompat$Api29Impl.class", "size": 1024, "crc": 89574739}, {"key": "androidx/core/content/LocusIdCompat.class", "name": "androidx/core/content/LocusIdCompat.class", "size": 2782, "crc": 412886872}, {"key": "androidx/core/content/MimeTypeFilter.class", "name": "androidx/core/content/MimeTypeFilter.class", "size": 2742, "crc": 147689054}, {"key": "androidx/core/content/OnConfigurationChangedProvider.class", "name": "androidx/core/content/OnConfigurationChangedProvider.class", "size": 486, "crc": -327760253}, {"key": "androidx/core/content/OnTrimMemoryProvider.class", "name": "androidx/core/content/OnTrimMemoryProvider.class", "size": 430, "crc": -53127615}, {"key": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "name": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "size": 1002, "crc": -**********}, {"key": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "name": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "size": 691, "crc": **********}, {"key": "androidx/core/content/PackageManagerCompat.class", "name": "androidx/core/content/PackageManagerCompat.class", "size": 6194, "crc": **********}, {"key": "androidx/core/content/PermissionChecker$PermissionResult.class", "name": "androidx/core/content/PermissionChecker$PermissionResult.class", "size": 673, "crc": -666488629}, {"key": "androidx/core/content/PermissionChecker.class", "name": "androidx/core/content/PermissionChecker.class", "size": 2862, "crc": **********}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "size": 1116, "crc": 587673952}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "size": 1381, "crc": -494629376}, {"key": "androidx/core/content/SharedPreferencesCompat.class", "name": "androidx/core/content/SharedPreferencesCompat.class", "size": 532, "crc": 1567352910}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "size": 1290, "crc": 1695615859}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "size": 1604, "crc": 843169627}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "size": 1591, "crc": -668859029}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "size": 1862, "crc": 1397470612}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "size": 4184, "crc": -1517503401}, {"key": "androidx/core/content/UnusedAppRestrictionsConstants.class", "name": "androidx/core/content/UnusedAppRestrictionsConstants.class", "size": 588, "crc": -1244214169}, {"key": "androidx/core/content/UriMatcherCompat.class", "name": "androidx/core/content/UriMatcherCompat.class", "size": 1524, "crc": 1164334199}, {"key": "androidx/core/content/pm/ActivityInfoCompat.class", "name": "androidx/core/content/pm/ActivityInfoCompat.class", "size": 493, "crc": -1364477614}, {"key": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "size": 1924, "crc": 1118492697}, {"key": "androidx/core/content/pm/PackageInfoCompat.class", "name": "androidx/core/content/pm/PackageInfoCompat.class", "size": 6094, "crc": -587297962}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "size": 929, "crc": -417971139}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "size": 663, "crc": 1629059509}, {"key": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "name": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "size": 739, "crc": 1522065108}, {"key": "androidx/core/content/pm/PermissionInfoCompat.class", "name": "androidx/core/content/pm/PermissionInfoCompat.class", "size": 1364, "crc": 1297374523}, {"key": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "name": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "size": 1703, "crc": 427897560}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "size": 1000, "crc": 1878927332}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "size": 12666, "crc": -1317622191}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "size": 664, "crc": 282044856}, {"key": "androidx/core/content/pm/ShortcutInfoCompat.class", "name": "androidx/core/content/pm/ShortcutInfoCompat.class", "size": 12893, "crc": -1893822061}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "size": 1718, "crc": 780394003}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "size": 1544, "crc": -976705994}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "size": 1316, "crc": -837853435}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "size": 1498, "crc": -518766418}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "size": 695, "crc": 856990973}, {"key": "androidx/core/content/pm/ShortcutManagerCompat.class", "name": "androidx/core/content/pm/ShortcutManagerCompat.class", "size": 20131, "crc": 1875370769}, {"key": "androidx/core/content/pm/ShortcutXmlParser.class", "name": "androidx/core/content/pm/ShortcutXmlParser.class", "size": 5957, "crc": 443382465}, {"key": "androidx/core/content/res/CamColor.class", "name": "androidx/core/content/res/CamColor.class", "size": 10209, "crc": 707293299}, {"key": "androidx/core/content/res/CamUtils.class", "name": "androidx/core/content/res/CamUtils.class", "size": 3152, "crc": 1105906618}, {"key": "androidx/core/content/res/ColorStateListInflaterCompat.class", "name": "androidx/core/content/res/ColorStateListInflaterCompat.class", "size": 8167, "crc": 271679968}, {"key": "androidx/core/content/res/ComplexColorCompat.class", "name": "androidx/core/content/res/ComplexColorCompat.class", "size": 5222, "crc": -1398844043}, {"key": "androidx/core/content/res/ConfigurationHelper.class", "name": "androidx/core/content/res/ConfigurationHelper.class", "size": 1019, "crc": 1994258360}, {"key": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "name": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "size": 854, "crc": -1930670641}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "size": 287, "crc": -39793732}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "size": 454, "crc": 1823731370}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "size": 1265, "crc": -**********}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "size": 1652, "crc": **********}, {"key": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "size": 1996, "crc": -537995543}, {"key": "androidx/core/content/res/FontResourcesParserCompat.class", "name": "androidx/core/content/res/FontResourcesParserCompat.class", "size": 8708, "crc": -346635419}, {"key": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "name": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "size": 1735, "crc": 246697236}, {"key": "androidx/core/content/res/GradientColorInflaterCompat.class", "name": "androidx/core/content/res/GradientColorInflaterCompat.class", "size": 7737, "crc": 594254899}, {"key": "androidx/core/content/res/GrowingArrayUtils.class", "name": "androidx/core/content/res/GrowingArrayUtils.class", "size": 2719, "crc": **********}, {"key": "androidx/core/content/res/ResourcesCompat$Api15Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api15Impl.class", "size": 914, "crc": -**********}, {"key": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "size": 1412, "crc": -2116481781}, {"key": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "size": 1484, "crc": 1720045768}, {"key": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "size": 925, "crc": 1053692186}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "size": 1305, "crc": -1522123679}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "size": 1493, "crc": 2049931843}, {"key": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "name": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "size": 2653, "crc": -560905658}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "size": 2299, "crc": -664508770}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "size": 1036, "crc": 1625359043}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "size": 1131, "crc": 812159584}, {"key": "androidx/core/content/res/ResourcesCompat.class", "name": "androidx/core/content/res/ResourcesCompat.class", "size": 14425, "crc": 1968615805}, {"key": "androidx/core/content/res/TypedArrayUtils.class", "name": "androidx/core/content/res/TypedArrayUtils.class", "size": 8994, "crc": 743892670}, {"key": "androidx/core/content/res/ViewingConditions.class", "name": "androidx/core/content/res/ViewingConditions.class", "size": 3538, "crc": -131663576}, {"key": "androidx/core/database/CursorWindowCompat$Api15Impl.class", "name": "androidx/core/database/CursorWindowCompat$Api15Impl.class", "size": 820, "crc": -363931225}, {"key": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "name": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "size": 855, "crc": 1161348905}, {"key": "androidx/core/database/CursorWindowCompat.class", "name": "androidx/core/database/CursorWindowCompat.class", "size": 1166, "crc": -701861813}, {"key": "androidx/core/database/DatabaseUtilsCompat.class", "name": "androidx/core/database/DatabaseUtilsCompat.class", "size": 1371, "crc": -2044651927}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "size": 889, "crc": -948675496}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "size": 903, "crc": 1059320687}, {"key": "androidx/core/graphics/BitmapCompat$Api17Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api17Impl.class", "size": 901, "crc": 676655578}, {"key": "androidx/core/graphics/BitmapCompat$Api19Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api19Impl.class", "size": 758, "crc": 1257339246}, {"key": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "size": 2569, "crc": 233311887}, {"key": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "size": 878, "crc": -1543917151}, {"key": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "size": 1082, "crc": -2065648772}, {"key": "androidx/core/graphics/BitmapCompat.class", "name": "androidx/core/graphics/BitmapCompat.class", "size": 6262, "crc": -1760324130}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "size": 937, "crc": 652344203}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat.class", "size": 1837, "crc": -1703162937}, {"key": "androidx/core/graphics/BlendModeCompat.class", "name": "androidx/core/graphics/BlendModeCompat.class", "size": 2888, "crc": -645227784}, {"key": "androidx/core/graphics/BlendModeUtils$1.class", "name": "androidx/core/graphics/BlendModeUtils$1.class", "size": 2083, "crc": 1721728409}, {"key": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "name": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "size": 2166, "crc": 1973059920}, {"key": "androidx/core/graphics/BlendModeUtils.class", "name": "androidx/core/graphics/BlendModeUtils.class", "size": 1725, "crc": -1150213795}, {"key": "androidx/core/graphics/ColorUtils$Api26Impl.class", "name": "androidx/core/graphics/ColorUtils$Api26Impl.class", "size": 2133, "crc": -1767282957}, {"key": "androidx/core/graphics/ColorUtils.class", "name": "androidx/core/graphics/ColorUtils.class", "size": 11354, "crc": 572223509}, {"key": "androidx/core/graphics/Insets$Api29Impl.class", "name": "androidx/core/graphics/Insets$Api29Impl.class", "size": 741, "crc": -861437350}, {"key": "androidx/core/graphics/Insets.class", "name": "androidx/core/graphics/Insets.class", "size": 3797, "crc": 1611887429}, {"key": "androidx/core/graphics/PaintCompat$Api23Impl.class", "name": "androidx/core/graphics/PaintCompat$Api23Impl.class", "size": 813, "crc": -3322387}, {"key": "androidx/core/graphics/PaintCompat$Api29Impl.class", "name": "androidx/core/graphics/PaintCompat$Api29Impl.class", "size": 869, "crc": 1376448595}, {"key": "androidx/core/graphics/PaintCompat.class", "name": "androidx/core/graphics/PaintCompat.class", "size": 4190, "crc": -1948255028}, {"key": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "name": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "size": 492, "crc": 1852582435}, {"key": "androidx/core/graphics/PathParser$PathDataNode.class", "name": "androidx/core/graphics/PathParser$PathDataNode.class", "size": 8608, "crc": -1959139859}, {"key": "androidx/core/graphics/PathParser.class", "name": "androidx/core/graphics/PathParser.class", "size": 7006, "crc": 299179472}, {"key": "androidx/core/graphics/PathSegment.class", "name": "androidx/core/graphics/PathSegment.class", "size": 2433, "crc": 1148524092}, {"key": "androidx/core/graphics/PathUtils$Api26Impl.class", "name": "androidx/core/graphics/PathUtils$Api26Impl.class", "size": 764, "crc": 1780295725}, {"key": "androidx/core/graphics/PathUtils.class", "name": "androidx/core/graphics/PathUtils.class", "size": 2168, "crc": -716351640}, {"key": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "name": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "size": 1711, "crc": -1952277969}, {"key": "androidx/core/graphics/TypefaceCompat.class", "name": "androidx/core/graphics/TypefaceCompat.class", "size": 10224, "crc": -464929013}, {"key": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "size": 9920, "crc": 1427569578}, {"key": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "size": 8166, "crc": 541108707}, {"key": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "size": 13353, "crc": -1021628}, {"key": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "size": 3170, "crc": 1765335205}, {"key": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "size": 8266, "crc": -766524581}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "size": 1680, "crc": 2020885920}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "size": 1989, "crc": 992270278}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "size": 1990, "crc": 1646487761}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "size": 446, "crc": 1807309830}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "size": 10576, "crc": 126501558}, {"key": "androidx/core/graphics/TypefaceCompatUtil$Api19Impl.class", "name": "androidx/core/graphics/TypefaceCompatUtil$Api19Impl.class", "size": 1207, "crc": -903087186}, {"key": "androidx/core/graphics/TypefaceCompatUtil.class", "name": "androidx/core/graphics/TypefaceCompatUtil.class", "size": 7907, "crc": 158881309}, {"key": "androidx/core/graphics/WeightTypefaceApi14.class", "name": "androidx/core/graphics/WeightTypefaceApi14.class", "size": 5229, "crc": -489331575}, {"key": "androidx/core/graphics/WeightTypefaceApi21.class", "name": "androidx/core/graphics/WeightTypefaceApi21.class", "size": 6056, "crc": 458843757}, {"key": "androidx/core/graphics/WeightTypefaceApi26.class", "name": "androidx/core/graphics/WeightTypefaceApi26.class", "size": 5541, "crc": -1847966844}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api19Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api19Impl.class", "size": 1971, "crc": -502692922}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "size": 3290, "crc": 223090167}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "size": 1011, "crc": -1707470127}, {"key": "androidx/core/graphics/drawable/DrawableCompat.class", "name": "androidx/core/graphics/drawable/DrawableCompat.class", "size": 8532, "crc": 1256185088}, {"key": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "size": 7848, "crc": -1288128777}, {"key": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "size": 1260, "crc": 1316781001}, {"key": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "size": 1176, "crc": 1899525487}, {"key": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "size": 802, "crc": -2007874236}, {"key": "androidx/core/graphics/drawable/IconCompat$IconType.class", "name": "androidx/core/graphics/drawable/IconCompat$IconType.class", "size": 643, "crc": 1843019074}, {"key": "androidx/core/graphics/drawable/IconCompat.class", "name": "androidx/core/graphics/drawable/IconCompat.class", "size": 23766, "crc": -1057666863}, {"key": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "name": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "size": 2391, "crc": -1370437693}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "size": 7892, "crc": -626571484}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "size": 1864, "crc": 468816631}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "size": 1643, "crc": 938684441}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "size": 2653, "crc": 1800860871}, {"key": "androidx/core/graphics/drawable/TintAwareDrawable.class", "name": "androidx/core/graphics/drawable/TintAwareDrawable.class", "size": 745, "crc": 1348044187}, {"key": "androidx/core/graphics/drawable/WrappedDrawable.class", "name": "androidx/core/graphics/drawable/WrappedDrawable.class", "size": 560, "crc": -747113056}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "size": 8984, "crc": -2009081194}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "size": 4146, "crc": -859088974}, {"key": "androidx/core/graphics/drawable/WrappedDrawableState.class", "name": "androidx/core/graphics/drawable/WrappedDrawableState.class", "size": 2151, "crc": 1765756452}, {"key": "androidx/core/hardware/display/DisplayManagerCompat$Api17Impl.class", "name": "androidx/core/hardware/display/DisplayManagerCompat$Api17Impl.class", "size": 1123, "crc": -464833568}, {"key": "androidx/core/hardware/display/DisplayManagerCompat.class", "name": "androidx/core/hardware/display/DisplayManagerCompat.class", "size": 3240, "crc": -971817686}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "size": 2815, "crc": 415213846}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "size": 4151, "crc": 1807485789}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "size": 1321, "crc": -2076756923}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "size": 1010, "crc": 2024161105}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "size": 1522, "crc": -2124223204}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "size": 4724, "crc": -78804119}, {"key": "androidx/core/internal/package-info.class", "name": "androidx/core/internal/package-info.class", "size": 404, "crc": -1162714476}, {"key": "androidx/core/internal/view/SupportMenu.class", "name": "androidx/core/internal/view/SupportMenu.class", "size": 765, "crc": 1726273822}, {"key": "androidx/core/internal/view/SupportMenuItem.class", "name": "androidx/core/internal/view/SupportMenuItem.class", "size": 2836, "crc": 1950125442}, {"key": "androidx/core/internal/view/SupportSubMenu.class", "name": "androidx/core/internal/view/SupportSubMenu.class", "size": 488, "crc": 341992331}, {"key": "androidx/core/location/GnssStatusCompat$Callback.class", "name": "androidx/core/location/GnssStatusCompat$Callback.class", "size": 1011, "crc": -106236767}, {"key": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "name": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "size": 661, "crc": 600754332}, {"key": "androidx/core/location/GnssStatusCompat.class", "name": "androidx/core/location/GnssStatusCompat.class", "size": 2791, "crc": 821246616}, {"key": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "size": 988, "crc": -2010772826}, {"key": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "size": 982, "crc": -494001482}, {"key": "androidx/core/location/GnssStatusWrapper.class", "name": "androidx/core/location/GnssStatusWrapper.class", "size": 3112, "crc": -197759893}, {"key": "androidx/core/location/GpsStatusWrapper.class", "name": "androidx/core/location/GpsStatusWrapper.class", "size": 4956, "crc": 1004775428}, {"key": "androidx/core/location/LocationCompat$Api17Impl.class", "name": "androidx/core/location/LocationCompat$Api17Impl.class", "size": 775, "crc": -339127679}, {"key": "androidx/core/location/LocationCompat$Api18Impl.class", "name": "androidx/core/location/LocationCompat$Api18Impl.class", "size": 779, "crc": 854471195}, {"key": "androidx/core/location/LocationCompat$Api26Impl.class", "name": "androidx/core/location/LocationCompat$Api26Impl.class", "size": 3437, "crc": 1370503878}, {"key": "androidx/core/location/LocationCompat$Api28Impl.class", "name": "androidx/core/location/LocationCompat$Api28Impl.class", "size": 4116, "crc": 389406481}, {"key": "androidx/core/location/LocationCompat$Api29Impl.class", "name": "androidx/core/location/LocationCompat$Api29Impl.class", "size": 1476, "crc": 1439795986}, {"key": "androidx/core/location/LocationCompat$Api33Impl.class", "name": "androidx/core/location/LocationCompat$Api33Impl.class", "size": 988, "crc": 1508570960}, {"key": "androidx/core/location/LocationCompat$Api34Impl.class", "name": "androidx/core/location/LocationCompat$Api34Impl.class", "size": 1815, "crc": 1822782053}, {"key": "androidx/core/location/LocationCompat.class", "name": "androidx/core/location/LocationCompat.class", "size": 11003, "crc": -1595270392}, {"key": "androidx/core/location/LocationListenerCompat.class", "name": "androidx/core/location/LocationListenerCompat.class", "size": 1553, "crc": 1096933018}, {"key": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "size": 3902, "crc": 1277866107}, {"key": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "size": 4045, "crc": -1140816175}, {"key": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "size": 1168, "crc": 579110376}, {"key": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "size": 6124, "crc": 1436564282}, {"key": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "size": 2316, "crc": 1940596961}, {"key": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "name": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "size": 4695, "crc": 1199461944}, {"key": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "name": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "size": 1188, "crc": 1963999382}, {"key": "androidx/core/location/LocationManagerCompat$GnssMeasurementsTransport.class", "name": "androidx/core/location/LocationManagerCompat$GnssMeasurementsTransport.class", "size": 2936, "crc": -1562865333}, {"key": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "size": 1738, "crc": 1638196693}, {"key": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "size": 4297, "crc": 1299802817}, {"key": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "name": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "size": 1595, "crc": 160722682}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "size": 1396, "crc": 844033778}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "size": 5175, "crc": 1411058934}, {"key": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "size": 4058, "crc": -714916522}, {"key": "androidx/core/location/LocationManagerCompat.class", "name": "androidx/core/location/LocationManagerCompat.class", "size": 21891, "crc": 473982506}, {"key": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "size": 3099, "crc": 1349377216}, {"key": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "size": 1654, "crc": -1472976218}, {"key": "androidx/core/location/LocationRequestCompat$Builder.class", "name": "androidx/core/location/LocationRequestCompat$Builder.class", "size": 3768, "crc": -1665239680}, {"key": "androidx/core/location/LocationRequestCompat$Quality.class", "name": "androidx/core/location/LocationRequestCompat$Quality.class", "size": 656, "crc": -1304604809}, {"key": "androidx/core/location/LocationRequestCompat.class", "name": "androidx/core/location/LocationRequestCompat.class", "size": 4935, "crc": 1144624716}, {"key": "androidx/core/math/MathUtils.class", "name": "androidx/core/math/MathUtils.class", "size": 2973, "crc": -1763529000}, {"key": "androidx/core/net/ConnectivityManagerCompat$Api16Impl.class", "name": "androidx/core/net/ConnectivityManagerCompat$Api16Impl.class", "size": 926, "crc": -1610839211}, {"key": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "name": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "size": 837, "crc": 890930868}, {"key": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "name": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "size": 705, "crc": 716344203}, {"key": "androidx/core/net/ConnectivityManagerCompat.class", "name": "androidx/core/net/ConnectivityManagerCompat.class", "size": 2474, "crc": -699277786}, {"key": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "size": 2647, "crc": 1217154937}, {"key": "androidx/core/net/DatagramSocketWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper.class", "size": 694, "crc": 1450467363}, {"key": "androidx/core/net/MailTo.class", "name": "androidx/core/net/MailTo.class", "size": 4777, "crc": 716149100}, {"key": "androidx/core/net/ParseException.class", "name": "androidx/core/net/ParseException.class", "size": 531, "crc": -1332245760}, {"key": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "name": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "size": 933, "crc": -2134102274}, {"key": "androidx/core/net/TrafficStatsCompat.class", "name": "androidx/core/net/TrafficStatsCompat.class", "size": 2384, "crc": 2031265214}, {"key": "androidx/core/net/UriCompat.class", "name": "androidx/core/net/UriCompat.class", "size": 1861, "crc": 957459528}, {"key": "androidx/core/os/BundleCompat$Api18Impl.class", "name": "androidx/core/os/BundleCompat$Api18Impl.class", "size": 1072, "crc": -992064560}, {"key": "androidx/core/os/BundleCompat$Api33Impl.class", "name": "androidx/core/os/BundleCompat$Api33Impl.class", "size": 2513, "crc": 1675127610}, {"key": "androidx/core/os/BundleCompat$BeforeApi18Impl.class", "name": "androidx/core/os/BundleCompat$BeforeApi18Impl.class", "size": 2505, "crc": -1269823287}, {"key": "androidx/core/os/BundleCompat.class", "name": "androidx/core/os/BundleCompat.class", "size": 3727, "crc": -332060587}, {"key": "androidx/core/os/CancellationSignal$Api16Impl.class", "name": "androidx/core/os/CancellationSignal$Api16Impl.class", "size": 879, "crc": 844625927}, {"key": "androidx/core/os/CancellationSignal$OnCancelListener.class", "name": "androidx/core/os/CancellationSignal$OnCancelListener.class", "size": 267, "crc": 1768443177}, {"key": "androidx/core/os/CancellationSignal.class", "name": "androidx/core/os/CancellationSignal.class", "size": 2715, "crc": -1580098536}, {"key": "androidx/core/os/ConfigurationCompat$Api17Impl.class", "name": "androidx/core/os/ConfigurationCompat$Api17Impl.class", "size": 1143, "crc": 1135921636}, {"key": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "name": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "size": 1305, "crc": -934623649}, {"key": "androidx/core/os/ConfigurationCompat.class", "name": "androidx/core/os/ConfigurationCompat.class", "size": 1650, "crc": 1990453140}, {"key": "androidx/core/os/EnvironmentCompat$Api19Impl.class", "name": "androidx/core/os/EnvironmentCompat$Api19Impl.class", "size": 739, "crc": 742022865}, {"key": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "name": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "size": 747, "crc": -547922024}, {"key": "androidx/core/os/EnvironmentCompat.class", "name": "androidx/core/os/EnvironmentCompat.class", "size": 1828, "crc": -1399338412}, {"key": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "name": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "size": 1394, "crc": 817974452}, {"key": "androidx/core/os/ExecutorCompat.class", "name": "androidx/core/os/ExecutorCompat.class", "size": 732, "crc": 813340522}, {"key": "androidx/core/os/HandlerCompat$Api28Impl.class", "name": "androidx/core/os/HandlerCompat$Api28Impl.class", "size": 1266, "crc": -523528807}, {"key": "androidx/core/os/HandlerCompat$Api29Impl.class", "name": "androidx/core/os/HandlerCompat$Api29Impl.class", "size": 750, "crc": 999905912}, {"key": "androidx/core/os/HandlerCompat.class", "name": "androidx/core/os/HandlerCompat.class", "size": 5070, "crc": 1332842940}, {"key": "androidx/core/os/LocaleListCompat$Api21Impl.class", "name": "androidx/core/os/LocaleListCompat$Api21Impl.class", "size": 1997, "crc": -1056214764}, {"key": "androidx/core/os/LocaleListCompat$Api24Impl.class", "name": "androidx/core/os/LocaleListCompat$Api24Impl.class", "size": 969, "crc": 2114069988}, {"key": "androidx/core/os/LocaleListCompat.class", "name": "androidx/core/os/LocaleListCompat.class", "size": 5727, "crc": 504905637}, {"key": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "name": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "size": 784, "crc": -803210832}, {"key": "androidx/core/os/LocaleListCompatWrapper.class", "name": "androidx/core/os/LocaleListCompatWrapper.class", "size": 6913, "crc": 1732702063}, {"key": "androidx/core/os/LocaleListInterface.class", "name": "androidx/core/os/LocaleListInterface.class", "size": 673, "crc": -1204215516}, {"key": "androidx/core/os/LocaleListPlatformWrapper.class", "name": "androidx/core/os/LocaleListPlatformWrapper.class", "size": 1937, "crc": 1832161889}, {"key": "androidx/core/os/MessageCompat$Api22Impl.class", "name": "androidx/core/os/MessageCompat$Api22Impl.class", "size": 884, "crc": -1707707376}, {"key": "androidx/core/os/MessageCompat.class", "name": "androidx/core/os/MessageCompat.class", "size": 1409, "crc": 2099829457}, {"key": "androidx/core/os/OperationCanceledException.class", "name": "androidx/core/os/OperationCanceledException.class", "size": 734, "crc": 1084133341}, {"key": "androidx/core/os/ParcelCompat$Api29Impl.class", "name": "androidx/core/os/ParcelCompat$Api29Impl.class", "size": 1234, "crc": 695637281}, {"key": "androidx/core/os/ParcelCompat$Api30Impl.class", "name": "androidx/core/os/ParcelCompat$Api30Impl.class", "size": 1173, "crc": -1581830279}, {"key": "androidx/core/os/ParcelCompat$Api33Impl.class", "name": "androidx/core/os/ParcelCompat$Api33Impl.class", "size": 5851, "crc": 359949473}, {"key": "androidx/core/os/ParcelCompat.class", "name": "androidx/core/os/ParcelCompat.class", "size": 9011, "crc": 1763807924}, {"key": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "name": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "size": 1865, "crc": -1411295453}, {"key": "androidx/core/os/ParcelableCompat.class", "name": "androidx/core/os/ParcelableCompat.class", "size": 1172, "crc": 276111255}, {"key": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "name": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "size": 521, "crc": -1661855711}, {"key": "androidx/core/os/ProcessCompat$Api16Impl.class", "name": "androidx/core/os/ProcessCompat$Api16Impl.class", "size": 1794, "crc": 1573717268}, {"key": "androidx/core/os/ProcessCompat$Api17Impl.class", "name": "androidx/core/os/ProcessCompat$Api17Impl.class", "size": 1746, "crc": -1787655522}, {"key": "androidx/core/os/ProcessCompat$Api24Impl.class", "name": "androidx/core/os/ProcessCompat$Api24Impl.class", "size": 628, "crc": -451466529}, {"key": "androidx/core/os/ProcessCompat.class", "name": "androidx/core/os/ProcessCompat.class", "size": 817, "crc": -44670261}, {"key": "androidx/core/os/TraceCompat$Api18Impl.class", "name": "androidx/core/os/TraceCompat$Api18Impl.class", "size": 789, "crc": -719725466}, {"key": "androidx/core/os/TraceCompat$Api29Impl.class", "name": "androidx/core/os/TraceCompat$Api29Impl.class", "size": 1104, "crc": -1969664624}, {"key": "androidx/core/os/TraceCompat.class", "name": "androidx/core/os/TraceCompat.class", "size": 3795, "crc": 1272681506}, {"key": "androidx/core/os/UserHandleCompat$Api24Impl.class", "name": "androidx/core/os/UserHandleCompat$Api24Impl.class", "size": 712, "crc": 640979588}, {"key": "androidx/core/os/UserHandleCompat.class", "name": "androidx/core/os/UserHandleCompat.class", "size": 3061, "crc": -864588795}, {"key": "androidx/core/os/UserManagerCompat$Api24Impl.class", "name": "androidx/core/os/UserManagerCompat$Api24Impl.class", "size": 858, "crc": -689420772}, {"key": "androidx/core/os/UserManagerCompat.class", "name": "androidx/core/os/UserManagerCompat.class", "size": 785, "crc": -**********}, {"key": "androidx/core/provider/CallbackWithHandler$1.class", "name": "androidx/core/provider/CallbackWithHandler$1.class", "size": 1177, "crc": -**********}, {"key": "androidx/core/provider/CallbackWithHandler$2.class", "name": "androidx/core/provider/CallbackWithHandler$2.class", "size": 1101, "crc": -579370637}, {"key": "androidx/core/provider/CallbackWithHandler.class", "name": "androidx/core/provider/CallbackWithHandler.class", "size": 2578, "crc": -255845993}, {"key": "androidx/core/provider/CalleeHandler.class", "name": "androidx/core/provider/CalleeHandler.class", "size": 748, "crc": -356584081}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "size": 539, "crc": -744260981}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi19Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi19Impl.class", "size": 1632, "crc": 793146053}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "size": 2243, "crc": -**********}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "size": 1243, "crc": -**********}, {"key": "androidx/core/provider/DocumentsContractCompat.class", "name": "androidx/core/provider/DocumentsContractCompat.class", "size": 4399, "crc": -**********}, {"key": "androidx/core/provider/FontProvider$Api16Impl.class", "name": "androidx/core/provider/FontProvider$Api16Impl.class", "size": 1324, "crc": 93699198}, {"key": "androidx/core/provider/FontProvider.class", "name": "androidx/core/provider/FontProvider.class", "size": 9787, "crc": **********}, {"key": "androidx/core/provider/FontRequest.class", "name": "androidx/core/provider/FontRequest.class", "size": 4101, "crc": -843739472}, {"key": "androidx/core/provider/FontRequestWorker$1.class", "name": "androidx/core/provider/FontRequestWorker$1.class", "size": 1628, "crc": **********}, {"key": "androidx/core/provider/FontRequestWorker$2.class", "name": "androidx/core/provider/FontRequestWorker$2.class", "size": 1631, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker$3.class", "name": "androidx/core/provider/FontRequestWorker$3.class", "size": 1940, "crc": 129325094}, {"key": "androidx/core/provider/FontRequestWorker$4.class", "name": "androidx/core/provider/FontRequestWorker$4.class", "size": 2067, "crc": -757734499}, {"key": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "name": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "size": 1197, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker.class", "name": "androidx/core/provider/FontRequestWorker.class", "size": 8149, "crc": **********}, {"key": "androidx/core/provider/FontsContractCompat$Columns.class", "name": "androidx/core/provider/FontsContractCompat$Columns.class", "size": 1073, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "name": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "size": 1802, "crc": 569129717}, {"key": "androidx/core/provider/FontsContractCompat$FontInfo.class", "name": "androidx/core/provider/FontsContractCompat$FontInfo.class", "size": 2142, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "size": 809, "crc": -566122054}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "size": 1726, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat.class", "name": "androidx/core/provider/FontsContractCompat.class", "size": 7004, "crc": **********}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "size": 989, "crc": -**********}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "size": 1108, "crc": -**********}, {"key": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "name": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "size": 1416, "crc": **********}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "size": 1134, "crc": -721637920}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "size": 2086, "crc": -361715969}, {"key": "androidx/core/provider/RequestExecutor.class", "name": "androidx/core/provider/RequestExecutor.class", "size": 4032, "crc": 45623863}, {"key": "androidx/core/provider/SelfDestructiveThread$1.class", "name": "androidx/core/provider/SelfDestructiveThread$1.class", "size": 1098, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$2$1.class", "name": "androidx/core/provider/SelfDestructiveThread$2$1.class", "size": 1084, "crc": -**********}, {"key": "androidx/core/provider/SelfDestructiveThread$2.class", "name": "androidx/core/provider/SelfDestructiveThread$2.class", "size": 1835, "crc": -507995055}, {"key": "androidx/core/provider/SelfDestructiveThread$3.class", "name": "androidx/core/provider/SelfDestructiveThread$3.class", "size": 1943, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "name": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "size": 379, "crc": -538938372}, {"key": "androidx/core/provider/SelfDestructiveThread.class", "name": "androidx/core/provider/SelfDestructiveThread.class", "size": 6254, "crc": **********}, {"key": "androidx/core/service/quicksettings/PendingIntentActivityWrapper.class", "name": "androidx/core/service/quicksettings/PendingIntentActivityWrapper.class", "size": 2541, "crc": 1624604}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$Api24Impl.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$Api24Impl.class", "size": 969, "crc": **********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$Api34Impl.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$Api34Impl.class", "size": 985, "crc": -**********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat$TileServiceWrapper.class", "name": "androidx/core/service/quicksettings/TileServiceCompat$TileServiceWrapper.class", "size": 387, "crc": -**********}, {"key": "androidx/core/service/quicksettings/TileServiceCompat.class", "name": "androidx/core/service/quicksettings/TileServiceCompat.class", "size": 2414, "crc": 1757355155}, {"key": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "name": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "size": 758, "crc": 1419660002}, {"key": "androidx/core/telephony/SubscriptionManagerCompat.class", "name": "androidx/core/telephony/SubscriptionManagerCompat.class", "size": 1698, "crc": 1557138825}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "size": 1087, "crc": 1091714028}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "size": 1054, "crc": -1566470395}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "size": 835, "crc": 1008375281}, {"key": "androidx/core/telephony/TelephonyManagerCompat.class", "name": "androidx/core/telephony/TelephonyManagerCompat.class", "size": 2906, "crc": 1474125055}, {"key": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "name": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "size": 2186, "crc": -1878492408}, {"key": "androidx/core/telephony/mbms/MbmsHelper.class", "name": "androidx/core/telephony/mbms/MbmsHelper.class", "size": 1004, "crc": -1438195000}, {"key": "androidx/core/text/BidiFormatter$Builder.class", "name": "androidx/core/text/BidiFormatter$Builder.class", "size": 2036, "crc": 1378760591}, {"key": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "name": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "size": 4094, "crc": -1902417185}, {"key": "androidx/core/text/BidiFormatter.class", "name": "androidx/core/text/BidiFormatter.class", "size": 5801, "crc": 1804739059}, {"key": "androidx/core/text/HtmlCompat$Api24Impl.class", "name": "androidx/core/text/HtmlCompat$Api24Impl.class", "size": 1362, "crc": 146217190}, {"key": "androidx/core/text/HtmlCompat.class", "name": "androidx/core/text/HtmlCompat.class", "size": 2554, "crc": -261951905}, {"key": "androidx/core/text/ICUCompat$Api21Impl.class", "name": "androidx/core/text/ICUCompat$Api21Impl.class", "size": 734, "crc": 1318579150}, {"key": "androidx/core/text/ICUCompat$Api24Impl.class", "name": "androidx/core/text/ICUCompat$Api24Impl.class", "size": 1128, "crc": -209947284}, {"key": "androidx/core/text/ICUCompat.class", "name": "androidx/core/text/ICUCompat.class", "size": 3456, "crc": -1561930858}, {"key": "androidx/core/text/PrecomputedTextCompat$Api28Impl.class", "name": "androidx/core/text/PrecomputedTextCompat$Api28Impl.class", "size": 769, "crc": -1317756250}, {"key": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "name": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "size": 2140, "crc": -1496359467}, {"key": "androidx/core/text/PrecomputedTextCompat$Params.class", "name": "androidx/core/text/PrecomputedTextCompat$Params.class", "size": 6752, "crc": -773675009}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "size": 1631, "crc": 1300001370}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "size": 1136, "crc": 717005093}, {"key": "androidx/core/text/PrecomputedTextCompat.class", "name": "androidx/core/text/PrecomputedTextCompat.class", "size": 9196, "crc": -533518916}, {"key": "androidx/core/text/TextDirectionHeuristicCompat.class", "name": "androidx/core/text/TextDirectionHeuristicCompat.class", "size": 222, "crc": -118200751}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "size": 1358, "crc": -116397953}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "size": 1162, "crc": 1465235069}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "size": 342, "crc": 964437877}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "size": 1745, "crc": 2136242730}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "size": 1131, "crc": -1371447970}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "size": 1270, "crc": 1216013965}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat.class", "size": 2235, "crc": 1050206764}, {"key": "androidx/core/text/TextUtilsCompat$Api17Impl.class", "name": "androidx/core/text/TextUtilsCompat$Api17Impl.class", "size": 743, "crc": 232804009}, {"key": "androidx/core/text/TextUtilsCompat.class", "name": "androidx/core/text/TextUtilsCompat.class", "size": 2588, "crc": -1493075492}, {"key": "androidx/core/text/method/LinkMovementMethodCompat.class", "name": "androidx/core/text/method/LinkMovementMethodCompat.class", "size": 2369, "crc": -1733823913}, {"key": "androidx/core/text/util/FindAddress$ZipRange.class", "name": "androidx/core/text/util/FindAddress$ZipRange.class", "size": 1027, "crc": 1759049123}, {"key": "androidx/core/text/util/FindAddress.class", "name": "androidx/core/text/util/FindAddress.class", "size": 11163, "crc": 106913627}, {"key": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "name": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "size": 1668, "crc": 1461729766}, {"key": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "name": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "size": 548, "crc": 1762324880}, {"key": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "name": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "size": 655, "crc": -1830119234}, {"key": "androidx/core/text/util/LinkifyCompat.class", "name": "androidx/core/text/util/LinkifyCompat.class", "size": 12426, "crc": -1419732869}, {"key": "androidx/core/text/util/LocalePreferences$1.class", "name": "androidx/core/text/util/LocalePreferences$1.class", "size": 940, "crc": 378637435}, {"key": "androidx/core/text/util/LocalePreferences$Api24Impl.class", "name": "androidx/core/text/util/LocalePreferences$Api24Impl.class", "size": 1260, "crc": 1069784012}, {"key": "androidx/core/text/util/LocalePreferences$Api33Impl.class", "name": "androidx/core/text/util/LocalePreferences$Api33Impl.class", "size": 2948, "crc": 1431973722}, {"key": "androidx/core/text/util/LocalePreferences$CalendarType$CalendarTypes.class", "name": "androidx/core/text/util/LocalePreferences$CalendarType$CalendarTypes.class", "size": 754, "crc": 2111337730}, {"key": "androidx/core/text/util/LocalePreferences$CalendarType.class", "name": "androidx/core/text/util/LocalePreferences$CalendarType.class", "size": 1131, "crc": 1121003693}, {"key": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek$Days.class", "name": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek$Days.class", "size": 742, "crc": -925039115}, {"key": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek.class", "name": "androidx/core/text/util/LocalePreferences$FirstDayOfWeek.class", "size": 892, "crc": -1082602966}, {"key": "androidx/core/text/util/LocalePreferences$HourCycle$HourCycleTypes.class", "name": "androidx/core/text/util/LocalePreferences$HourCycle$HourCycleTypes.class", "size": 747, "crc": -1451601347}, {"key": "androidx/core/text/util/LocalePreferences$HourCycle.class", "name": "androidx/core/text/util/LocalePreferences$HourCycle.class", "size": 770, "crc": 2050120332}, {"key": "androidx/core/text/util/LocalePreferences$TemperatureUnit$TemperatureUnits.class", "name": "androidx/core/text/util/LocalePreferences$TemperatureUnit$TemperatureUnits.class", "size": 769, "crc": 372884259}, {"key": "androidx/core/text/util/LocalePreferences$TemperatureUnit.class", "name": "androidx/core/text/util/LocalePreferences$TemperatureUnit.class", "size": 793, "crc": -221813898}, {"key": "androidx/core/text/util/LocalePreferences.class", "name": "androidx/core/text/util/LocalePreferences.class", "size": 5452, "crc": 518336472}, {"key": "androidx/core/util/AtomicFile.class", "name": "androidx/core/util/AtomicFile.class", "size": 4462, "crc": 1282749343}, {"key": "androidx/core/util/Consumer.class", "name": "androidx/core/util/Consumer.class", "size": 237, "crc": 408644938}, {"key": "androidx/core/util/DebugUtils.class", "name": "androidx/core/util/DebugUtils.class", "size": 1525, "crc": -788472052}, {"key": "androidx/core/util/Function.class", "name": "androidx/core/util/Function.class", "size": 349, "crc": 1699460227}, {"key": "androidx/core/util/LogWriter.class", "name": "androidx/core/util/LogWriter.class", "size": 1684, "crc": -707190681}, {"key": "androidx/core/util/ObjectsCompat$Api19Impl.class", "name": "androidx/core/util/ObjectsCompat$Api19Impl.class", "size": 882, "crc": -289606262}, {"key": "androidx/core/util/ObjectsCompat.class", "name": "androidx/core/util/ObjectsCompat.class", "size": 2168, "crc": -557564951}, {"key": "androidx/core/util/Pair.class", "name": "androidx/core/util/Pair.class", "size": 1918, "crc": -1970849920}, {"key": "androidx/core/util/PatternsCompat.class", "name": "androidx/core/util/PatternsCompat.class", "size": 45442, "crc": -1052121400}, {"key": "androidx/core/util/Pools$Pool.class", "name": "androidx/core/util/Pools$Pool.class", "size": 521, "crc": 1580738153}, {"key": "androidx/core/util/Pools$SimplePool.class", "name": "androidx/core/util/Pools$SimplePool.class", "size": 1789, "crc": -771314487}, {"key": "androidx/core/util/Pools$SynchronizedPool.class", "name": "androidx/core/util/Pools$SynchronizedPool.class", "size": 1315, "crc": -521654601}, {"key": "androidx/core/util/Pools.class", "name": "androidx/core/util/Pools.class", "size": 491, "crc": 1684647274}, {"key": "androidx/core/util/Preconditions.class", "name": "androidx/core/util/Preconditions.class", "size": 6183, "crc": 1496653811}, {"key": "androidx/core/util/Predicate.class", "name": "androidx/core/util/Predicate.class", "size": 3398, "crc": -106517694}, {"key": "androidx/core/util/SizeFCompat$Api21Impl.class", "name": "androidx/core/util/SizeFCompat$Api21Impl.class", "size": 1233, "crc": 1355410199}, {"key": "androidx/core/util/SizeFCompat.class", "name": "androidx/core/util/SizeFCompat.class", "size": 1988, "crc": 703664480}, {"key": "androidx/core/util/Supplier.class", "name": "androidx/core/util/Supplier.class", "size": 232, "crc": -1241756455}, {"key": "androidx/core/util/TimeUtils.class", "name": "androidx/core/util/TimeUtils.class", "size": 4432, "crc": 943155114}, {"key": "androidx/core/util/TypedValueCompat$Api34Impl.class", "name": "androidx/core/util/TypedValueCompat$Api34Impl.class", "size": 817, "crc": 1538916558}, {"key": "androidx/core/util/TypedValueCompat$ComplexDimensionUnit.class", "name": "androidx/core/util/TypedValueCompat$ComplexDimensionUnit.class", "size": 659, "crc": 1797910148}, {"key": "androidx/core/util/TypedValueCompat.class", "name": "androidx/core/util/TypedValueCompat.class", "size": 2402, "crc": -1160262685}, {"key": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "name": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "size": 4872, "crc": 297555897}, {"key": "androidx/core/view/AccessibilityDelegateCompat$Api16Impl.class", "name": "androidx/core/view/AccessibilityDelegateCompat$Api16Impl.class", "size": 1432, "crc": -385260210}, {"key": "androidx/core/view/AccessibilityDelegateCompat.class", "name": "androidx/core/view/AccessibilityDelegateCompat.class", "size": 7562, "crc": **********}, {"key": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "name": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "size": 543, "crc": -**********}, {"key": "androidx/core/view/ActionProvider$VisibilityListener.class", "name": "androidx/core/view/ActionProvider$VisibilityListener.class", "size": 289, "crc": -**********}, {"key": "androidx/core/view/ActionProvider.class", "name": "androidx/core/view/ActionProvider.class", "size": 3576, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "size": 3079, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$Builder.class", "name": "androidx/core/view/ContentInfoCompat$Builder.class", "size": 2697, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "size": 666, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "size": 2633, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "size": 2445, "crc": 58939708}, {"key": "androidx/core/view/ContentInfoCompat$Compat.class", "name": "androidx/core/view/ContentInfoCompat$Compat.class", "size": 599, "crc": 322977362}, {"key": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "size": 1978, "crc": -1986240561}, {"key": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "size": 2855, "crc": 1115722401}, {"key": "androidx/core/view/ContentInfoCompat$Flags.class", "name": "androidx/core/view/ContentInfoCompat$Flags.class", "size": 645, "crc": 103647730}, {"key": "androidx/core/view/ContentInfoCompat$Source.class", "name": "androidx/core/view/ContentInfoCompat$Source.class", "size": 647, "crc": -1098283764}, {"key": "androidx/core/view/ContentInfoCompat.class", "name": "androidx/core/view/ContentInfoCompat.class", "size": 7544, "crc": 865363801}, {"key": "androidx/core/view/DisplayCompat$Api17Impl.class", "name": "androidx/core/view/DisplayCompat$Api17Impl.class", "size": 783, "crc": 1817832886}, {"key": "androidx/core/view/DisplayCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$Api23Impl.class", "size": 3224, "crc": -1149992379}, {"key": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "size": 984, "crc": 499830806}, {"key": "androidx/core/view/DisplayCompat$ModeCompat.class", "name": "androidx/core/view/DisplayCompat$ModeCompat.class", "size": 2226, "crc": 1782302470}, {"key": "androidx/core/view/DisplayCompat.class", "name": "androidx/core/view/DisplayCompat.class", "size": 5477, "crc": -381714626}, {"key": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "size": 1861, "crc": -2061863409}, {"key": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "size": 1139, "crc": 2065672441}, {"key": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "size": 1458, "crc": -1323522111}, {"key": "androidx/core/view/DisplayCutoutCompat.class", "name": "androidx/core/view/DisplayCutoutCompat.class", "size": 5691, "crc": -278841617}, {"key": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "name": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "size": 1222, "crc": -1494528412}, {"key": "androidx/core/view/DragAndDropPermissionsCompat.class", "name": "androidx/core/view/DragAndDropPermissionsCompat.class", "size": 1686, "crc": -112841901}, {"key": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "name": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "size": 416, "crc": -2064185111}, {"key": "androidx/core/view/DragStartHelper.class", "name": "androidx/core/view/DragStartHelper.class", "size": 3436, "crc": -511789484}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImpl.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImpl.class", "size": 609, "crc": 1055230217}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase$GestureHandler.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase$GestureHandler.class", "size": 2437, "crc": -1316806887}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase.class", "size": 8651, "crc": 1476896763}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplJellybeanMr2.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplJellybeanMr2.class", "size": 1808, "crc": -239387221}, {"key": "androidx/core/view/GestureDetectorCompat.class", "name": "androidx/core/view/GestureDetectorCompat.class", "size": 2621, "crc": 1500182683}, {"key": "androidx/core/view/GravityCompat$Api17Impl.class", "name": "androidx/core/view/GravityCompat$Api17Impl.class", "size": 1311, "crc": -952907712}, {"key": "androidx/core/view/GravityCompat.class", "name": "androidx/core/view/GravityCompat.class", "size": 2024, "crc": -558372288}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackFlags.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackFlags.class", "size": 696, "crc": 172944499}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackType.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat$HapticFeedbackType.class", "size": 694, "crc": 1352997797}, {"key": "androidx/core/view/HapticFeedbackConstantsCompat.class", "name": "androidx/core/view/HapticFeedbackConstantsCompat.class", "size": 2165, "crc": -233855463}, {"key": "androidx/core/view/InputDeviceCompat.class", "name": "androidx/core/view/InputDeviceCompat.class", "size": 1178, "crc": -1611156102}, {"key": "androidx/core/view/KeyEventDispatcher$Component.class", "name": "androidx/core/view/KeyEventDispatcher$Component.class", "size": 377, "crc": 1819687057}, {"key": "androidx/core/view/KeyEventDispatcher.class", "name": "androidx/core/view/KeyEventDispatcher.class", "size": 5617, "crc": -251757181}, {"key": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "name": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "size": 1824, "crc": 709925757}, {"key": "androidx/core/view/LayoutInflaterCompat.class", "name": "androidx/core/view/LayoutInflaterCompat.class", "size": 3446, "crc": 1815067315}, {"key": "androidx/core/view/LayoutInflaterFactory.class", "name": "androidx/core/view/LayoutInflaterFactory.class", "size": 362, "crc": 1217133356}, {"key": "androidx/core/view/MarginLayoutParamsCompat$Api17Impl.class", "name": "androidx/core/view/MarginLayoutParamsCompat$Api17Impl.class", "size": 1831, "crc": -401068417}, {"key": "androidx/core/view/MarginLayoutParamsCompat.class", "name": "androidx/core/view/MarginLayoutParamsCompat.class", "size": 2227, "crc": -1022510054}, {"key": "androidx/core/view/MenuCompat$Api28Impl.class", "name": "androidx/core/view/MenuCompat$Api28Impl.class", "size": 763, "crc": 493244291}, {"key": "androidx/core/view/MenuCompat.class", "name": "androidx/core/view/MenuCompat.class", "size": 1173, "crc": -465812236}, {"key": "androidx/core/view/MenuHost.class", "name": "androidx/core/view/MenuHost.class", "size": 802, "crc": 1764291970}, {"key": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "name": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "size": 1047, "crc": -2092317000}, {"key": "androidx/core/view/MenuHostHelper.class", "name": "androidx/core/view/MenuHostHelper.class", "size": 6015, "crc": 1640962395}, {"key": "androidx/core/view/MenuItemCompat$1.class", "name": "androidx/core/view/MenuItemCompat$1.class", "size": 1202, "crc": -1742634925}, {"key": "androidx/core/view/MenuItemCompat$Api26Impl.class", "name": "androidx/core/view/MenuItemCompat$Api26Impl.class", "size": 3334, "crc": -**********}, {"key": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "name": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "size": 427, "crc": -**********}, {"key": "androidx/core/view/MenuItemCompat.class", "name": "androidx/core/view/MenuItemCompat.class", "size": 7928, "crc": **********}, {"key": "androidx/core/view/MenuProvider.class", "name": "androidx/core/view/MenuProvider.class", "size": 694, "crc": -168585429}, {"key": "androidx/core/view/MotionEventCompat.class", "name": "androidx/core/view/MotionEventCompat.class", "size": 5062, "crc": **********}, {"key": "androidx/core/view/NestedScrollingChild.class", "name": "androidx/core/view/NestedScrollingChild.class", "size": 612, "crc": 643083790}, {"key": "androidx/core/view/NestedScrollingChild2.class", "name": "androidx/core/view/NestedScrollingChild2.class", "size": 512, "crc": **********}, {"key": "androidx/core/view/NestedScrollingChild3.class", "name": "androidx/core/view/NestedScrollingChild3.class", "size": 369, "crc": 538066002}, {"key": "androidx/core/view/NestedScrollingChildHelper.class", "name": "androidx/core/view/NestedScrollingChildHelper.class", "size": 5709, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingParent.class", "name": "androidx/core/view/NestedScrollingParent.class", "size": 808, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingParent2.class", "name": "androidx/core/view/NestedScrollingParent2.class", "size": 695, "crc": -892996660}, {"key": "androidx/core/view/NestedScrollingParent3.class", "name": "androidx/core/view/NestedScrollingParent3.class", "size": 350, "crc": -686595616}, {"key": "androidx/core/view/NestedScrollingParentHelper.class", "name": "androidx/core/view/NestedScrollingParentHelper.class", "size": 1541, "crc": 461928738}, {"key": "androidx/core/view/OnApplyWindowInsetsListener.class", "name": "androidx/core/view/OnApplyWindowInsetsListener.class", "size": 418, "crc": 821875535}, {"key": "androidx/core/view/OnReceiveContentListener.class", "name": "androidx/core/view/OnReceiveContentListener.class", "size": 440, "crc": -51731300}, {"key": "androidx/core/view/OnReceiveContentViewBehavior.class", "name": "androidx/core/view/OnReceiveContentViewBehavior.class", "size": 423, "crc": -1932407804}, {"key": "androidx/core/view/OneShotPreDrawListener.class", "name": "androidx/core/view/OneShotPreDrawListener.class", "size": 2307, "crc": -501338103}, {"key": "androidx/core/view/PointerIconCompat$Api24Impl.class", "name": "androidx/core/view/PointerIconCompat$Api24Impl.class", "size": 1259, "crc": -1765924208}, {"key": "androidx/core/view/PointerIconCompat.class", "name": "androidx/core/view/PointerIconCompat.class", "size": 3083, "crc": 652835173}, {"key": "androidx/core/view/ScaleGestureDetectorCompat$Api19Impl.class", "name": "androidx/core/view/ScaleGestureDetectorCompat$Api19Impl.class", "size": 1026, "crc": 1798524504}, {"key": "androidx/core/view/ScaleGestureDetectorCompat.class", "name": "androidx/core/view/ScaleGestureDetectorCompat.class", "size": 1426, "crc": -763250653}, {"key": "androidx/core/view/ScrollingView.class", "name": "androidx/core/view/ScrollingView.class", "size": 364, "crc": -218140119}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl.class", "size": 596, "crc": -596741455}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl20.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl20.class", "size": 2596, "crc": 1848626303}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl30.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat$Impl30.class", "size": 4056, "crc": 2091328905}, {"key": "androidx/core/view/SoftwareKeyboardControllerCompat.class", "name": "androidx/core/view/SoftwareKeyboardControllerCompat.class", "size": 1685, "crc": -1955609034}, {"key": "androidx/core/view/TintableBackgroundView.class", "name": "androidx/core/view/TintableBackgroundView.class", "size": 723, "crc": 1795767615}, {"key": "androidx/core/view/VelocityTrackerCompat$Api34Impl.class", "name": "androidx/core/view/VelocityTrackerCompat$Api34Impl.class", "size": 1135, "crc": 1222755308}, {"key": "androidx/core/view/VelocityTrackerCompat$VelocityTrackableMotionEventAxis.class", "name": "androidx/core/view/VelocityTrackerCompat$VelocityTrackableMotionEventAxis.class", "size": 711, "crc": 1429362118}, {"key": "androidx/core/view/VelocityTrackerCompat.class", "name": "androidx/core/view/VelocityTrackerCompat.class", "size": 1809, "crc": 2052508}, {"key": "androidx/core/view/ViewCompat$1.class", "name": "androidx/core/view/ViewCompat$1.class", "size": 2347, "crc": *********}, {"key": "androidx/core/view/ViewCompat$2.class", "name": "androidx/core/view/ViewCompat$2.class", "size": 2160, "crc": -*********}, {"key": "androidx/core/view/ViewCompat$3.class", "name": "androidx/core/view/ViewCompat$3.class", "size": 2155, "crc": *********}, {"key": "androidx/core/view/ViewCompat$4.class", "name": "androidx/core/view/ViewCompat$4.class", "size": 2217, "crc": *********}, {"key": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "name": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "size": 4069, "crc": -*********}, {"key": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "name": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "size": 3288, "crc": -1981446737}, {"key": "androidx/core/view/ViewCompat$Api15Impl.class", "name": "androidx/core/view/ViewCompat$Api15Impl.class", "size": 799, "crc": 1038001215}, {"key": "androidx/core/view/ViewCompat$Api16Impl.class", "name": "androidx/core/view/ViewCompat$Api16Impl.class", "size": 4092, "crc": -690030067}, {"key": "androidx/core/view/ViewCompat$Api17Impl.class", "name": "androidx/core/view/ViewCompat$Api17Impl.class", "size": 2219, "crc": 1054358389}, {"key": "androidx/core/view/ViewCompat$Api18Impl.class", "name": "androidx/core/view/ViewCompat$Api18Impl.class", "size": 1222, "crc": -716363321}, {"key": "androidx/core/view/ViewCompat$Api19Impl.class", "name": "androidx/core/view/ViewCompat$Api19Impl.class", "size": 2034, "crc": -1921820685}, {"key": "androidx/core/view/ViewCompat$Api20Impl.class", "name": "androidx/core/view/ViewCompat$Api20Impl.class", "size": 1126, "crc": -937515512}, {"key": "androidx/core/view/ViewCompat$Api21Impl$1.class", "name": "androidx/core/view/ViewCompat$Api21Impl$1.class", "size": 2038, "crc": 1426266368}, {"key": "androidx/core/view/ViewCompat$Api21Impl.class", "name": "androidx/core/view/ViewCompat$Api21Impl.class", "size": 7146, "crc": -457024664}, {"key": "androidx/core/view/ViewCompat$Api23Impl.class", "name": "androidx/core/view/ViewCompat$Api23Impl.class", "size": 1943, "crc": -768157512}, {"key": "androidx/core/view/ViewCompat$Api24Impl.class", "name": "androidx/core/view/ViewCompat$Api24Impl.class", "size": 2146, "crc": -866513655}, {"key": "androidx/core/view/ViewCompat$Api26Impl.class", "name": "androidx/core/view/ViewCompat$Api26Impl.class", "size": 3597, "crc": -788702953}, {"key": "androidx/core/view/ViewCompat$Api28Impl.class", "name": "androidx/core/view/ViewCompat$Api28Impl.class", "size": 4487, "crc": -1536890685}, {"key": "androidx/core/view/ViewCompat$Api29Impl.class", "name": "androidx/core/view/ViewCompat$Api29Impl.class", "size": 2803, "crc": -1873583332}, {"key": "androidx/core/view/ViewCompat$Api30Impl.class", "name": "androidx/core/view/ViewCompat$Api30Impl.class", "size": 2180, "crc": 172388591}, {"key": "androidx/core/view/ViewCompat$Api31Impl.class", "name": "androidx/core/view/ViewCompat$Api31Impl.class", "size": 2229, "crc": -1815357959}, {"key": "androidx/core/view/ViewCompat$FocusDirection.class", "name": "androidx/core/view/ViewCompat$FocusDirection.class", "size": 642, "crc": 1155425484}, {"key": "androidx/core/view/ViewCompat$FocusRealDirection.class", "name": "androidx/core/view/ViewCompat$FocusRealDirection.class", "size": 650, "crc": 749280326}, {"key": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "name": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "size": 658, "crc": 1576246077}, {"key": "androidx/core/view/ViewCompat$NestedScrollType.class", "name": "androidx/core/view/ViewCompat$NestedScrollType.class", "size": 646, "crc": 1643799421}, {"key": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "name": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "size": 1714, "crc": 2052409258}, {"key": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "name": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "size": 424, "crc": -762508931}, {"key": "androidx/core/view/ViewCompat$ScrollAxis.class", "name": "androidx/core/view/ViewCompat$ScrollAxis.class", "size": 634, "crc": -1481000453}, {"key": "androidx/core/view/ViewCompat$ScrollIndicators.class", "name": "androidx/core/view/ViewCompat$ScrollIndicators.class", "size": 646, "crc": -1203320779}, {"key": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "name": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "size": 5939, "crc": -517289096}, {"key": "androidx/core/view/ViewCompat.class", "name": "androidx/core/view/ViewCompat.class", "size": 61677, "crc": -2015593062}, {"key": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "size": 946, "crc": 1505925990}, {"key": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "size": 990, "crc": -1062044641}, {"key": "androidx/core/view/ViewConfigurationCompat$Api34Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api34Impl.class", "size": 1161, "crc": -563811220}, {"key": "androidx/core/view/ViewConfigurationCompat.class", "name": "androidx/core/view/ViewConfigurationCompat.class", "size": 6137, "crc": -850250551}, {"key": "androidx/core/view/ViewGroupCompat$Api18Impl.class", "name": "androidx/core/view/ViewGroupCompat$Api18Impl.class", "size": 918, "crc": -1742576781}, {"key": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "name": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "size": 1055, "crc": -1742838564}, {"key": "androidx/core/view/ViewGroupCompat.class", "name": "androidx/core/view/ViewGroupCompat.class", "size": 2919, "crc": -1156431332}, {"key": "androidx/core/view/ViewParentCompat$Api19Impl.class", "name": "androidx/core/view/ViewParentCompat$Api19Impl.class", "size": 944, "crc": -1058335948}, {"key": "androidx/core/view/ViewParentCompat$Api21Impl.class", "name": "androidx/core/view/ViewParentCompat$Api21Impl.class", "size": 2353, "crc": -585042330}, {"key": "androidx/core/view/ViewParentCompat.class", "name": "androidx/core/view/ViewParentCompat.class", "size": 7342, "crc": 935669967}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "size": 1433, "crc": 2093844466}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api16Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api16Impl.class", "size": 1312, "crc": -1976894863}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api18Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api18Impl.class", "size": 954, "crc": -493018886}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api19Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api19Impl.class", "size": 1232, "crc": 1932437586}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "size": 1230, "crc": 1102609358}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$ViewPropertyAnimatorListenerApi14.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$ViewPropertyAnimatorListenerApi14.class", "size": 2348, "crc": 119070716}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat.class", "size": 11376, "crc": 96606281}, {"key": "androidx/core/view/ViewPropertyAnimatorListener.class", "name": "androidx/core/view/ViewPropertyAnimatorListener.class", "size": 371, "crc": 1924827163}, {"key": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "name": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "size": 844, "crc": -799948790}, {"key": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "name": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "size": 305, "crc": 1797357548}, {"key": "androidx/core/view/ViewStructureCompat$Api23Impl.class", "name": "androidx/core/view/ViewStructureCompat$Api23Impl.class", "size": 1495, "crc": 1645219278}, {"key": "androidx/core/view/ViewStructureCompat.class", "name": "androidx/core/view/ViewStructureCompat.class", "size": 2193, "crc": -729643503}, {"key": "androidx/core/view/WindowCompat$Api16Impl.class", "name": "androidx/core/view/WindowCompat$Api16Impl.class", "size": 1221, "crc": 1361878302}, {"key": "androidx/core/view/WindowCompat$Api28Impl.class", "name": "androidx/core/view/WindowCompat$Api28Impl.class", "size": 856, "crc": -493321089}, {"key": "androidx/core/view/WindowCompat$Api30Impl.class", "name": "androidx/core/view/WindowCompat$Api30Impl.class", "size": 869, "crc": -1783732149}, {"key": "androidx/core/view/WindowCompat.class", "name": "androidx/core/view/WindowCompat.class", "size": 2206, "crc": -459789725}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "size": 3016, "crc": -663239670}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "size": 771, "crc": -19343690}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "size": 2066, "crc": 209494665}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "size": 1808, "crc": 400888180}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "size": 2587, "crc": 430262637}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "size": 1635, "crc": 138536965}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "size": 2016, "crc": -1061163575}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "size": 5763, "crc": -1045538398}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "size": 9225, "crc": -312622289}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "size": 4827, "crc": 21717133}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "size": 3826, "crc": 426416019}, {"key": "androidx/core/view/WindowInsetsAnimationCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat.class", "size": 3503, "crc": -476679620}, {"key": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "size": 519, "crc": -1375959183}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "size": 1814, "crc": 1764069739}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "size": 2580, "crc": -1670379821}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "size": 2455, "crc": -1206962161}, {"key": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "name": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "size": 3507, "crc": -451676334}, {"key": "androidx/core/view/WindowInsetsCompat$Builder.class", "name": "androidx/core/view/WindowInsetsCompat$Builder.class", "size": 3668, "crc": 873871434}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "size": 3188, "crc": 952023470}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "size": 3845, "crc": -1367789157}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "size": 3157, "crc": -1700995255}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "size": 1858, "crc": -362799567}, {"key": "androidx/core/view/WindowInsetsCompat$Impl.class", "name": "androidx/core/view/WindowInsetsCompat$Impl.class", "size": 4440, "crc": 587911647}, {"key": "androidx/core/view/WindowInsetsCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsCompat$Impl20.class", "size": 9787, "crc": -1633841618}, {"key": "androidx/core/view/WindowInsetsCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsCompat$Impl21.class", "size": 2425, "crc": 202908659}, {"key": "androidx/core/view/WindowInsetsCompat$Impl28.class", "name": "androidx/core/view/WindowInsetsCompat$Impl28.class", "size": 2327, "crc": -559644749}, {"key": "androidx/core/view/WindowInsetsCompat$Impl29.class", "name": "androidx/core/view/WindowInsetsCompat$Impl29.class", "size": 2617, "crc": -220432070}, {"key": "androidx/core/view/WindowInsetsCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsCompat$Impl30.class", "size": 2255, "crc": 2128860632}, {"key": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "name": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "size": 719, "crc": 1917876408}, {"key": "androidx/core/view/WindowInsetsCompat$Type.class", "name": "androidx/core/view/WindowInsetsCompat$Type.class", "size": 2387, "crc": 1816556379}, {"key": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "size": 1293, "crc": 903114413}, {"key": "androidx/core/view/WindowInsetsCompat.class", "name": "androidx/core/view/WindowInsetsCompat.class", "size": 10760, "crc": 325569596}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "size": 2318, "crc": -1855552373}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "size": 4149, "crc": 1196657124}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "size": 1629, "crc": -408184768}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "size": 1634, "crc": 874906771}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "size": 2291, "crc": -882907914}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "size": 7346, "crc": -1542620156}, {"key": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "name": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "size": 494, "crc": 1407225033}, {"key": "androidx/core/view/WindowInsetsControllerCompat.class", "name": "androidx/core/view/WindowInsetsControllerCompat.class", "size": 5064, "crc": -2001361378}, {"key": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "name": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "size": 1703, "crc": 566446454}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$Api16Impl.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$Api16Impl.class", "size": 1330, "crc": 589086211}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$Api19Impl.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$Api19Impl.class", "size": 1114, "crc": -1840859211}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$Api34Impl.class", "size": 1131, "crc": 265495813}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "size": 718, "crc": 701609424}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "size": 5615, "crc": 535997810}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "size": 477, "crc": 1509031422}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "size": 811, "crc": 929443126}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "size": 1686, "crc": 575055372}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api19Impl.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api19Impl.class", "size": 2019, "crc": -1613136514}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api34Impl.class", "size": 938, "crc": -1852513469}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "size": 384, "crc": -1837815159}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "size": 1809, "crc": 1182236419}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "size": 4359, "crc": -1508034005}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "size": 11390, "crc": 810281420}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api19Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api19Impl.class", "size": 1974, "crc": 66294551}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api21Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api21Impl.class", "size": 1399, "crc": -1955866154}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api30Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api30Impl.class", "size": 1536, "crc": -784209239}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "size": 4419, "crc": 1663076439}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api34Impl.class", "size": 3216, "crc": -1997715021}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "size": 2258, "crc": 1393341205}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat$Builder.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat$Builder.class", "size": 3524, "crc": -1140524139}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "size": 3180, "crc": -375455792}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "size": 2296, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "size": 2545, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "size": 47797, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi16.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi16.class", "size": 2577, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "size": 1489, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "size": 1697, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "size": 2825, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api15Impl.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api15Impl.class", "size": 1332, "crc": -386561304}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api16Impl.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api16Impl.class", "size": 1030, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "size": 8961, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "size": 1020, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "size": 1120, "crc": 1283304259}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "size": 1014, "crc": 712771277}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "size": 1000, "crc": 2123823754}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "size": 1070, "crc": -838097745}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "size": 932, "crc": 1128765126}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "size": 1022, "crc": 1087470567}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "size": 1024, "crc": 637844774}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "size": 1347, "crc": -1100584727}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "size": 2788, "crc": 917831218}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "size": 1224, "crc": -523677408}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api26Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api26Impl.class", "size": 937, "crc": 855397755}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api30Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api30Impl.class", "size": 858, "crc": 1761157054}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "size": 1687, "crc": -1238990567}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api34Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api34Impl.class", "size": 1140, "crc": -814534727}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "size": 8894, "crc": -1701157246}, {"key": "androidx/core/view/animation/PathInterpolatorApi14.class", "name": "androidx/core/view/animation/PathInterpolatorApi14.class", "size": 2333, "crc": 1168129793}, {"key": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "name": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "size": 1289, "crc": 1556389558}, {"key": "androidx/core/view/animation/PathInterpolatorCompat.class", "name": "androidx/core/view/animation/PathInterpolatorCompat.class", "size": 1515, "crc": -1645199051}, {"key": "androidx/core/view/autofill/AutofillIdCompat.class", "name": "androidx/core/view/autofill/AutofillIdCompat.class", "size": 1073, "crc": -867735316}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api23Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api23Impl.class", "size": 890, "crc": -112680947}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api29Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api29Impl.class", "size": 2720, "crc": -1799560617}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api34Impl.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat$Api34Impl.class", "size": 1238, "crc": 1545892060}, {"key": "androidx/core/view/contentcapture/ContentCaptureSessionCompat.class", "name": "androidx/core/view/contentcapture/ContentCaptureSessionCompat.class", "size": 5395, "crc": -518664933}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "size": 1627, "crc": -1392162936}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat.class", "size": 8110, "crc": -1532557623}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "size": 1833, "crc": 797796400}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "size": 1649, "crc": 2047863385}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "size": 1134, "crc": 1360607248}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "size": 525, "crc": 1476989102}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat.class", "size": 11367, "crc": -1885061227}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "size": 2094, "crc": 1456256422}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "size": 1773, "crc": 1624196168}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "size": 690, "crc": 1074534587}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "size": 2707, "crc": 877487393}, {"key": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "name": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "size": 3002, "crc": -1881276443}, {"key": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "name": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "size": 1582, "crc": 1842928479}, {"key": "androidx/core/widget/AutoScrollHelper.class", "name": "androidx/core/widget/AutoScrollHelper.class", "size": 8621, "crc": 1783212020}, {"key": "androidx/core/widget/AutoSizeableTextView.class", "name": "androidx/core/widget/AutoSizeableTextView.class", "size": 1312, "crc": 383680365}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api14Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api14Impl.class", "size": 1800, "crc": -1441103580}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api16Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api16Impl.class", "size": 960, "crc": 1589534141}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "size": 1853, "crc": -1977490155}, {"key": "androidx/core/widget/CheckedTextViewCompat.class", "name": "androidx/core/widget/CheckedTextViewCompat.class", "size": 2736, "crc": 1711554873}, {"key": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "size": 1729, "crc": 410159180}, {"key": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "size": 875, "crc": -1778730663}, {"key": "androidx/core/widget/CompoundButtonCompat.class", "name": "androidx/core/widget/CompoundButtonCompat.class", "size": 3549, "crc": -53223216}, {"key": "androidx/core/widget/ContentLoadingProgressBar.class", "name": "androidx/core/widget/ContentLoadingProgressBar.class", "size": 3255, "crc": -251890886}, {"key": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "size": 827, "crc": 598038572}, {"key": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "size": 1565, "crc": -499322779}, {"key": "androidx/core/widget/EdgeEffectCompat.class", "name": "androidx/core/widget/EdgeEffectCompat.class", "size": 2939, "crc": 1488300045}, {"key": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "name": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "size": 1670, "crc": -1775682416}, {"key": "androidx/core/widget/ImageViewCompat.class", "name": "androidx/core/widget/ImageViewCompat.class", "size": 2780, "crc": -795067799}, {"key": "androidx/core/widget/ListPopupWindowCompat$Api19Impl.class", "name": "androidx/core/widget/ListPopupWindowCompat$Api19Impl.class", "size": 1053, "crc": -762289808}, {"key": "androidx/core/widget/ListPopupWindowCompat.class", "name": "androidx/core/widget/ListPopupWindowCompat.class", "size": 1404, "crc": 1888066202}, {"key": "androidx/core/widget/ListViewAutoScrollHelper.class", "name": "androidx/core/widget/ListViewAutoScrollHelper.class", "size": 1677, "crc": 1245945836}, {"key": "androidx/core/widget/ListViewCompat$Api19Impl.class", "name": "androidx/core/widget/ListViewCompat$Api19Impl.class", "size": 941, "crc": 854630232}, {"key": "androidx/core/widget/ListViewCompat.class", "name": "androidx/core/widget/ListViewCompat.class", "size": 1775, "crc": -747710768}, {"key": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "name": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "size": 3675, "crc": -1070158388}, {"key": "androidx/core/widget/NestedScrollView$Api21Impl.class", "name": "androidx/core/widget/NestedScrollView$Api21Impl.class", "size": 762, "crc": -661473074}, {"key": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "name": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "size": 422, "crc": -761668443}, {"key": "androidx/core/widget/NestedScrollView$SavedState$1.class", "name": "androidx/core/widget/NestedScrollView$SavedState$1.class", "size": 1315, "crc": 1688685239}, {"key": "androidx/core/widget/NestedScrollView$SavedState.class", "name": "androidx/core/widget/NestedScrollView$SavedState.class", "size": 1969, "crc": 1325367922}, {"key": "androidx/core/widget/NestedScrollView.class", "name": "androidx/core/widget/NestedScrollView.class", "size": 40930, "crc": 146985194}, {"key": "androidx/core/widget/PopupMenuCompat$Api19Impl.class", "name": "androidx/core/widget/PopupMenuCompat$Api19Impl.class", "size": 925, "crc": -399152401}, {"key": "androidx/core/widget/PopupMenuCompat.class", "name": "androidx/core/widget/PopupMenuCompat.class", "size": 1080, "crc": 1505045219}, {"key": "androidx/core/widget/PopupWindowCompat$Api19Impl.class", "name": "androidx/core/widget/PopupWindowCompat$Api19Impl.class", "size": 930, "crc": -1406900133}, {"key": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "name": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "size": 1283, "crc": 1365337325}, {"key": "androidx/core/widget/PopupWindowCompat.class", "name": "androidx/core/widget/PopupWindowCompat.class", "size": 4403, "crc": 1116554972}, {"key": "androidx/core/widget/ScrollerCompat.class", "name": "androidx/core/widget/ScrollerCompat.class", "size": 3656, "crc": -54539407}, {"key": "androidx/core/widget/TextViewCompat$Api16Impl.class", "name": "androidx/core/widget/TextViewCompat$Api16Impl.class", "size": 992, "crc": -1034403535}, {"key": "androidx/core/widget/TextViewCompat$Api17Impl.class", "name": "androidx/core/widget/TextViewCompat$Api17Impl.class", "size": 2384, "crc": 1286226216}, {"key": "androidx/core/widget/TextViewCompat$Api23Impl.class", "name": "androidx/core/widget/TextViewCompat$Api23Impl.class", "size": 2274, "crc": 2048395349}, {"key": "androidx/core/widget/TextViewCompat$Api24Impl.class", "name": "androidx/core/widget/TextViewCompat$Api24Impl.class", "size": 781, "crc": -261615737}, {"key": "androidx/core/widget/TextViewCompat$Api26Impl.class", "name": "androidx/core/widget/TextViewCompat$Api26Impl.class", "size": 1950, "crc": -843659651}, {"key": "androidx/core/widget/TextViewCompat$Api28Impl.class", "name": "androidx/core/widget/TextViewCompat$Api28Impl.class", "size": 1619, "crc": -543764285}, {"key": "androidx/core/widget/TextViewCompat$Api34Impl.class", "name": "androidx/core/widget/TextViewCompat$Api34Impl.class", "size": 959, "crc": -650515672}, {"key": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "name": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "size": 662, "crc": -1792362665}, {"key": "androidx/core/widget/TextViewCompat$OreoCallback.class", "name": "androidx/core/widget/TextViewCompat$OreoCallback.class", "size": 7346, "crc": 1376681177}, {"key": "androidx/core/widget/TextViewCompat.class", "name": "androidx/core/widget/TextViewCompat.class", "size": 18513, "crc": -460822353}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener$Api16Impl.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener$Api16Impl.class", "size": 1394, "crc": 1409504583}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener$ApiImpl.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener$ApiImpl.class", "size": 1246, "crc": -320340918}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener.class", "size": 4009, "crc": -881457967}, {"key": "androidx/core/widget/TintableCheckedTextView.class", "name": "androidx/core/widget/TintableCheckedTextView.class", "size": 946, "crc": -75470643}, {"key": "androidx/core/widget/TintableCompoundButton.class", "name": "androidx/core/widget/TintableCompoundButton.class", "size": 709, "crc": -546728220}, {"key": "androidx/core/widget/TintableCompoundDrawablesView.class", "name": "androidx/core/widget/TintableCompoundDrawablesView.class", "size": 767, "crc": -2060620041}, {"key": "androidx/core/widget/TintableImageSourceView.class", "name": "androidx/core/widget/TintableImageSourceView.class", "size": 930, "crc": -1647897496}, {"key": "META-INF/androidx.core_core.version", "name": "META-INF/androidx.core_core.version", "size": 7, "crc": 1711079621}, {"key": "META-INF/core_release.kotlin_module", "name": "META-INF/core_release.kotlin_module", "size": 24, "crc": 1613429616}]