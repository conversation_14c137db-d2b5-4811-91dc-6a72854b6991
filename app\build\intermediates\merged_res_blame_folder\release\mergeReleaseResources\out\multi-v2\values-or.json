{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeReleaseResources-40:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f608d021a4f1e83e8e85d7b0e5006a22\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8643,8728", "endColumns": "84,87", "endOffsets": "8723,8811"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19b642e7465edfc9a64434f74e28ca0a\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4746,4837,4942,5022,5107,5208,5314,5407,5508,5595,5703,5802,5905,6029,6109,6212", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4741,4832,4937,5017,5102,5203,5309,5402,5503,5590,5698,5797,5900,6024,6104,6207,6301"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1477,1599,1716,1829,1948,2042,2142,2259,2402,2528,2679,2764,2869,2965,3060,3176,3306,3416,3559,3697,3828,4020,4146,4275,4410,4540,4637,4733,4850,4972,5077,5182,5285,5427,5577,5684,5793,5868,5972,6074,6168,6259,6364,6444,6529,6630,6736,6829,6930,7017,7125,7224,7327,7451,7531,7634", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "1594,1711,1824,1943,2037,2137,2254,2397,2523,2674,2759,2864,2960,3055,3171,3301,3411,3554,3692,3823,4015,4141,4270,4405,4535,4632,4728,4845,4967,5072,5177,5280,5422,5572,5679,5788,5863,5967,6069,6163,6254,6359,6439,6524,6625,6731,6824,6925,7012,7120,7219,7322,7446,7526,7629,7723"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\71219f197f28274676b3874ecf964996\\transformed\\core-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,208,310,413,518,619,721,8273", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "203,305,408,513,614,716,835,8369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62f35d7bdb9f40920fe06c2287c5bbc3\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "840,937,1024,1116,1216,1302,1379,7728,7816,7903,7973,8043,8121,8203,8374,8457,8524", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "932,1019,1111,1211,1297,1374,1472,7811,7898,7968,8038,8116,8198,8268,8452,8519,8638"}}]}]}