-- Merging decision tree log ---
manifest
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-11:12
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-11:12
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-11:12
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6414d93292e5fa535c225f4e365cd7e1\transformed\core-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\acefde5fcaa880503faa77ebd330348d\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a7971d08025b62c516fe5d74a0a120f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4a6dd199e5bbe96d328b4c8d22bea6\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a5365fef1dcf89c14c5e7106678385\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\573dd9dfb76dee073cfd28cb16243a69\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:4:5-9:62
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:4:5-9:62
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a7971d08025b62c516fe5d74a0a120f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a7971d08025b62c516fe5d74a0a120f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:8:9-35
	android:label
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:7:9-41
	android:icon
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:6:9-43
	android:allowBackup
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:5:9-35
	android:theme
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:9:9-59
uses-sdk
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6414d93292e5fa535c225f4e365cd7e1\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6414d93292e5fa535c225f4e365cd7e1\transformed\core-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\acefde5fcaa880503faa77ebd330348d\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\acefde5fcaa880503faa77ebd330348d\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a7971d08025b62c516fe5d74a0a120f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7a7971d08025b62c516fe5d74a0a120f\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4a6dd199e5bbe96d328b4c8d22bea6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ae4a6dd199e5bbe96d328b4c8d22bea6\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a5365fef1dcf89c14c5e7106678385\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\e7a5365fef1dcf89c14c5e7106678385\transformed\lifecycle-runtime-2.3.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\573dd9dfb76dee073cfd28cb16243a69\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\573dd9dfb76dee073cfd28cb16243a69\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
permission#com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
uses-permission#com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
