-- Merging decision tree log ---
manifest
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-33:12
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:1-33:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8a40b306b07846e8173f134e6942ef\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tv:tv-material:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f98b795102d6b4e4676c93db494360b5\transformed\tv-material-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a66032b6022265c5288acd5a936a98\transformed\material\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b1246630bde30479f19fc5b4073d2e7\transformed\foundation-layout\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6024b83d99b04049e31590204dce3a8\transformed\material-ripple\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c6efc5356a40c412603779ad33ac6f6\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84cf17b6fc2a0db58a02b081c470684f\transformed\animation-core\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97b17d1f4504b1d2ff9f5779d17f6aef\transformed\animation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b04d3580225dc28344bdc6a991196ab\transformed\ui-unit\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7521d88ff0347f3ff9f7fdb84fd13e3\transformed\ui-geometry\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c973031b75abf18905f712047eeb7d1\transformed\ui-tooling-data\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b66830d5d9cc3314f43e80f692feb2\transformed\ui-util\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb98d58b483ebc3824619f3621b218c8\transformed\ui-text\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\557634109b7b09251114749fe3b7115c\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25aa28cd1e9c53bed012b2f2cab0e8bf\transformed\ui-tooling-preview\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b30643a6152d77c751abb9b4d93b27b\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4c82e9dfe5df03207c372a77c2a15c6\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a7c15b5704c5cd72721d62019d8ae9e\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b4a5470bf7d2434f32ddaad0dfcd3e\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75c74aa3e76f6fb3e4e4146530eaa29d\transformed\runtime-annotation\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07ba624be74b7a6b6d1655f253db90ec\transformed\runtime-saveable\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0a9b5a388e29507c4518f209aacf73\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ead5c53f729658b41468b23347412a0\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4697eb1d6cb89b6b1b6abafefe3c0988\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25bd785bfd4841d697fac4f0c60b3286\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32a5f01e7290c5c64acd530548caf4e6\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c90e455b4a9b864a99b0eab5a1c9e54\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80a6fdf120df4fd81dbae087e0b96087\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e30a37f3727b83582693c9169e9f9099\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58286070e39d48177abd2ac4b868b502\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6eabe5f630da04f1d96266e2e03a9c9a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8b0c7bf16ed86e318f3d68af2cc071b\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6d0716a22b8ebac98718228fb9440a\transformed\savedstate-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\987c5533b1b4e11605b3d12af1048f07\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7db23c47a20658d48456fa3336e41e30\transformed\savedstate-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d784389198144cc34e7df8c8957f981\transformed\runtime\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f49d10cdba556b797e8c799fce9e666d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7964333283afe3a8ff90f54b2e54397\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc01c317dec743d2fe02f0d24e62dc58\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c3b4f0c876296593d4cfeb6e08c2282\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a33d1bb46bdca911c241746e202157c\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dad6230f51cd42e104065cdaef9e8341\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fecee9419144ebd624b98ceb15be1c2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:2:11-69
uses-feature#android.software.leanback
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:4:5-6:35
	android:required
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:6:9-32
	android:name
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:5:9-49
uses-feature#android.hardware.touchscreen
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:8:5-10:36
	android:required
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:10:9-33
	android:name
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:9:9-52
application
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:12:5-31:19
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:12:5-31:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc01c317dec743d2fe02f0d24e62dc58\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc01c317dec743d2fe02f0d24e62dc58\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c3b4f0c876296593d4cfeb6e08c2282\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c3b4f0c876296593d4cfeb6e08c2282\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:15:9-41
	android:icon
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:13:9-35
	android:banner
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:18:9-45
	android:theme
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:17:9-59
activity#com.aj.aj_tv_player.MainActivity
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:20:9-29:20
	android:exported
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:23:13-75
	android:name
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:24:13-28:29
action#android.intent.action.MAIN
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:26:17-77
	android:name
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:26:27-74
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:27:17-86
	android:name
		ADDED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:27:27-83
uses-sdk
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8a40b306b07846e8173f134e6942ef\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8a40b306b07846e8173f134e6942ef\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.tv:tv-material:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f98b795102d6b4e4676c93db494360b5\transformed\tv-material-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.tv:tv-material:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f98b795102d6b4e4676c93db494360b5\transformed\tv-material-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a66032b6022265c5288acd5a936a98\transformed\material\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a66032b6022265c5288acd5a936a98\transformed\material\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b1246630bde30479f19fc5b4073d2e7\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b1246630bde30479f19fc5b4073d2e7\transformed\foundation-layout\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6024b83d99b04049e31590204dce3a8\transformed\material-ripple\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d6024b83d99b04049e31590204dce3a8\transformed\material-ripple\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c6efc5356a40c412603779ad33ac6f6\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7c6efc5356a40c412603779ad33ac6f6\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84cf17b6fc2a0db58a02b081c470684f\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\84cf17b6fc2a0db58a02b081c470684f\transformed\animation-core\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97b17d1f4504b1d2ff9f5779d17f6aef\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\97b17d1f4504b1d2ff9f5779d17f6aef\transformed\animation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b04d3580225dc28344bdc6a991196ab\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b04d3580225dc28344bdc6a991196ab\transformed\ui-unit\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7521d88ff0347f3ff9f7fdb84fd13e3\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7521d88ff0347f3ff9f7fdb84fd13e3\transformed\ui-geometry\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c973031b75abf18905f712047eeb7d1\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2c973031b75abf18905f712047eeb7d1\transformed\ui-tooling-data\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b66830d5d9cc3314f43e80f692feb2\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\66b66830d5d9cc3314f43e80f692feb2\transformed\ui-util\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb98d58b483ebc3824619f3621b218c8\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\fb98d58b483ebc3824619f3621b218c8\transformed\ui-text\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\557634109b7b09251114749fe3b7115c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\557634109b7b09251114749fe3b7115c\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25aa28cd1e9c53bed012b2f2cab0e8bf\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25aa28cd1e9c53bed012b2f2cab0e8bf\transformed\ui-tooling-preview\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b30643a6152d77c751abb9b4d93b27b\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b30643a6152d77c751abb9b4d93b27b\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4c82e9dfe5df03207c372a77c2a15c6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4c82e9dfe5df03207c372a77c2a15c6\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a7c15b5704c5cd72721d62019d8ae9e\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1a7c15b5704c5cd72721d62019d8ae9e\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b4a5470bf7d2434f32ddaad0dfcd3e\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b4a5470bf7d2434f32ddaad0dfcd3e\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75c74aa3e76f6fb3e4e4146530eaa29d\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-annotation-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75c74aa3e76f6fb3e4e4146530eaa29d\transformed\runtime-annotation\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07ba624be74b7a6b6d1655f253db90ec\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07ba624be74b7a6b6d1655f253db90ec\transformed\runtime-saveable\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0a9b5a388e29507c4518f209aacf73\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0a9b5a388e29507c4518f209aacf73\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ead5c53f729658b41468b23347412a0\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ead5c53f729658b41468b23347412a0\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4697eb1d6cb89b6b1b6abafefe3c0988\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4697eb1d6cb89b6b1b6abafefe3c0988\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25bd785bfd4841d697fac4f0c60b3286\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25bd785bfd4841d697fac4f0c60b3286\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32a5f01e7290c5c64acd530548caf4e6\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32a5f01e7290c5c64acd530548caf4e6\transformed\lifecycle-livedata-core-2.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c90e455b4a9b864a99b0eab5a1c9e54\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1c90e455b4a9b864a99b0eab5a1c9e54\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80a6fdf120df4fd81dbae087e0b96087\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\80a6fdf120df4fd81dbae087e0b96087\transformed\lifecycle-runtime-ktx\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e30a37f3727b83582693c9169e9f9099\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e30a37f3727b83582693c9169e9f9099\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58286070e39d48177abd2ac4b868b502\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58286070e39d48177abd2ac4b868b502\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6eabe5f630da04f1d96266e2e03a9c9a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6eabe5f630da04f1d96266e2e03a9c9a\transformed\lifecycle-viewmodel-ktx-2.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8b0c7bf16ed86e318f3d68af2cc071b\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d8b0c7bf16ed86e318f3d68af2cc071b\transformed\lifecycle-viewmodel-savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6d0716a22b8ebac98718228fb9440a\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6d0716a22b8ebac98718228fb9440a\transformed\savedstate-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\987c5533b1b4e11605b3d12af1048f07\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\987c5533b1b4e11605b3d12af1048f07\transformed\savedstate-ktx-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7db23c47a20658d48456fa3336e41e30\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-compose-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7db23c47a20658d48456fa3336e41e30\transformed\savedstate-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d784389198144cc34e7df8c8957f981\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d784389198144cc34e7df8c8957f981\transformed\runtime\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f49d10cdba556b797e8c799fce9e666d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f49d10cdba556b797e8c799fce9e666d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7964333283afe3a8ff90f54b2e54397\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f7964333283afe3a8ff90f54b2e54397\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc01c317dec743d2fe02f0d24e62dc58\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\bc01c317dec743d2fe02f0d24e62dc58\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c3b4f0c876296593d4cfeb6e08c2282\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c3b4f0c876296593d4cfeb6e08c2282\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a33d1bb46bdca911c241746e202157c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a33d1bb46bdca911c241746e202157c\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dad6230f51cd42e104065cdaef9e8341\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\dad6230f51cd42e104065cdaef9e8341\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fecee9419144ebd624b98ceb15be1c2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4fecee9419144ebd624b98ceb15be1c2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c3b4f0c876296593d4cfeb6e08c2282\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9c3b4f0c876296593d4cfeb6e08c2282\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
