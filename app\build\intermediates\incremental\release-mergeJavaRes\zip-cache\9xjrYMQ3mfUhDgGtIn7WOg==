[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/lifecycle-common.kotlin_module", "name": "META-INF/lifecycle-common.kotlin_module", "size": 98, "crc": -1596970967}, {"key": "androidx/lifecycle/AtomicReference.class", "name": "androidx/lifecycle/AtomicReference.class", "size": 1671, "crc": 2111073127}, {"key": "androidx/lifecycle/CompositeGeneratedAdaptersObserver.class", "name": "androidx/lifecycle/CompositeGeneratedAdaptersObserver.class", "size": 2201, "crc": -862408449}, {"key": "androidx/lifecycle/DefaultLifecycleObserver.class", "name": "androidx/lifecycle/DefaultLifecycleObserver.class", "size": 1561, "crc": 233156521}, {"key": "androidx/lifecycle/DefaultLifecycleObserverAdapter$WhenMappings.class", "name": "androidx/lifecycle/DefaultLifecycleObserverAdapter$WhenMappings.class", "size": 1124, "crc": 194980480}, {"key": "androidx/lifecycle/DefaultLifecycleObserverAdapter.class", "name": "androidx/lifecycle/DefaultLifecycleObserverAdapter.class", "size": 2593, "crc": 2000201212}, {"key": "androidx/lifecycle/DispatchQueue.class", "name": "androidx/lifecycle/DispatchQueue.class", "size": 4664, "crc": 524013656}, {"key": "androidx/lifecycle/GeneratedAdapter.class", "name": "androidx/lifecycle/GeneratedAdapter.class", "size": 1304, "crc": -800622336}, {"key": "androidx/lifecycle/Lifecycle$Event$Companion$WhenMappings.class", "name": "androidx/lifecycle/Lifecycle$Event$Companion$WhenMappings.class", "size": 1068, "crc": -664237588}, {"key": "androidx/lifecycle/Lifecycle$Event$Companion.class", "name": "androidx/lifecycle/Lifecycle$Event$Companion.class", "size": 2624, "crc": 860768929}, {"key": "androidx/lifecycle/Lifecycle$Event$WhenMappings.class", "name": "androidx/lifecycle/Lifecycle$Event$WhenMappings.class", "size": 1030, "crc": 733833837}, {"key": "androidx/lifecycle/Lifecycle$Event.class", "name": "androidx/lifecycle/Lifecycle$Event.class", "size": 4129, "crc": -1065798672}, {"key": "androidx/lifecycle/Lifecycle$State.class", "name": "androidx/lifecycle/Lifecycle$State.class", "size": 2515, "crc": 1845968421}, {"key": "androidx/lifecycle/Lifecycle.class", "name": "androidx/lifecycle/Lifecycle.class", "size": 4636, "crc": 2012783844}, {"key": "androidx/lifecycle/LifecycleController.class", "name": "androidx/lifecycle/LifecycleController.class", "size": 4634, "crc": 674517011}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenCreated$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenCreated$1.class", "size": 3878, "crc": -443776910}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenResumed$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenResumed$1.class", "size": 3878, "crc": 1457811220}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenStarted$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenStarted$1.class", "size": 3878, "crc": -1202600443}, {"key": "androidx/lifecycle/LifecycleCoroutineScope.class", "name": "androidx/lifecycle/LifecycleCoroutineScope.class", "size": 3527, "crc": -313803048}, {"key": "androidx/lifecycle/LifecycleCoroutineScopeImpl$register$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScopeImpl$register$1.class", "size": 3916, "crc": 1971163055}, {"key": "androidx/lifecycle/LifecycleCoroutineScopeImpl.class", "name": "androidx/lifecycle/LifecycleCoroutineScopeImpl.class", "size": 3627, "crc": -829931819}, {"key": "androidx/lifecycle/LifecycleEventObserver.class", "name": "androidx/lifecycle/LifecycleEventObserver.class", "size": 968, "crc": 1203295853}, {"key": "androidx/lifecycle/LifecycleKt$eventFlow$1.class", "name": "androidx/lifecycle/LifecycleKt$eventFlow$1.class", "size": 5326, "crc": -90274249}, {"key": "androidx/lifecycle/LifecycleKt.class", "name": "androidx/lifecycle/LifecycleKt.class", "size": 3106, "crc": 1755327459}, {"key": "androidx/lifecycle/LifecycleObserver.class", "name": "androidx/lifecycle/LifecycleObserver.class", "size": 395, "crc": 2078844495}, {"key": "androidx/lifecycle/LifecycleOwner.class", "name": "androidx/lifecycle/LifecycleOwner.class", "size": 611, "crc": 375983147}, {"key": "androidx/lifecycle/LifecycleOwnerKt.class", "name": "androidx/lifecycle/LifecycleOwnerKt.class", "size": 1198, "crc": 1471944349}, {"key": "androidx/lifecycle/Lifecycling.class", "name": "androidx/lifecycle/Lifecycling.class", "size": 9153, "crc": -1321919832}, {"key": "androidx/lifecycle/MethodCallsLogger.class", "name": "androidx/lifecycle/MethodCallsLogger.class", "size": 2000, "crc": 1260161869}, {"key": "androidx/lifecycle/PausingDispatcher.class", "name": "androidx/lifecycle/PausingDispatcher.class", "size": 1904, "crc": -238853660}, {"key": "androidx/lifecycle/PausingDispatcherKt$whenStateAtLeast$2.class", "name": "androidx/lifecycle/PausingDispatcherKt$whenStateAtLeast$2.class", "size": 5462, "crc": 88622880}, {"key": "androidx/lifecycle/PausingDispatcherKt.class", "name": "androidx/lifecycle/PausingDispatcherKt.class", "size": 6048, "crc": -986163588}, {"key": "androidx/lifecycle/SingleGeneratedAdapterObserver.class", "name": "androidx/lifecycle/SingleGeneratedAdapterObserver.class", "size": 1793, "crc": -1068498639}, {"key": "androidx/lifecycle/ClassesInfoCache$CallbackInfo.class", "name": "androidx/lifecycle/ClassesInfoCache$CallbackInfo.class", "size": 3345, "crc": -313150915}, {"key": "androidx/lifecycle/ClassesInfoCache$MethodReference.class", "name": "androidx/lifecycle/ClassesInfoCache$MethodReference.class", "size": 2284, "crc": 508395143}, {"key": "androidx/lifecycle/ClassesInfoCache.class", "name": "androidx/lifecycle/ClassesInfoCache.class", "size": 7415, "crc": -1989687608}, {"key": "androidx/lifecycle/GenericLifecycleObserver.class", "name": "androidx/lifecycle/GenericLifecycleObserver.class", "size": 557, "crc": **********}, {"key": "androidx/lifecycle/OnLifecycleEvent.class", "name": "androidx/lifecycle/OnLifecycleEvent.class", "size": 614, "crc": -662081732}, {"key": "androidx/lifecycle/ReflectiveGenericLifecycleObserver.class", "name": "androidx/lifecycle/ReflectiveGenericLifecycleObserver.class", "size": 1629, "crc": 851704607}, {"key": "META-INF/androidx/lifecycle/lifecycle-common/LICENSE.txt", "name": "META-INF/androidx/lifecycle/lifecycle-common/LICENSE.txt", "size": 10175, "crc": -106424664}]