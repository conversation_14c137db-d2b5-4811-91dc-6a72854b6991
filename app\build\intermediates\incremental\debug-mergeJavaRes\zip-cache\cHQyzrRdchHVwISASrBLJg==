[{"key": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "size": 12614, "crc": -851320551}, {"key": "androidx/compose/material/ripple/AndroidRippleNode.class", "name": "androidx/compose/material/ripple/AndroidRippleNode.class", "size": 9337, "crc": -25020629}, {"key": "androidx/compose/material/ripple/CommonRipple.class", "name": "androidx/compose/material/ripple/CommonRipple.class", "size": 5698, "crc": -2102949217}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "size": 4455, "crc": 430555123}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "size": 9257, "crc": -793695862}, {"key": "androidx/compose/material/ripple/CommonRippleNode$addRipple$2.class", "name": "androidx/compose/material/ripple/CommonRippleNode$addRipple$2.class", "size": 4476, "crc": -428510048}, {"key": "androidx/compose/material/ripple/CommonRippleNode.class", "name": "androidx/compose/material/ripple/CommonRippleNode.class", "size": 9611, "crc": -149188167}, {"key": "androidx/compose/material/ripple/DebugRippleTheme.class", "name": "androidx/compose/material/ripple/DebugRippleTheme.class", "size": 3058, "crc": -1623845055}, {"key": "androidx/compose/material/ripple/PlatformRipple.class", "name": "androidx/compose/material/ripple/PlatformRipple.class", "size": 6753, "crc": 813238906}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1$1.class", "size": 2858, "crc": -69196829}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "size": 4194, "crc": 1578840702}, {"key": "androidx/compose/material/ripple/Ripple.class", "name": "androidx/compose/material/ripple/Ripple.class", "size": 9189, "crc": 1841540764}, {"key": "androidx/compose/material/ripple/RippleAlpha.class", "name": "androidx/compose/material/ripple/RippleAlpha.class", "size": 2582, "crc": -325110805}, {"key": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "size": 1737, "crc": 865894535}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "size": 4142, "crc": 604963317}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "size": 4158, "crc": -530212425}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "size": 4151, "crc": -1404971676}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "size": 3802, "crc": 407361871}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "size": 4148, "crc": -615956297}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "size": 3594, "crc": 277474654}, {"key": "androidx/compose/material/ripple/RippleAnimation.class", "name": "androidx/compose/material/ripple/RippleAnimation.class", "size": 12258, "crc": 1823993854}, {"key": "androidx/compose/material/ripple/RippleAnimationKt.class", "name": "androidx/compose/material/ripple/RippleAnimationKt.class", "size": 2739, "crc": -1628516194}, {"key": "androidx/compose/material/ripple/RippleContainer.class", "name": "androidx/compose/material/ripple/RippleContainer.class", "size": 5284, "crc": -1742809780}, {"key": "androidx/compose/material/ripple/RippleHostKey.class", "name": "androidx/compose/material/ripple/RippleHostKey.class", "size": 481, "crc": -1611070690}, {"key": "androidx/compose/material/ripple/RippleHostMap.class", "name": "androidx/compose/material/ripple/RippleHostMap.class", "size": 3235, "crc": 1049204405}, {"key": "androidx/compose/material/ripple/RippleHostView$Companion.class", "name": "androidx/compose/material/ripple/RippleHostView$Companion.class", "size": 1063, "crc": -1920324607}, {"key": "androidx/compose/material/ripple/RippleHostView.class", "name": "androidx/compose/material/ripple/RippleHostView.class", "size": 8177, "crc": -2023896265}, {"key": "androidx/compose/material/ripple/RippleIndicationInstance.class", "name": "androidx/compose/material/ripple/RippleIndicationInstance.class", "size": 5388, "crc": -848458620}, {"key": "androidx/compose/material/ripple/RippleKt.class", "name": "androidx/compose/material/ripple/RippleKt.class", "size": 8072, "crc": 404983489}, {"key": "androidx/compose/material/ripple/RippleNode$onAttach$1$1.class", "name": "androidx/compose/material/ripple/RippleNode$onAttach$1$1.class", "size": 3514, "crc": 969142144}, {"key": "androidx/compose/material/ripple/RippleNode$onAttach$1.class", "name": "androidx/compose/material/ripple/RippleNode$onAttach$1.class", "size": 3837, "crc": 21561681}, {"key": "androidx/compose/material/ripple/RippleNode.class", "name": "androidx/compose/material/ripple/RippleNode.class", "size": 12074, "crc": -277760958}, {"key": "androidx/compose/material/ripple/RippleTheme$Companion.class", "name": "androidx/compose/material/ripple/RippleTheme$Companion.class", "size": 3193, "crc": -919863757}, {"key": "androidx/compose/material/ripple/RippleTheme.class", "name": "androidx/compose/material/ripple/RippleTheme.class", "size": 1734, "crc": -1044362850}, {"key": "androidx/compose/material/ripple/RippleThemeKt.class", "name": "androidx/compose/material/ripple/RippleThemeKt.class", "size": 3414, "crc": 1114531720}, {"key": "androidx/compose/material/ripple/Ripple_androidKt.class", "name": "androidx/compose/material/ripple/Ripple_androidKt.class", "size": 4689, "crc": 578520858}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "size": 4172, "crc": -1963250126}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "size": 4110, "crc": 2000453500}, {"key": "androidx/compose/material/ripple/StateLayer.class", "name": "androidx/compose/material/ripple/StateLayer.class", "size": 9889, "crc": -212809656}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "size": 1012, "crc": 698959908}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "size": 1321, "crc": -1720724788}, {"key": "androidx/compose/material/ripple/UnprojectedRipple.class", "name": "androidx/compose/material/ripple/UnprojectedRipple.class", "size": 4534, "crc": -927212903}, {"key": "META-INF/androidx.compose.material_material-ripple.version", "name": "META-INF/androidx.compose.material_material-ripple.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/material-ripple.kotlin_module", "name": "META-INF/material-ripple.kotlin_module", "size": 122, "crc": 1399178563}]