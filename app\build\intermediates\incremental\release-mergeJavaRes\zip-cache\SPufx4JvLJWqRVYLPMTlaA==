[{"key": "androidx/compose/material/ripple/AndroidRippleIndicationInstance$onInvalidateRipple$1.class", "name": "androidx/compose/material/ripple/AndroidRippleIndicationInstance$onInvalidateRipple$1.class", "size": 1681, "crc": 305142096}, {"key": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/AndroidRippleIndicationInstance.class", "size": 12512, "crc": -746905861}, {"key": "androidx/compose/material/ripple/CommonRipple.class", "name": "androidx/compose/material/ripple/CommonRipple.class", "size": 5584, "crc": 1473136587}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance$addRipple$2.class", "size": 4695, "crc": -1151163524}, {"key": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "name": "androidx/compose/material/ripple/CommonRippleIndicationInstance.class", "size": 8659, "crc": -911548904}, {"key": "androidx/compose/material/ripple/DebugRippleTheme.class", "name": "androidx/compose/material/ripple/DebugRippleTheme.class", "size": 2986, "crc": -1235656909}, {"key": "androidx/compose/material/ripple/PlatformRipple.class", "name": "androidx/compose/material/ripple/PlatformRipple.class", "size": 8997, "crc": 738220272}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1$1.class", "size": 3151, "crc": -145879697}, {"key": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1.class", "name": "androidx/compose/material/ripple/Ripple$rememberUpdatedInstance$1.class", "size": 4424, "crc": -1089802775}, {"key": "androidx/compose/material/ripple/Ripple.class", "name": "androidx/compose/material/ripple/Ripple.class", "size": 8009, "crc": -2092681093}, {"key": "androidx/compose/material/ripple/RippleAlpha.class", "name": "androidx/compose/material/ripple/RippleAlpha.class", "size": 2585, "crc": 1978479508}, {"key": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$animate$1.class", "size": 1774, "crc": 988044865}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$1.class", "size": 4382, "crc": 1914083859}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$2.class", "size": 4398, "crc": 499613251}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2$3.class", "size": 4391, "crc": 831418267}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeIn$2.class", "size": 4038, "crc": -2047710313}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2$1.class", "size": 4388, "crc": 2054628460}, {"key": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "name": "androidx/compose/material/ripple/RippleAnimation$fadeOut$2.class", "size": 3830, "crc": -176136221}, {"key": "androidx/compose/material/ripple/RippleAnimation.class", "name": "androidx/compose/material/ripple/RippleAnimation.class", "size": 12805, "crc": -110032316}, {"key": "androidx/compose/material/ripple/RippleAnimationKt.class", "name": "androidx/compose/material/ripple/RippleAnimationKt.class", "size": 2783, "crc": 377153763}, {"key": "androidx/compose/material/ripple/RippleContainer.class", "name": "androidx/compose/material/ripple/RippleContainer.class", "size": 5295, "crc": 747718168}, {"key": "androidx/compose/material/ripple/RippleHostMap.class", "name": "androidx/compose/material/ripple/RippleHostMap.class", "size": 3382, "crc": -1181498160}, {"key": "androidx/compose/material/ripple/RippleHostView$Companion.class", "name": "androidx/compose/material/ripple/RippleHostView$Companion.class", "size": 1065, "crc": 1758176980}, {"key": "androidx/compose/material/ripple/RippleHostView.class", "name": "androidx/compose/material/ripple/RippleHostView.class", "size": 7961, "crc": -9497462}, {"key": "androidx/compose/material/ripple/RippleIndicationInstance.class", "name": "androidx/compose/material/ripple/RippleIndicationInstance.class", "size": 3174, "crc": -12828781}, {"key": "androidx/compose/material/ripple/RippleKt.class", "name": "androidx/compose/material/ripple/RippleKt.class", "size": 6650, "crc": 1292438521}, {"key": "androidx/compose/material/ripple/RippleTheme$Companion.class", "name": "androidx/compose/material/ripple/RippleTheme$Companion.class", "size": 2177, "crc": -1809515154}, {"key": "androidx/compose/material/ripple/RippleTheme.class", "name": "androidx/compose/material/ripple/RippleTheme.class", "size": 1312, "crc": 904924050}, {"key": "androidx/compose/material/ripple/RippleThemeKt$LocalRippleTheme$1.class", "name": "androidx/compose/material/ripple/RippleThemeKt$LocalRippleTheme$1.class", "size": 1452, "crc": 755061933}, {"key": "androidx/compose/material/ripple/RippleThemeKt.class", "name": "androidx/compose/material/ripple/RippleThemeKt.class", "size": 2323, "crc": -642220676}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$1.class", "size": 4396, "crc": -1504849677}, {"key": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "name": "androidx/compose/material/ripple/StateLayer$handleInteraction$2.class", "size": 4334, "crc": 252793206}, {"key": "androidx/compose/material/ripple/StateLayer.class", "name": "androidx/compose/material/ripple/StateLayer.class", "size": 10176, "crc": -233084035}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$Companion.class", "size": 1015, "crc": -1806182937}, {"key": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "name": "androidx/compose/material/ripple/UnprojectedRipple$MRadiusHelper.class", "size": 1374, "crc": -1580893769}, {"key": "androidx/compose/material/ripple/UnprojectedRipple.class", "name": "androidx/compose/material/ripple/UnprojectedRipple.class", "size": 4503, "crc": 1564306385}, {"key": "META-INF/androidx.compose.material_material-ripple.version", "name": "META-INF/androidx.compose.material_material-ripple.version", "size": 6, "crc": -1055996171}, {"key": "META-INF/material-ripple_release.kotlin_module", "name": "META-INF/material-ripple_release.kotlin_module", "size": 104, "crc": 1291800774}]