{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeReleaseResources-31:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7c6efc5356a40c412603779ad33ac6f6\\transformed\\foundation-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,140,251", "endColumns": "84,110,100", "endOffsets": "135,246,347"}, "to": {"startLines": "2,30,31", "startColumns": "4,4,4", "startOffsets": "105,2682,2793", "endColumns": "84,110,100", "endOffsets": "185,2788,2889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3bdf59f548fbb8efb4688937f11638bc\\transformed\\core-1.15.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "3,4,5,6,7,8,9,26", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "190,292,394,495,595,703,807,2313", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "287,389,490,590,698,802,921,2409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c4c82e9dfe5df03207c372a77c2a15c6\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "191,290,375,479,586,682,765,855,948,1031,1112,1195,1269,1354,1430,1505,1578,1661,1729", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "285,370,474,581,677,760,850,943,1026,1107,1190,1264,1349,1425,1500,1573,1656,1724,1841"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "926,1025,1110,1214,1321,1417,1500,1590,1683,1766,1847,1930,2004,2089,2165,2240,2414,2497,2565", "endColumns": "98,84,103,106,95,82,89,92,82,80,82,73,84,75,74,72,82,67,116", "endOffsets": "1020,1105,1209,1316,1412,1495,1585,1678,1761,1842,1925,1999,2084,2160,2235,2308,2492,2560,2677"}}]}]}