[{"key": "androidx/compose/runtime/AbstractApplier.class", "name": "androidx/compose/runtime/AbstractApplier.class", "size": 3852, "crc": -408717301}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt.class", "size": 629, "crc": -1228878909}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt__MonotonicFrameClock_androidKt.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt__MonotonicFrameClock_androidKt.class", "size": 2420, "crc": -148114641}, {"key": "androidx/compose/runtime/ActualJvm_jvmKt.class", "name": "androidx/compose/runtime/ActualJvm_jvmKt.class", "size": 1040, "crc": 1132329205}, {"key": "androidx/compose/runtime/ActualJvm_jvmKt__OldSynchronization_jvmKt.class", "name": "androidx/compose/runtime/ActualJvm_jvmKt__OldSynchronization_jvmKt.class", "size": 2543, "crc": 422005786}, {"key": "androidx/compose/runtime/Anchor.class", "name": "androidx/compose/runtime/Anchor.class", "size": 2181, "crc": 30607919}, {"key": "androidx/compose/runtime/AnchoredGroupPath.class", "name": "androidx/compose/runtime/AnchoredGroupPath.class", "size": 1312, "crc": -1109534636}, {"key": "androidx/compose/runtime/Applier$DefaultImpls.class", "name": "androidx/compose/runtime/Applier$DefaultImpls.class", "size": 1631, "crc": 1215078761}, {"key": "androidx/compose/runtime/Applier.class", "name": "androidx/compose/runtime/Applier.class", "size": 2891, "crc": -1098003506}, {"key": "androidx/compose/runtime/BitVector.class", "name": "androidx/compose/runtime/BitVector.class", "size": 8450, "crc": 403189556}, {"key": "androidx/compose/runtime/BitwiseOperatorsKt.class", "name": "androidx/compose/runtime/BitwiseOperatorsKt.class", "size": 1029, "crc": 1347034839}, {"key": "androidx/compose/runtime/BroadcastFrameClock$AtomicAwaitersCount$Companion.class", "name": "androidx/compose/runtime/BroadcastFrameClock$AtomicAwaitersCount$Companion.class", "size": 1068, "crc": 464085822}, {"key": "androidx/compose/runtime/BroadcastFrameClock$AtomicAwaitersCount.class", "name": "androidx/compose/runtime/BroadcastFrameClock$AtomicAwaitersCount.class", "size": 8694, "crc": 1786050582}, {"key": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "name": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "size": 3419, "crc": -1821578259}, {"key": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$2.class", "name": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$2.class", "size": 3980, "crc": -1539297949}, {"key": "androidx/compose/runtime/BroadcastFrameClock.class", "name": "androidx/compose/runtime/BroadcastFrameClock.class", "size": 15797, "crc": -1288660596}, {"key": "androidx/compose/runtime/CancelledCoroutineContext$Key.class", "name": "androidx/compose/runtime/CancelledCoroutineContext$Key.class", "size": 1169, "crc": 13060282}, {"key": "androidx/compose/runtime/CancelledCoroutineContext.class", "name": "androidx/compose/runtime/CancelledCoroutineContext.class", "size": 3306, "crc": 344319503}, {"key": "androidx/compose/runtime/CheckResultKt.class", "name": "androidx/compose/runtime/CheckResultKt.class", "size": 731, "crc": -1337530885}, {"key": "androidx/compose/runtime/Composable.class", "name": "androidx/compose/runtime/Composable.class", "size": 961, "crc": 108876710}, {"key": "androidx/compose/runtime/ComposableInferredTarget.class", "name": "androidx/compose/runtime/ComposableInferredTarget.class", "size": 1037, "crc": 1197228465}, {"key": "androidx/compose/runtime/ComposableOpenTarget.class", "name": "androidx/compose/runtime/ComposableOpenTarget.class", "size": 972, "crc": 795183045}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda$1918065384$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda$1918065384$1.class", "size": 2055, "crc": 1388414385}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda$954879418$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda$954879418$1.class", "size": 2052, "crc": 1709911701}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "size": 1885, "crc": 393766654}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda$-1091980426$1.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda$-1091980426$1.class", "size": 2051, "crc": -1077260661}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "size": 1520, "crc": 1929879147}, {"key": "androidx/compose/runtime/ComposableTarget.class", "name": "androidx/compose/runtime/ComposableTarget.class", "size": 1001, "crc": 128946172}, {"key": "androidx/compose/runtime/ComposableTargetMarker.class", "name": "androidx/compose/runtime/ComposableTargetMarker.class", "size": 971, "crc": -715322491}, {"key": "androidx/compose/runtime/ComposablesKt.class", "name": "androidx/compose/runtime/ComposablesKt.class", "size": 20648, "crc": -1125255858}, {"key": "androidx/compose/runtime/ComposeCompilerApi.class", "name": "androidx/compose/runtime/ComposeCompilerApi.class", "size": 790, "crc": 1810392482}, {"key": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "name": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "size": 548, "crc": 998842281}, {"key": "androidx/compose/runtime/ComposePausableCompositionException$operationsSequence$1.class", "name": "androidx/compose/runtime/ComposePausableCompositionException$operationsSequence$1.class", "size": 7520, "crc": 1192493805}, {"key": "androidx/compose/runtime/ComposePausableCompositionException.class", "name": "androidx/compose/runtime/ComposePausableCompositionException.class", "size": 4182, "crc": 1312662579}, {"key": "androidx/compose/runtime/ComposeRuntimeError.class", "name": "androidx/compose/runtime/ComposeRuntimeError.class", "size": 1166, "crc": -1010156463}, {"key": "androidx/compose/runtime/ComposeRuntimeFlags.class", "name": "androidx/compose/runtime/ComposeRuntimeFlags.class", "size": 1140, "crc": -82189490}, {"key": "androidx/compose/runtime/ComposeVersion.class", "name": "androidx/compose/runtime/ComposeVersion.class", "size": 879, "crc": 1482536474}, {"key": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "name": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "size": 824, "crc": -402243558}, {"key": "androidx/compose/runtime/Composer$Companion.class", "name": "androidx/compose/runtime/Composer$Companion.class", "size": 1808, "crc": 1047326241}, {"key": "androidx/compose/runtime/Composer.class", "name": "androidx/compose/runtime/Composer.class", "size": 10398, "crc": -859032222}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "size": 1901, "crc": 160311591}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "size": 15830, "crc": -396177822}, {"key": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "name": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "size": 1844, "crc": 1769279273}, {"key": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "name": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "size": 2767, "crc": 367309626}, {"key": "androidx/compose/runtime/ComposerImpl.class", "name": "androidx/compose/runtime/ComposerImpl.class", "size": 121718, "crc": 451973409}, {"key": "androidx/compose/runtime/ComposerKt$extractMovableContentAtCurrent$movableContentRecomposeScopeOwner$1.class", "name": "androidx/compose/runtime/ComposerKt$extractMovableContentAtCurrent$movableContentRecomposeScopeOwner$1.class", "size": 2933, "crc": 297611588}, {"key": "androidx/compose/runtime/ComposerKt.class", "name": "androidx/compose/runtime/ComposerKt.class", "size": 31372, "crc": -1535144571}, {"key": "androidx/compose/runtime/CompositeKeyHashCode_jvmKt.class", "name": "androidx/compose/runtime/CompositeKeyHashCode_jvmKt.class", "size": 3395, "crc": 1502458942}, {"key": "androidx/compose/runtime/Composition.class", "name": "androidx/compose/runtime/Composition.class", "size": 983, "crc": -829249455}, {"key": "androidx/compose/runtime/CompositionContext.class", "name": "androidx/compose/runtime/CompositionContext.class", "size": 7441, "crc": -1614514893}, {"key": "androidx/compose/runtime/CompositionContextKt.class", "name": "androidx/compose/runtime/CompositionContextKt.class", "size": 1026, "crc": -786070038}, {"key": "androidx/compose/runtime/CompositionDataImpl.class", "name": "androidx/compose/runtime/CompositionDataImpl.class", "size": 5963, "crc": 1757653417}, {"key": "androidx/compose/runtime/CompositionImpl$setObserver$2.class", "name": "androidx/compose/runtime/CompositionImpl$setObserver$2.class", "size": 3132, "crc": 1240171178}, {"key": "androidx/compose/runtime/CompositionImpl.class", "name": "androidx/compose/runtime/CompositionImpl.class", "size": 101371, "crc": -1687104964}, {"key": "androidx/compose/runtime/CompositionKt$ObservableCompositionServiceKey$1.class", "name": "androidx/compose/runtime/CompositionKt$ObservableCompositionServiceKey$1.class", "size": 998, "crc": 1004460805}, {"key": "androidx/compose/runtime/CompositionKt.class", "name": "androidx/compose/runtime/CompositionKt.class", "size": 7840, "crc": 1163882645}, {"key": "androidx/compose/runtime/CompositionLocal.class", "name": "androidx/compose/runtime/CompositionLocal.class", "size": 3379, "crc": -1667958161}, {"key": "androidx/compose/runtime/CompositionLocalAccessorScope.class", "name": "androidx/compose/runtime/CompositionLocalAccessorScope.class", "size": 829, "crc": -1145591376}, {"key": "androidx/compose/runtime/CompositionLocalContext.class", "name": "androidx/compose/runtime/CompositionLocalContext.class", "size": 1180, "crc": 1406601325}, {"key": "androidx/compose/runtime/CompositionLocalKt.class", "name": "androidx/compose/runtime/CompositionLocalKt.class", "size": 11741, "crc": -1544882690}, {"key": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "name": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "size": 1286, "crc": -1653326054}, {"key": "androidx/compose/runtime/CompositionLocalMap.class", "name": "androidx/compose/runtime/CompositionLocalMap.class", "size": 1175, "crc": 1230531005}, {"key": "androidx/compose/runtime/CompositionLocalMapKt.class", "name": "androidx/compose/runtime/CompositionLocalMapKt.class", "size": 6808, "crc": 2021873486}, {"key": "androidx/compose/runtime/CompositionObserverHolder.class", "name": "androidx/compose/runtime/CompositionObserverHolder.class", "size": 2758, "crc": 2018689174}, {"key": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "name": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "size": 2117, "crc": -166856712}, {"key": "androidx/compose/runtime/CompositionServiceKey.class", "name": "androidx/compose/runtime/CompositionServiceKey.class", "size": 480, "crc": -1508237848}, {"key": "androidx/compose/runtime/CompositionServices.class", "name": "androidx/compose/runtime/CompositionServices.class", "size": 897, "crc": -1045680428}, {"key": "androidx/compose/runtime/CompositionTracer.class", "name": "androidx/compose/runtime/CompositionTracer.class", "size": 855, "crc": 1533712688}, {"key": "androidx/compose/runtime/ComputedProvidableCompositionLocal.class", "name": "androidx/compose/runtime/ComputedProvidableCompositionLocal.class", "size": 3658, "crc": -1663540295}, {"key": "androidx/compose/runtime/ComputedValueHolder.class", "name": "androidx/compose/runtime/ComputedValueHolder.class", "size": 4626, "crc": -500156103}, {"key": "androidx/compose/runtime/ControlledComposition.class", "name": "androidx/compose/runtime/ControlledComposition.class", "size": 3393, "crc": -959891217}, {"key": "androidx/compose/runtime/DataIterator.class", "name": "androidx/compose/runtime/DataIterator.class", "size": 3849, "crc": -722327195}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "size": 3102, "crc": 392993523}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "size": 1599, "crc": 586051980}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "size": 2926, "crc": 1366702915}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "size": 7047, "crc": 1551691554}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "size": 1197, "crc": -928419953}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "size": 13193, "crc": 902395596}, {"key": "androidx/compose/runtime/DerivedSnapshotState.class", "name": "androidx/compose/runtime/DerivedSnapshotState.class", "size": 19459, "crc": -1491314682}, {"key": "androidx/compose/runtime/DerivedState$Record.class", "name": "androidx/compose/runtime/DerivedState$Record.class", "size": 1071, "crc": -903002777}, {"key": "androidx/compose/runtime/DerivedState.class", "name": "androidx/compose/runtime/DerivedState.class", "size": 1311, "crc": -1601362079}, {"key": "androidx/compose/runtime/DerivedStateObserver.class", "name": "androidx/compose/runtime/DerivedStateObserver.class", "size": 785, "crc": 1412120266}, {"key": "androidx/compose/runtime/DisallowComposableCalls.class", "name": "androidx/compose/runtime/DisallowComposableCalls.class", "size": 920, "crc": -118904171}, {"key": "androidx/compose/runtime/DisposableEffectImpl.class", "name": "androidx/compose/runtime/DisposableEffectImpl.class", "size": 2202, "crc": -1526984502}, {"key": "androidx/compose/runtime/DisposableEffectResult.class", "name": "androidx/compose/runtime/DisposableEffectResult.class", "size": 445, "crc": -1620010747}, {"key": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "name": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "size": 1301, "crc": -847004363}, {"key": "androidx/compose/runtime/DisposableEffectScope.class", "name": "androidx/compose/runtime/DisposableEffectScope.class", "size": 1524, "crc": 298318558}, {"key": "androidx/compose/runtime/DontMemoize.class", "name": "androidx/compose/runtime/DontMemoize.class", "size": 746, "crc": -424331357}, {"key": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "size": 1028, "crc": -1141957788}, {"key": "androidx/compose/runtime/DoubleState.class", "name": "androidx/compose/runtime/DoubleState.class", "size": 1486, "crc": -869173401}, {"key": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "name": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "size": 2464, "crc": 1996111078}, {"key": "androidx/compose/runtime/DynamicValueHolder.class", "name": "androidx/compose/runtime/DynamicValueHolder.class", "size": 4263, "crc": 2140000273}, {"key": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "name": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "size": 1236, "crc": -1722628393}, {"key": "androidx/compose/runtime/EffectsKt.class", "name": "androidx/compose/runtime/EffectsKt.class", "size": 20281, "crc": -1013820955}, {"key": "androidx/compose/runtime/ExperimentalComposeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeApi.class", "size": 1215, "crc": 1379821114}, {"key": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "size": 1239, "crc": 1257241425}, {"key": "androidx/compose/runtime/ExplicitGroupsComposable.class", "name": "androidx/compose/runtime/ExplicitGroupsComposable.class", "size": 857, "crc": 728937930}, {"key": "androidx/compose/runtime/FallbackFrameClock$withFrameNanos$2.class", "name": "androidx/compose/runtime/FallbackFrameClock$withFrameNanos$2.class", "size": 3574, "crc": -25362772}, {"key": "androidx/compose/runtime/FallbackFrameClock.class", "name": "androidx/compose/runtime/FallbackFrameClock.class", "size": 4190, "crc": -1871168452}, {"key": "androidx/compose/runtime/FloatState$DefaultImpls.class", "name": "androidx/compose/runtime/FloatState$DefaultImpls.class", "size": 1018, "crc": 249586429}, {"key": "androidx/compose/runtime/FloatState.class", "name": "androidx/compose/runtime/FloatState.class", "size": 1475, "crc": -2134758026}, {"key": "androidx/compose/runtime/ForgottenCoroutineScopeException.class", "name": "androidx/compose/runtime/ForgottenCoroutineScopeException.class", "size": 763, "crc": -791493996}, {"key": "androidx/compose/runtime/GroupInfo.class", "name": "androidx/compose/runtime/GroupInfo.class", "size": 1346, "crc": 917416988}, {"key": "androidx/compose/runtime/GroupIterator.class", "name": "androidx/compose/runtime/GroupIterator.class", "size": 2790, "crc": -1519531863}, {"key": "androidx/compose/runtime/GroupKind$Companion.class", "name": "androidx/compose/runtime/GroupKind$Companion.class", "size": 1326, "crc": -1372917439}, {"key": "androidx/compose/runtime/GroupKind.class", "name": "androidx/compose/runtime/GroupKind.class", "size": 3163, "crc": 1528892531}, {"key": "androidx/compose/runtime/GroupSourceInformation.class", "name": "androidx/compose/runtime/GroupSourceInformation.class", "size": 8335, "crc": 1926717211}, {"key": "androidx/compose/runtime/HotReloader$Companion.class", "name": "androidx/compose/runtime/HotReloader$Companion.class", "size": 2536, "crc": 647271877}, {"key": "androidx/compose/runtime/HotReloader.class", "name": "androidx/compose/runtime/HotReloader.class", "size": 880, "crc": 1273152831}, {"key": "androidx/compose/runtime/HotReloaderKt.class", "name": "androidx/compose/runtime/HotReloaderKt.class", "size": 5849, "crc": 152472498}, {"key": "androidx/compose/runtime/IntStack.class", "name": "androidx/compose/runtime/IntStack.class", "size": 2802, "crc": 1805047810}, {"key": "androidx/compose/runtime/IntState$DefaultImpls.class", "name": "androidx/compose/runtime/IntState$DefaultImpls.class", "size": 1010, "crc": 752975292}, {"key": "androidx/compose/runtime/IntState.class", "name": "androidx/compose/runtime/IntState.class", "size": 1469, "crc": 1311516041}, {"key": "androidx/compose/runtime/InternalComposeApi.class", "name": "androidx/compose/runtime/InternalComposeApi.class", "size": 1227, "crc": 1179035018}, {"key": "androidx/compose/runtime/InternalComposeTracingApi.class", "name": "androidx/compose/runtime/InternalComposeTracingApi.class", "size": 947, "crc": -398647920}, {"key": "androidx/compose/runtime/Invalidation.class", "name": "androidx/compose/runtime/Invalidation.class", "size": 1924, "crc": -835813378}, {"key": "androidx/compose/runtime/InvalidationResult.class", "name": "androidx/compose/runtime/InvalidationResult.class", "size": 2009, "crc": -281213320}, {"key": "androidx/compose/runtime/JoinedKey.class", "name": "androidx/compose/runtime/JoinedKey.class", "size": 3016, "crc": -765257422}, {"key": "androidx/compose/runtime/KeyInfo.class", "name": "androidx/compose/runtime/KeyInfo.class", "size": 1698, "crc": 105104218}, {"key": "androidx/compose/runtime/Latch$await$2$2.class", "name": "androidx/compose/runtime/Latch$await$2$2.class", "size": 2772, "crc": -1918370361}, {"key": "androidx/compose/runtime/Latch.class", "name": "androidx/compose/runtime/Latch.class", "size": 6862, "crc": -1474401230}, {"key": "androidx/compose/runtime/LaunchedEffectImpl.class", "name": "androidx/compose/runtime/LaunchedEffectImpl.class", "size": 6892, "crc": -2068753963}, {"key": "androidx/compose/runtime/LazyValueHolder.class", "name": "androidx/compose/runtime/LazyValueHolder.class", "size": 2651, "crc": 617431681}, {"key": "androidx/compose/runtime/LeftCompositionCancellationException.class", "name": "androidx/compose/runtime/LeftCompositionCancellationException.class", "size": 768, "crc": 47336684}, {"key": "androidx/compose/runtime/LongState$DefaultImpls.class", "name": "androidx/compose/runtime/LongState$DefaultImpls.class", "size": 1008, "crc": 661868951}, {"key": "androidx/compose/runtime/LongState.class", "name": "androidx/compose/runtime/LongState.class", "size": 1464, "crc": -993900591}, {"key": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "name": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "size": 3392, "crc": -1014972382}, {"key": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "name": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "size": 1048, "crc": -1567739474}, {"key": "androidx/compose/runtime/MonotonicFrameClock.class", "name": "androidx/compose/runtime/MonotonicFrameClock.class", "size": 2111, "crc": -1638664477}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "size": 1554, "crc": -1283338605}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt.class", "size": 4511, "crc": -1698214420}, {"key": "androidx/compose/runtime/MovableContent.class", "name": "androidx/compose/runtime/MovableContent.class", "size": 1780, "crc": 1851257304}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "size": 2244, "crc": 951191766}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "size": 2698, "crc": -1566733283}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "size": 3019, "crc": 69567926}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "size": 3255, "crc": -1144076141}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "size": 3382, "crc": 1445958678}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "size": 2431, "crc": 1957914340}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "size": 3107, "crc": 442956472}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "size": 3181, "crc": 386164581}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "size": 2765, "crc": 186070443}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "size": 2746, "crc": 1124069842}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "size": 3071, "crc": -1334254237}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "size": 3297, "crc": 1845265743}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "size": 3434, "crc": -566099262}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "size": 2870, "crc": -359305538}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "size": 3149, "crc": -1401446382}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "size": 3225, "crc": 62648067}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "size": 2811, "crc": -1358402270}, {"key": "androidx/compose/runtime/MovableContentKt.class", "name": "androidx/compose/runtime/MovableContentKt.class", "size": 8695, "crc": 2005428848}, {"key": "androidx/compose/runtime/MovableContentState.class", "name": "androidx/compose/runtime/MovableContentState.class", "size": 9772, "crc": 1211081662}, {"key": "androidx/compose/runtime/MovableContentStateReference.class", "name": "androidx/compose/runtime/MovableContentStateReference.class", "size": 5570, "crc": -2092223293}, {"key": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "size": 1295, "crc": -1880066779}, {"key": "androidx/compose/runtime/MutableDoubleState.class", "name": "androidx/compose/runtime/MutableDoubleState.class", "size": 2169, "crc": -955982485}, {"key": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "size": 1284, "crc": -2013718594}, {"key": "androidx/compose/runtime/MutableFloatState.class", "name": "androidx/compose/runtime/MutableFloatState.class", "size": 2153, "crc": 1248508446}, {"key": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "size": 1274, "crc": -1123336495}, {"key": "androidx/compose/runtime/MutableIntState.class", "name": "androidx/compose/runtime/MutableIntState.class", "size": 2137, "crc": -2130064701}, {"key": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "size": 1273, "crc": -1018505683}, {"key": "androidx/compose/runtime/MutableLongState.class", "name": "androidx/compose/runtime/MutableLongState.class", "size": 2137, "crc": -1388766015}, {"key": "androidx/compose/runtime/MutableState.class", "name": "androidx/compose/runtime/MutableState.class", "size": 1116, "crc": -1970911074}, {"key": "androidx/compose/runtime/NestedContentMap.class", "name": "androidx/compose/runtime/NestedContentMap.class", "size": 6823, "crc": -126391724}, {"key": "androidx/compose/runtime/NestedMovableContent.class", "name": "androidx/compose/runtime/NestedMovableContent.class", "size": 1335, "crc": 953434906}, {"key": "androidx/compose/runtime/NeverEqualPolicy.class", "name": "androidx/compose/runtime/NeverEqualPolicy.class", "size": 1395, "crc": 662450731}, {"key": "androidx/compose/runtime/NoLiveLiterals.class", "name": "androidx/compose/runtime/NoLiveLiterals.class", "size": 857, "crc": -580896318}, {"key": "androidx/compose/runtime/NonRestartableComposable.class", "name": "androidx/compose/runtime/NonRestartableComposable.class", "size": 857, "crc": -1413705260}, {"key": "androidx/compose/runtime/NonSkippableComposable.class", "name": "androidx/compose/runtime/NonSkippableComposable.class", "size": 851, "crc": -926426537}, {"key": "androidx/compose/runtime/OffsetApplier.class", "name": "androidx/compose/runtime/OffsetApplier.class", "size": 4572, "crc": 773834934}, {"key": "androidx/compose/runtime/OpaqueKey.class", "name": "androidx/compose/runtime/OpaqueKey.class", "size": 2284, "crc": -1085428947}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "size": 1846, "crc": -282552450}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "size": 1142, "crc": -981624033}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "size": 2166, "crc": 809830302}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "size": 1837, "crc": -152242959}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "size": 1137, "crc": 1044813670}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "size": 2155, "crc": -1198161425}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "size": 1819, "crc": 569185293}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "size": 1127, "crc": 2086421439}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "size": 2113, "crc": -944093346}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "size": 1828, "crc": 644681874}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "size": 1132, "crc": 432877554}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "size": 2144, "crc": -1382294133}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "size": 3909, "crc": 929282228}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "size": 1272, "crc": -1082928158}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "size": 3425, "crc": -1934274297}, {"key": "androidx/compose/runtime/PausableComposition.class", "name": "androidx/compose/runtime/PausableComposition.class", "size": 1286, "crc": -1313647005}, {"key": "androidx/compose/runtime/PausableCompositionKt.class", "name": "androidx/compose/runtime/PausableCompositionKt.class", "size": 1381, "crc": -2133822894}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "size": 1949, "crc": -491240654}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "size": 5428, "crc": -1091478714}, {"key": "androidx/compose/runtime/PausedComposition.class", "name": "androidx/compose/runtime/PausedComposition.class", "size": 955, "crc": -1433539253}, {"key": "androidx/compose/runtime/PausedCompositionImpl$WhenMappings.class", "name": "androidx/compose/runtime/PausedCompositionImpl$WhenMappings.class", "size": 1112, "crc": -1328719851}, {"key": "androidx/compose/runtime/PausedCompositionImpl.class", "name": "androidx/compose/runtime/PausedCompositionImpl.class", "size": 16401, "crc": -1408795197}, {"key": "androidx/compose/runtime/PausedCompositionState.class", "name": "androidx/compose/runtime/PausedCompositionState.class", "size": 2257, "crc": 650631118}, {"key": "androidx/compose/runtime/Pending$keyMap$2.class", "name": "androidx/compose/runtime/Pending$keyMap$2.class", "size": 2227, "crc": 193391377}, {"key": "androidx/compose/runtime/Pending.class", "name": "androidx/compose/runtime/Pending.class", "size": 13797, "crc": 725991968}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "size": 1441, "crc": 1800785382}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "size": 2769, "crc": -949166270}, {"key": "androidx/compose/runtime/PreconditionsKt.class", "name": "androidx/compose/runtime/PreconditionsKt.class", "size": 1848, "crc": 1187402115}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "size": 1636, "crc": -1268378753}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "size": 2156, "crc": 103130652}, {"key": "androidx/compose/runtime/PrioritySet.class", "name": "androidx/compose/runtime/PrioritySet.class", "size": 7406, "crc": -373490267}, {"key": "androidx/compose/runtime/ProduceFrameSignal.class", "name": "androidx/compose/runtime/ProduceFrameSignal.class", "size": 6226, "crc": -1226786964}, {"key": "androidx/compose/runtime/ProduceStateScope.class", "name": "androidx/compose/runtime/ProduceStateScope.class", "size": 1236, "crc": 37548477}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "size": 1859, "crc": -1406877388}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl.class", "size": 5646, "crc": -820648421}, {"key": "androidx/compose/runtime/ProvidableCompositionLocal.class", "name": "androidx/compose/runtime/ProvidableCompositionLocal.class", "size": 5405, "crc": -518292533}, {"key": "androidx/compose/runtime/ProvidedValue.class", "name": "androidx/compose/runtime/ProvidedValue.class", "size": 5760, "crc": -964613661}, {"key": "androidx/compose/runtime/ReadOnlyComposable.class", "name": "androidx/compose/runtime/ReadOnlyComposable.class", "size": 930, "crc": 1491935930}, {"key": "androidx/compose/runtime/RecomposeScope.class", "name": "androidx/compose/runtime/RecomposeScope.class", "size": 443, "crc": -1892498070}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "size": 4888, "crc": -721722822}, {"key": "androidx/compose/runtime/RecomposeScopeImpl.class", "name": "androidx/compose/runtime/RecomposeScopeImpl.class", "size": 24616, "crc": -1606050051}, {"key": "androidx/compose/runtime/RecomposeScopeImplKt.class", "name": "androidx/compose/runtime/RecomposeScopeImplKt.class", "size": 2666, "crc": -855402772}, {"key": "androidx/compose/runtime/RecomposeScopeOwner.class", "name": "androidx/compose/runtime/RecomposeScopeOwner.class", "size": 1108, "crc": 1457589012}, {"key": "androidx/compose/runtime/Recomposer$Companion.class", "name": "androidx/compose/runtime/Recomposer$Companion.class", "size": 11107, "crc": 1987525762}, {"key": "androidx/compose/runtime/Recomposer$HotReloadable.class", "name": "androidx/compose/runtime/Recomposer$HotReloadable.class", "size": 2055, "crc": 354620324}, {"key": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "name": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "size": 1339, "crc": -996559890}, {"key": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "name": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "size": 9042, "crc": -460970823}, {"key": "androidx/compose/runtime/Recomposer$State.class", "name": "androidx/compose/runtime/Recomposer$State.class", "size": 2216, "crc": 1716380698}, {"key": "androidx/compose/runtime/Recomposer$addCompositionRegistrationObserver$2.class", "name": "androidx/compose/runtime/Recomposer$addCompositionRegistrationObserver$2.class", "size": 3093, "crc": -2126896911}, {"key": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "name": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "size": 3186, "crc": -816662410}, {"key": "androidx/compose/runtime/Recomposer$join$2.class", "name": "androidx/compose/runtime/Recomposer$join$2.class", "size": 3095, "crc": -906069946}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2$2.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2$2.class", "size": 3829, "crc": -956025204}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "size": 14755, "crc": 963072492}, {"key": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "size": 2133, "crc": -1910346014}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "size": 30023, "crc": 1090769900}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "size": 6109, "crc": -1247245738}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "size": 3906, "crc": 459295424}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "size": 14888, "crc": 955610151}, {"key": "androidx/compose/runtime/Recomposer.class", "name": "androidx/compose/runtime/Recomposer.class", "size": 98904, "crc": 1396913074}, {"key": "androidx/compose/runtime/RecomposerErrorInfo.class", "name": "androidx/compose/runtime/RecomposerErrorInfo.class", "size": 717, "crc": 1841123636}, {"key": "androidx/compose/runtime/RecomposerInfo.class", "name": "androidx/compose/runtime/RecomposerInfo.class", "size": 1685, "crc": 1455999655}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "size": 3262, "crc": 2099399619}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "size": 4734, "crc": 172828994}, {"key": "androidx/compose/runtime/RecomposerKt.class", "name": "androidx/compose/runtime/RecomposerKt.class", "size": 3257, "crc": -411155094}, {"key": "androidx/compose/runtime/RecordingApplier$Companion.class", "name": "androidx/compose/runtime/RecordingApplier$Companion.class", "size": 1134, "crc": -115777376}, {"key": "androidx/compose/runtime/RecordingApplier.class", "name": "androidx/compose/runtime/RecordingApplier.class", "size": 8735, "crc": -1720797331}, {"key": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "name": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "size": 1458, "crc": -1870336610}, {"key": "androidx/compose/runtime/RelativeGroupPath.class", "name": "androidx/compose/runtime/RelativeGroupPath.class", "size": 1648, "crc": -13065253}, {"key": "androidx/compose/runtime/RememberManager.class", "name": "androidx/compose/runtime/RememberManager.class", "size": 1481, "crc": -1476646318}, {"key": "androidx/compose/runtime/RememberObserver.class", "name": "androidx/compose/runtime/RememberObserver.class", "size": 518, "crc": -1947456438}, {"key": "androidx/compose/runtime/RememberObserverHolder.class", "name": "androidx/compose/runtime/RememberObserverHolder.class", "size": 1814, "crc": -2078652549}, {"key": "androidx/compose/runtime/RememberedCoroutineScope$Companion.class", "name": "androidx/compose/runtime/RememberedCoroutineScope$Companion.class", "size": 949, "crc": 1040835212}, {"key": "androidx/compose/runtime/RememberedCoroutineScope$special$$inlined$CoroutineExceptionHandler$1.class", "name": "androidx/compose/runtime/RememberedCoroutineScope$special$$inlined$CoroutineExceptionHandler$1.class", "size": 3959, "crc": -885089243}, {"key": "androidx/compose/runtime/RememberedCoroutineScope.class", "name": "androidx/compose/runtime/RememberedCoroutineScope.class", "size": 7349, "crc": 1978657198}, {"key": "androidx/compose/runtime/ReusableComposition.class", "name": "androidx/compose/runtime/ReusableComposition.class", "size": 1068, "crc": -1910238991}, {"key": "androidx/compose/runtime/ReusableRememberObserver.class", "name": "androidx/compose/runtime/ReusableRememberObserver.class", "size": 494, "crc": -1127880081}, {"key": "androidx/compose/runtime/ScopeInvalidated.class", "name": "androidx/compose/runtime/ScopeInvalidated.class", "size": 801, "crc": -1739968744}, {"key": "androidx/compose/runtime/ScopeUpdateScope.class", "name": "androidx/compose/runtime/ScopeUpdateScope.class", "size": 861, "crc": 1105834190}, {"key": "androidx/compose/runtime/ShouldPauseCallback.class", "name": "androidx/compose/runtime/ShouldPauseCallback.class", "size": 459, "crc": 333458841}, {"key": "androidx/compose/runtime/SkippableUpdater.class", "name": "androidx/compose/runtime/SkippableUpdater.class", "size": 3909, "crc": 1839852485}, {"key": "androidx/compose/runtime/SlotReader.class", "name": "androidx/compose/runtime/SlotReader.class", "size": 20751, "crc": 1872629836}, {"key": "androidx/compose/runtime/SlotTable.class", "name": "androidx/compose/runtime/SlotTable.class", "size": 40151, "crc": 1633453069}, {"key": "androidx/compose/runtime/SlotTableGroup.class", "name": "androidx/compose/runtime/SlotTableGroup.class", "size": 9384, "crc": 413282915}, {"key": "androidx/compose/runtime/SlotTableKt.class", "name": "androidx/compose/runtime/SlotTableKt.class", "size": 21368, "crc": 197868433}, {"key": "androidx/compose/runtime/SlotWriter$Companion.class", "name": "androidx/compose/runtime/SlotWriter$Companion.class", "size": 12322, "crc": 1439263909}, {"key": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "name": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "size": 2048, "crc": -2114659498}, {"key": "androidx/compose/runtime/SlotWriter.class", "name": "androidx/compose/runtime/SlotWriter.class", "size": 78276, "crc": 1519619034}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "size": 1640, "crc": -782543925}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "size": 2166, "crc": -153218020}, {"key": "androidx/compose/runtime/SnapshotDoubleState_androidKt.class", "name": "androidx/compose/runtime/SnapshotDoubleState_androidKt.class", "size": 853, "crc": -1916468838}, {"key": "androidx/compose/runtime/SnapshotFloatState_androidKt.class", "name": "androidx/compose/runtime/SnapshotFloatState_androidKt.class", "size": 846, "crc": 1659638557}, {"key": "androidx/compose/runtime/SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt.class", "size": 1604, "crc": 499474566}, {"key": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "size": 2108, "crc": -1009883342}, {"key": "androidx/compose/runtime/SnapshotIntState_androidKt.class", "name": "androidx/compose/runtime/SnapshotIntState_androidKt.class", "size": 832, "crc": -2113738129}, {"key": "androidx/compose/runtime/SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt.class", "size": 1616, "crc": -1602437527}, {"key": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "size": 2130, "crc": -1073193758}, {"key": "androidx/compose/runtime/SnapshotLongState_androidKt.class", "name": "androidx/compose/runtime/SnapshotLongState_androidKt.class", "size": 839, "crc": 692593256}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "size": 2134, "crc": 917518040}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "size": 10388, "crc": -1998428510}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "size": 2297, "crc": 673967393}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "size": 10341, "crc": -1765611224}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "size": 2279, "crc": -1559612044}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "size": 9978, "crc": -2135520883}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "size": 2261, "crc": -471715623}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "size": 9509, "crc": -1014085443}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "size": 3070, "crc": 382525482}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "size": 10665, "crc": -1510069408}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "size": 1138, "crc": 483606787}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy.class", "size": 1454, "crc": -430290336}, {"key": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "name": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "size": 2653, "crc": 338941281}, {"key": "androidx/compose/runtime/SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt.class", "size": 11712, "crc": 1290797040}, {"key": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "size": 8096, "crc": -756744786}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1$1.class", "size": 4093, "crc": 2049004715}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2$1.class", "size": 4111, "crc": -619386803}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3$1.class", "size": 4129, "crc": -1766997533}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4$1.class", "size": 4147, "crc": 1471760119}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5$1.class", "size": 4112, "crc": 115563938}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "size": 13229, "crc": 70272315}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$1.class", "size": 1601, "crc": -65810774}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2$1.class", "size": 1696, "crc": -176569241}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1$2.class", "size": 3869, "crc": -582233793}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "size": 4791, "crc": 852497763}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "size": 15021, "crc": -1642386014}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "size": 8763, "crc": -441781881}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "size": 2124, "crc": 359780036}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "size": 10050, "crc": 368500814}, {"key": "androidx/compose/runtime/SnapshotState_androidKt.class", "name": "androidx/compose/runtime/SnapshotState_androidKt.class", "size": 1351, "crc": 5715400}, {"key": "androidx/compose/runtime/SourceInformationGroupDataIterator.class", "name": "androidx/compose/runtime/SourceInformationGroupDataIterator.class", "size": 5338, "crc": 747328756}, {"key": "androidx/compose/runtime/SourceInformationGroupIterator.class", "name": "androidx/compose/runtime/SourceInformationGroupIterator.class", "size": 4645, "crc": 240926873}, {"key": "androidx/compose/runtime/SourceInformationGroupPath.class", "name": "androidx/compose/runtime/SourceInformationGroupPath.class", "size": 1142, "crc": 1243182949}, {"key": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "name": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "size": 4682, "crc": -191130992}, {"key": "androidx/compose/runtime/SourceInformationSlotTableGroupIdentity.class", "name": "androidx/compose/runtime/SourceInformationSlotTableGroupIdentity.class", "size": 2817, "crc": -129368435}, {"key": "androidx/compose/runtime/Stack.class", "name": "androidx/compose/runtime/Stack.class", "size": 4904, "crc": 178193232}, {"key": "androidx/compose/runtime/State.class", "name": "androidx/compose/runtime/State.class", "size": 629, "crc": 253210034}, {"key": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "name": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "size": 2083, "crc": -2145015474}, {"key": "androidx/compose/runtime/StaticValueHolder.class", "name": "androidx/compose/runtime/StaticValueHolder.class", "size": 3936, "crc": -208934811}, {"key": "androidx/compose/runtime/StructuralEqualityPolicy.class", "name": "androidx/compose/runtime/StructuralEqualityPolicy.class", "size": 1480, "crc": -1996143623}, {"key": "androidx/compose/runtime/TestOnly_jvmKt.class", "name": "androidx/compose/runtime/TestOnly_jvmKt.class", "size": 371, "crc": -1875321075}, {"key": "androidx/compose/runtime/UnboxedDoubleState.class", "name": "androidx/compose/runtime/UnboxedDoubleState.class", "size": 2155, "crc": -1694762571}, {"key": "androidx/compose/runtime/UnboxedFloatState.class", "name": "androidx/compose/runtime/UnboxedFloatState.class", "size": 2144, "crc": 792784744}, {"key": "androidx/compose/runtime/UnboxedIntState.class", "name": "androidx/compose/runtime/UnboxedIntState.class", "size": 2132, "crc": 139133731}, {"key": "androidx/compose/runtime/UnboxedLongState.class", "name": "androidx/compose/runtime/UnboxedLongState.class", "size": 2133, "crc": -1860413309}, {"key": "androidx/compose/runtime/Updater.class", "name": "androidx/compose/runtime/Updater.class", "size": 7112, "crc": -733836974}, {"key": "androidx/compose/runtime/ValueHolder.class", "name": "androidx/compose/runtime/ValueHolder.class", "size": 1258, "crc": 1658771175}, {"key": "androidx/compose/runtime/changelist/ChangeList.class", "name": "androidx/compose/runtime/changelist/ChangeList.class", "size": 39788, "crc": -702728404}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "size": 953, "crc": -1522853918}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "size": 20513, "crc": 1764518489}, {"key": "androidx/compose/runtime/changelist/FixupList.class", "name": "androidx/compose/runtime/changelist/FixupList.class", "size": 11130, "crc": -705403648}, {"key": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "name": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "size": 3594, "crc": 982982488}, {"key": "androidx/compose/runtime/changelist/Operation$AppendValue.class", "name": "androidx/compose/runtime/changelist/Operation$AppendValue.class", "size": 4764, "crc": -913349611}, {"key": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "name": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "size": 5516, "crc": 1691338882}, {"key": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "size": 5659, "crc": 1638690691}, {"key": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "size": 7076, "crc": 1091613740}, {"key": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "size": 2388, "crc": -1585027117}, {"key": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "name": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "size": 4992, "crc": -37146392}, {"key": "androidx/compose/runtime/changelist/Operation$Downs.class", "name": "androidx/compose/runtime/changelist/Operation$Downs.class", "size": 4341, "crc": 215711503}, {"key": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "name": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "size": 4519, "crc": 1499818606}, {"key": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "size": 2273, "crc": 1074165762}, {"key": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "name": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "size": 2694, "crc": -679911159}, {"key": "androidx/compose/runtime/changelist/Operation$EndResumingScope.class", "name": "androidx/compose/runtime/changelist/Operation$EndResumingScope.class", "size": 4051, "crc": 2014583725}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "size": 4004, "crc": -716632129}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "size": 2300, "crc": 248400078}, {"key": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "size": 5982, "crc": -1786988512}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "size": 4645, "crc": 797672307}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "size": 7045, "crc": 1753578493}, {"key": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "size": 3600, "crc": -1851360010}, {"key": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "size": 4086, "crc": -1102269661}, {"key": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "name": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "size": 2364, "crc": 603772310}, {"key": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "size": 5476, "crc": 330758936}, {"key": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "name": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "size": 5740, "crc": 1296983214}, {"key": "androidx/compose/runtime/changelist/Operation$Remember.class", "name": "androidx/compose/runtime/changelist/Operation$Remember.class", "size": 3976, "crc": -1039529063}, {"key": "androidx/compose/runtime/changelist/Operation$RememberPausingScope.class", "name": "androidx/compose/runtime/changelist/Operation$RememberPausingScope.class", "size": 4083, "crc": 1562242638}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "size": 2372, "crc": 1058176586}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "size": 3864, "crc": -1058345798}, {"key": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "name": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "size": 2248, "crc": -692705004}, {"key": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "name": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "size": 3942, "crc": -1439410798}, {"key": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "size": 2296, "crc": -434625165}, {"key": "androidx/compose/runtime/changelist/Operation$StartResumingScope.class", "name": "androidx/compose/runtime/changelist/Operation$StartResumingScope.class", "size": 4067, "crc": -1267438194}, {"key": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "name": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "size": 6855, "crc": 1931909970}, {"key": "androidx/compose/runtime/changelist/Operation$TrimParentValues.class", "name": "androidx/compose/runtime/changelist/Operation$TrimParentValues.class", "size": 5026, "crc": 744476861}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateAnchoredValue.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateAnchoredValue.class", "size": 5726, "crc": 787001433}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "size": 3869, "crc": -1297544988}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "size": 4461, "crc": 1290146312}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "size": 5332, "crc": -167617521}, {"key": "androidx/compose/runtime/changelist/Operation$Ups.class", "name": "androidx/compose/runtime/changelist/Operation$Ups.class", "size": 3695, "crc": 451265328}, {"key": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "name": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "size": 2259, "crc": -976809930}, {"key": "androidx/compose/runtime/changelist/Operation.class", "name": "androidx/compose/runtime/changelist/Operation.class", "size": 12614, "crc": -1238191977}, {"key": "androidx/compose/runtime/changelist/OperationArgContainer.class", "name": "androidx/compose/runtime/changelist/OperationArgContainer.class", "size": 917, "crc": 732961870}, {"key": "androidx/compose/runtime/changelist/OperationErrorContext.class", "name": "androidx/compose/runtime/changelist/OperationErrorContext.class", "size": 933, "crc": -2009110018}, {"key": "androidx/compose/runtime/changelist/OperationKt$withCurrentStackTrace$1.class", "name": "androidx/compose/runtime/changelist/OperationKt$withCurrentStackTrace$1.class", "size": 2359, "crc": 1791240858}, {"key": "androidx/compose/runtime/changelist/OperationKt.class", "name": "androidx/compose/runtime/changelist/OperationKt.class", "size": 9042, "crc": 1760488789}, {"key": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "name": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "size": 3468, "crc": -1006090319}, {"key": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "name": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "size": 9547, "crc": -765894436}, {"key": "androidx/compose/runtime/changelist/Operations.class", "name": "androidx/compose/runtime/changelist/Operations.class", "size": 21141, "crc": 994090410}, {"key": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "name": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "size": 1411, "crc": 1630752555}, {"key": "androidx/compose/runtime/changelist/OperationsKt.class", "name": "androidx/compose/runtime/changelist/OperationsKt.class", "size": 476, "crc": 1271730060}, {"key": "androidx/compose/runtime/collection/ArrayUtils_androidKt.class", "name": "androidx/compose/runtime/collection/ArrayUtils_androidKt.class", "size": 1119, "crc": 1357206811}, {"key": "androidx/compose/runtime/collection/ExtensionsKt$sortBy$$inlined$sortBy$1.class", "name": "androidx/compose/runtime/collection/ExtensionsKt$sortBy$$inlined$sortBy$1.class", "size": 1732, "crc": -1452874159}, {"key": "androidx/compose/runtime/collection/ExtensionsKt.class", "name": "androidx/compose/runtime/collection/ExtensionsKt.class", "size": 8737, "crc": -1151291505}, {"key": "androidx/compose/runtime/collection/MultiValueMap.class", "name": "androidx/compose/runtime/collection/MultiValueMap.class", "size": 14742, "crc": -1465882865}, {"key": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "name": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "size": 6967, "crc": -2068440243}, {"key": "androidx/compose/runtime/collection/MutableVector$SubList.class", "name": "androidx/compose/runtime/collection/MutableVector$SubList.class", "size": 8216, "crc": -90845309}, {"key": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "name": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "size": 2708, "crc": 1429431973}, {"key": "androidx/compose/runtime/collection/MutableVector.class", "name": "androidx/compose/runtime/collection/MutableVector.class", "size": 29290, "crc": -304762773}, {"key": "androidx/compose/runtime/collection/MutableVectorKt.class", "name": "androidx/compose/runtime/collection/MutableVectorKt.class", "size": 5358, "crc": -1062393404}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapper$iterator$1.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapper$iterator$1.class", "size": 6729, "crc": -769571701}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapper.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapper.class", "size": 5146, "crc": -859591202}, {"key": "androidx/compose/runtime/collection/ScatterSetWrapperKt.class", "name": "androidx/compose/runtime/collection/ScatterSetWrapperKt.class", "size": 6481, "crc": 466472659}, {"key": "androidx/compose/runtime/collection/ScopeMap.class", "name": "androidx/compose/runtime/collection/ScopeMap.class", "size": 22385, "crc": 665164631}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "size": 44647, "crc": -348711036}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "size": 716, "crc": -856790228}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "size": 2868, "crc": -273769489}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "size": 1836, "crc": 1593629523}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "size": 1795, "crc": 410209861}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "size": 967, "crc": 1516462030}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "size": 1281, "crc": 1466914269}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "size": 3009, "crc": -524928057}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "size": 1626, "crc": 1307089883}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "size": 4123, "crc": -1194923656}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "size": 1256, "crc": -2145013311}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "size": 2696, "crc": -195728748}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "size": 1615, "crc": -1207893963}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "size": 3172, "crc": 817666433}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "size": 4277, "crc": -288187204}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "size": 6248, "crc": -1132891887}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "size": 7709, "crc": 1158094060}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "size": 1727, "crc": -1693330670}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "size": 2971, "crc": -818810429}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "size": 9045, "crc": 2028256129}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "size": 2131, "crc": 1847745346}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "size": 1293, "crc": -346554870}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "size": 16437, "crc": -70377857}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "size": 30464, "crc": 1579928467}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "size": 3163, "crc": -244309092}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "size": 5839, "crc": 1731136236}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "size": 1900, "crc": -1283593797}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "size": 1546, "crc": -19175197}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "size": 12728, "crc": 746134648}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "size": 4181, "crc": -532924545}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "size": 2490, "crc": 1820245019}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "size": 2263, "crc": 1319972024}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "size": 3694, "crc": 257776195}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "size": 3082, "crc": 1227440380}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "size": 2312, "crc": 1874846043}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "size": 12979, "crc": 1855686227}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "size": 5655, "crc": -2046745891}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "size": 11487, "crc": 1057870115}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "size": 7439, "crc": 1717791485}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "size": 4971, "crc": 172711238}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "size": 4192, "crc": 1821094381}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "size": 3328, "crc": -1323151254}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "size": 2781, "crc": 703450946}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "size": 3166, "crc": -841337174}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "size": 2787, "crc": -1717205943}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "size": 497, "crc": 958398069}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "size": 4634, "crc": 2007605780}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "size": 2798, "crc": -28385806}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "size": 3107, "crc": -1795773347}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "size": 2656, "crc": -2038576570}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "size": 3155, "crc": 1346597091}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "size": 2662, "crc": -1268608426}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "size": 1481, "crc": -634794952}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "size": 4367, "crc": 2099779188}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "size": 43644, "crc": 1182274487}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "size": 4773, "crc": 1268384457}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "size": 2250, "crc": -1624171605}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "size": 1755, "crc": -325044619}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "size": 4336, "crc": 693353990}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "size": 3184, "crc": -1131446243}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "size": 1758, "crc": 702707956}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "size": 1961, "crc": 2009049356}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "size": 11206, "crc": -206025839}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "size": 10396, "crc": 317412036}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "size": 5592, "crc": 279693688}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "size": 6230, "crc": -2061501126}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "size": 1475, "crc": 804130476}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "size": 37063, "crc": 1645696964}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "size": 3896, "crc": -1792785087}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "size": 1977, "crc": -1773172800}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "size": 3920, "crc": 1815948710}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "size": 3575, "crc": -293781722}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "size": 3454, "crc": -736697485}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "size": 2414, "crc": 380054588}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "size": 14489, "crc": 175022599}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "size": 9498, "crc": 1253928978}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "size": 5095, "crc": 1140766109}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "size": 4257, "crc": -74356446}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "size": 3419, "crc": -751490052}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "size": 3217, "crc": 533274058}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "size": 6530, "crc": -1470272124}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "size": 3257, "crc": -1122862354}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "size": 3311, "crc": 1750577909}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "size": 4514, "crc": -1212016815}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "size": 3920, "crc": 1428535478}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "size": 2950, "crc": 1875161282}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "size": 3331, "crc": 1798908193}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "size": 4406, "crc": -1290739224}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "size": 2998, "crc": -31431079}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "size": 3382, "crc": 1583571244}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "size": 2611, "crc": 604518177}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "size": 2019, "crc": -403782730}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "size": 13232, "crc": -560928544}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "size": 8221, "crc": 1469402540}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "size": 4003, "crc": 44139204}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "size": 4195, "crc": 482465884}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "size": 530, "crc": -905011959}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "size": 2799, "crc": 1413622818}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "size": 884, "crc": -507930377}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "size": 1369, "crc": 2085068990}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "size": 3486, "crc": 86516584}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "size": 813, "crc": -78243705}, {"key": "androidx/compose/runtime/internal/AtomicInt.class", "name": "androidx/compose/runtime/internal/AtomicInt.class", "size": 2617, "crc": -1396920128}, {"key": "androidx/compose/runtime/internal/Atomic_jvmKt.class", "name": "androidx/compose/runtime/internal/Atomic_jvmKt.class", "size": 419, "crc": 1653228805}, {"key": "androidx/compose/runtime/internal/ComposableLambda.class", "name": "androidx/compose/runtime/internal/ComposableLambda.class", "size": 9076, "crc": -1516716340}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "size": 1629, "crc": -164593676}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "size": 59823, "crc": 833890185}, {"key": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "size": 6397, "crc": 1016799944}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN.class", "size": 731, "crc": -699943546}, {"key": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "size": 8053, "crc": 1145199736}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "size": 5123, "crc": 115291757}, {"key": "androidx/compose/runtime/internal/Decoy.class", "name": "androidx/compose/runtime/internal/Decoy.class", "size": 1021, "crc": 845789874}, {"key": "androidx/compose/runtime/internal/DecoyImplementation.class", "name": "androidx/compose/runtime/internal/DecoyImplementation.class", "size": 998, "crc": 1683922638}, {"key": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "name": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "size": 953, "crc": 2072199455}, {"key": "androidx/compose/runtime/internal/DecoyKt.class", "name": "androidx/compose/runtime/internal/DecoyKt.class", "size": 1053, "crc": 1753532754}, {"key": "androidx/compose/runtime/internal/Expect_jvmKt.class", "name": "androidx/compose/runtime/internal/Expect_jvmKt.class", "size": 1604, "crc": -2135305356}, {"key": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "name": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "size": 1483, "crc": -580380083}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "size": 938, "crc": 706065162}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "size": 1319, "crc": 1751255917}, {"key": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "name": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "size": 962, "crc": 257323199}, {"key": "androidx/compose/runtime/internal/IntRef.class", "name": "androidx/compose/runtime/internal/IntRef.class", "size": 1872, "crc": 1691697440}, {"key": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 441, "crc": 1399260776}, {"key": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "size": 957, "crc": 1325033132}, {"key": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "size": 1018, "crc": -2078554715}, {"key": "androidx/compose/runtime/internal/LiveLiteralKt.class", "name": "androidx/compose/runtime/internal/LiveLiteralKt.class", "size": 4327, "crc": -492656921}, {"key": "androidx/compose/runtime/internal/PausedCompositionRemembers.class", "name": "androidx/compose/runtime/internal/PausedCompositionRemembers.class", "size": 4055, "crc": -1197971267}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "size": 6728, "crc": 309695096}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "size": 1379, "crc": 2041682947}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "size": 9732, "crc": 335683794}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "size": 3314, "crc": -1389794381}, {"key": "androidx/compose/runtime/internal/PlatformOptimizedCancellationException.class", "name": "androidx/compose/runtime/internal/PlatformOptimizedCancellationException.class", "size": 1788, "crc": -1676962613}, {"key": "androidx/compose/runtime/internal/PlatformOptimizedCancellationException_jvmKt.class", "name": "androidx/compose/runtime/internal/PlatformOptimizedCancellationException_jvmKt.class", "size": 840, "crc": -1611459936}, {"key": "androidx/compose/runtime/internal/RememberEventDispatcher.class", "name": "androidx/compose/runtime/internal/RememberEventDispatcher.class", "size": 19752, "crc": 1787092238}, {"key": "androidx/compose/runtime/internal/SnapshotThreadLocal.class", "name": "androidx/compose/runtime/internal/SnapshotThreadLocal.class", "size": 4071, "crc": 2022550876}, {"key": "androidx/compose/runtime/internal/SnapshotThreadLocalKt.class", "name": "androidx/compose/runtime/internal/SnapshotThreadLocalKt.class", "size": 827, "crc": 1811234891}, {"key": "androidx/compose/runtime/internal/StabilityInferred.class", "name": "androidx/compose/runtime/internal/StabilityInferred.class", "size": 947, "crc": -1591363398}, {"key": "androidx/compose/runtime/internal/System_jvmKt.class", "name": "androidx/compose/runtime/internal/System_jvmKt.class", "size": 654, "crc": 110631575}, {"key": "androidx/compose/runtime/internal/ThreadMap.class", "name": "androidx/compose/runtime/internal/ThreadMap.class", "size": 3952, "crc": -1618373469}, {"key": "androidx/compose/runtime/internal/Thread_androidKt.class", "name": "androidx/compose/runtime/internal/Thread_androidKt.class", "size": 876, "crc": -4343218}, {"key": "androidx/compose/runtime/internal/Thread_jvmKt.class", "name": "androidx/compose/runtime/internal/Thread_jvmKt.class", "size": 720, "crc": -1701504970}, {"key": "androidx/compose/runtime/internal/Trace.class", "name": "androidx/compose/runtime/internal/Trace.class", "size": 1350, "crc": -1608818118}, {"key": "androidx/compose/runtime/internal/TraceKt.class", "name": "androidx/compose/runtime/internal/TraceKt.class", "size": 1517, "crc": -391174082}, {"key": "androidx/compose/runtime/internal/Utils_androidKt.class", "name": "androidx/compose/runtime/internal/Utils_androidKt.class", "size": 931, "crc": 1766226092}, {"key": "androidx/compose/runtime/internal/WeakReference.class", "name": "androidx/compose/runtime/internal/WeakReference.class", "size": 1083, "crc": 718409694}, {"key": "androidx/compose/runtime/platform/Synchronization_androidKt.class", "name": "androidx/compose/runtime/platform/Synchronization_androidKt.class", "size": 1996, "crc": 80428335}, {"key": "androidx/compose/runtime/reflect/ComposableInfo.class", "name": "androidx/compose/runtime/reflect/ComposableInfo.class", "size": 3349, "crc": -277370472}, {"key": "androidx/compose/runtime/reflect/ComposableMethod.class", "name": "androidx/compose/runtime/reflect/ComposableMethod.class", "size": 8431, "crc": 1013185324}, {"key": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "name": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "size": 10592, "crc": -1353533559}, {"key": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "name": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "size": 1001, "crc": 883558123}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1$1.class", "size": 4206, "crc": 16961202}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1$1.class", "size": 3902, "crc": 128116566}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "size": 10026, "crc": -1182058841}, {"key": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "name": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "size": 12556, "crc": 507867726}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "size": 900, "crc": 1592345917}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "size": 41290, "crc": 765550849}, {"key": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "size": 8300, "crc": -1739631704}, {"key": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "size": 8649, "crc": -666916234}, {"key": "androidx/compose/runtime/snapshots/ObserverHandle.class", "name": "androidx/compose/runtime/snapshots/ObserverHandle.class", "size": 454, "crc": 36801526}, {"key": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "name": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "size": 1497, "crc": 814927781}, {"key": "androidx/compose/runtime/snapshots/ReaderKind.class", "name": "androidx/compose/runtime/snapshots/ReaderKind.class", "size": 3029, "crc": -102224561}, {"key": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "size": 8248, "crc": 1684857416}, {"key": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "size": 18467, "crc": -2036020691}, {"key": "androidx/compose/runtime/snapshots/Snapshot.class", "name": "androidx/compose/runtime/snapshots/Snapshot.class", "size": 13054, "crc": -1003945094}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "size": 1252, "crc": -841361004}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "size": 1772, "crc": -1768756353}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "size": 1258, "crc": 1404466675}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "size": 1399, "crc": -1709415360}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "size": 3152, "crc": -1902044186}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "size": 1116, "crc": -1067806698}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "size": 1040, "crc": -1866397480}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElementImpl.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElementImpl.class", "size": 4991, "crc": -1802976481}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "size": 1064, "crc": -1086056844}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "size": 8175, "crc": -699467215}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "size": 420, "crc": 280754846}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdArrayBuilder.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdArrayBuilder.class", "size": 3937, "crc": 799698244}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "size": 1190, "crc": -1741239513}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "size": 6118, "crc": -895920574}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "size": 23450, "crc": -352356410}, {"key": "androidx/compose/runtime/snapshots/SnapshotId_jvmKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotId_jvmKt.class", "size": 5646, "crc": 1421312514}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt.class", "size": 45231, "crc": -169436651}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "size": 13247, "crc": 473768312}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "size": 12158, "crc": 1947001732}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "size": 2502, "crc": -743745490}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "size": 13974, "crc": 1550541153}, {"key": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "name": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "size": 971, "crc": -175246413}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$Companion$CREATOR$1.class", "size": 3800, "crc": -1991866505}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$Companion.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$Companion.class", "size": 1112, "crc": 968043627}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "size": 36807, "crc": 1683510313}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "size": 28289, "crc": -1261867646}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "size": 5229, "crc": -2022112763}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "size": 30009, "crc": -1296611546}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "size": 1833, "crc": -425280136}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "size": 1918, "crc": -276606818}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "size": 41327, "crc": -1557192888}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "size": 21482, "crc": -890703995}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSet$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSet$Companion$CREATOR$1.class", "size": 3839, "crc": -1759607722}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSet$Companion.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSet$Companion.class", "size": 1107, "crc": 1546957812}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSet.class", "size": 21535, "crc": 1306165152}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateSetKt.class", "size": 19497, "crc": -1860991605}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "size": 7092, "crc": 297112781}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "size": 404, "crc": 1955856537}, {"key": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "name": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "size": 927, "crc": -1467691615}, {"key": "androidx/compose/runtime/snapshots/StateListIterator.class", "name": "androidx/compose/runtime/snapshots/StateListIterator.class", "size": 4632, "crc": -1262125645}, {"key": "androidx/compose/runtime/snapshots/StateListStateRecord.class", "name": "androidx/compose/runtime/snapshots/StateListStateRecord.class", "size": 5121, "crc": -1289758276}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "size": 4035, "crc": -588258987}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "size": 2283, "crc": 1224867349}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "size": 5987, "crc": 885554402}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "size": 1977, "crc": -1251654115}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "size": 1982, "crc": -922024834}, {"key": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "size": 1187, "crc": 2026385255}, {"key": "androidx/compose/runtime/snapshots/StateObject.class", "name": "androidx/compose/runtime/snapshots/StateObject.class", "size": 1851, "crc": 2043006599}, {"key": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "name": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "size": 2709, "crc": -1560930658}, {"key": "androidx/compose/runtime/snapshots/StateRecord.class", "name": "androidx/compose/runtime/snapshots/StateRecord.class", "size": 3387, "crc": 719892383}, {"key": "androidx/compose/runtime/snapshots/StateSetIterator.class", "name": "androidx/compose/runtime/snapshots/StateSetIterator.class", "size": 5775, "crc": 549221210}, {"key": "androidx/compose/runtime/snapshots/StateSetStateRecord.class", "name": "androidx/compose/runtime/snapshots/StateSetStateRecord.class", "size": 4806, "crc": -1450246019}, {"key": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "name": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "size": 3225, "crc": 1288880715}, {"key": "androidx/compose/runtime/snapshots/SubList.class", "name": "androidx/compose/runtime/snapshots/SubList.class", "size": 9970, "crc": -1091270632}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "size": 10027, "crc": 1678971294}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "size": 8142, "crc": -611540300}, {"key": "androidx/compose/runtime/snapshots/tooling/SnapshotInstanceObservers.class", "name": "androidx/compose/runtime/snapshots/tooling/SnapshotInstanceObservers.class", "size": 2174, "crc": -1745440781}, {"key": "androidx/compose/runtime/snapshots/tooling/SnapshotObserver.class", "name": "androidx/compose/runtime/snapshots/tooling/SnapshotObserver.class", "size": 2050, "crc": 2055260703}, {"key": "androidx/compose/runtime/snapshots/tooling/SnapshotObserverKt.class", "name": "androidx/compose/runtime/snapshots/tooling/SnapshotObserverKt.class", "size": 14297, "crc": -802325795}, {"key": "androidx/compose/runtime/tooling/ComposeStackTraceBuilder.class", "name": "androidx/compose/runtime/tooling/ComposeStackTraceBuilder.class", "size": 8087, "crc": 1680407842}, {"key": "androidx/compose/runtime/tooling/ComposeStackTraceBuilderKt.class", "name": "androidx/compose/runtime/tooling/ComposeStackTraceBuilderKt.class", "size": 9902, "crc": -1603627180}, {"key": "androidx/compose/runtime/tooling/ComposeStackTraceFrame.class", "name": "androidx/compose/runtime/tooling/ComposeStackTraceFrame.class", "size": 3336, "crc": -565949829}, {"key": "androidx/compose/runtime/tooling/ComposeStackTraceKt.class", "name": "androidx/compose/runtime/tooling/ComposeStackTraceKt.class", "size": 8246, "crc": 1823690405}, {"key": "androidx/compose/runtime/tooling/ComposeToolingApi.class", "name": "androidx/compose/runtime/tooling/ComposeToolingApi.class", "size": 787, "crc": -1012353657}, {"key": "androidx/compose/runtime/tooling/CompositionData.class", "name": "androidx/compose/runtime/tooling/CompositionData.class", "size": 1203, "crc": -19709618}, {"key": "androidx/compose/runtime/tooling/CompositionDataKt.class", "name": "androidx/compose/runtime/tooling/CompositionDataKt.class", "size": 994, "crc": 332554751}, {"key": "androidx/compose/runtime/tooling/CompositionErrorContext.class", "name": "androidx/compose/runtime/tooling/CompositionErrorContext.class", "size": 746, "crc": 68983627}, {"key": "androidx/compose/runtime/tooling/CompositionErrorContextImpl$Key.class", "name": "androidx/compose/runtime/tooling/CompositionErrorContextImpl$Key.class", "size": 1463, "crc": 1311213808}, {"key": "androidx/compose/runtime/tooling/CompositionErrorContextImpl.class", "name": "androidx/compose/runtime/tooling/CompositionErrorContextImpl.class", "size": 5750, "crc": -1431621157}, {"key": "androidx/compose/runtime/tooling/CompositionErrorContextKt.class", "name": "androidx/compose/runtime/tooling/CompositionErrorContextKt.class", "size": 1865, "crc": -695998294}, {"key": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "name": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "size": 1556, "crc": -77580754}, {"key": "androidx/compose/runtime/tooling/CompositionGroup.class", "name": "androidx/compose/runtime/tooling/CompositionGroup.class", "size": 2399, "crc": 1850677469}, {"key": "androidx/compose/runtime/tooling/CompositionInstance.class", "name": "androidx/compose/runtime/tooling/CompositionInstance.class", "size": 1019, "crc": 1980886721}, {"key": "androidx/compose/runtime/tooling/CompositionObserver.class", "name": "androidx/compose/runtime/tooling/CompositionObserver.class", "size": 1433, "crc": -357378632}, {"key": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "name": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "size": 580, "crc": -1008459363}, {"key": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "name": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "size": 2364, "crc": 875840921}, {"key": "androidx/compose/runtime/tooling/CompositionRegistrationObserver.class", "name": "androidx/compose/runtime/tooling/CompositionRegistrationObserver.class", "size": 919, "crc": -1821744730}, {"key": "androidx/compose/runtime/tooling/DiagnosticComposeException.class", "name": "androidx/compose/runtime/tooling/DiagnosticComposeException.class", "size": 2519, "crc": 1159037214}, {"key": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "name": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "size": 1803, "crc": -1516299026}, {"key": "androidx/compose/runtime/tooling/LocationSourceInformation.class", "name": "androidx/compose/runtime/tooling/LocationSourceInformation.class", "size": 1475, "crc": 1577387019}, {"key": "androidx/compose/runtime/tooling/ObjectLocation.class", "name": "androidx/compose/runtime/tooling/ObjectLocation.class", "size": 2948, "crc": -1811941927}, {"key": "androidx/compose/runtime/tooling/ObservableComposition.class", "name": "androidx/compose/runtime/tooling/ObservableComposition.class", "size": 943, "crc": -287551941}, {"key": "androidx/compose/runtime/tooling/ParameterSourceInformation.class", "name": "androidx/compose/runtime/tooling/ParameterSourceInformation.class", "size": 1774, "crc": -284978733}, {"key": "androidx/compose/runtime/tooling/ParseException.class", "name": "androidx/compose/runtime/tooling/ParseException.class", "size": 979, "crc": 1425177404}, {"key": "androidx/compose/runtime/tooling/ReaderTraceBuilder.class", "name": "androidx/compose/runtime/tooling/ReaderTraceBuilder.class", "size": 1973, "crc": 596404841}, {"key": "androidx/compose/runtime/tooling/SourceInfoParserState.class", "name": "androidx/compose/runtime/tooling/SourceInfoParserState.class", "size": 4168, "crc": 1959561532}, {"key": "androidx/compose/runtime/tooling/SourceInformation.class", "name": "androidx/compose/runtime/tooling/SourceInformation.class", "size": 3401, "crc": -977253250}, {"key": "androidx/compose/runtime/tooling/SourceInformationKt.class", "name": "androidx/compose/runtime/tooling/SourceInformationKt.class", "size": 9649, "crc": -1978967722}, {"key": "androidx/compose/runtime/tooling/WriterTraceBuilder.class", "name": "androidx/compose/runtime/tooling/WriterTraceBuilder.class", "size": 1884, "crc": 872814422}, {"key": "META-INF/androidx.compose.runtime_runtime.version", "name": "META-INF/androidx.compose.runtime_runtime.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/runtime.kotlin_module", "name": "META-INF/runtime.kotlin_module", "size": 2884, "crc": 302452654}]