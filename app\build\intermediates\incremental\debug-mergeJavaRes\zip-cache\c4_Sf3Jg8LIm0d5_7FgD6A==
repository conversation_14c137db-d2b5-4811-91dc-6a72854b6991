[{"key": "android/support/v4/app/INotificationSideChannel$Default.class", "name": "android/support/v4/app/INotificationSideChannel$Default.class", "size": 1135, "crc": -395248935}, {"key": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "name": "android/support/v4/app/INotificationSideChannel$Stub$Proxy.class", "size": 2463, "crc": 1439477579}, {"key": "android/support/v4/app/INotificationSideChannel$Stub.class", "name": "android/support/v4/app/INotificationSideChannel$Stub.class", "size": 2811, "crc": 1365534975}, {"key": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "name": "android/support/v4/app/INotificationSideChannel$_Parcel.class", "size": 1753, "crc": 153920445}, {"key": "android/support/v4/app/INotificationSideChannel.class", "name": "android/support/v4/app/INotificationSideChannel.class", "size": 965, "crc": 265470547}, {"key": "android/support/v4/app/RemoteActionCompatParcelizer.class", "name": "android/support/v4/app/RemoteActionCompatParcelizer.class", "size": 1104, "crc": -224852417}, {"key": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "name": "android/support/v4/graphics/drawable/IconCompatParcelizer.class", "size": 1132, "crc": 2146182622}, {"key": "android/support/v4/os/IResultReceiver$Default.class", "name": "android/support/v4/os/IResultReceiver$Default.class", "size": 727, "crc": -2113054516}, {"key": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver$Stub$Proxy.class", "size": 1698, "crc": -1321240844}, {"key": "android/support/v4/os/IResultReceiver$Stub.class", "name": "android/support/v4/os/IResultReceiver$Stub.class", "size": 2276, "crc": -1806608294}, {"key": "android/support/v4/os/IResultReceiver$_Parcel.class", "name": "android/support/v4/os/IResultReceiver$_Parcel.class", "size": 1714, "crc": -160417332}, {"key": "android/support/v4/os/IResultReceiver.class", "name": "android/support/v4/os/IResultReceiver.class", "size": 737, "crc": -1723538072}, {"key": "android/support/v4/os/IResultReceiver2$Default.class", "name": "android/support/v4/os/IResultReceiver2$Default.class", "size": 731, "crc": -1519151952}, {"key": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "name": "android/support/v4/os/IResultReceiver2$Stub$Proxy.class", "size": 1704, "crc": 903012677}, {"key": "android/support/v4/os/IResultReceiver2$Stub.class", "name": "android/support/v4/os/IResultReceiver2$Stub.class", "size": 2283, "crc": 1673453137}, {"key": "android/support/v4/os/IResultReceiver2$_Parcel.class", "name": "android/support/v4/os/IResultReceiver2$_Parcel.class", "size": 1718, "crc": 1899535325}, {"key": "android/support/v4/os/IResultReceiver2.class", "name": "android/support/v4/os/IResultReceiver2.class", "size": 743, "crc": -129673136}, {"key": "android/support/v4/os/ResultReceiver$1.class", "name": "android/support/v4/os/ResultReceiver$1.class", "size": 1177, "crc": 1977962591}, {"key": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "name": "android/support/v4/os/ResultReceiver$MyResultReceiver.class", "size": 1181, "crc": -1104139338}, {"key": "android/support/v4/os/ResultReceiver$MyRunnable.class", "name": "android/support/v4/os/ResultReceiver$MyRunnable.class", "size": 886, "crc": -2062869641}, {"key": "android/support/v4/os/ResultReceiver.class", "name": "android/support/v4/os/ResultReceiver.class", "size": 2978, "crc": -1047097527}, {"key": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "name": "androidx/core/accessibilityservice/AccessibilityServiceInfoCompat.class", "size": 3351, "crc": 74942830}, {"key": "androidx/core/app/ActivityCompat$1.class", "name": "androidx/core/app/ActivityCompat$1.class", "size": 1622, "crc": 435813033}, {"key": "androidx/core/app/ActivityCompat$Api16Impl.class", "name": "androidx/core/app/ActivityCompat$Api16Impl.class", "size": 1700, "crc": 125239726}, {"key": "androidx/core/app/ActivityCompat$Api21Impl.class", "name": "androidx/core/app/ActivityCompat$Api21Impl.class", "size": 1385, "crc": 1856756998}, {"key": "androidx/core/app/ActivityCompat$Api22Impl.class", "name": "androidx/core/app/ActivityCompat$Api22Impl.class", "size": 765, "crc": 1357745067}, {"key": "androidx/core/app/ActivityCompat$Api23Impl.class", "name": "androidx/core/app/ActivityCompat$Api23Impl.class", "size": 1442, "crc": 1767273775}, {"key": "androidx/core/app/ActivityCompat$Api28Impl.class", "name": "androidx/core/app/ActivityCompat$Api28Impl.class", "size": 867, "crc": 1813693761}, {"key": "androidx/core/app/ActivityCompat$Api30Impl.class", "name": "androidx/core/app/ActivityCompat$Api30Impl.class", "size": 1549, "crc": -1256787216}, {"key": "androidx/core/app/ActivityCompat$Api31Impl.class", "name": "androidx/core/app/ActivityCompat$Api31Impl.class", "size": 2075, "crc": -177732954}, {"key": "androidx/core/app/ActivityCompat$Api32Impl.class", "name": "androidx/core/app/ActivityCompat$Api32Impl.class", "size": 839, "crc": -1661058035}, {"key": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "name": "androidx/core/app/ActivityCompat$OnRequestPermissionsResultCallback.class", "size": 425, "crc": 1567962088}, {"key": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "name": "androidx/core/app/ActivityCompat$PermissionCompatDelegate.class", "size": 621, "crc": -671588751}, {"key": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "name": "androidx/core/app/ActivityCompat$RequestPermissionsRequestCodeValidator.class", "size": 584, "crc": 780710089}, {"key": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "name": "androidx/core/app/ActivityCompat$SharedElementCallback21Impl.class", "size": 4428, "crc": 977046335}, {"key": "androidx/core/app/ActivityCompat.class", "name": "androidx/core/app/ActivityCompat.class", "size": 11122, "crc": 50223178}, {"key": "androidx/core/app/ActivityManagerCompat.class", "name": "androidx/core/app/ActivityManagerCompat.class", "size": 780, "crc": -1698937269}, {"key": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "name": "androidx/core/app/ActivityOptionsCompat$ActivityOptionsCompatImpl.class", "size": 2348, "crc": -825917519}, {"key": "androidx/core/app/ActivityOptionsCompat$Api16Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api16Impl.class", "size": 1398, "crc": -1114803006}, {"key": "androidx/core/app/ActivityOptionsCompat$Api21Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api21Impl.class", "size": 1582, "crc": 346520904}, {"key": "androidx/core/app/ActivityOptionsCompat$Api23Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api23Impl.class", "size": 1301, "crc": -1755345782}, {"key": "androidx/core/app/ActivityOptionsCompat$Api24Impl.class", "name": "androidx/core/app/ActivityOptionsCompat$Api24Impl.class", "size": 1141, "crc": 1674674915}, {"key": "androidx/core/app/ActivityOptionsCompat.class", "name": "androidx/core/app/ActivityOptionsCompat.class", "size": 5395, "crc": 290241144}, {"key": "androidx/core/app/ActivityRecreator$1.class", "name": "androidx/core/app/ActivityRecreator$1.class", "size": 964, "crc": -338343361}, {"key": "androidx/core/app/ActivityRecreator$2.class", "name": "androidx/core/app/ActivityRecreator$2.class", "size": 1176, "crc": -568525450}, {"key": "androidx/core/app/ActivityRecreator$3.class", "name": "androidx/core/app/ActivityRecreator$3.class", "size": 1787, "crc": -879425643}, {"key": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "name": "androidx/core/app/ActivityRecreator$LifecycleCheckCallbacks.class", "size": 2055, "crc": -955498617}, {"key": "androidx/core/app/ActivityRecreator.class", "name": "androidx/core/app/ActivityRecreator.class", "size": 6249, "crc": 1191481447}, {"key": "androidx/core/app/AlarmManagerCompat$Api19Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api19Impl.class", "size": 933, "crc": 311163197}, {"key": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api21Impl.class", "size": 1309, "crc": -730771635}, {"key": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "name": "androidx/core/app/AlarmManagerCompat$Api23Impl.class", "size": 1094, "crc": -298738575}, {"key": "androidx/core/app/AlarmManagerCompat.class", "name": "androidx/core/app/AlarmManagerCompat.class", "size": 2252, "crc": 1627972445}, {"key": "androidx/core/app/AppComponentFactory.class", "name": "androidx/core/app/AppComponentFactory.class", "size": 4319, "crc": 941336973}, {"key": "androidx/core/app/AppLaunchChecker.class", "name": "androidx/core/app/AppLaunchChecker.class", "size": 2271, "crc": -405740589}, {"key": "androidx/core/app/AppOpsManagerCompat$Api19Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api19Impl.class", "size": 1057, "crc": -1170247109}, {"key": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api23Impl.class", "size": 1679, "crc": 1106233868}, {"key": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "name": "androidx/core/app/AppOpsManagerCompat$Api29Impl.class", "size": 1581, "crc": -168458857}, {"key": "androidx/core/app/AppOpsManagerCompat.class", "name": "androidx/core/app/AppOpsManagerCompat.class", "size": 3230, "crc": -1987166702}, {"key": "androidx/core/app/BundleCompat$Api18Impl.class", "name": "androidx/core/app/BundleCompat$Api18Impl.class", "size": 1075, "crc": -2147347784}, {"key": "androidx/core/app/BundleCompat$BeforeApi18Impl.class", "name": "androidx/core/app/BundleCompat$BeforeApi18Impl.class", "size": 2516, "crc": -922514571}, {"key": "androidx/core/app/BundleCompat.class", "name": "androidx/core/app/BundleCompat.class", "size": 1258, "crc": 201521805}, {"key": "androidx/core/app/ComponentActivity$ExtraData.class", "name": "androidx/core/app/ComponentActivity$ExtraData.class", "size": 753, "crc": -166733667}, {"key": "androidx/core/app/ComponentActivity.class", "name": "androidx/core/app/ComponentActivity.class", "size": 5371, "crc": 1153620703}, {"key": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "name": "androidx/core/app/CoreComponentFactory$CompatWrapped.class", "size": 541, "crc": -1240361783}, {"key": "androidx/core/app/CoreComponentFactory.class", "name": "androidx/core/app/CoreComponentFactory.class", "size": 2957, "crc": 2016705809}, {"key": "androidx/core/app/DialogCompat$Api28Impl.class", "name": "androidx/core/app/DialogCompat$Api28Impl.class", "size": 849, "crc": -1446145350}, {"key": "androidx/core/app/DialogCompat.class", "name": "androidx/core/app/DialogCompat.class", "size": 1185, "crc": 1507368120}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl$1.class", "size": 1859, "crc": 1034290187}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsApi24Impl.class", "size": 4121, "crc": -1956594132}, {"key": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "name": "androidx/core/app/FrameMetricsAggregator$FrameMetricsBaseImpl.class", "size": 981, "crc": -1640616801}, {"key": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "name": "androidx/core/app/FrameMetricsAggregator$MetricType.class", "size": 668, "crc": -1630363674}, {"key": "androidx/core/app/FrameMetricsAggregator.class", "name": "androidx/core/app/FrameMetricsAggregator.class", "size": 2570, "crc": -2032210404}, {"key": "androidx/core/app/JobIntentService$CommandProcessor.class", "name": "androidx/core/app/JobIntentService$CommandProcessor.class", "size": 1825, "crc": 44568175}, {"key": "androidx/core/app/JobIntentService$CompatJobEngine.class", "name": "androidx/core/app/JobIntentService$CompatJobEngine.class", "size": 448, "crc": 1210592104}, {"key": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$CompatWorkEnqueuer.class", "size": 2923, "crc": 373299418}, {"key": "androidx/core/app/JobIntentService$CompatWorkItem.class", "name": "androidx/core/app/JobIntentService$CompatWorkItem.class", "size": 1021, "crc": -287917668}, {"key": "androidx/core/app/JobIntentService$GenericWorkItem.class", "name": "androidx/core/app/JobIntentService$GenericWorkItem.class", "size": 310, "crc": 683365043}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl$WrapperWorkItem.class", "size": 1478, "crc": 1773720750}, {"key": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "name": "androidx/core/app/JobIntentService$JobServiceEngineImpl.class", "size": 2592, "crc": 871330440}, {"key": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$JobWorkEnqueuer.class", "size": 1852, "crc": -1663926146}, {"key": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "name": "androidx/core/app/JobIntentService$WorkEnqueuer.class", "size": 1443, "crc": -862902624}, {"key": "androidx/core/app/JobIntentService.class", "name": "androidx/core/app/JobIntentService.class", "size": 7130, "crc": -1781495434}, {"key": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api21Impl.class", "size": 771, "crc": 1719030997}, {"key": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api24Impl.class", "size": 1056, "crc": -910109210}, {"key": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "name": "androidx/core/app/LocaleManagerCompat$Api33Impl.class", "size": 898, "crc": 1060055725}, {"key": "androidx/core/app/LocaleManagerCompat.class", "name": "androidx/core/app/LocaleManagerCompat.class", "size": 2997, "crc": 41638773}, {"key": "androidx/core/app/MultiWindowModeChangedInfo.class", "name": "androidx/core/app/MultiWindowModeChangedInfo.class", "size": 1387, "crc": -1036727142}, {"key": "androidx/core/app/NavUtils$Api16Impl.class", "name": "androidx/core/app/NavUtils$Api16Impl.class", "size": 1128, "crc": -660975539}, {"key": "androidx/core/app/NavUtils.class", "name": "androidx/core/app/NavUtils.class", "size": 5971, "crc": 1952896136}, {"key": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "name": "androidx/core/app/NotificationBuilderWithBuilderAccessor.class", "size": 597, "crc": -1985100633}, {"key": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api26Impl.class", "size": 4350, "crc": 1487899892}, {"key": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api29Impl.class", "size": 815, "crc": 1721686020}, {"key": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "name": "androidx/core/app/NotificationChannelCompat$Api30Impl.class", "size": 1417, "crc": -1496769729}, {"key": "androidx/core/app/NotificationChannelCompat$Builder.class", "name": "androidx/core/app/NotificationChannelCompat$Builder.class", "size": 3517, "crc": -1458413883}, {"key": "androidx/core/app/NotificationChannelCompat.class", "name": "androidx/core/app/NotificationChannelCompat.class", "size": 6989, "crc": -268500644}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api26Impl.class", "size": 1934, "crc": -347056883}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Api28Impl.class", "size": 1275, "crc": 1629656438}, {"key": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "name": "androidx/core/app/NotificationChannelGroupCompat$Builder.class", "size": 1411, "crc": 237511248}, {"key": "androidx/core/app/NotificationChannelGroupCompat.class", "name": "androidx/core/app/NotificationChannelGroupCompat.class", "size": 4884, "crc": -381039034}, {"key": "androidx/core/app/NotificationCompat$1.class", "name": "androidx/core/app/NotificationCompat$1.class", "size": 238, "crc": -598238096}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api20Impl.class", "size": 1039, "crc": -77079683}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api23Impl.class", "size": 1043, "crc": -1509805650}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api24Impl.class", "size": 998, "crc": 1262297875}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api28Impl.class", "size": 991, "crc": -502291906}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api29Impl.class", "size": 986, "crc": 478325980}, {"key": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Action$Builder$Api31Impl.class", "size": 998, "crc": -644993207}, {"key": "androidx/core/app/NotificationCompat$Action$Builder.class", "name": "androidx/core/app/NotificationCompat$Action$Builder.class", "size": 9129, "crc": -969763996}, {"key": "androidx/core/app/NotificationCompat$Action$Extender.class", "name": "androidx/core/app/NotificationCompat$Action$Extender.class", "size": 630, "crc": 258657630}, {"key": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "name": "androidx/core/app/NotificationCompat$Action$SemanticAction.class", "size": 492, "crc": 271952193}, {"key": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$Action$WearableExtender.class", "size": 5051, "crc": 1295239122}, {"key": "androidx/core/app/NotificationCompat$Action.class", "name": "androidx/core/app/NotificationCompat$Action.class", "size": 5711, "crc": 420303621}, {"key": "androidx/core/app/NotificationCompat$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$Api20Impl.class", "size": 2205, "crc": -1228297377}, {"key": "androidx/core/app/NotificationCompat$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Api23Impl.class", "size": 881, "crc": -419758611}, {"key": "androidx/core/app/NotificationCompat$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Api24Impl.class", "size": 836, "crc": 310851516}, {"key": "androidx/core/app/NotificationCompat$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$Api26Impl.class", "size": 1466, "crc": -1360921205}, {"key": "androidx/core/app/NotificationCompat$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$Api28Impl.class", "size": 829, "crc": 2063714826}, {"key": "androidx/core/app/NotificationCompat$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$Api29Impl.class", "size": 1720, "crc": -140493669}, {"key": "androidx/core/app/NotificationCompat$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$Api31Impl.class", "size": 836, "crc": -499740368}, {"key": "androidx/core/app/NotificationCompat$BadgeIconType.class", "name": "androidx/core/app/NotificationCompat$BadgeIconType.class", "size": 662, "crc": -1600074890}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api16Impl.class", "size": 2238, "crc": -1762896022}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api23Impl.class", "size": 1111, "crc": -1160919905}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle$Api31Impl.class", "size": 1668, "crc": -801011316}, {"key": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "name": "androidx/core/app/NotificationCompat$BigPictureStyle.class", "size": 7557, "crc": -1018366952}, {"key": "androidx/core/app/NotificationCompat$BigTextStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$BigTextStyle$Api16Impl.class", "size": 1672, "crc": -630642086}, {"key": "androidx/core/app/NotificationCompat$BigTextStyle.class", "name": "androidx/core/app/NotificationCompat$BigTextStyle.class", "size": 3964, "crc": -1162687825}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api29Impl.class", "size": 3541, "crc": -335783879}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Api30Impl.class", "size": 3618, "crc": 558225169}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata$Builder.class", "size": 4784, "crc": 311663367}, {"key": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "name": "androidx/core/app/NotificationCompat$BubbleMetadata.class", "size": 4276, "crc": -1296475824}, {"key": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api21Impl.class", "size": 1599, "crc": 254227286}, {"key": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api23Impl.class", "size": 908, "crc": -1571262672}, {"key": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Builder$Api24Impl.class", "size": 1440, "crc": 639041215}, {"key": "androidx/core/app/NotificationCompat$Builder.class", "name": "androidx/core/app/NotificationCompat$Builder.class", "size": 28681, "crc": 1807462539}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api16Impl.class", "size": 1099, "crc": 774235159}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api20Impl.class", "size": 2485, "crc": 843138188}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api21Impl.class", "size": 1181, "crc": 94524295}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api23Impl.class", "size": 1610, "crc": -322444861}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api24Impl.class", "size": 1477, "crc": -2010685963}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api28Impl.class", "size": 1071, "crc": 420151717}, {"key": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$CallStyle$Api31Impl.class", "size": 3467, "crc": 1342155612}, {"key": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "name": "androidx/core/app/NotificationCompat$CallStyle$CallType.class", "size": 734, "crc": 2136238121}, {"key": "androidx/core/app/NotificationCompat$CallStyle.class", "name": "androidx/core/app/NotificationCompat$CallStyle.class", "size": 19115, "crc": 471906138}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api20Impl.class", "size": 3206, "crc": -534905853}, {"key": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "name": "androidx/core/app/NotificationCompat$CarExtender$Api29Impl.class", "size": 873, "crc": -225689525}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation$Builder.class", "size": 2896, "crc": 1460162153}, {"key": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "name": "androidx/core/app/NotificationCompat$CarExtender$UnreadConversation.class", "size": 2343, "crc": 28279771}, {"key": "androidx/core/app/NotificationCompat$CarExtender.class", "name": "androidx/core/app/NotificationCompat$CarExtender.class", "size": 9092, "crc": 1110713015}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api15Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api15Impl.class", "size": 1065, "crc": 1156182005}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api16Impl.class", "size": 1188, "crc": -695381862}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle$Api24Impl.class", "size": 949, "crc": 125046827}, {"key": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "name": "androidx/core/app/NotificationCompat$DecoratedCustomViewStyle.class", "size": 7163, "crc": -1022690316}, {"key": "androidx/core/app/NotificationCompat$Extender.class", "name": "androidx/core/app/NotificationCompat$Extender.class", "size": 536, "crc": -1922350891}, {"key": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "name": "androidx/core/app/NotificationCompat$GroupAlertBehavior.class", "size": 672, "crc": 900946520}, {"key": "androidx/core/app/NotificationCompat$InboxStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$InboxStyle$Api16Impl.class", "size": 1644, "crc": -237758050}, {"key": "androidx/core/app/NotificationCompat$InboxStyle.class", "name": "androidx/core/app/NotificationCompat$InboxStyle.class", "size": 4275, "crc": -971571964}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api16Impl.class", "size": 1858, "crc": -1798775261}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api24Impl.class", "size": 1782, "crc": -966638758}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api26Impl.class", "size": 1287, "crc": -566388610}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Api28Impl.class", "size": 1318, "crc": 588341778}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api24Impl.class", "size": 1723, "crc": -1704187614}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message$Api28Impl.class", "size": 1268, "crc": 155176491}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle$Message.class", "size": 7939, "crc": 1595437591}, {"key": "androidx/core/app/NotificationCompat$MessagingStyle.class", "name": "androidx/core/app/NotificationCompat$MessagingStyle.class", "size": 14323, "crc": 1558208076}, {"key": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "name": "androidx/core/app/NotificationCompat$NotificationVisibility.class", "size": 680, "crc": 1088614023}, {"key": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "name": "androidx/core/app/NotificationCompat$ServiceNotificationBehavior.class", "size": 690, "crc": 1857766983}, {"key": "androidx/core/app/NotificationCompat$StreamType.class", "name": "androidx/core/app/NotificationCompat$StreamType.class", "size": 656, "crc": -1750087649}, {"key": "androidx/core/app/NotificationCompat$Style$Api16Impl.class", "name": "androidx/core/app/NotificationCompat$Style$Api16Impl.class", "size": 1166, "crc": 1311882135}, {"key": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$Style$Api24Impl.class", "size": 915, "crc": -707557298}, {"key": "androidx/core/app/NotificationCompat$Style.class", "name": "androidx/core/app/NotificationCompat$Style.class", "size": 16219, "crc": -958622961}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api20Impl.class", "size": 2755, "crc": 792673074}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api23Impl.class", "size": 1278, "crc": -2041954650}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api24Impl.class", "size": 1157, "crc": -630982793}, {"key": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "name": "androidx/core/app/NotificationCompat$WearableExtender$Api31Impl.class", "size": 1159, "crc": -681044024}, {"key": "androidx/core/app/NotificationCompat$WearableExtender.class", "name": "androidx/core/app/NotificationCompat$WearableExtender.class", "size": 18093, "crc": -1678688577}, {"key": "androidx/core/app/NotificationCompat.class", "name": "androidx/core/app/NotificationCompat.class", "size": 23240, "crc": -985717206}, {"key": "androidx/core/app/NotificationCompatBuilder$Api16Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api16Impl.class", "size": 1670, "crc": -1401559043}, {"key": "androidx/core/app/NotificationCompatBuilder$Api17Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api17Impl.class", "size": 950, "crc": 46956775}, {"key": "androidx/core/app/NotificationCompatBuilder$Api19Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api19Impl.class", "size": 1000, "crc": 970432418}, {"key": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api20Impl.class", "size": 3391, "crc": 1547689623}, {"key": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api21Impl.class", "size": 2192, "crc": 1919931296}, {"key": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api23Impl.class", "size": 1610, "crc": -80168167}, {"key": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api24Impl.class", "size": 2037, "crc": -1922565654}, {"key": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api26Impl.class", "size": 2464, "crc": -447645605}, {"key": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api28Impl.class", "size": 1428, "crc": -751508512}, {"key": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api29Impl.class", "size": 2143, "crc": 1668337320}, {"key": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "name": "androidx/core/app/NotificationCompatBuilder$Api31Impl.class", "size": 1408, "crc": -617873940}, {"key": "androidx/core/app/NotificationCompatBuilder.class", "name": "androidx/core/app/NotificationCompatBuilder.class", "size": 20377, "crc": 810956376}, {"key": "androidx/core/app/NotificationCompatExtras.class", "name": "androidx/core/app/NotificationCompatExtras.class", "size": 780, "crc": 1230724101}, {"key": "androidx/core/app/NotificationCompatJellybean.class", "name": "androidx/core/app/NotificationCompatJellybean.class", "size": 12882, "crc": 80573459}, {"key": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "name": "androidx/core/app/NotificationCompatSideChannelService$NotificationSideChannelStub.class", "size": 2066, "crc": 1177375189}, {"key": "androidx/core/app/NotificationCompatSideChannelService.class", "name": "androidx/core/app/NotificationCompatSideChannelService.class", "size": 2134, "crc": -1284554455}, {"key": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api24Impl.class", "size": 973, "crc": 1070270587}, {"key": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api26Impl.class", "size": 3604, "crc": 113650614}, {"key": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api28Impl.class", "size": 992, "crc": -1410085124}, {"key": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "name": "androidx/core/app/NotificationManagerCompat$Api30Impl.class", "size": 1314, "crc": 862698635}, {"key": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "name": "androidx/core/app/NotificationManagerCompat$CancelTask.class", "size": 1902, "crc": 1556800150}, {"key": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "name": "androidx/core/app/NotificationManagerCompat$NotifyTask.class", "size": 1712, "crc": -764239571}, {"key": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "name": "androidx/core/app/NotificationManagerCompat$ServiceConnectedEvent.class", "size": 702, "crc": 2071682410}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager$ListenerRecord.class", "size": 1146, "crc": 1324237485}, {"key": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "name": "androidx/core/app/NotificationManagerCompat$SideChannelManager.class", "size": 10593, "crc": 64689791}, {"key": "androidx/core/app/NotificationManagerCompat$Task.class", "name": "androidx/core/app/NotificationManagerCompat$Task.class", "size": 366, "crc": **********}, {"key": "androidx/core/app/NotificationManagerCompat.class", "name": "androidx/core/app/NotificationManagerCompat.class", "size": 16539, "crc": **********}, {"key": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "name": "androidx/core/app/OnMultiWindowModeChangedProvider.class", "size": 501, "crc": **********}, {"key": "androidx/core/app/OnNewIntentProvider.class", "name": "androidx/core/app/OnNewIntentProvider.class", "size": 427, "crc": 152539972}, {"key": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "name": "androidx/core/app/OnPictureInPictureModeChangedProvider.class", "size": 526, "crc": **********}, {"key": "androidx/core/app/PendingIntentCompat$Api16Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api16Impl.class", "size": 1447, "crc": **********}, {"key": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "name": "androidx/core/app/PendingIntentCompat$Api26Impl.class", "size": 909, "crc": -825858638}, {"key": "androidx/core/app/PendingIntentCompat$Flags.class", "name": "androidx/core/app/PendingIntentCompat$Flags.class", "size": 404, "crc": -**********}, {"key": "androidx/core/app/PendingIntentCompat.class", "name": "androidx/core/app/PendingIntentCompat.class", "size": 3367, "crc": -**********}, {"key": "androidx/core/app/Person$Api22Impl.class", "name": "androidx/core/app/Person$Api22Impl.class", "size": 2091, "crc": -**********}, {"key": "androidx/core/app/Person$Api28Impl.class", "name": "androidx/core/app/Person$Api28Impl.class", "size": 2483, "crc": **********}, {"key": "androidx/core/app/Person$Builder.class", "name": "androidx/core/app/Person$Builder.class", "size": 2157, "crc": -**********}, {"key": "androidx/core/app/Person.class", "name": "androidx/core/app/Person.class", "size": 5386, "crc": -**********}, {"key": "androidx/core/app/PictureInPictureModeChangedInfo.class", "name": "androidx/core/app/PictureInPictureModeChangedInfo.class", "size": 1417, "crc": **********}, {"key": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api26Impl.class", "size": 2126, "crc": -970786781}, {"key": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "name": "androidx/core/app/RemoteActionCompat$Api28Impl.class", "size": 930, "crc": **********}, {"key": "androidx/core/app/RemoteActionCompat.class", "name": "androidx/core/app/RemoteActionCompat.class", "size": 4541, "crc": -**********}, {"key": "androidx/core/app/RemoteActionCompatParcelizer.class", "name": "androidx/core/app/RemoteActionCompatParcelizer.class", "size": 2304, "crc": 303528497}, {"key": "androidx/core/app/RemoteInput$Api16Impl.class", "name": "androidx/core/app/RemoteInput$Api16Impl.class", "size": 1011, "crc": -419286300}, {"key": "androidx/core/app/RemoteInput$Api20Impl.class", "name": "androidx/core/app/RemoteInput$Api20Impl.class", "size": 4260, "crc": **********}, {"key": "androidx/core/app/RemoteInput$Api26Impl.class", "name": "androidx/core/app/RemoteInput$Api26Impl.class", "size": 2250, "crc": 1803895548}, {"key": "androidx/core/app/RemoteInput$Api28Impl.class", "name": "androidx/core/app/RemoteInput$Api28Impl.class", "size": 886, "crc": -1914401348}, {"key": "androidx/core/app/RemoteInput$Api29Impl.class", "name": "androidx/core/app/RemoteInput$Api29Impl.class", "size": 1105, "crc": 1831262225}, {"key": "androidx/core/app/RemoteInput$Builder.class", "name": "androidx/core/app/RemoteInput$Builder.class", "size": 3002, "crc": -369547510}, {"key": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "name": "androidx/core/app/RemoteInput$EditChoicesBeforeSending.class", "size": 663, "crc": 137218255}, {"key": "androidx/core/app/RemoteInput$Source.class", "name": "androidx/core/app/RemoteInput$Source.class", "size": 627, "crc": 1225634699}, {"key": "androidx/core/app/RemoteInput.class", "name": "androidx/core/app/RemoteInput.class", "size": 10936, "crc": 1790539750}, {"key": "androidx/core/app/ServiceCompat$Api24Impl.class", "name": "androidx/core/app/ServiceCompat$Api24Impl.class", "size": 757, "crc": 1445214510}, {"key": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "name": "androidx/core/app/ServiceCompat$StopForegroundFlags.class", "size": 659, "crc": 655432038}, {"key": "androidx/core/app/ServiceCompat.class", "name": "androidx/core/app/ServiceCompat.class", "size": 1094, "crc": -1301059058}, {"key": "androidx/core/app/ShareCompat$Api16Impl.class", "name": "androidx/core/app/ShareCompat$Api16Impl.class", "size": 2751, "crc": -976233903}, {"key": "androidx/core/app/ShareCompat$IntentBuilder.class", "name": "androidx/core/app/ShareCompat$IntentBuilder.class", "size": 8775, "crc": -591765183}, {"key": "androidx/core/app/ShareCompat$IntentReader.class", "name": "androidx/core/app/ShareCompat$IntentReader.class", "size": 7893, "crc": -407420263}, {"key": "androidx/core/app/ShareCompat.class", "name": "androidx/core/app/ShareCompat.class", "size": 4677, "crc": 1402677223}, {"key": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "name": "androidx/core/app/SharedElementCallback$OnSharedElementsReadyListener.class", "size": 317, "crc": 1217450772}, {"key": "androidx/core/app/SharedElementCallback.class", "name": "androidx/core/app/SharedElementCallback.class", "size": 7188, "crc": 376381531}, {"key": "androidx/core/app/TaskStackBuilder$Api16Impl.class", "name": "androidx/core/app/TaskStackBuilder$Api16Impl.class", "size": 960, "crc": 13500967}, {"key": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "name": "androidx/core/app/TaskStackBuilder$SupportParentable.class", "size": 385, "crc": 2113967134}, {"key": "androidx/core/app/TaskStackBuilder.class", "name": "androidx/core/app/TaskStackBuilder.class", "size": 6844, "crc": -334827036}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Default.class", "size": 868, "crc": -1059759517}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub$Proxy.class", "size": 1775, "crc": -447033825}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback$Stub.class", "size": 2247, "crc": -1373048578}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportCallback.class", "size": 876, "crc": 987886314}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Default.class", "size": 992, "crc": 358761936}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub$Proxy.class", "size": 1945, "crc": **********}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService$Stub.class", "size": 2625, "crc": **********}, {"key": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "name": "androidx/core/app/unusedapprestrictions/IUnusedAppRestrictionsBackportService.class", "size": 941, "crc": -**********}, {"key": "androidx/core/content/ContentProviderCompat.class", "name": "androidx/core/content/ContentProviderCompat.class", "size": 986, "crc": -917373858}, {"key": "androidx/core/content/ContentResolverCompat$Api16Impl.class", "name": "androidx/core/content/ContentResolverCompat$Api16Impl.class", "size": 1345, "crc": 721940027}, {"key": "androidx/core/content/ContentResolverCompat.class", "name": "androidx/core/content/ContentResolverCompat.class", "size": 2209, "crc": 855935904}, {"key": "androidx/core/content/ContextCompat$Api16Impl.class", "name": "androidx/core/content/ContextCompat$Api16Impl.class", "size": 1217, "crc": -**********}, {"key": "androidx/core/content/ContextCompat$Api19Impl.class", "name": "androidx/core/content/ContextCompat$Api19Impl.class", "size": 1124, "crc": -**********}, {"key": "androidx/core/content/ContextCompat$Api21Impl.class", "name": "androidx/core/content/ContextCompat$Api21Impl.class", "size": 1106, "crc": -**********}, {"key": "androidx/core/content/ContextCompat$Api23Impl.class", "name": "androidx/core/content/ContextCompat$Api23Impl.class", "size": 1521, "crc": 577162085}, {"key": "androidx/core/content/ContextCompat$Api24Impl.class", "name": "androidx/core/content/ContextCompat$Api24Impl.class", "size": 1123, "crc": 645951929}, {"key": "androidx/core/content/ContextCompat$Api26Impl.class", "name": "androidx/core/content/ContextCompat$Api26Impl.class", "size": 1971, "crc": -2003451148}, {"key": "androidx/core/content/ContextCompat$Api28Impl.class", "name": "androidx/core/content/ContextCompat$Api28Impl.class", "size": 809, "crc": 373428011}, {"key": "androidx/core/content/ContextCompat$Api30Impl.class", "name": "androidx/core/content/ContextCompat$Api30Impl.class", "size": 785, "crc": -1901387401}, {"key": "androidx/core/content/ContextCompat$Api33Impl.class", "name": "androidx/core/content/ContextCompat$Api33Impl.class", "size": 1337, "crc": -888161442}, {"key": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "name": "androidx/core/content/ContextCompat$LegacyServiceMapHolder.class", "size": 4410, "crc": 721484450}, {"key": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "name": "androidx/core/content/ContextCompat$RegisterReceiverFlags.class", "size": 658, "crc": 794825751}, {"key": "androidx/core/content/ContextCompat.class", "name": "androidx/core/content/ContextCompat.class", "size": 12273, "crc": -**********}, {"key": "androidx/core/content/FileProvider$Api21Impl.class", "name": "androidx/core/content/FileProvider$Api21Impl.class", "size": 782, "crc": **********}, {"key": "androidx/core/content/FileProvider$PathStrategy.class", "name": "androidx/core/content/FileProvider$PathStrategy.class", "size": 346, "crc": 770878075}, {"key": "androidx/core/content/FileProvider$SimplePathStrategy.class", "name": "androidx/core/content/FileProvider$SimplePathStrategy.class", "size": 4336, "crc": 865097666}, {"key": "androidx/core/content/FileProvider.class", "name": "androidx/core/content/FileProvider.class", "size": 12350, "crc": -**********}, {"key": "androidx/core/content/IntentCompat$Api15Impl.class", "name": "androidx/core/content/IntentCompat$Api15Impl.class", "size": 815, "crc": 151801962}, {"key": "androidx/core/content/IntentCompat$Api33Impl.class", "name": "androidx/core/content/IntentCompat$Api33Impl.class", "size": 2132, "crc": -630038860}, {"key": "androidx/core/content/IntentCompat.class", "name": "androidx/core/content/IntentCompat.class", "size": 5402, "crc": 880914034}, {"key": "androidx/core/content/IntentSanitizer$1.class", "name": "androidx/core/content/IntentSanitizer$1.class", "size": 237, "crc": -61702665}, {"key": "androidx/core/content/IntentSanitizer$Api15Impl.class", "name": "androidx/core/content/IntentSanitizer$Api15Impl.class", "size": 1006, "crc": **********}, {"key": "androidx/core/content/IntentSanitizer$Api16Impl$Api31Impl.class", "name": "androidx/core/content/IntentSanitizer$Api16Impl$Api31Impl.class", "size": 1834, "crc": **********}, {"key": "androidx/core/content/IntentSanitizer$Api16Impl.class", "name": "androidx/core/content/IntentSanitizer$Api16Impl.class", "size": 4375, "crc": -170329507}, {"key": "androidx/core/content/IntentSanitizer$Api29Impl.class", "name": "androidx/core/content/IntentSanitizer$Api29Impl.class", "size": 1051, "crc": 1195648172}, {"key": "androidx/core/content/IntentSanitizer$Builder.class", "name": "androidx/core/content/IntentSanitizer$Builder.class", "size": 13723, "crc": -959352565}, {"key": "androidx/core/content/IntentSanitizer.class", "name": "androidx/core/content/IntentSanitizer.class", "size": 10774, "crc": **********}, {"key": "androidx/core/content/LocusIdCompat$Api29Impl.class", "name": "androidx/core/content/LocusIdCompat$Api29Impl.class", "size": 1024, "crc": 89574739}, {"key": "androidx/core/content/LocusIdCompat.class", "name": "androidx/core/content/LocusIdCompat.class", "size": 2782, "crc": 412886872}, {"key": "androidx/core/content/MimeTypeFilter.class", "name": "androidx/core/content/MimeTypeFilter.class", "size": 2742, "crc": 147689054}, {"key": "androidx/core/content/OnConfigurationChangedProvider.class", "name": "androidx/core/content/OnConfigurationChangedProvider.class", "size": 486, "crc": -327760253}, {"key": "androidx/core/content/OnTrimMemoryProvider.class", "name": "androidx/core/content/OnTrimMemoryProvider.class", "size": 430, "crc": -53127615}, {"key": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "name": "androidx/core/content/PackageManagerCompat$Api30Impl.class", "size": 1002, "crc": -**********}, {"key": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "name": "androidx/core/content/PackageManagerCompat$UnusedAppRestrictionsStatus.class", "size": 691, "crc": **********}, {"key": "androidx/core/content/PackageManagerCompat.class", "name": "androidx/core/content/PackageManagerCompat.class", "size": 6194, "crc": 851152866}, {"key": "androidx/core/content/PermissionChecker$PermissionResult.class", "name": "androidx/core/content/PermissionChecker$PermissionResult.class", "size": 673, "crc": -666488629}, {"key": "androidx/core/content/PermissionChecker.class", "name": "androidx/core/content/PermissionChecker.class", "size": 2862, "crc": -**********}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat$Helper.class", "size": 1116, "crc": 587673952}, {"key": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "name": "androidx/core/content/SharedPreferencesCompat$EditorCompat.class", "size": 1381, "crc": -494629376}, {"key": "androidx/core/content/SharedPreferencesCompat.class", "name": "androidx/core/content/SharedPreferencesCompat.class", "size": 532, "crc": 1567352910}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportCallback.class", "size": 1290, "crc": 1147547714}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService$1.class", "size": 1604, "crc": 843169627}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportService.class", "size": 1591, "crc": -668859029}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection$1.class", "size": 1862, "crc": 1397470612}, {"key": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "name": "androidx/core/content/UnusedAppRestrictionsBackportServiceConnection.class", "size": 4184, "crc": -1517503401}, {"key": "androidx/core/content/UnusedAppRestrictionsConstants.class", "name": "androidx/core/content/UnusedAppRestrictionsConstants.class", "size": 588, "crc": -1244214169}, {"key": "androidx/core/content/UriMatcherCompat.class", "name": "androidx/core/content/UriMatcherCompat.class", "size": 1524, "crc": 1164334199}, {"key": "androidx/core/content/pm/ActivityInfoCompat.class", "name": "androidx/core/content/pm/ActivityInfoCompat.class", "size": 493, "crc": -1364477614}, {"key": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PackageInfoCompat$Api28Impl.class", "size": 1924, "crc": 1118492697}, {"key": "androidx/core/content/pm/PackageInfoCompat.class", "name": "androidx/core/content/pm/PackageInfoCompat.class", "size": 6094, "crc": -587297962}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Api28Impl.class", "size": 929, "crc": 842667549}, {"key": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "name": "androidx/core/content/pm/PermissionInfoCompat$Protection.class", "size": 663, "crc": 1629059509}, {"key": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "name": "androidx/core/content/pm/PermissionInfoCompat$ProtectionFlags.class", "size": 739, "crc": 1522065108}, {"key": "androidx/core/content/pm/PermissionInfoCompat.class", "name": "androidx/core/content/pm/PermissionInfoCompat.class", "size": 1364, "crc": -671737600}, {"key": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "name": "androidx/core/content/pm/ShortcutInfoChangeListener.class", "size": 1703, "crc": 1987550408}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Api33Impl.class", "size": 1000, "crc": 982246646}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Builder.class", "size": 12488, "crc": 990538647}, {"key": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "name": "androidx/core/content/pm/ShortcutInfoCompat$Surface.class", "size": 664, "crc": 282044856}, {"key": "androidx/core/content/pm/ShortcutInfoCompat.class", "name": "androidx/core/content/pm/ShortcutInfoCompat.class", "size": 12893, "crc": -669081215}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver$NoopImpl.class", "size": 1718, "crc": -796503050}, {"key": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "name": "androidx/core/content/pm/ShortcutInfoCompatSaver.class", "size": 1544, "crc": -1733909811}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$1.class", "size": 1316, "crc": 2121141673}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$Api25Impl.class", "size": 1498, "crc": 334083253}, {"key": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "name": "androidx/core/content/pm/ShortcutManagerCompat$ShortcutMatchFlags.class", "size": 695, "crc": 856990973}, {"key": "androidx/core/content/pm/ShortcutManagerCompat.class", "name": "androidx/core/content/pm/ShortcutManagerCompat.class", "size": 20131, "crc": -2038018954}, {"key": "androidx/core/content/pm/ShortcutXmlParser.class", "name": "androidx/core/content/pm/ShortcutXmlParser.class", "size": 5957, "crc": -1345405448}, {"key": "androidx/core/content/res/CamColor.class", "name": "androidx/core/content/res/CamColor.class", "size": 9512, "crc": -2032349472}, {"key": "androidx/core/content/res/CamUtils.class", "name": "androidx/core/content/res/CamUtils.class", "size": 3169, "crc": 1632992777}, {"key": "androidx/core/content/res/ColorStateListInflaterCompat.class", "name": "androidx/core/content/res/ColorStateListInflaterCompat.class", "size": 8167, "crc": 659487199}, {"key": "androidx/core/content/res/ComplexColorCompat.class", "name": "androidx/core/content/res/ComplexColorCompat.class", "size": 5222, "crc": 186755863}, {"key": "androidx/core/content/res/ConfigurationHelper.class", "name": "androidx/core/content/res/ConfigurationHelper.class", "size": 1019, "crc": 1994258360}, {"key": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "name": "androidx/core/content/res/FontResourcesParserCompat$Api21Impl.class", "size": 854, "crc": 232163820}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FamilyResourceEntry.class", "size": 287, "crc": -39793732}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FetchStrategy.class", "size": 454, "crc": 1823731370}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFamilyFilesResourceEntry.class", "size": 1265, "crc": **********}, {"key": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$FontFileResourceEntry.class", "size": 1652, "crc": -**********}, {"key": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "name": "androidx/core/content/res/FontResourcesParserCompat$ProviderResourceEntry.class", "size": 1996, "crc": -**********}, {"key": "androidx/core/content/res/FontResourcesParserCompat.class", "name": "androidx/core/content/res/FontResourcesParserCompat.class", "size": 8708, "crc": -384250054}, {"key": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "name": "androidx/core/content/res/GradientColorInflaterCompat$ColorStops.class", "size": 1735, "crc": -895449601}, {"key": "androidx/core/content/res/GradientColorInflaterCompat.class", "name": "androidx/core/content/res/GradientColorInflaterCompat.class", "size": 7737, "crc": **********}, {"key": "androidx/core/content/res/GrowingArrayUtils.class", "name": "androidx/core/content/res/GrowingArrayUtils.class", "size": 2719, "crc": **********}, {"key": "androidx/core/content/res/ResourcesCompat$Api15Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api15Impl.class", "size": 914, "crc": **********}, {"key": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api21Impl.class", "size": 1412, "crc": 64173212}, {"key": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api23Impl.class", "size": 1484, "crc": -1795966558}, {"key": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$Api29Impl.class", "size": 925, "crc": -1470770964}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheEntry.class", "size": 1305, "crc": -1522123679}, {"key": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "name": "androidx/core/content/res/ResourcesCompat$ColorStateListCacheKey.class", "size": 1493, "crc": 2049931843}, {"key": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "name": "androidx/core/content/res/ResourcesCompat$FontCallback.class", "size": 2653, "crc": -1704908219}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api23Impl.class", "size": 2299, "crc": -239257091}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat$Api29Impl.class", "size": 1036, "crc": -604755197}, {"key": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "name": "androidx/core/content/res/ResourcesCompat$ThemeCompat.class", "size": 1131, "crc": 344166688}, {"key": "androidx/core/content/res/ResourcesCompat.class", "name": "androidx/core/content/res/ResourcesCompat.class", "size": 14425, "crc": 1883535964}, {"key": "androidx/core/content/res/TypedArrayUtils.class", "name": "androidx/core/content/res/TypedArrayUtils.class", "size": 8994, "crc": 234992000}, {"key": "androidx/core/content/res/ViewingConditions.class", "name": "androidx/core/content/res/ViewingConditions.class", "size": 3538, "crc": -131663576}, {"key": "androidx/core/database/CursorWindowCompat$Api15Impl.class", "name": "androidx/core/database/CursorWindowCompat$Api15Impl.class", "size": 820, "crc": -363931225}, {"key": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "name": "androidx/core/database/CursorWindowCompat$Api28Impl.class", "size": 855, "crc": 1161348905}, {"key": "androidx/core/database/CursorWindowCompat.class", "name": "androidx/core/database/CursorWindowCompat.class", "size": 1166, "crc": -701861813}, {"key": "androidx/core/database/DatabaseUtilsCompat.class", "name": "androidx/core/database/DatabaseUtilsCompat.class", "size": 1371, "crc": -2044651927}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat$Api28Impl.class", "size": 889, "crc": -948675496}, {"key": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "name": "androidx/core/database/sqlite/SQLiteCursorCompat.class", "size": 903, "crc": 1059320687}, {"key": "androidx/core/graphics/BitmapCompat$Api17Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api17Impl.class", "size": 901, "crc": -2072561382}, {"key": "androidx/core/graphics/BitmapCompat$Api19Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api19Impl.class", "size": 758, "crc": 1727147010}, {"key": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api27Impl.class", "size": 2569, "crc": 651698370}, {"key": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api29Impl.class", "size": 878, "crc": -210670078}, {"key": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "name": "androidx/core/graphics/BitmapCompat$Api31Impl.class", "size": 1082, "crc": 56763293}, {"key": "androidx/core/graphics/BitmapCompat.class", "name": "androidx/core/graphics/BitmapCompat.class", "size": 6262, "crc": -1833557350}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat$Api29Impl.class", "size": 937, "crc": 652344203}, {"key": "androidx/core/graphics/BlendModeColorFilterCompat.class", "name": "androidx/core/graphics/BlendModeColorFilterCompat.class", "size": 1837, "crc": -1703162937}, {"key": "androidx/core/graphics/BlendModeCompat.class", "name": "androidx/core/graphics/BlendModeCompat.class", "size": 2888, "crc": -645227784}, {"key": "androidx/core/graphics/BlendModeUtils$1.class", "name": "androidx/core/graphics/BlendModeUtils$1.class", "size": 2083, "crc": 1721728409}, {"key": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "name": "androidx/core/graphics/BlendModeUtils$Api29Impl.class", "size": 2166, "crc": 1973059920}, {"key": "androidx/core/graphics/BlendModeUtils.class", "name": "androidx/core/graphics/BlendModeUtils.class", "size": 1725, "crc": -1150213795}, {"key": "androidx/core/graphics/ColorUtils$Api26Impl.class", "name": "androidx/core/graphics/ColorUtils$Api26Impl.class", "size": 2133, "crc": 898604083}, {"key": "androidx/core/graphics/ColorUtils.class", "name": "androidx/core/graphics/ColorUtils.class", "size": 10853, "crc": -1043103176}, {"key": "androidx/core/graphics/Insets$Api29Impl.class", "name": "androidx/core/graphics/Insets$Api29Impl.class", "size": 741, "crc": -514050756}, {"key": "androidx/core/graphics/Insets.class", "name": "androidx/core/graphics/Insets.class", "size": 3797, "crc": 645554349}, {"key": "androidx/core/graphics/PaintCompat$Api23Impl.class", "name": "androidx/core/graphics/PaintCompat$Api23Impl.class", "size": 813, "crc": -3322387}, {"key": "androidx/core/graphics/PaintCompat$Api29Impl.class", "name": "androidx/core/graphics/PaintCompat$Api29Impl.class", "size": 869, "crc": 1376448595}, {"key": "androidx/core/graphics/PaintCompat.class", "name": "androidx/core/graphics/PaintCompat.class", "size": 4190, "crc": -1948255028}, {"key": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "name": "androidx/core/graphics/PathParser$ExtractFloatResult.class", "size": 492, "crc": 1852582435}, {"key": "androidx/core/graphics/PathParser$PathDataNode.class", "name": "androidx/core/graphics/PathParser$PathDataNode.class", "size": 8608, "crc": 1984343044}, {"key": "androidx/core/graphics/PathParser.class", "name": "androidx/core/graphics/PathParser.class", "size": 6766, "crc": 1021142439}, {"key": "androidx/core/graphics/PathSegment.class", "name": "androidx/core/graphics/PathSegment.class", "size": 2433, "crc": 1148524092}, {"key": "androidx/core/graphics/PathUtils$Api26Impl.class", "name": "androidx/core/graphics/PathUtils$Api26Impl.class", "size": 764, "crc": 1780295725}, {"key": "androidx/core/graphics/PathUtils.class", "name": "androidx/core/graphics/PathUtils.class", "size": 2168, "crc": -716351640}, {"key": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "name": "androidx/core/graphics/TypefaceCompat$ResourcesCallbackAdapter.class", "size": 1711, "crc": -1279503597}, {"key": "androidx/core/graphics/TypefaceCompat.class", "name": "androidx/core/graphics/TypefaceCompat.class", "size": 10224, "crc": -1645898366}, {"key": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi21Impl.class", "size": 9920, "crc": -32795165}, {"key": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi24Impl.class", "size": 8166, "crc": -1048287158}, {"key": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi26Impl.class", "size": 13353, "crc": 1681707617}, {"key": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi28Impl.class", "size": 3170, "crc": -1628154168}, {"key": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "name": "androidx/core/graphics/TypefaceCompatApi29Impl.class", "size": 8266, "crc": 1017089889}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$1.class", "size": 1680, "crc": 1320454661}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$2.class", "size": 1989, "crc": -1325305591}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$3.class", "size": 1990, "crc": -402207202}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl$StyleExtractor.class", "size": 446, "crc": 1807309830}, {"key": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "name": "androidx/core/graphics/TypefaceCompatBaseImpl.class", "size": 10576, "crc": -1915722572}, {"key": "androidx/core/graphics/TypefaceCompatUtil$Api19Impl.class", "name": "androidx/core/graphics/TypefaceCompatUtil$Api19Impl.class", "size": 1207, "crc": -1561107486}, {"key": "androidx/core/graphics/TypefaceCompatUtil.class", "name": "androidx/core/graphics/TypefaceCompatUtil.class", "size": 7907, "crc": 2017387970}, {"key": "androidx/core/graphics/WeightTypefaceApi14.class", "name": "androidx/core/graphics/WeightTypefaceApi14.class", "size": 5229, "crc": -1834034755}, {"key": "androidx/core/graphics/WeightTypefaceApi21.class", "name": "androidx/core/graphics/WeightTypefaceApi21.class", "size": 6056, "crc": 464421975}, {"key": "androidx/core/graphics/WeightTypefaceApi26.class", "name": "androidx/core/graphics/WeightTypefaceApi26.class", "size": 5541, "crc": 1627236453}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api19Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api19Impl.class", "size": 1971, "crc": -502692922}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api21Impl.class", "size": 3290, "crc": 223090167}, {"key": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/DrawableCompat$Api23Impl.class", "size": 1011, "crc": -1707470127}, {"key": "androidx/core/graphics/drawable/DrawableCompat.class", "name": "androidx/core/graphics/drawable/DrawableCompat.class", "size": 8532, "crc": 1256185088}, {"key": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api23Impl.class", "size": 7848, "crc": -206761027}, {"key": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api26Impl.class", "size": 1260, "crc": -1520057537}, {"key": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api28Impl.class", "size": 1176, "crc": 2025865831}, {"key": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "name": "androidx/core/graphics/drawable/IconCompat$Api30Impl.class", "size": 802, "crc": 766867821}, {"key": "androidx/core/graphics/drawable/IconCompat$IconType.class", "name": "androidx/core/graphics/drawable/IconCompat$IconType.class", "size": 643, "crc": 1843019074}, {"key": "androidx/core/graphics/drawable/IconCompat.class", "name": "androidx/core/graphics/drawable/IconCompat.class", "size": 23766, "crc": 91166451}, {"key": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "name": "androidx/core/graphics/drawable/IconCompatParcelizer.class", "size": 2391, "crc": -1920001871}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable.class", "size": 7892, "crc": -626571484}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawable21.class", "size": 1864, "crc": 468816631}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory$DefaultRoundedBitmapDrawable.class", "size": 1643, "crc": 938684441}, {"key": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "name": "androidx/core/graphics/drawable/RoundedBitmapDrawableFactory.class", "size": 2653, "crc": 1800860871}, {"key": "androidx/core/graphics/drawable/TintAwareDrawable.class", "name": "androidx/core/graphics/drawable/TintAwareDrawable.class", "size": 745, "crc": 1348044187}, {"key": "androidx/core/graphics/drawable/WrappedDrawable.class", "name": "androidx/core/graphics/drawable/WrappedDrawable.class", "size": 560, "crc": -747113056}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi14.class", "size": 8984, "crc": -2009081194}, {"key": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "name": "androidx/core/graphics/drawable/WrappedDrawableApi21.class", "size": 4146, "crc": -859088974}, {"key": "androidx/core/graphics/drawable/WrappedDrawableState.class", "name": "androidx/core/graphics/drawable/WrappedDrawableState.class", "size": 2151, "crc": 1765756452}, {"key": "androidx/core/hardware/display/DisplayManagerCompat$Api17Impl.class", "name": "androidx/core/hardware/display/DisplayManagerCompat$Api17Impl.class", "size": 1123, "crc": 1467353889}, {"key": "androidx/core/hardware/display/DisplayManagerCompat.class", "name": "androidx/core/hardware/display/DisplayManagerCompat.class", "size": 2926, "crc": 470383822}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$1.class", "size": 2815, "crc": 415213846}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$Api23Impl.class", "size": 4151, "crc": 1807485789}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationCallback.class", "size": 1321, "crc": -2076756923}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$AuthenticationResult.class", "size": 1010, "crc": 2024161105}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat$CryptoObject.class", "size": 1522, "crc": -2124223204}, {"key": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "name": "androidx/core/hardware/fingerprint/FingerprintManagerCompat.class", "size": 4724, "crc": -78804119}, {"key": "androidx/core/internal/package-info.class", "name": "androidx/core/internal/package-info.class", "size": 404, "crc": -1162714476}, {"key": "androidx/core/internal/view/SupportMenu.class", "name": "androidx/core/internal/view/SupportMenu.class", "size": 765, "crc": 1726273822}, {"key": "androidx/core/internal/view/SupportMenuItem.class", "name": "androidx/core/internal/view/SupportMenuItem.class", "size": 2836, "crc": 403312890}, {"key": "androidx/core/internal/view/SupportSubMenu.class", "name": "androidx/core/internal/view/SupportSubMenu.class", "size": 488, "crc": 341992331}, {"key": "androidx/core/location/GnssStatusCompat$Callback.class", "name": "androidx/core/location/GnssStatusCompat$Callback.class", "size": 1011, "crc": -1940582241}, {"key": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "name": "androidx/core/location/GnssStatusCompat$ConstellationType.class", "size": 661, "crc": 600754332}, {"key": "androidx/core/location/GnssStatusCompat.class", "name": "androidx/core/location/GnssStatusCompat.class", "size": 2791, "crc": 1745113587}, {"key": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api26Impl.class", "size": 988, "crc": -967658997}, {"key": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "name": "androidx/core/location/GnssStatusWrapper$Api30Impl.class", "size": 982, "crc": -24400282}, {"key": "androidx/core/location/GnssStatusWrapper.class", "name": "androidx/core/location/GnssStatusWrapper.class", "size": 3112, "crc": 210917875}, {"key": "androidx/core/location/GpsStatusWrapper.class", "name": "androidx/core/location/GpsStatusWrapper.class", "size": 4956, "crc": 1376165145}, {"key": "androidx/core/location/LocationCompat$Api17Impl.class", "name": "androidx/core/location/LocationCompat$Api17Impl.class", "size": 771, "crc": -308831577}, {"key": "androidx/core/location/LocationCompat$Api18Impl.class", "name": "androidx/core/location/LocationCompat$Api18Impl.class", "size": 775, "crc": 1801484201}, {"key": "androidx/core/location/LocationCompat$Api26Impl.class", "name": "androidx/core/location/LocationCompat$Api26Impl.class", "size": 1838, "crc": 1419626127}, {"key": "androidx/core/location/LocationCompat.class", "name": "androidx/core/location/LocationCompat.class", "size": 8150, "crc": 1421404516}, {"key": "androidx/core/location/LocationListenerCompat.class", "name": "androidx/core/location/LocationListenerCompat.class", "size": 1553, "crc": 1096933018}, {"key": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api19Impl.class", "size": 3902, "crc": -1792000124}, {"key": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api24Impl.class", "size": 3779, "crc": 1034509170}, {"key": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api28Impl.class", "size": 1168, "crc": 1317041193}, {"key": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api30Impl.class", "size": 6124, "crc": 734233937}, {"key": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "name": "androidx/core/location/LocationManagerCompat$Api31Impl.class", "size": 2316, "crc": -1809972324}, {"key": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "name": "androidx/core/location/LocationManagerCompat$CancellableLocationListener.class", "size": 4695, "crc": -1653845621}, {"key": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "name": "androidx/core/location/LocationManagerCompat$GnssListenersHolder.class", "size": 848, "crc": -50297299}, {"key": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GnssStatusTransport.class", "size": 1738, "crc": 684047790}, {"key": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$GpsStatusTransport.class", "size": 4297, "crc": 498556513}, {"key": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "name": "androidx/core/location/LocationManagerCompat$InlineHandlerExecutor.class", "size": 1595, "crc": -1887179516}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerKey.class", "size": 1396, "crc": 1233889551}, {"key": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "name": "androidx/core/location/LocationManagerCompat$LocationListenerTransport.class", "size": 5175, "crc": 1218714554}, {"key": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "name": "androidx/core/location/LocationManagerCompat$PreRGnssStatusTransport.class", "size": 4058, "crc": 1896460170}, {"key": "androidx/core/location/LocationManagerCompat.class", "name": "androidx/core/location/LocationManagerCompat.class", "size": 21008, "crc": -1410702485}, {"key": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api19Impl.class", "size": 3099, "crc": -1642027927}, {"key": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "name": "androidx/core/location/LocationRequestCompat$Api31Impl.class", "size": 1654, "crc": 1306261069}, {"key": "androidx/core/location/LocationRequestCompat$Builder.class", "name": "androidx/core/location/LocationRequestCompat$Builder.class", "size": 3768, "crc": -32690801}, {"key": "androidx/core/location/LocationRequestCompat$Quality.class", "name": "androidx/core/location/LocationRequestCompat$Quality.class", "size": 656, "crc": -1304604809}, {"key": "androidx/core/location/LocationRequestCompat.class", "name": "androidx/core/location/LocationRequestCompat.class", "size": 4935, "crc": 1371897432}, {"key": "androidx/core/math/MathUtils.class", "name": "androidx/core/math/MathUtils.class", "size": 2769, "crc": 381934772}, {"key": "androidx/core/net/ConnectivityManagerCompat$Api16Impl.class", "name": "androidx/core/net/ConnectivityManagerCompat$Api16Impl.class", "size": 926, "crc": -1055285520}, {"key": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "name": "androidx/core/net/ConnectivityManagerCompat$Api24Impl.class", "size": 837, "crc": 421123032}, {"key": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "name": "androidx/core/net/ConnectivityManagerCompat$RestrictBackgroundStatus.class", "size": 705, "crc": 716344203}, {"key": "androidx/core/net/ConnectivityManagerCompat.class", "name": "androidx/core/net/ConnectivityManagerCompat.class", "size": 2474, "crc": -346725648}, {"key": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper$DatagramSocketImplWrapper.class", "size": 2647, "crc": 1217154937}, {"key": "androidx/core/net/DatagramSocketWrapper.class", "name": "androidx/core/net/DatagramSocketWrapper.class", "size": 694, "crc": 1450467363}, {"key": "androidx/core/net/MailTo.class", "name": "androidx/core/net/MailTo.class", "size": 4777, "crc": 716149100}, {"key": "androidx/core/net/ParseException.class", "name": "androidx/core/net/ParseException.class", "size": 531, "crc": -1332245760}, {"key": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "name": "androidx/core/net/TrafficStatsCompat$Api24Impl.class", "size": 933, "crc": -2134102274}, {"key": "androidx/core/net/TrafficStatsCompat.class", "name": "androidx/core/net/TrafficStatsCompat.class", "size": 2384, "crc": 2031265214}, {"key": "androidx/core/net/UriCompat.class", "name": "androidx/core/net/UriCompat.class", "size": 1861, "crc": 957459528}, {"key": "androidx/core/os/BuildCompat$Extensions30Impl.class", "name": "androidx/core/os/BuildCompat$Extensions30Impl.class", "size": 869, "crc": -169526630}, {"key": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "name": "androidx/core/os/BuildCompat$PrereleaseSdkCheck.class", "size": 344, "crc": 1972204035}, {"key": "androidx/core/os/BuildCompat.class", "name": "androidx/core/os/BuildCompat.class", "size": 3710, "crc": -311590808}, {"key": "androidx/core/os/BundleCompat$Api33Impl.class", "name": "androidx/core/os/BundleCompat$Api33Impl.class", "size": 2513, "crc": 1028536752}, {"key": "androidx/core/os/BundleCompat.class", "name": "androidx/core/os/BundleCompat.class", "size": 3271, "crc": 1159292008}, {"key": "androidx/core/os/CancellationSignal$Api16Impl.class", "name": "androidx/core/os/CancellationSignal$Api16Impl.class", "size": 879, "crc": 844625927}, {"key": "androidx/core/os/CancellationSignal$OnCancelListener.class", "name": "androidx/core/os/CancellationSignal$OnCancelListener.class", "size": 267, "crc": 1768443177}, {"key": "androidx/core/os/CancellationSignal.class", "name": "androidx/core/os/CancellationSignal.class", "size": 2715, "crc": -1580098536}, {"key": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "name": "androidx/core/os/ConfigurationCompat$Api24Impl.class", "size": 837, "crc": -1604099595}, {"key": "androidx/core/os/ConfigurationCompat.class", "name": "androidx/core/os/ConfigurationCompat.class", "size": 1268, "crc": -1179017301}, {"key": "androidx/core/os/EnvironmentCompat$Api19Impl.class", "name": "androidx/core/os/EnvironmentCompat$Api19Impl.class", "size": 739, "crc": 742022865}, {"key": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "name": "androidx/core/os/EnvironmentCompat$Api21Impl.class", "size": 747, "crc": -547922024}, {"key": "androidx/core/os/EnvironmentCompat.class", "name": "androidx/core/os/EnvironmentCompat.class", "size": 1828, "crc": -1399338412}, {"key": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "name": "androidx/core/os/ExecutorCompat$HandlerExecutor.class", "size": 1394, "crc": 817974452}, {"key": "androidx/core/os/ExecutorCompat.class", "name": "androidx/core/os/ExecutorCompat.class", "size": 732, "crc": 813340522}, {"key": "androidx/core/os/HandlerCompat$Api28Impl.class", "name": "androidx/core/os/HandlerCompat$Api28Impl.class", "size": 1266, "crc": -523528807}, {"key": "androidx/core/os/HandlerCompat$Api29Impl.class", "name": "androidx/core/os/HandlerCompat$Api29Impl.class", "size": 750, "crc": 999905912}, {"key": "androidx/core/os/HandlerCompat.class", "name": "androidx/core/os/HandlerCompat.class", "size": 5070, "crc": 1332842940}, {"key": "androidx/core/os/LocaleListCompat$Api21Impl.class", "name": "androidx/core/os/LocaleListCompat$Api21Impl.class", "size": 1997, "crc": -1998897945}, {"key": "androidx/core/os/LocaleListCompat$Api24Impl.class", "name": "androidx/core/os/LocaleListCompat$Api24Impl.class", "size": 969, "crc": -916775485}, {"key": "androidx/core/os/LocaleListCompat.class", "name": "androidx/core/os/LocaleListCompat.class", "size": 5972, "crc": -123395160}, {"key": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "name": "androidx/core/os/LocaleListCompatWrapper$Api21Impl.class", "size": 784, "crc": -803210832}, {"key": "androidx/core/os/LocaleListCompatWrapper.class", "name": "androidx/core/os/LocaleListCompatWrapper.class", "size": 6913, "crc": 1732702063}, {"key": "androidx/core/os/LocaleListInterface.class", "name": "androidx/core/os/LocaleListInterface.class", "size": 673, "crc": -1204215516}, {"key": "androidx/core/os/LocaleListPlatformWrapper.class", "name": "androidx/core/os/LocaleListPlatformWrapper.class", "size": 1937, "crc": 1832161889}, {"key": "androidx/core/os/MessageCompat$Api22Impl.class", "name": "androidx/core/os/MessageCompat$Api22Impl.class", "size": 884, "crc": -1707707376}, {"key": "androidx/core/os/MessageCompat.class", "name": "androidx/core/os/MessageCompat.class", "size": 1409, "crc": 2099829457}, {"key": "androidx/core/os/OperationCanceledException.class", "name": "androidx/core/os/OperationCanceledException.class", "size": 734, "crc": 1084133341}, {"key": "androidx/core/os/ParcelCompat$Api29Impl.class", "name": "androidx/core/os/ParcelCompat$Api29Impl.class", "size": 1234, "crc": 637369862}, {"key": "androidx/core/os/ParcelCompat$Api30Impl.class", "name": "androidx/core/os/ParcelCompat$Api30Impl.class", "size": 1173, "crc": 111130707}, {"key": "androidx/core/os/ParcelCompat$Api33Impl.class", "name": "androidx/core/os/ParcelCompat$Api33Impl.class", "size": 5851, "crc": -256629757}, {"key": "androidx/core/os/ParcelCompat.class", "name": "androidx/core/os/ParcelCompat.class", "size": 9321, "crc": 592014205}, {"key": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "name": "androidx/core/os/ParcelableCompat$ParcelableCompatCreatorHoneycombMR2.class", "size": 1865, "crc": -1411295453}, {"key": "androidx/core/os/ParcelableCompat.class", "name": "androidx/core/os/ParcelableCompat.class", "size": 1172, "crc": 276111255}, {"key": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "name": "androidx/core/os/ParcelableCompatCreatorCallbacks.class", "size": 521, "crc": -1661855711}, {"key": "androidx/core/os/ProcessCompat$Api16Impl.class", "name": "androidx/core/os/ProcessCompat$Api16Impl.class", "size": 1794, "crc": 1573717268}, {"key": "androidx/core/os/ProcessCompat$Api17Impl.class", "name": "androidx/core/os/ProcessCompat$Api17Impl.class", "size": 1746, "crc": -1787655522}, {"key": "androidx/core/os/ProcessCompat$Api24Impl.class", "name": "androidx/core/os/ProcessCompat$Api24Impl.class", "size": 628, "crc": -451466529}, {"key": "androidx/core/os/ProcessCompat.class", "name": "androidx/core/os/ProcessCompat.class", "size": 817, "crc": -44670261}, {"key": "androidx/core/os/TraceCompat$Api18Impl.class", "name": "androidx/core/os/TraceCompat$Api18Impl.class", "size": 789, "crc": -719725466}, {"key": "androidx/core/os/TraceCompat$Api29Impl.class", "name": "androidx/core/os/TraceCompat$Api29Impl.class", "size": 1104, "crc": -1969664624}, {"key": "androidx/core/os/TraceCompat.class", "name": "androidx/core/os/TraceCompat.class", "size": 3795, "crc": 1272681506}, {"key": "androidx/core/os/UserHandleCompat$Api24Impl.class", "name": "androidx/core/os/UserHandleCompat$Api24Impl.class", "size": 712, "crc": 640979588}, {"key": "androidx/core/os/UserHandleCompat.class", "name": "androidx/core/os/UserHandleCompat.class", "size": 3061, "crc": -864588795}, {"key": "androidx/core/os/UserManagerCompat$Api24Impl.class", "name": "androidx/core/os/UserManagerCompat$Api24Impl.class", "size": 858, "crc": -689420772}, {"key": "androidx/core/os/UserManagerCompat.class", "name": "androidx/core/os/UserManagerCompat.class", "size": 785, "crc": -**********}, {"key": "androidx/core/provider/CallbackWithHandler$1.class", "name": "androidx/core/provider/CallbackWithHandler$1.class", "size": 1177, "crc": -**********}, {"key": "androidx/core/provider/CallbackWithHandler$2.class", "name": "androidx/core/provider/CallbackWithHandler$2.class", "size": 1101, "crc": -579370637}, {"key": "androidx/core/provider/CallbackWithHandler.class", "name": "androidx/core/provider/CallbackWithHandler.class", "size": 2578, "crc": -255845993}, {"key": "androidx/core/provider/CalleeHandler.class", "name": "androidx/core/provider/CalleeHandler.class", "size": 748, "crc": -356584081}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentCompat.class", "size": 539, "crc": -744260981}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi19Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi19Impl.class", "size": 1632, "crc": 793146053}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi21Impl.class", "size": 2243, "crc": -**********}, {"key": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "name": "androidx/core/provider/DocumentsContractCompat$DocumentsContractApi24Impl.class", "size": 1243, "crc": -**********}, {"key": "androidx/core/provider/DocumentsContractCompat.class", "name": "androidx/core/provider/DocumentsContractCompat.class", "size": 4399, "crc": -**********}, {"key": "androidx/core/provider/FontProvider$Api16Impl.class", "name": "androidx/core/provider/FontProvider$Api16Impl.class", "size": 1324, "crc": 93699198}, {"key": "androidx/core/provider/FontProvider.class", "name": "androidx/core/provider/FontProvider.class", "size": 9787, "crc": **********}, {"key": "androidx/core/provider/FontRequest.class", "name": "androidx/core/provider/FontRequest.class", "size": 4101, "crc": 766638385}, {"key": "androidx/core/provider/FontRequestWorker$1.class", "name": "androidx/core/provider/FontRequestWorker$1.class", "size": 1628, "crc": **********}, {"key": "androidx/core/provider/FontRequestWorker$2.class", "name": "androidx/core/provider/FontRequestWorker$2.class", "size": 1631, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker$3.class", "name": "androidx/core/provider/FontRequestWorker$3.class", "size": 1940, "crc": 129325094}, {"key": "androidx/core/provider/FontRequestWorker$4.class", "name": "androidx/core/provider/FontRequestWorker$4.class", "size": 2067, "crc": -757734499}, {"key": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "name": "androidx/core/provider/FontRequestWorker$TypefaceResult.class", "size": 1197, "crc": -**********}, {"key": "androidx/core/provider/FontRequestWorker.class", "name": "androidx/core/provider/FontRequestWorker.class", "size": 8149, "crc": **********}, {"key": "androidx/core/provider/FontsContractCompat$Columns.class", "name": "androidx/core/provider/FontsContractCompat$Columns.class", "size": 1073, "crc": -536012212}, {"key": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "name": "androidx/core/provider/FontsContractCompat$FontFamilyResult.class", "size": 1802, "crc": -**********}, {"key": "androidx/core/provider/FontsContractCompat$FontInfo.class", "name": "androidx/core/provider/FontsContractCompat$FontInfo.class", "size": 2142, "crc": 555367403}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback$FontRequestFailReason.class", "size": 809, "crc": -566122054}, {"key": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "name": "androidx/core/provider/FontsContractCompat$FontRequestCallback.class", "size": 1726, "crc": -487185620}, {"key": "androidx/core/provider/FontsContractCompat.class", "name": "androidx/core/provider/FontsContractCompat.class", "size": 7012, "crc": 621149968}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory$ProcessPriorityThread.class", "size": 989, "crc": -**********}, {"key": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "name": "androidx/core/provider/RequestExecutor$DefaultThreadFactory.class", "size": 1108, "crc": -**********}, {"key": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "name": "androidx/core/provider/RequestExecutor$HandlerExecutor.class", "size": 1416, "crc": **********}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable$1.class", "size": 1134, "crc": -721637920}, {"key": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "name": "androidx/core/provider/RequestExecutor$ReplyRunnable.class", "size": 2086, "crc": -361715969}, {"key": "androidx/core/provider/RequestExecutor.class", "name": "androidx/core/provider/RequestExecutor.class", "size": 4032, "crc": 45623863}, {"key": "androidx/core/provider/SelfDestructiveThread$1.class", "name": "androidx/core/provider/SelfDestructiveThread$1.class", "size": 1098, "crc": -237993764}, {"key": "androidx/core/provider/SelfDestructiveThread$2$1.class", "name": "androidx/core/provider/SelfDestructiveThread$2$1.class", "size": 1084, "crc": **********}, {"key": "androidx/core/provider/SelfDestructiveThread$2.class", "name": "androidx/core/provider/SelfDestructiveThread$2.class", "size": 1835, "crc": -**********}, {"key": "androidx/core/provider/SelfDestructiveThread$3.class", "name": "androidx/core/provider/SelfDestructiveThread$3.class", "size": 1943, "crc": 545114781}, {"key": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "name": "androidx/core/provider/SelfDestructiveThread$ReplyCallback.class", "size": 379, "crc": -538938372}, {"key": "androidx/core/provider/SelfDestructiveThread.class", "name": "androidx/core/provider/SelfDestructiveThread.class", "size": 6254, "crc": -629807692}, {"key": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "name": "androidx/core/telephony/SubscriptionManagerCompat$Api29Impl.class", "size": 758, "crc": **********}, {"key": "androidx/core/telephony/SubscriptionManagerCompat.class", "name": "androidx/core/telephony/SubscriptionManagerCompat.class", "size": 1698, "crc": **********}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api23Impl.class", "size": 1087, "crc": **********}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api26Impl.class", "size": 1054, "crc": -**********}, {"key": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "name": "androidx/core/telephony/TelephonyManagerCompat$Api30Impl.class", "size": 835, "crc": **********}, {"key": "androidx/core/telephony/TelephonyManagerCompat.class", "name": "androidx/core/telephony/TelephonyManagerCompat.class", "size": 2906, "crc": 1474125055}, {"key": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "name": "androidx/core/telephony/mbms/MbmsHelper$Api28Impl.class", "size": 2186, "crc": -1878492408}, {"key": "androidx/core/telephony/mbms/MbmsHelper.class", "name": "androidx/core/telephony/mbms/MbmsHelper.class", "size": 1004, "crc": -1438195000}, {"key": "androidx/core/text/BidiFormatter$Builder.class", "name": "androidx/core/text/BidiFormatter$Builder.class", "size": 2036, "crc": 1378760591}, {"key": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "name": "androidx/core/text/BidiFormatter$DirectionalityEstimator.class", "size": 4094, "crc": -1902417185}, {"key": "androidx/core/text/BidiFormatter.class", "name": "androidx/core/text/BidiFormatter.class", "size": 5801, "crc": 1804739059}, {"key": "androidx/core/text/HtmlCompat$Api24Impl.class", "name": "androidx/core/text/HtmlCompat$Api24Impl.class", "size": 1362, "crc": -1369758415}, {"key": "androidx/core/text/HtmlCompat.class", "name": "androidx/core/text/HtmlCompat.class", "size": 2554, "crc": 190443410}, {"key": "androidx/core/text/ICUCompat$Api21Impl.class", "name": "androidx/core/text/ICUCompat$Api21Impl.class", "size": 734, "crc": 1318579150}, {"key": "androidx/core/text/ICUCompat$Api24Impl.class", "name": "androidx/core/text/ICUCompat$Api24Impl.class", "size": 1128, "crc": -209947284}, {"key": "androidx/core/text/ICUCompat.class", "name": "androidx/core/text/ICUCompat.class", "size": 3456, "crc": -1561930858}, {"key": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "name": "androidx/core/text/PrecomputedTextCompat$Params$Builder.class", "size": 2140, "crc": 545398241}, {"key": "androidx/core/text/PrecomputedTextCompat$Params.class", "name": "androidx/core/text/PrecomputedTextCompat$Params.class", "size": 6752, "crc": 188558442}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask$PrecomputedTextCallback.class", "size": 1631, "crc": 1221161191}, {"key": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "name": "androidx/core/text/PrecomputedTextCompat$PrecomputedTextFutureTask.class", "size": 1136, "crc": -1090637348}, {"key": "androidx/core/text/PrecomputedTextCompat.class", "name": "androidx/core/text/PrecomputedTextCompat.class", "size": 9030, "crc": -2061631751}, {"key": "androidx/core/text/TextDirectionHeuristicCompat.class", "name": "androidx/core/text/TextDirectionHeuristicCompat.class", "size": 222, "crc": -118200751}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$AnyStrong.class", "size": 1358, "crc": -116397953}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$FirstStrong.class", "size": 1162, "crc": 1465235069}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionAlgorithm.class", "size": 342, "crc": 964437877}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicImpl.class", "size": 1745, "crc": 2136242730}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicInternal.class", "size": 1131, "crc": -1371447970}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat$TextDirectionHeuristicLocale.class", "size": 1270, "crc": 1216013965}, {"key": "androidx/core/text/TextDirectionHeuristicsCompat.class", "name": "androidx/core/text/TextDirectionHeuristicsCompat.class", "size": 2235, "crc": 1050206764}, {"key": "androidx/core/text/TextUtilsCompat$Api17Impl.class", "name": "androidx/core/text/TextUtilsCompat$Api17Impl.class", "size": 743, "crc": 232804009}, {"key": "androidx/core/text/TextUtilsCompat.class", "name": "androidx/core/text/TextUtilsCompat.class", "size": 2588, "crc": -1493075492}, {"key": "androidx/core/text/util/FindAddress$ZipRange.class", "name": "androidx/core/text/util/FindAddress$ZipRange.class", "size": 1027, "crc": -1838276139}, {"key": "androidx/core/text/util/FindAddress.class", "name": "androidx/core/text/util/FindAddress.class", "size": 11163, "crc": 1363984777}, {"key": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "name": "androidx/core/text/util/LinkifyCompat$Api24Impl.class", "size": 1668, "crc": 818694512}, {"key": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "name": "androidx/core/text/util/LinkifyCompat$LinkSpec.class", "size": 548, "crc": 978455525}, {"key": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "name": "androidx/core/text/util/LinkifyCompat$LinkifyMask.class", "size": 655, "crc": -1830119234}, {"key": "androidx/core/text/util/LinkifyCompat.class", "name": "androidx/core/text/util/LinkifyCompat.class", "size": 12426, "crc": 1130807464}, {"key": "androidx/core/util/AtomicFile.class", "name": "androidx/core/util/AtomicFile.class", "size": 4462, "crc": 1282749343}, {"key": "androidx/core/util/Consumer.class", "name": "androidx/core/util/Consumer.class", "size": 237, "crc": 408644938}, {"key": "androidx/core/util/DebugUtils.class", "name": "androidx/core/util/DebugUtils.class", "size": 1525, "crc": -648213977}, {"key": "androidx/core/util/LogWriter.class", "name": "androidx/core/util/LogWriter.class", "size": 1684, "crc": -1823315376}, {"key": "androidx/core/util/ObjectsCompat$Api19Impl.class", "name": "androidx/core/util/ObjectsCompat$Api19Impl.class", "size": 882, "crc": -289606262}, {"key": "androidx/core/util/ObjectsCompat.class", "name": "androidx/core/util/ObjectsCompat.class", "size": 2168, "crc": -557564951}, {"key": "androidx/core/util/Pair.class", "name": "androidx/core/util/Pair.class", "size": 1918, "crc": -1970849920}, {"key": "androidx/core/util/PatternsCompat.class", "name": "androidx/core/util/PatternsCompat.class", "size": 45442, "crc": -790769780}, {"key": "androidx/core/util/Pools$Pool.class", "name": "androidx/core/util/Pools$Pool.class", "size": 521, "crc": 1580738153}, {"key": "androidx/core/util/Pools$SimplePool.class", "name": "androidx/core/util/Pools$SimplePool.class", "size": 1789, "crc": -771314487}, {"key": "androidx/core/util/Pools$SynchronizedPool.class", "name": "androidx/core/util/Pools$SynchronizedPool.class", "size": 1315, "crc": -521654601}, {"key": "androidx/core/util/Pools.class", "name": "androidx/core/util/Pools.class", "size": 491, "crc": 1684647274}, {"key": "androidx/core/util/Preconditions.class", "name": "androidx/core/util/Preconditions.class", "size": 6183, "crc": -877174335}, {"key": "androidx/core/util/Predicate.class", "name": "androidx/core/util/Predicate.class", "size": 3398, "crc": -106517694}, {"key": "androidx/core/util/SizeFCompat$Api21Impl.class", "name": "androidx/core/util/SizeFCompat$Api21Impl.class", "size": 1233, "crc": 1355410199}, {"key": "androidx/core/util/SizeFCompat.class", "name": "androidx/core/util/SizeFCompat.class", "size": 1988, "crc": 703664480}, {"key": "androidx/core/util/Supplier.class", "name": "androidx/core/util/Supplier.class", "size": 232, "crc": -1241756455}, {"key": "androidx/core/util/TimeUtils.class", "name": "androidx/core/util/TimeUtils.class", "size": 4432, "crc": 792950436}, {"key": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "name": "androidx/core/view/AccessibilityDelegateCompat$AccessibilityDelegateAdapter.class", "size": 4872, "crc": 297555897}, {"key": "androidx/core/view/AccessibilityDelegateCompat$Api16Impl.class", "name": "androidx/core/view/AccessibilityDelegateCompat$Api16Impl.class", "size": 1432, "crc": -341443625}, {"key": "androidx/core/view/AccessibilityDelegateCompat.class", "name": "androidx/core/view/AccessibilityDelegateCompat.class", "size": 7562, "crc": **********}, {"key": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "name": "androidx/core/view/ActionProvider$SubUiVisibilityListener.class", "size": 543, "crc": -**********}, {"key": "androidx/core/view/ActionProvider$VisibilityListener.class", "name": "androidx/core/view/ActionProvider$VisibilityListener.class", "size": 289, "crc": -**********}, {"key": "androidx/core/view/ActionProvider.class", "name": "androidx/core/view/ActionProvider.class", "size": 3576, "crc": -709321438}, {"key": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Api31Impl.class", "size": 3079, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$Builder.class", "name": "androidx/core/view/ContentInfoCompat$Builder.class", "size": 2697, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat.class", "size": 666, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompat31Impl.class", "size": 2633, "crc": -**********}, {"key": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$BuilderCompatImpl.class", "size": 2445, "crc": **********}, {"key": "androidx/core/view/ContentInfoCompat$Compat.class", "name": "androidx/core/view/ContentInfoCompat$Compat.class", "size": 599, "crc": 322977362}, {"key": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "name": "androidx/core/view/ContentInfoCompat$Compat31Impl.class", "size": 1978, "crc": -798266082}, {"key": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "name": "androidx/core/view/ContentInfoCompat$CompatImpl.class", "size": 2855, "crc": 960199018}, {"key": "androidx/core/view/ContentInfoCompat$Flags.class", "name": "androidx/core/view/ContentInfoCompat$Flags.class", "size": 645, "crc": 103647730}, {"key": "androidx/core/view/ContentInfoCompat$Source.class", "name": "androidx/core/view/ContentInfoCompat$Source.class", "size": 647, "crc": -1098283764}, {"key": "androidx/core/view/ContentInfoCompat.class", "name": "androidx/core/view/ContentInfoCompat.class", "size": 7544, "crc": 1021952575}, {"key": "androidx/core/view/DisplayCompat$Api17Impl.class", "name": "androidx/core/view/DisplayCompat$Api17Impl.class", "size": 783, "crc": 1817832886}, {"key": "androidx/core/view/DisplayCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$Api23Impl.class", "size": 3224, "crc": -1149992379}, {"key": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "name": "androidx/core/view/DisplayCompat$ModeCompat$Api23Impl.class", "size": 984, "crc": 499830806}, {"key": "androidx/core/view/DisplayCompat$ModeCompat.class", "name": "androidx/core/view/DisplayCompat$ModeCompat.class", "size": 2226, "crc": 1782302470}, {"key": "androidx/core/view/DisplayCompat.class", "name": "androidx/core/view/DisplayCompat.class", "size": 5477, "crc": -381714626}, {"key": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api28Impl.class", "size": 1861, "crc": -107262589}, {"key": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api29Impl.class", "size": 1139, "crc": 1779322507}, {"key": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "name": "androidx/core/view/DisplayCutoutCompat$Api30Impl.class", "size": 1458, "crc": 497110824}, {"key": "androidx/core/view/DisplayCutoutCompat.class", "name": "androidx/core/view/DisplayCutoutCompat.class", "size": 5691, "crc": -1710624267}, {"key": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "name": "androidx/core/view/DragAndDropPermissionsCompat$Api24Impl.class", "size": 1222, "crc": 249459427}, {"key": "androidx/core/view/DragAndDropPermissionsCompat.class", "name": "androidx/core/view/DragAndDropPermissionsCompat.class", "size": 1686, "crc": 251573488}, {"key": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "name": "androidx/core/view/DragStartHelper$OnDragStartListener.class", "size": 416, "crc": -2064185111}, {"key": "androidx/core/view/DragStartHelper.class", "name": "androidx/core/view/DragStartHelper.class", "size": 3398, "crc": 590659996}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImpl.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImpl.class", "size": 609, "crc": 1055230217}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase$GestureHandler.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase$GestureHandler.class", "size": 2437, "crc": -1316806887}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplBase.class", "size": 8651, "crc": 1476896763}, {"key": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplJellybeanMr2.class", "name": "androidx/core/view/GestureDetectorCompat$GestureDetectorCompatImplJellybeanMr2.class", "size": 1808, "crc": -239387221}, {"key": "androidx/core/view/GestureDetectorCompat.class", "name": "androidx/core/view/GestureDetectorCompat.class", "size": 2621, "crc": 1500182683}, {"key": "androidx/core/view/GravityCompat$Api17Impl.class", "name": "androidx/core/view/GravityCompat$Api17Impl.class", "size": 1311, "crc": -952907712}, {"key": "androidx/core/view/GravityCompat.class", "name": "androidx/core/view/GravityCompat.class", "size": 2024, "crc": -558372288}, {"key": "androidx/core/view/InputDeviceCompat.class", "name": "androidx/core/view/InputDeviceCompat.class", "size": 1178, "crc": -1611156102}, {"key": "androidx/core/view/KeyEventDispatcher$Component.class", "name": "androidx/core/view/KeyEventDispatcher$Component.class", "size": 377, "crc": 1819687057}, {"key": "androidx/core/view/KeyEventDispatcher.class", "name": "androidx/core/view/KeyEventDispatcher.class", "size": 5617, "crc": 1741433}, {"key": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "name": "androidx/core/view/LayoutInflaterCompat$Factory2Wrapper.class", "size": 1824, "crc": 709925757}, {"key": "androidx/core/view/LayoutInflaterCompat.class", "name": "androidx/core/view/LayoutInflaterCompat.class", "size": 3446, "crc": 1815067315}, {"key": "androidx/core/view/LayoutInflaterFactory.class", "name": "androidx/core/view/LayoutInflaterFactory.class", "size": 362, "crc": 1217133356}, {"key": "androidx/core/view/MarginLayoutParamsCompat$Api17Impl.class", "name": "androidx/core/view/MarginLayoutParamsCompat$Api17Impl.class", "size": 1831, "crc": -401068417}, {"key": "androidx/core/view/MarginLayoutParamsCompat.class", "name": "androidx/core/view/MarginLayoutParamsCompat.class", "size": 2227, "crc": -1022510054}, {"key": "androidx/core/view/MenuCompat$Api28Impl.class", "name": "androidx/core/view/MenuCompat$Api28Impl.class", "size": 763, "crc": 493244291}, {"key": "androidx/core/view/MenuCompat.class", "name": "androidx/core/view/MenuCompat.class", "size": 1173, "crc": -465812236}, {"key": "androidx/core/view/MenuHost.class", "name": "androidx/core/view/MenuHost.class", "size": 802, "crc": 1764291970}, {"key": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "name": "androidx/core/view/MenuHostHelper$LifecycleContainer.class", "size": 1047, "crc": -2092317000}, {"key": "androidx/core/view/MenuHostHelper.class", "name": "androidx/core/view/MenuHostHelper.class", "size": 6015, "crc": 1640962395}, {"key": "androidx/core/view/MenuItemCompat$1.class", "name": "androidx/core/view/MenuItemCompat$1.class", "size": 1202, "crc": -1742634925}, {"key": "androidx/core/view/MenuItemCompat$Api26Impl.class", "name": "androidx/core/view/MenuItemCompat$Api26Impl.class", "size": 3334, "crc": -**********}, {"key": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "name": "androidx/core/view/MenuItemCompat$OnActionExpandListener.class", "size": 427, "crc": -**********}, {"key": "androidx/core/view/MenuItemCompat.class", "name": "androidx/core/view/MenuItemCompat.class", "size": 7928, "crc": **********}, {"key": "androidx/core/view/MenuProvider.class", "name": "androidx/core/view/MenuProvider.class", "size": 694, "crc": -168585429}, {"key": "androidx/core/view/MotionEventCompat.class", "name": "androidx/core/view/MotionEventCompat.class", "size": 5062, "crc": **********}, {"key": "androidx/core/view/NestedScrollingChild.class", "name": "androidx/core/view/NestedScrollingChild.class", "size": 612, "crc": 643083790}, {"key": "androidx/core/view/NestedScrollingChild2.class", "name": "androidx/core/view/NestedScrollingChild2.class", "size": 512, "crc": **********}, {"key": "androidx/core/view/NestedScrollingChild3.class", "name": "androidx/core/view/NestedScrollingChild3.class", "size": 369, "crc": 538066002}, {"key": "androidx/core/view/NestedScrollingChildHelper.class", "name": "androidx/core/view/NestedScrollingChildHelper.class", "size": 5709, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingParent.class", "name": "androidx/core/view/NestedScrollingParent.class", "size": 808, "crc": -**********}, {"key": "androidx/core/view/NestedScrollingParent2.class", "name": "androidx/core/view/NestedScrollingParent2.class", "size": 695, "crc": -892996660}, {"key": "androidx/core/view/NestedScrollingParent3.class", "name": "androidx/core/view/NestedScrollingParent3.class", "size": 350, "crc": -686595616}, {"key": "androidx/core/view/NestedScrollingParentHelper.class", "name": "androidx/core/view/NestedScrollingParentHelper.class", "size": 1541, "crc": 461928738}, {"key": "androidx/core/view/OnApplyWindowInsetsListener.class", "name": "androidx/core/view/OnApplyWindowInsetsListener.class", "size": 418, "crc": 821875535}, {"key": "androidx/core/view/OnReceiveContentListener.class", "name": "androidx/core/view/OnReceiveContentListener.class", "size": 440, "crc": -51731300}, {"key": "androidx/core/view/OnReceiveContentViewBehavior.class", "name": "androidx/core/view/OnReceiveContentViewBehavior.class", "size": 423, "crc": -1932407804}, {"key": "androidx/core/view/OneShotPreDrawListener.class", "name": "androidx/core/view/OneShotPreDrawListener.class", "size": 2307, "crc": -501338103}, {"key": "androidx/core/view/PointerIconCompat$Api24Impl.class", "name": "androidx/core/view/PointerIconCompat$Api24Impl.class", "size": 1259, "crc": 393629244}, {"key": "androidx/core/view/PointerIconCompat.class", "name": "androidx/core/view/PointerIconCompat.class", "size": 3083, "crc": 2039203107}, {"key": "androidx/core/view/ScaleGestureDetectorCompat$Api19Impl.class", "name": "androidx/core/view/ScaleGestureDetectorCompat$Api19Impl.class", "size": 1026, "crc": 1798524504}, {"key": "androidx/core/view/ScaleGestureDetectorCompat.class", "name": "androidx/core/view/ScaleGestureDetectorCompat.class", "size": 1426, "crc": -763250653}, {"key": "androidx/core/view/ScrollingView.class", "name": "androidx/core/view/ScrollingView.class", "size": 364, "crc": -218140119}, {"key": "androidx/core/view/TintableBackgroundView.class", "name": "androidx/core/view/TintableBackgroundView.class", "size": 723, "crc": 1795767615}, {"key": "androidx/core/view/VelocityTrackerCompat.class", "name": "androidx/core/view/VelocityTrackerCompat.class", "size": 777, "crc": 1797235289}, {"key": "androidx/core/view/ViewCompat$1.class", "name": "androidx/core/view/ViewCompat$1.class", "size": 2347, "crc": -1877699888}, {"key": "androidx/core/view/ViewCompat$2.class", "name": "androidx/core/view/ViewCompat$2.class", "size": 2160, "crc": -1358635888}, {"key": "androidx/core/view/ViewCompat$3.class", "name": "androidx/core/view/ViewCompat$3.class", "size": 2155, "crc": 529958490}, {"key": "androidx/core/view/ViewCompat$4.class", "name": "androidx/core/view/ViewCompat$4.class", "size": 2217, "crc": -869650707}, {"key": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "name": "androidx/core/view/ViewCompat$AccessibilityPaneVisibilityManager.class", "size": 4069, "crc": 1529809972}, {"key": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "name": "androidx/core/view/ViewCompat$AccessibilityViewProperty.class", "size": 3288, "crc": -1732581981}, {"key": "androidx/core/view/ViewCompat$Api15Impl.class", "name": "androidx/core/view/ViewCompat$Api15Impl.class", "size": 799, "crc": -1836250639}, {"key": "androidx/core/view/ViewCompat$Api16Impl.class", "name": "androidx/core/view/ViewCompat$Api16Impl.class", "size": 4092, "crc": 2017629226}, {"key": "androidx/core/view/ViewCompat$Api17Impl.class", "name": "androidx/core/view/ViewCompat$Api17Impl.class", "size": 2219, "crc": -846268322}, {"key": "androidx/core/view/ViewCompat$Api18Impl.class", "name": "androidx/core/view/ViewCompat$Api18Impl.class", "size": 1222, "crc": -1630158470}, {"key": "androidx/core/view/ViewCompat$Api19Impl.class", "name": "androidx/core/view/ViewCompat$Api19Impl.class", "size": 2034, "crc": -1683437304}, {"key": "androidx/core/view/ViewCompat$Api20Impl.class", "name": "androidx/core/view/ViewCompat$Api20Impl.class", "size": 1126, "crc": 2076615150}, {"key": "androidx/core/view/ViewCompat$Api21Impl$1.class", "name": "androidx/core/view/ViewCompat$Api21Impl$1.class", "size": 2038, "crc": -1279881417}, {"key": "androidx/core/view/ViewCompat$Api21Impl.class", "name": "androidx/core/view/ViewCompat$Api21Impl.class", "size": 7146, "crc": 1963995129}, {"key": "androidx/core/view/ViewCompat$Api23Impl.class", "name": "androidx/core/view/ViewCompat$Api23Impl.class", "size": 1943, "crc": -1801230805}, {"key": "androidx/core/view/ViewCompat$Api24Impl.class", "name": "androidx/core/view/ViewCompat$Api24Impl.class", "size": 2146, "crc": 110653927}, {"key": "androidx/core/view/ViewCompat$Api26Impl.class", "name": "androidx/core/view/ViewCompat$Api26Impl.class", "size": 3401, "crc": 1265244457}, {"key": "androidx/core/view/ViewCompat$Api28Impl.class", "name": "androidx/core/view/ViewCompat$Api28Impl.class", "size": 4237, "crc": -2088004394}, {"key": "androidx/core/view/ViewCompat$Api29Impl.class", "name": "androidx/core/view/ViewCompat$Api29Impl.class", "size": 2226, "crc": -880339247}, {"key": "androidx/core/view/ViewCompat$Api30Impl.class", "name": "androidx/core/view/ViewCompat$Api30Impl.class", "size": 1715, "crc": -209707099}, {"key": "androidx/core/view/ViewCompat$Api31Impl.class", "name": "androidx/core/view/ViewCompat$Api31Impl.class", "size": 2229, "crc": 1370761717}, {"key": "androidx/core/view/ViewCompat$FocusDirection.class", "name": "androidx/core/view/ViewCompat$FocusDirection.class", "size": 642, "crc": 1155425484}, {"key": "androidx/core/view/ViewCompat$FocusRealDirection.class", "name": "androidx/core/view/ViewCompat$FocusRealDirection.class", "size": 650, "crc": 749280326}, {"key": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "name": "androidx/core/view/ViewCompat$FocusRelativeDirection.class", "size": 658, "crc": 1576246077}, {"key": "androidx/core/view/ViewCompat$NestedScrollType.class", "name": "androidx/core/view/ViewCompat$NestedScrollType.class", "size": 646, "crc": 1643799421}, {"key": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "name": "androidx/core/view/ViewCompat$OnReceiveContentListenerAdapter.class", "size": 1714, "crc": -618367019}, {"key": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "name": "androidx/core/view/ViewCompat$OnUnhandledKeyEventListenerCompat.class", "size": 424, "crc": -762508931}, {"key": "androidx/core/view/ViewCompat$ScrollAxis.class", "name": "androidx/core/view/ViewCompat$ScrollAxis.class", "size": 634, "crc": -1481000453}, {"key": "androidx/core/view/ViewCompat$ScrollIndicators.class", "name": "androidx/core/view/ViewCompat$ScrollIndicators.class", "size": 646, "crc": -1203320779}, {"key": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "name": "androidx/core/view/ViewCompat$UnhandledKeyEventManager.class", "size": 5939, "crc": -219710437}, {"key": "androidx/core/view/ViewCompat.class", "name": "androidx/core/view/ViewCompat.class", "size": 58551, "crc": -1770305364}, {"key": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api26Impl.class", "size": 946, "crc": -2084945526}, {"key": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "name": "androidx/core/view/ViewConfigurationCompat$Api28Impl.class", "size": 990, "crc": -1923064715}, {"key": "androidx/core/view/ViewConfigurationCompat.class", "name": "androidx/core/view/ViewConfigurationCompat.class", "size": 3959, "crc": -1125329404}, {"key": "androidx/core/view/ViewGroupCompat$Api18Impl.class", "name": "androidx/core/view/ViewGroupCompat$Api18Impl.class", "size": 918, "crc": -1742576781}, {"key": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "name": "androidx/core/view/ViewGroupCompat$Api21Impl.class", "size": 1055, "crc": -1742838564}, {"key": "androidx/core/view/ViewGroupCompat.class", "name": "androidx/core/view/ViewGroupCompat.class", "size": 2919, "crc": -1156431332}, {"key": "androidx/core/view/ViewParentCompat$Api19Impl.class", "name": "androidx/core/view/ViewParentCompat$Api19Impl.class", "size": 944, "crc": -1058335948}, {"key": "androidx/core/view/ViewParentCompat$Api21Impl.class", "name": "androidx/core/view/ViewParentCompat$Api21Impl.class", "size": 2353, "crc": -585042330}, {"key": "androidx/core/view/ViewParentCompat.class", "name": "androidx/core/view/ViewParentCompat.class", "size": 7342, "crc": 935669967}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$1.class", "size": 1433, "crc": 2093844466}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api16Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api16Impl.class", "size": 1312, "crc": -1976894863}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api18Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api18Impl.class", "size": 954, "crc": -493018886}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api19Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api19Impl.class", "size": 1232, "crc": 1932437586}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$Api21Impl.class", "size": 1230, "crc": 1102609358}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat$ViewPropertyAnimatorListenerApi14.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat$ViewPropertyAnimatorListenerApi14.class", "size": 2348, "crc": 119070716}, {"key": "androidx/core/view/ViewPropertyAnimatorCompat.class", "name": "androidx/core/view/ViewPropertyAnimatorCompat.class", "size": 11376, "crc": 96606281}, {"key": "androidx/core/view/ViewPropertyAnimatorListener.class", "name": "androidx/core/view/ViewPropertyAnimatorListener.class", "size": 371, "crc": 1924827163}, {"key": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "name": "androidx/core/view/ViewPropertyAnimatorListenerAdapter.class", "size": 844, "crc": -799948790}, {"key": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "name": "androidx/core/view/ViewPropertyAnimatorUpdateListener.class", "size": 305, "crc": 1797357548}, {"key": "androidx/core/view/WindowCompat$Api16Impl.class", "name": "androidx/core/view/WindowCompat$Api16Impl.class", "size": 1221, "crc": 1361878302}, {"key": "androidx/core/view/WindowCompat$Api28Impl.class", "name": "androidx/core/view/WindowCompat$Api28Impl.class", "size": 856, "crc": -493321089}, {"key": "androidx/core/view/WindowCompat$Api30Impl.class", "name": "androidx/core/view/WindowCompat$Api30Impl.class", "size": 869, "crc": -1783732149}, {"key": "androidx/core/view/WindowCompat.class", "name": "androidx/core/view/WindowCompat.class", "size": 2206, "crc": -459789725}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$BoundsCompat.class", "size": 3016, "crc": -663239670}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback$DispatchMode.class", "size": 771, "crc": -19343690}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Callback.class", "size": 2066, "crc": 203743957}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl.class", "size": 1808, "crc": -2102166663}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$1.class", "size": 2587, "crc": 2103369541}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$2.class", "size": 1635, "crc": 55666051}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener$3.class", "size": 2016, "crc": 406754703}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21$Impl21OnApplyWindowInsetsListener.class", "size": 5763, "crc": 1650808278}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl21.class", "size": 9225, "crc": 1270709337}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30$ProxyCallback.class", "size": 4827, "crc": -1342839880}, {"key": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationCompat$Impl30.class", "size": 3826, "crc": -513361215}, {"key": "androidx/core/view/WindowInsetsAnimationCompat.class", "name": "androidx/core/view/WindowInsetsAnimationCompat.class", "size": 3503, "crc": -2127542909}, {"key": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControlListenerCompat.class", "size": 519, "crc": -1375959183}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl.class", "size": 1814, "crc": 1764069739}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat$Impl30.class", "size": 2580, "crc": -1670379821}, {"key": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "name": "androidx/core/view/WindowInsetsAnimationControllerCompat.class", "size": 2455, "crc": -1206962161}, {"key": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "name": "androidx/core/view/WindowInsetsCompat$Api21ReflectionHolder.class", "size": 3507, "crc": -2038718768}, {"key": "androidx/core/view/WindowInsetsCompat$Builder.class", "name": "androidx/core/view/WindowInsetsCompat$Builder.class", "size": 3668, "crc": 873871434}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl.class", "size": 3188, "crc": 952023470}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl20.class", "size": 3845, "crc": -1367789157}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl29.class", "size": 3157, "crc": -1700995255}, {"key": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$BuilderImpl30.class", "size": 1858, "crc": -362799567}, {"key": "androidx/core/view/WindowInsetsCompat$Impl.class", "name": "androidx/core/view/WindowInsetsCompat$Impl.class", "size": 4440, "crc": 587911647}, {"key": "androidx/core/view/WindowInsetsCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsCompat$Impl20.class", "size": 9787, "crc": -1633841618}, {"key": "androidx/core/view/WindowInsetsCompat$Impl21.class", "name": "androidx/core/view/WindowInsetsCompat$Impl21.class", "size": 2425, "crc": 202908659}, {"key": "androidx/core/view/WindowInsetsCompat$Impl28.class", "name": "androidx/core/view/WindowInsetsCompat$Impl28.class", "size": 2327, "crc": -559644749}, {"key": "androidx/core/view/WindowInsetsCompat$Impl29.class", "name": "androidx/core/view/WindowInsetsCompat$Impl29.class", "size": 2617, "crc": -220432070}, {"key": "androidx/core/view/WindowInsetsCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsCompat$Impl30.class", "size": 2255, "crc": 2128860632}, {"key": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "name": "androidx/core/view/WindowInsetsCompat$Type$InsetsType.class", "size": 719, "crc": 1917876408}, {"key": "androidx/core/view/WindowInsetsCompat$Type.class", "name": "androidx/core/view/WindowInsetsCompat$Type.class", "size": 2387, "crc": 1551158410}, {"key": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "name": "androidx/core/view/WindowInsetsCompat$TypeImpl30.class", "size": 1293, "crc": 350746220}, {"key": "androidx/core/view/WindowInsetsCompat.class", "name": "androidx/core/view/WindowInsetsCompat.class", "size": 10760, "crc": -1012434421}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl.class", "size": 2318, "crc": 816648880}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl20.class", "size": 5466, "crc": 1559047287}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl23.class", "size": 1566, "crc": -890883026}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl26.class", "size": 1571, "crc": 528197649}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30$1.class", "size": 2291, "crc": 1181988990}, {"key": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "name": "androidx/core/view/WindowInsetsControllerCompat$Impl30.class", "size": 7374, "crc": 1274271570}, {"key": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "name": "androidx/core/view/WindowInsetsControllerCompat$OnControllableInsetsChangedListener.class", "size": 494, "crc": 1407225033}, {"key": "androidx/core/view/WindowInsetsControllerCompat.class", "name": "androidx/core/view/WindowInsetsControllerCompat.class", "size": 4655, "crc": 1824644639}, {"key": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "name": "androidx/core/view/accessibility/AccessibilityClickableSpanCompat.class", "size": 1703, "crc": 1236826592}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$Api16Impl.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$Api16Impl.class", "size": 1330, "crc": -644995752}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$Api19Impl.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$Api19Impl.class", "size": 1114, "crc": 879220243}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat$ContentChangeType.class", "size": 718, "crc": 701609424}, {"key": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "name": "androidx/core/view/accessibility/AccessibilityEventCompat.class", "size": 4890, "crc": 821791346}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListener.class", "size": 477, "crc": 1509031422}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerCompat.class", "size": 811, "crc": -54021927}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$AccessibilityStateChangeListenerWrapper.class", "size": 1686, "crc": 575055372}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api19Impl.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$Api19Impl.class", "size": 2019, "crc": 48224768}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListener.class", "size": 384, "crc": -1837815159}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat$TouchExplorationStateChangeListenerWrapper.class", "size": 1809, "crc": -1204570496}, {"key": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "name": "androidx/core/view/accessibility/AccessibilityManagerCompat.class", "size": 4120, "crc": 1591658932}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$AccessibilityActionCompat.class", "size": 11016, "crc": 612551479}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api19Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api19Impl.class", "size": 944, "crc": 1815897629}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$Api33Impl.class", "size": 1521, "crc": -494226795}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionInfoCompat.class", "size": 2258, "crc": 673625354}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$CollectionItemInfoCompat.class", "size": 2524, "crc": 1782851247}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$RangeInfoCompat.class", "size": 1834, "crc": -194729922}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat$TouchDelegateInfoCompat.class", "size": 2545, "crc": 63167685}, {"key": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeInfoCompat.class", "size": 42763, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi16.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi16.class", "size": 2577, "crc": 231517480}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi19.class", "size": 1489, "crc": 211668058}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat$AccessibilityNodeProviderApi26.class", "size": 1697, "crc": -173442166}, {"key": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "name": "androidx/core/view/accessibility/AccessibilityNodeProviderCompat.class", "size": 2825, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api15Impl.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api15Impl.class", "size": 1332, "crc": -386561304}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api16Impl.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat$Api16Impl.class", "size": 1030, "crc": **********}, {"key": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "name": "androidx/core/view/accessibility/AccessibilityRecordCompat.class", "size": 8961, "crc": -**********}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$CommandArguments.class", "size": 1020, "crc": 955676878}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveAtGranularityArguments.class", "size": 1120, "crc": 759849651}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveHtmlArguments.class", "size": 1014, "crc": 230405818}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$MoveWindowArguments.class", "size": 1000, "crc": 173731620}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$ScrollToPositionArguments.class", "size": 1070, "crc": 36962913}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetProgressArguments.class", "size": 932, "crc": 1461547209}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetSelectionArguments.class", "size": 1022, "crc": 1847485403}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand$SetTextArguments.class", "size": 1024, "crc": 29560145}, {"key": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "name": "androidx/core/view/accessibility/AccessibilityViewCommand.class", "size": 1347, "crc": -1100584727}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api21Impl.class", "size": 2788, "crc": 435430794}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api24Impl.class", "size": 1224, "crc": 646661550}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat$Api33Impl.class", "size": 1371, "crc": -217179086}, {"key": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "name": "androidx/core/view/accessibility/AccessibilityWindowInfoCompat.class", "size": 7337, "crc": -1497075649}, {"key": "androidx/core/view/animation/PathInterpolatorApi14.class", "name": "androidx/core/view/animation/PathInterpolatorApi14.class", "size": 2333, "crc": 1168129793}, {"key": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "name": "androidx/core/view/animation/PathInterpolatorCompat$Api21Impl.class", "size": 1301, "crc": 1944549089}, {"key": "androidx/core/view/animation/PathInterpolatorCompat.class", "name": "androidx/core/view/animation/PathInterpolatorCompat.class", "size": 1682, "crc": 1722356442}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat$Api30Impl.class", "size": 1627, "crc": -1392162936}, {"key": "androidx/core/view/inputmethod/EditorInfoCompat.class", "name": "androidx/core/view/inputmethod/EditorInfoCompat.class", "size": 8110, "crc": -1532557623}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$1.class", "size": 1833, "crc": 623798447}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$2.class", "size": 1649, "crc": -1881400469}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$Api25Impl.class", "size": 1134, "crc": -2105217904}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat$OnCommitContentListener.class", "size": 525, "crc": 1476989102}, {"key": "androidx/core/view/inputmethod/InputConnectionCompat.class", "name": "androidx/core/view/inputmethod/InputConnectionCompat.class", "size": 11360, "crc": -457205703}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatApi25Impl.class", "size": 2094, "crc": 1456256422}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatBaseImpl.class", "size": 1773, "crc": 1624196168}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat$InputContentInfoCompatImpl.class", "size": 690, "crc": 1074534587}, {"key": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "name": "androidx/core/view/inputmethod/InputContentInfoCompat.class", "size": 2707, "crc": 877487393}, {"key": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "name": "androidx/core/widget/AutoScrollHelper$ClampedScroller.class", "size": 3002, "crc": -1881276443}, {"key": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "name": "androidx/core/widget/AutoScrollHelper$ScrollAnimationRunnable.class", "size": 1582, "crc": 1842928479}, {"key": "androidx/core/widget/AutoScrollHelper.class", "name": "androidx/core/widget/AutoScrollHelper.class", "size": 8621, "crc": 1783212020}, {"key": "androidx/core/widget/AutoSizeableTextView.class", "name": "androidx/core/widget/AutoSizeableTextView.class", "size": 1312, "crc": -593146071}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api14Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api14Impl.class", "size": 1800, "crc": -1441103580}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api16Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api16Impl.class", "size": 960, "crc": 1589534141}, {"key": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "name": "androidx/core/widget/CheckedTextViewCompat$Api21Impl.class", "size": 1853, "crc": -1977490155}, {"key": "androidx/core/widget/CheckedTextViewCompat.class", "name": "androidx/core/widget/CheckedTextViewCompat.class", "size": 2736, "crc": 1711554873}, {"key": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api21Impl.class", "size": 1729, "crc": 410159180}, {"key": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "name": "androidx/core/widget/CompoundButtonCompat$Api23Impl.class", "size": 875, "crc": -1778730663}, {"key": "androidx/core/widget/CompoundButtonCompat.class", "name": "androidx/core/widget/CompoundButtonCompat.class", "size": 3549, "crc": -53223216}, {"key": "androidx/core/widget/ContentLoadingProgressBar.class", "name": "androidx/core/widget/ContentLoadingProgressBar.class", "size": 3255, "crc": -251890886}, {"key": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api21Impl.class", "size": 827, "crc": 598038572}, {"key": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "name": "androidx/core/widget/EdgeEffectCompat$Api31Impl.class", "size": 1565, "crc": -499322779}, {"key": "androidx/core/widget/EdgeEffectCompat.class", "name": "androidx/core/widget/EdgeEffectCompat.class", "size": 2939, "crc": 1488300045}, {"key": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "name": "androidx/core/widget/ImageViewCompat$Api21Impl.class", "size": 1670, "crc": -1775682416}, {"key": "androidx/core/widget/ImageViewCompat.class", "name": "androidx/core/widget/ImageViewCompat.class", "size": 2780, "crc": -795067799}, {"key": "androidx/core/widget/ListPopupWindowCompat$Api19Impl.class", "name": "androidx/core/widget/ListPopupWindowCompat$Api19Impl.class", "size": 1053, "crc": -762289808}, {"key": "androidx/core/widget/ListPopupWindowCompat.class", "name": "androidx/core/widget/ListPopupWindowCompat.class", "size": 1404, "crc": 1888066202}, {"key": "androidx/core/widget/ListViewAutoScrollHelper.class", "name": "androidx/core/widget/ListViewAutoScrollHelper.class", "size": 1677, "crc": 1245945836}, {"key": "androidx/core/widget/ListViewCompat$Api19Impl.class", "name": "androidx/core/widget/ListViewCompat$Api19Impl.class", "size": 941, "crc": 854630232}, {"key": "androidx/core/widget/ListViewCompat.class", "name": "androidx/core/widget/ListViewCompat.class", "size": 1775, "crc": -747710768}, {"key": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "name": "androidx/core/widget/NestedScrollView$AccessibilityDelegate.class", "size": 3675, "crc": -876453870}, {"key": "androidx/core/widget/NestedScrollView$Api21Impl.class", "name": "androidx/core/widget/NestedScrollView$Api21Impl.class", "size": 762, "crc": -1946623239}, {"key": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "name": "androidx/core/widget/NestedScrollView$OnScrollChangeListener.class", "size": 422, "crc": -761668443}, {"key": "androidx/core/widget/NestedScrollView$SavedState$1.class", "name": "androidx/core/widget/NestedScrollView$SavedState$1.class", "size": 1315, "crc": 1047844467}, {"key": "androidx/core/widget/NestedScrollView$SavedState.class", "name": "androidx/core/widget/NestedScrollView$SavedState.class", "size": 1969, "crc": 1678177691}, {"key": "androidx/core/widget/NestedScrollView.class", "name": "androidx/core/widget/NestedScrollView.class", "size": 40820, "crc": 1477858200}, {"key": "androidx/core/widget/PopupMenuCompat$Api19Impl.class", "name": "androidx/core/widget/PopupMenuCompat$Api19Impl.class", "size": 925, "crc": -399152401}, {"key": "androidx/core/widget/PopupMenuCompat.class", "name": "androidx/core/widget/PopupMenuCompat.class", "size": 1080, "crc": 1505045219}, {"key": "androidx/core/widget/PopupWindowCompat$Api19Impl.class", "name": "androidx/core/widget/PopupWindowCompat$Api19Impl.class", "size": 930, "crc": -1406900133}, {"key": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "name": "androidx/core/widget/PopupWindowCompat$Api23Impl.class", "size": 1283, "crc": 1365337325}, {"key": "androidx/core/widget/PopupWindowCompat.class", "name": "androidx/core/widget/PopupWindowCompat.class", "size": 4403, "crc": 1116554972}, {"key": "androidx/core/widget/ScrollerCompat.class", "name": "androidx/core/widget/ScrollerCompat.class", "size": 3656, "crc": -54539407}, {"key": "androidx/core/widget/TextViewCompat$Api16Impl.class", "name": "androidx/core/widget/TextViewCompat$Api16Impl.class", "size": 992, "crc": -946691620}, {"key": "androidx/core/widget/TextViewCompat$Api17Impl.class", "name": "androidx/core/widget/TextViewCompat$Api17Impl.class", "size": 2384, "crc": 691649143}, {"key": "androidx/core/widget/TextViewCompat$Api23Impl.class", "name": "androidx/core/widget/TextViewCompat$Api23Impl.class", "size": 2274, "crc": -825272093}, {"key": "androidx/core/widget/TextViewCompat$Api24Impl.class", "name": "androidx/core/widget/TextViewCompat$Api24Impl.class", "size": 781, "crc": -552642780}, {"key": "androidx/core/widget/TextViewCompat$Api26Impl.class", "name": "androidx/core/widget/TextViewCompat$Api26Impl.class", "size": 1950, "crc": -595768270}, {"key": "androidx/core/widget/TextViewCompat$Api28Impl.class", "name": "androidx/core/widget/TextViewCompat$Api28Impl.class", "size": 1418, "crc": -1589582499}, {"key": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "name": "androidx/core/widget/TextViewCompat$AutoSizeTextType.class", "size": 662, "crc": -1792362665}, {"key": "androidx/core/widget/TextViewCompat$OreoCallback.class", "name": "androidx/core/widget/TextViewCompat$OreoCallback.class", "size": 7346, "crc": 2098359046}, {"key": "androidx/core/widget/TextViewCompat.class", "name": "androidx/core/widget/TextViewCompat.class", "size": 17774, "crc": -1821400327}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener$Api16Impl.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener$Api16Impl.class", "size": 1394, "crc": -250836076}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener$ApiImpl.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener$ApiImpl.class", "size": 1246, "crc": -1593218737}, {"key": "androidx/core/widget/TextViewOnReceiveContentListener.class", "name": "androidx/core/widget/TextViewOnReceiveContentListener.class", "size": 4009, "crc": -924307791}, {"key": "androidx/core/widget/TintableCheckedTextView.class", "name": "androidx/core/widget/TintableCheckedTextView.class", "size": 946, "crc": -75470643}, {"key": "androidx/core/widget/TintableCompoundButton.class", "name": "androidx/core/widget/TintableCompoundButton.class", "size": 709, "crc": -546728220}, {"key": "androidx/core/widget/TintableCompoundDrawablesView.class", "name": "androidx/core/widget/TintableCompoundDrawablesView.class", "size": 767, "crc": -2060620041}, {"key": "androidx/core/widget/TintableImageSourceView.class", "name": "androidx/core/widget/TintableImageSourceView.class", "size": 930, "crc": -1647897496}, {"key": "META-INF/androidx.core_core.version", "name": "META-INF/androidx.core_core.version", "size": 7, "crc": -688916209}, {"key": "META-INF/core_release.kotlin_module", "name": "META-INF/core_release.kotlin_module", "size": 24, "crc": 1613429616}]