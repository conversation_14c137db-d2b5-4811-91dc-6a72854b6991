<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.tv:tv-material:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f98b795102d6b4e4676c93db494360b5\transformed\tv-material-1.0.1\jars\classes.jar"
      resolved="androidx.tv:tv-material:1.0.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f98b795102d6b4e4676c93db494360b5\transformed\tv-material-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8a40b306b07846e8173f134e6942ef\transformed\activity-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9e8a40b306b07846e8173f134e6942ef\transformed\activity-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1a7c15b5704c5cd72721d62019d8ae9e\transformed\activity-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1a7c15b5704c5cd72721d62019d8ae9e\transformed\activity-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b4a5470bf7d2434f32ddaad0dfcd3e\transformed\activity-compose-1.10.1\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c5b4a5470bf7d2434f32ddaad0dfcd3e\transformed\activity-compose-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\84cf17b6fc2a0db58a02b081c470684f\transformed\animation-core\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\84cf17b6fc2a0db58a02b081c470684f\transformed\animation-core"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\97b17d1f4504b1d2ff9f5779d17f6aef\transformed\animation\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\97b17d1f4504b1d2ff9f5779d17f6aef\transformed\animation"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6b1246630bde30479f19fc5b4073d2e7\transformed\foundation-layout\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6b1246630bde30479f19fc5b4073d2e7\transformed\foundation-layout"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7c6efc5356a40c412603779ad33ac6f6\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7c6efc5356a40c412603779ad33ac6f6\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.7.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\9b30643a6152d77c751abb9b4d93b27b\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.7.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\9b30643a6152d77c751abb9b4d93b27b\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b04d3580225dc28344bdc6a991196ab\transformed\ui-unit\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\8b04d3580225dc28344bdc6a991196ab\transformed\ui-unit"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\b7521d88ff0347f3ff9f7fdb84fd13e3\transformed\ui-geometry\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\b7521d88ff0347f3ff9f7fdb84fd13e3\transformed\ui-geometry"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\2c973031b75abf18905f712047eeb7d1\transformed\ui-tooling-data\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\2c973031b75abf18905f712047eeb7d1\transformed\ui-tooling-data"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\66b66830d5d9cc3314f43e80f692feb2\transformed\ui-util\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\66b66830d5d9cc3314f43e80f692feb2\transformed\ui-util"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb98d58b483ebc3824619f3621b218c8\transformed\ui-text\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\fb98d58b483ebc3824619f3621b218c8\transformed\ui-text"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\557634109b7b09251114749fe3b7115c\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\557634109b7b09251114749fe3b7115c\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\c4c82e9dfe5df03207c372a77c2a15c6\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\c4c82e9dfe5df03207c372a77c2a15c6\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\25aa28cd1e9c53bed012b2f2cab0e8bf\transformed\ui-tooling-preview\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\25aa28cd1e9c53bed012b2f2cab0e8bf\transformed\ui-tooling-preview"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-annotation-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\75c74aa3e76f6fb3e4e4146530eaa29d\transformed\runtime-annotation\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-annotation-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\75c74aa3e76f6fb3e4e4146530eaa29d\transformed\runtime-annotation"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6edfda30089f11dbb58bff7cfe80a0e1\transformed\core-1.13.0\jars\classes.jar"
      resolved="androidx.core:core:1.13.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6edfda30089f11dbb58bff7cfe80a0e1\transformed\core-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\32a5f01e7290c5c64acd530548caf4e6\transformed\lifecycle-livedata-core-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\32a5f01e7290c5c64acd530548caf4e6\transformed\lifecycle-livedata-core-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\1c90e455b4a9b864a99b0eab5a1c9e54\transformed\lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\1c90e455b4a9b864a99b0eab5a1c9e54\transformed\lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\80a6fdf120df4fd81dbae087e0b96087\transformed\lifecycle-runtime-ktx\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\80a6fdf120df4fd81dbae087e0b96087\transformed\lifecycle-runtime-ktx"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\58286070e39d48177abd2ac4b868b502\transformed\lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\58286070e39d48177abd2ac4b868b502\transformed\lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6eabe5f630da04f1d96266e2e03a9c9a\transformed\lifecycle-viewmodel-ktx-2.9.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6eabe5f630da04f1d96266e2e03a9c9a\transformed\lifecycle-viewmodel-ktx-2.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e30a37f3727b83582693c9169e9f9099\transformed\lifecycle-runtime-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-compose-android:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e30a37f3727b83582693c9169e9f9099\transformed\lifecycle-runtime-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\4d784389198144cc34e7df8c8957f981\transformed\runtime\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\4d784389198144cc34e7df8c8957f981\transformed\runtime"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\07ba624be74b7a6b6d1655f253db90ec\transformed\runtime-saveable\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\07ba624be74b7a6b6d1655f253db90ec\transformed\runtime-saveable"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\987c5533b1b4e11605b3d12af1048f07\transformed\savedstate-ktx-1.3.0\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\987c5533b1b4e11605b3d12af1048f07\transformed\savedstate-ktx-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-compose-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7db23c47a20658d48456fa3336e41e30\transformed\savedstate-compose-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-compose-android:1.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7db23c47a20658d48456fa3336e41e30\transformed\savedstate-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-android:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6d0716a22b8ebac98718228fb9440a\transformed\savedstate-release\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-android:1.3.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6f6d0716a22b8ebac98718228fb9440a\transformed\savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.9.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.9.0\ab739bccdb3541983385af169565c7c035d897e7\lifecycle-common-jvm-2.9.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.9.0"
      provided="true"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d8b0c7bf16ed86e318f3d68af2cc071b\transformed\lifecycle-viewmodel-savedstate-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate-android:2.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d8b0c7bf16ed86e318f3d68af2cc071b\transformed\lifecycle-viewmodel-savedstate-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e400a9316312d345d753a9a3fbfc3ce\transformed\core-ktx-1.13.0\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6e400a9316312d345d753a9a3fbfc3ce\transformed\core-ktx-1.13.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f49d10cdba556b797e8c799fce9e666d\transformed\annotation-experimental-1.4.1\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f49d10cdba556b797e8c799fce9e666d\transformed\annotation-experimental-1.4.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.8.1\bb0e192bd7c2b6b8217440d36e9758e377e450\kotlinx-coroutines-core-jvm-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.8.1"
      provided="true"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.8.1\73e2acdd18df99dd4849d99f188dff529fc0afe0\kotlinx-coroutines-android-1.8.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1"
      provided="true"/>
  <library
      name="androidx.core:core-viewtree:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f7964333283afe3a8ff90f54b2e54397\transformed\core-viewtree-1.0.0\jars\classes.jar"
      resolved="androidx.core:core-viewtree:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f7964333283afe3a8ff90f54b2e54397\transformed\core-viewtree-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bc01c317dec743d2fe02f0d24e62dc58\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bc01c317dec743d2fe02f0d24e62dc58\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.5.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.5.0\7ba2c69414d46ebc2dd76598bdd0a75c54281a57\collection-jvm-1.5.0.jar"
      resolved="androidx.collection:collection-jvm:1.5.0"
      provided="true"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"
      provided="true"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"
      provided="true"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.7.3\1f226780b845ff9206474c05159245d861556249\kotlinx-serialization-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.7.3"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"
      provided="true"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"
      provided="true"/>
  <library
      name="org.jspecify:jspecify:1.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jspecify\jspecify\1.0.0\7425a601c1c7ec76645a78d22b8c6a627edee507\jspecify-1.0.0.jar"
      resolved="org.jspecify:jspecify:1.0.0"
      provided="true"/>
</libraries>
