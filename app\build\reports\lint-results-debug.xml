<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.12.1">

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.17.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="coreKtx = &quot;1.10.1&quot;"
        errorLine2="          ~~~~~~~~">
        <location
            file="E:\1-test\aj-tv-player\gradle\libs.versions.toml"
            line="4"
            column="11"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.10"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="kotlin = &quot;2.0.21&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="E:\1-test\aj-tv-player\gradle\libs.versions.toml"
            line="3"
            column="10"/>
    </issue>

    <issue
        id="NewerVersionAvailable"
        severity="Warning"
        message="A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.10"
        category="Correctness"
        priority="4"
        summary="Newer Library Versions Available"
        explanation="This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the `GradleDependency` check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also **much** slower."
        errorLine1="kotlin = &quot;2.0.21&quot;"
        errorLine2="         ~~~~~~~~">
        <location
            file="E:\1-test\aj-tv-player\gradle\libs.versions.toml"
            line="3"
            column="10"/>
    </issue>

</issues>
