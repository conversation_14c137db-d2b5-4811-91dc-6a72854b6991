# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "2.0.21"
  }
  digests {
    sha256: "\363\034\305?\020Z~H\300\223h;\275T7V\035\0223\222\005\023wKG\b\005d\033\355\274\t"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "2.0.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2025.08.00"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.9.0"
  }
  digests {
    sha256: "jp6\240{\3267x\245\r\"G\023\245\003\321\036:zB%\233\\T\250\244m\240\235.^\327"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.5.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.5.0"
  }
  digests {
    sha256: "p\263Y$\344\272\274\337\3727\320\345u\356\003\234V\242\331q#4&$\304\213`23pCA"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.9.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.9.1"
  }
  digests {
    sha256: "\03649\027\353\362{\251o\344\334R\261\312\327\3752\2678\373\3065[\266\315[;0]r\022\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.5.0"
  }
  digests {
    sha256: "\223\233J\206\227d\016w\330>N\213LJ\235\030\370\023\001\230\305\226\021\224\257\215\331\333\235\307\303S"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-annotation"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-annotation-android"
    version: "1.9.0"
  }
  digests {
    sha256: "?\272\235k\3450}\231\204\003\003\217m\372\243\376\335\222LV\030\313E\244\237V\245\277b\202P\235"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.9.0"
  }
  digests {
    sha256: "&\357\245EvSBq\326\\[\337\223\303N\351\310\235\276\3124Xss\v\217\032\311#\332?\252"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose-android"
    version: "2.9.0"
  }
  digests {
    sha256: "_h\274\352\f\361\332\254S \242\375W\025r7\343\tE\317~e\206q\177\274qK5\263.o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\324\":\346\250Dvw&}\377\"\206;mi\210\312h\264\322|\246\327K\201\004k\004yBW"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-viewtree"
    version: "1.0.0"
  }
  digests {
    sha256: "\334\033g\215X\353\317+\372\025\207\276h\377\202f\224\316=\"\022Q\271\3570\324\324\263b\227\346\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-jvm"
    version: "2.9.0"
  }
  digests {
    sha256: "7\330\233!\001\360t\254l&\t\027\332\273\030V\ad^\342\000\2520\030\307\305\275\347\016\334\361\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\363\324\365\336\0349\033\274\302\017;45\314\272\300\023R\036v\266\220-}Yc^\301\\\037y~"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.8.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.8.1"
  }
  digests {
    sha256: "\2414\332\317Nex\262\2332\351z\245\005H\320\234\264^<\263U\034\347z\302~U\342e\330\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jspecify"
    artifactId: "jspecify"
    version: "1.0.0"
  }
  digests {
    sha256: "\037\255nk\347Uw\201\344\3237)\324\232\341\315\310\375\332o\344w\273\f\306\214\343Q\352\375\373\253"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.9.0"
  }
  digests {
    sha256: "\301\\\351r\314(\223jYj\234\322V\263/\"v\212a\325\335\201\017\245k\251ER*\016\347\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.2.0"
  }
  digests {
    sha256: "o\252\2209\r\037\333\360\255\271\251\233\371\235\346{\224\306\306\363Z\352\225\020Y:\235\027\22776\242"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx-android"
    version: "2.9.0"
  }
  digests {
    sha256: "3?\242\316m\267\n\303r\267\221\337\353\251\234\002\300\331{\032\213J\264\250MGbFm\022\262\a"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\372\235u\021\250\376\r^\334S\t\227\006\326O7b\035\317\353\237,Usi\355\203d_\310z#"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "\257B\375\204\364\345+D\334\253\257\236\317\224\022\005S\222\233\301\240Y\276\262$\n\236\301\333\356\201O"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate-android"
    version: "2.9.0"
  }
  digests {
    sha256: "\3336x8qnU\206h#\306\336\355b\374\354#\374\344\207\277*\217\245e\243o\024\232\263\321\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.15.0"
  }
  digests {
    sha256: "\307,\227\377J20\213\312\363\006\036\352]\3373E\\\344\023\316\344\252\035\\\202D\fWY\324\274"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.15.0"
  }
  digests {
    sha256: "C+\205\241\227@v\341KH~\316J(\305\232\204\361\271\357\303\374\213\347,\327\360]2\005^Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.9.0"
  }
  digests {
    sha256: "d\371kR\307\b\225\200\1770 \342\350\372\021\363\354-\251\301W\366*\256!\242\311\323\264\204\247G"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-android"
    version: "1.3.0"
  }
  digests {
    sha256: "!\vi\t\273\325\025\333\364\025\220\\\273\"\017\312\223\250\177\245F\203\217u@w\353\310\340H\205\220"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\360\255\336E\206ADGS\205\317J\247\340\267\376\262\177a\374\371G&e\355\230\314\227\033\006\261\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.7.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-compose"
    version: "1.3.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-compose-android"
    version: "1.3.0"
  }
  digests {
    sha256: "\253qV,\023\200\031\255`\204\334\217\306\220\257l\2715\233\376\'C\247\220b\006[(\275\357|n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.3.0"
  }
  digests {
    sha256: "E\305\366A(@\365\r\347\277}\233\034J\214\201\352#h2Hz\0218\3132\v\214\231t\026\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\312G(\035\221\201#\nx\370\243\035~WE\242\225\313\315{}\2549|\323\3251\230\315\221\305\246"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\023\223\310 \351\256\006\033X\252\335\2604\000?\251\240\340\027\206\222\212\306(\242\341-(&\373\336C"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.10.1"
  }
  digests {
    sha256: "\363\226\365\215\275w,\006[\2076\226\267J=M\buT\263vb\200\346;I\262]\273S\253\026"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.10.1"
  }
  digests {
    sha256: "\266+R\214\221}\351\276I~\266\370\2100\031| \351\322\022g\303\221la4\222\345\356\203}M"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.10.1"
  }
  digests {
    sha256: "\370\232\361\262l\314\203F48|\205|-\324\364eM7e\b\220\003\234R|/\355\a\333ja"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.9.0"
  }
  digests {
    sha256: "A\305\372B\311\017\302KMen\203\177\203\316\376\004\020\254\307\321\024\255x\306+\254E\t\212\306\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\223v\313\321\335\360e^q\005\210f7\352\024QF\037\317\323\243\316\356\317\376\314\352\356\273SA\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\320\025\315\240l\333\003\253Tq\2469`\344$\224\036_!~o\207\3439\253\327\316\3569) \232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\333\355\241\252\352\003\273\322\332\037M\361\001\2004n2\313\350\245\2029\242O\233\213\214\262B\002\375\214"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.9.0"
  }
  digests {
    sha256: "&\005\342@f%\377\0038\310\210\223}\242,Cz\366S\242\364*\372\332\312\255!tq\177^\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.4.0"
  }
  digests {
    sha256: "C?\353\323CJEfqv\307jd\363\362\005\312c5\246\265D\305\265\325\177%\243\2127RB"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.graphics"
    artifactId: "graphics-path"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\244\003+my\263Q\360\265\232\324\265\200\355\333\271B>\026R\367\311X\203\006\207\361\356\342\354\003"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.9.0"
  }
  digests {
    sha256: "i\033\244c\321\340\245\263-\224\360D\215i%r\372\255\367\241\n\234t`\257A\262\3040\263g\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.9.0"
  }
  digests {
    sha256: "x\216\352\300\001Y\252\355\331\177hPg\317,\207\313\361\024\302\027Fs&S\227\0160U#\023\""
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.9.0"
  }
  digests {
    sha256: "_\220\372c\247\317Rm\336\016\323~;e\275\030gI\211\000nK\353m8\005l\2056D\001\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.9.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.9.0"
  }
  digests {
    sha256: "\223T\264\025\024\210@\314\272\362\026\2651a\377,U\314\276\311\n\224\323Fn^W;KA\363N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.7.8"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.7.8"
  }
  digests {
    sha256: "3,\006\262^f,\304\027\373\b~v\270\372\245\313$\237I\222\377\2436\000\204\243\324\253\210\"\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tv"
    artifactId: "tv-material"
    version: "1.0.1"
  }
  digests {
    sha256: "6\313Gk\224\r\\\354\372\245]\257\353+\331\351F\213\204\005\272W\233\366l\243>RQ\340R\317"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 56
  library_dep_index: 14
  library_dep_index: 58
  library_dep_index: 79
  library_dep_index: 77
  library_dep_index: 83
  library_dep_index: 85
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 5
  library_dep_index: 57
  library_dep_index: 15
  library_dep_index: 59
  library_dep_index: 80
  library_dep_index: 78
  library_dep_index: 84
  library_dep_index: 86
  library_dep_index: 69
  library_dep_index: 73
  library_dep_index: 67
  library_dep_index: 12
  library_dep_index: 64
  library_dep_index: 70
  library_dep_index: 81
  library_dep_index: 13
  library_dep_index: 65
  library_dep_index: 71
  library_dep_index: 82
}
library_dependencies {
  library_index: 4
  library_dep_index: 5
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 6
  library_dep_index: 0
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 0
  library_dep_index: 11
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
}
library_dependencies {
  library_index: 10
  library_dep_index: 0
}
library_dependencies {
  library_index: 11
  library_dep_index: 7
  library_dep_index: 7
}
library_dependencies {
  library_index: 12
  library_dep_index: 13
}
library_dependencies {
  library_index: 13
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 0
}
library_dependencies {
  library_index: 14
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 4
  library_dep_index: 16
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 12
  library_dep_index: 0
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
}
library_dependencies {
  library_index: 17
  library_dep_index: 9
  library_dep_index: 4
  library_dep_index: 18
  library_dep_index: 33
  library_dep_index: 23
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 46
}
library_dependencies {
  library_index: 18
  library_dep_index: 19
}
library_dependencies {
  library_index: 19
  library_dep_index: 9
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 55
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 23
  library_dep_index: 30
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 20
  library_dep_index: 9
}
library_dependencies {
  library_index: 21
  library_dep_index: 9
  library_dep_index: 20
}
library_dependencies {
  library_index: 22
  library_dep_index: 0
  library_dep_index: 0
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 9
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
  library_dep_index: 27
  library_dep_index: 0
}
library_dependencies {
  library_index: 27
  library_dep_index: 28
  library_dep_index: 26
  library_dep_index: 25
}
library_dependencies {
  library_index: 28
  library_dep_index: 25
  library_dep_index: 27
  library_dep_index: 0
}
library_dependencies {
  library_index: 30
  library_dep_index: 9
  library_dep_index: 18
  library_dep_index: 31
  library_dep_index: 0
  library_dep_index: 23
  library_dep_index: 18
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 31
  library_dep_index: 9
  library_dep_index: 32
}
library_dependencies {
  library_index: 32
  library_dep_index: 9
}
library_dependencies {
  library_index: 33
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 9
  library_dep_index: 18
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 16
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 35
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 9
  library_dep_index: 22
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 23
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 37
  library_dep_index: 35
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 23
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 46
}
library_dependencies {
  library_index: 38
  library_dep_index: 39
}
library_dependencies {
  library_index: 39
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 46
  library_dep_index: 35
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 49
  library_dep_index: 23
  library_dep_index: 46
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 0
}
library_dependencies {
  library_index: 40
  library_dep_index: 9
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 40
}
library_dependencies {
  library_index: 42
  library_dep_index: 9
  library_dep_index: 43
}
library_dependencies {
  library_index: 44
  library_dep_index: 9
}
library_dependencies {
  library_index: 45
  library_dep_index: 9
  library_dep_index: 7
}
library_dependencies {
  library_index: 46
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 29
  library_dep_index: 23
  library_dep_index: 30
  library_dep_index: 18
  library_dep_index: 16
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 38
  library_dep_index: 0
}
library_dependencies {
  library_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 9
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 49
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 0
}
library_dependencies {
  library_index: 49
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 51
  library_dep_index: 0
}
library_dependencies {
  library_index: 51
  library_dep_index: 50
  library_dep_index: 49
}
library_dependencies {
  library_index: 52
  library_dep_index: 53
}
library_dependencies {
  library_index: 53
  library_dep_index: 9
  library_dep_index: 4
  library_dep_index: 40
  library_dep_index: 47
  library_dep_index: 47
  library_dep_index: 54
}
library_dependencies {
  library_index: 54
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 47
  library_dep_index: 52
  library_dep_index: 0
}
library_dependencies {
  library_index: 55
  library_dep_index: 9
  library_dep_index: 42
  library_dep_index: 31
  library_dep_index: 43
}
library_dependencies {
  library_index: 56
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 9
  library_dep_index: 4
  library_dep_index: 2
  library_dep_index: 58
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 2
  library_dep_index: 64
  library_dep_index: 70
}
library_dependencies {
  library_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 63
  library_dep_index: 7
  library_dep_index: 7
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 41
  library_dep_index: 76
  library_dep_index: 74
  library_dep_index: 16
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 55
  library_dep_index: 52
  library_dep_index: 54
  library_dep_index: 0
  library_dep_index: 28
  library_dep_index: 25
  library_dep_index: 29
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 56
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 60
  library_dep_index: 61
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 54
  library_dep_index: 61
  library_dep_index: 62
}
library_dependencies {
  library_index: 61
  library_dep_index: 9
  library_dep_index: 40
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 18
  library_dep_index: 35
  library_dep_index: 38
  library_dep_index: 55
  library_dep_index: 47
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 62
  library_dep_index: 60
  library_dep_index: 0
}
library_dependencies {
  library_index: 62
  library_dep_index: 60
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 58
  library_dep_index: 40
  library_dep_index: 23
  library_dep_index: 18
  library_dep_index: 35
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 60
  library_dep_index: 0
  library_dep_index: 61
}
library_dependencies {
  library_index: 63
  library_dep_index: 41
}
library_dependencies {
  library_index: 64
  library_dep_index: 65
}
library_dependencies {
  library_index: 65
  library_dep_index: 9
  library_dep_index: 4
  library_dep_index: 66
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 56
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 0
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 56
  library_dep_index: 70
  library_dep_index: 0
}
library_dependencies {
  library_index: 68
  library_dep_index: 69
}
library_dependencies {
  library_index: 69
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 4
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 41
  library_dep_index: 75
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 64
  library_dep_index: 72
  library_dep_index: 56
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 0
}
library_dependencies {
  library_index: 70
  library_dep_index: 71
}
library_dependencies {
  library_index: 71
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 11
  library_dep_index: 4
  library_dep_index: 64
  library_dep_index: 66
  library_dep_index: 0
  library_dep_index: 58
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 56
  library_dep_index: 66
  library_dep_index: 0
}
library_dependencies {
  library_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 4
  library_dep_index: 14
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 41
  library_dep_index: 74
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 58
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 56
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 0
}
library_dependencies {
  library_index: 74
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 41
  library_dep_index: 30
  library_dep_index: 31
}
library_dependencies {
  library_index: 75
  library_dep_index: 41
  library_dep_index: 0
}
library_dependencies {
  library_index: 76
  library_dep_index: 40
  library_dep_index: 0
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 79
  library_dep_index: 83
  library_dep_index: 4
  library_dep_index: 58
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 41
  library_dep_index: 74
  library_dep_index: 0
  library_dep_index: 83
  library_dep_index: 0
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 81
  library_dep_index: 83
  library_dep_index: 4
  library_dep_index: 58
  library_dep_index: 64
  library_dep_index: 68
  library_dep_index: 66
  library_dep_index: 0
  library_dep_index: 81
  library_dep_index: 0
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 9
  library_dep_index: 7
  library_dep_index: 4
  library_dep_index: 58
  library_dep_index: 68
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 79
  library_dep_index: 0
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
}
library_dependencies {
  library_index: 84
  library_dep_index: 9
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 81
  library_dep_index: 4
  library_dep_index: 58
  library_dep_index: 70
  library_dep_index: 66
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 77
  library_dep_index: 0
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 58
  library_dep_index: 0
  library_dep_index: 2
}
library_dependencies {
  library_index: 87
  library_dep_index: 9
  library_dep_index: 79
  library_dep_index: 77
  library_dep_index: 83
  library_dep_index: 85
  library_dep_index: 4
  library_dep_index: 58
  library_dep_index: 68
  library_dep_index: 72
  library_dep_index: 66
  library_dep_index: 55
  library_dep_index: 0
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 3
  dependency_index: 40
  dependency_index: 62
  dependency_index: 4
  dependency_index: 56
  dependency_index: 87
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
