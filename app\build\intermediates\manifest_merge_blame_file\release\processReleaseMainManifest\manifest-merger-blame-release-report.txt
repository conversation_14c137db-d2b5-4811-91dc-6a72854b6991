1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aj.aj_tv_player"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <uses-feature
11-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:4:5-6:35
12        android:name="android.software.leanback"
12-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:5:9-49
13        android:required="true" />
13-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:6:9-32
14    <uses-feature
14-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.hardware.touchscreen"
15-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:9:9-52
16        android:required="false" />
16-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:10:9-33
17
18    <permission
18-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\71219f197f28274676b3874ecf964996\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\71219f197f28274676b3874ecf964996\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\71219f197f28274676b3874ecf964996\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\71219f197f28274676b3874ecf964996\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\71219f197f28274676b3874ecf964996\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:12:5-31:19
25        android:allowBackup="true"
25-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:13:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\71219f197f28274676b3874ecf964996\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
27        android:banner="@mipmap/ic_launcher"
27-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:18:9-45
28        android:extractNativeLibs="true"
29        android:icon="@mipmap/ic_launcher"
29-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:15:9-41
31        android:supportsRtl="true"
31-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:16:9-35
32        android:theme="@style/Theme.AjTvPlayer" >
32-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:17:9-48
33        <activity
33-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:20:9-29:20
34            android:name="com.aj.aj_tv_player.MainActivity"
34-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:21:13-41
35            android:exported="true"
35-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:22:13-36
36            android:theme="@style/Theme.AjTvPlayer" >
36-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:23:13-52
37            <intent-filter>
37-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:24:13-28:29
38                <action android:name="android.intent.action.MAIN" />
38-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:25:17-69
38-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:25:25-66
39
40                <category android:name="android.intent.category.LAUNCHER" />
40-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:26:17-77
40-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:26:27-74
41                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
41-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:27:17-86
41-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:27:27-83
42            </intent-filter>
43        </activity>
44
45        <provider
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a95a57b0ae7e298393d93a126dce4147\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
46            android:name="androidx.startup.InitializationProvider"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a95a57b0ae7e298393d93a126dce4147\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
47            android:authorities="com.aj.aj_tv_player.androidx-startup"
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a95a57b0ae7e298393d93a126dce4147\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
48            android:exported="false" >
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a95a57b0ae7e298393d93a126dce4147\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
49            <meta-data
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a95a57b0ae7e298393d93a126dce4147\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.emoji2.text.EmojiCompatInitializer"
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a95a57b0ae7e298393d93a126dce4147\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
51                android:value="androidx.startup" />
51-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\a95a57b0ae7e298393d93a126dce4147\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cde46ad3686660f7eea33d6f0916eb64\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
53                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
53-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cde46ad3686660f7eea33d6f0916eb64\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
54                android:value="androidx.startup" />
54-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cde46ad3686660f7eea33d6f0916eb64\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
55            <meta-data
55-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
56                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
56-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
57                android:value="androidx.startup" />
57-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
58        </provider>
59
60        <receiver
60-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
61            android:name="androidx.profileinstaller.ProfileInstallReceiver"
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
62            android:directBootAware="false"
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
63            android:enabled="true"
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
64            android:exported="true"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
65            android:permission="android.permission.DUMP" >
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
67                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
70                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
73                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
76                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b31af800c4990a7f71071c53bf9b227\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
77            </intent-filter>
78        </receiver>
79    </application>
80
81</manifest>
