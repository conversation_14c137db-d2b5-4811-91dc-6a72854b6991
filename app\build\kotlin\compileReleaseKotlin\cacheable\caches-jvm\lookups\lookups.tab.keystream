  AjtvplayerTheme android.app.Activity  
HomeScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  androidx android.app.Activity  colors android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  AjtvplayerTheme android.content.Context  
HomeScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  androidx android.content.Context  colors android.content.Context  fillMaxSize android.content.Context  
setContent android.content.Context  AjtvplayerTheme android.content.ContextWrapper  
HomeScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  androidx android.content.ContextWrapper  colors android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  
setContent android.content.ContextWrapper  Bundle 
android.os  AjtvplayerTheme  android.view.ContextThemeWrapper  
HomeScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  colors  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  AjtvplayerTheme #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  
HomeScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  colors #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  AjtvplayerTheme -androidx.activity.ComponentActivity.Companion  
HomeScreen -androidx.activity.ComponentActivity.Companion  
MaterialTheme -androidx.activity.ComponentActivity.Companion  Modifier -androidx.activity.ComponentActivity.Companion  Surface -androidx.activity.ComponentActivity.Companion  androidx -androidx.activity.ComponentActivity.Companion  colors -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  
background androidx.compose.foundation  border androidx.compose.foundation  	focusable androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  	MediaCard "androidx.compose.foundation.layout  	MediaItem "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  	getOrNull "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableIntStateOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberLazyListState "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  shape "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  widthIn "androidx.compose.foundation.layout  Bottom .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  
HomeScreen +androidx.compose.foundation.layout.BoxScope  LazyRow +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  	MediaCard +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  itemsIndexed +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  rememberLazyListState +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  widthIn +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Brush .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  	MediaCard .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  itemsIndexed .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  rememberLazyListState .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  verticalGradient .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  widthIn .androidx.compose.foundation.layout.ColumnScope  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  
LazyListState  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  itemsIndexed  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  	MediaCard .androidx.compose.foundation.lazy.LazyItemScope  	MediaCard .androidx.compose.foundation.lazy.LazyListScope  itemsIndexed .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape !androidx.compose.foundation.shape  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Brush androidx.compose.runtime  CardDefaults androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  
FontWeight androidx.compose.runtime  Int androidx.compose.runtime  LazyRow androidx.compose.runtime  List androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  	MediaCard androidx.compose.runtime  	MediaItem androidx.compose.runtime  Modifier androidx.compose.runtime  MutableIntState androidx.compose.runtime  MutableState androidx.compose.runtime  Spacer androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  Unit androidx.compose.runtime  
background androidx.compose.runtime  colors androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  	getOrNull androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableIntStateOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberLazyListState androidx.compose.runtime  setValue androidx.compose.runtime  shape androidx.compose.runtime  spacedBy androidx.compose.runtime  verticalGradient androidx.compose.runtime  width androidx.compose.runtime  widthIn androidx.compose.runtime  setValue (androidx.compose.runtime.MutableIntState  setValue %androidx.compose.runtime.MutableState  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction3 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  border androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  	focusable androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  onFocusChanged androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  scale androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  widthIn androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  widthIn &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  scale androidx.compose.ui.draw  
FocusState androidx.compose.ui.focus  onFocusChanged androidx.compose.ui.focus  	isFocused $androidx.compose.ui.focus.FocusState  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  ContentScale androidx.compose.ui.layout  	TextStyle androidx.compose.ui.text  
FontWeight androidx.compose.ui.text.font  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AjtvplayerTheme #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  colors #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  Card androidx.tv.material3  
CardColors androidx.tv.material3  CardDefaults androidx.tv.material3  	CardShape androidx.tv.material3  ColorScheme androidx.tv.material3  
MaterialTheme androidx.tv.material3  Surface androidx.tv.material3  
SurfaceColors androidx.tv.material3  SurfaceDefaults androidx.tv.material3  Text androidx.tv.material3  
Typography androidx.tv.material3  darkColorScheme androidx.tv.material3  colors "androidx.tv.material3.CardDefaults  shape "androidx.tv.material3.CardDefaults  
background !androidx.tv.material3.ColorScheme  surface !androidx.tv.material3.ColorScheme  colorScheme #androidx.tv.material3.MaterialTheme  
typography #androidx.tv.material3.MaterialTheme  colors %androidx.tv.material3.SurfaceDefaults  	bodyLarge  androidx.tv.material3.Typography  
displayMedium  androidx.tv.material3.Typography  AjtvplayerTheme com.aj.aj_tv_player  	Alignment com.aj.aj_tv_player  Arrangement com.aj.aj_tv_player  Boolean com.aj.aj_tv_player  Box com.aj.aj_tv_player  Brush com.aj.aj_tv_player  Bundle com.aj.aj_tv_player  CardDefaults com.aj.aj_tv_player  Color com.aj.aj_tv_player  Column com.aj.aj_tv_player  ComponentActivity com.aj.aj_tv_player  
Composable com.aj.aj_tv_player  DefaultPreview com.aj.aj_tv_player  
FontWeight com.aj.aj_tv_player  
HomeScreen com.aj.aj_tv_player  
ImmersiveList com.aj.aj_tv_player  Int com.aj.aj_tv_player  LazyRow com.aj.aj_tv_player  List com.aj.aj_tv_player  MainActivity com.aj.aj_tv_player  
MaterialTheme com.aj.aj_tv_player  	MediaCard com.aj.aj_tv_player  	MediaItem com.aj.aj_tv_player  Modifier com.aj.aj_tv_player  Preview com.aj.aj_tv_player  Spacer com.aj.aj_tv_player  String com.aj.aj_tv_player  Surface com.aj.aj_tv_player  Text com.aj.aj_tv_player  	TextAlign com.aj.aj_tv_player  Unit com.aj.aj_tv_player  androidx com.aj.aj_tv_player  
background com.aj.aj_tv_player  colors com.aj.aj_tv_player  fillMaxSize com.aj.aj_tv_player  fillMaxWidth com.aj.aj_tv_player  	getOrNull com.aj.aj_tv_player  getValue com.aj.aj_tv_player  height com.aj.aj_tv_player  let com.aj.aj_tv_player  listOf com.aj.aj_tv_player  mutableIntStateOf com.aj.aj_tv_player  mutableStateOf com.aj.aj_tv_player  padding com.aj.aj_tv_player  provideDelegate com.aj.aj_tv_player  remember com.aj.aj_tv_player  rememberLazyListState com.aj.aj_tv_player  setValue com.aj.aj_tv_player  shape com.aj.aj_tv_player  spacedBy com.aj.aj_tv_player  verticalGradient com.aj.aj_tv_player  width com.aj.aj_tv_player  widthIn com.aj.aj_tv_player  AjtvplayerTheme  com.aj.aj_tv_player.MainActivity  
HomeScreen  com.aj.aj_tv_player.MainActivity  
MaterialTheme  com.aj.aj_tv_player.MainActivity  Modifier  com.aj.aj_tv_player.MainActivity  Surface  com.aj.aj_tv_player.MainActivity  androidx  com.aj.aj_tv_player.MainActivity  colors  com.aj.aj_tv_player.MainActivity  fillMaxSize  com.aj.aj_tv_player.MainActivity  
setContent  com.aj.aj_tv_player.MainActivity  description com.aj.aj_tv_player.MediaItem  let com.aj.aj_tv_player.MediaItem  title com.aj.aj_tv_player.MediaItem  AjtvplayerTheme com.aj.aj_tv_player.ui.theme  
Composable com.aj.aj_tv_player.ui.theme  DarkColorScheme com.aj.aj_tv_player.ui.theme  Unit com.aj.aj_tv_player.ui.theme  	Function0 kotlin  	Function1 kotlin  let kotlin  invoke kotlin.Function1  List kotlin.collections  	getOrNull kotlin.collections  listOf kotlin.collections  	getOrNull kotlin.collections.List  KMutableProperty0 kotlin.reflect  	getOrNull kotlin.text  delay kotlinx.coroutines                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  