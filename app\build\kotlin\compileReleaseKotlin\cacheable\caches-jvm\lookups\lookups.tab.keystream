  AjTvPlayerTheme android.app.Activity  
HomeScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  AjTvPlayerTheme android.content.Context  
HomeScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  fillMaxSize android.content.Context  
setContent android.content.Context  AjTvPlayerTheme android.content.ContextWrapper  
HomeScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  
setContent android.content.ContextWrapper  Bundle 
android.os  AjTvPlayerTheme  android.view.ContextThemeWrapper  
HomeScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  AjTvPlayerTheme #androidx.activity.ComponentActivity  
HomeScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  FeaturedContent "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Movie "androidx.compose.foundation.layout  	MovieCard "androidx.compose.foundation.layout  MovieRow "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  
SampleData "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  TextOverflow "androidx.compose.foundation.layout  TvLazyColumn "androidx.compose.foundation.layout  	TvLazyRow "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  horizontalGradient "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  FeaturedContent +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  MovieRow +androidx.compose.foundation.layout.BoxScope  
PaddingValues +androidx.compose.foundation.layout.BoxScope  
SampleData +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  TextOverflow +androidx.compose.foundation.layout.BoxScope  TvLazyColumn +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Brush .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  	MovieCard .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  	TvLazyRow .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  horizontalGradient .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  verticalGradient .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  RoundedCornerShape !androidx.compose.foundation.shape  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  darkColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  
background &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Box androidx.compose.runtime  Brush androidx.compose.runtime  CardDefaults androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  FeaturedContent androidx.compose.runtime  
FontWeight androidx.compose.runtime  List androidx.compose.runtime  Modifier androidx.compose.runtime  Movie androidx.compose.runtime  	MovieCard androidx.compose.runtime  MovieRow androidx.compose.runtime  
PaddingValues androidx.compose.runtime  
SampleData androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  TextOverflow androidx.compose.runtime  TvLazyColumn androidx.compose.runtime  	TvLazyRow androidx.compose.runtime  align androidx.compose.runtime  
background androidx.compose.runtime  colors androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  height androidx.compose.runtime  horizontalGradient androidx.compose.runtime  listOf androidx.compose.runtime  padding androidx.compose.runtime  spacedBy androidx.compose.runtime  verticalGradient androidx.compose.runtime  width androidx.compose.runtime  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomStart androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  BottomStart 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  horizontalGradient "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  horizontalGradient ,androidx.compose.ui.graphics.Brush.Companion  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  
FontWeight androidx.compose.ui.text.font  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  TextOverflow androidx.compose.ui.text.style  	Companion +androidx.compose.ui.text.style.TextOverflow  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AjTvPlayerTheme #androidx.core.app.ComponentActivity  
HomeScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  TvLazyColumn  androidx.tv.foundation.lazy.list  TvLazyListItemScope  androidx.tv.foundation.lazy.list  TvLazyListScope  androidx.tv.foundation.lazy.list  	TvLazyRow  androidx.tv.foundation.lazy.list  items  androidx.tv.foundation.lazy.list  FeaturedContent 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  Modifier 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  	MovieCard 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  MovieRow 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  
SampleData 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  dp 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  fillMaxWidth 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  width 4androidx.tv.foundation.lazy.list.TvLazyListItemScope  FeaturedContent 0androidx.tv.foundation.lazy.list.TvLazyListScope  Modifier 0androidx.tv.foundation.lazy.list.TvLazyListScope  	MovieCard 0androidx.tv.foundation.lazy.list.TvLazyListScope  MovieRow 0androidx.tv.foundation.lazy.list.TvLazyListScope  
SampleData 0androidx.tv.foundation.lazy.list.TvLazyListScope  dp 0androidx.tv.foundation.lazy.list.TvLazyListScope  fillMaxWidth 0androidx.tv.foundation.lazy.list.TvLazyListScope  item 0androidx.tv.foundation.lazy.list.TvLazyListScope  items 0androidx.tv.foundation.lazy.list.TvLazyListScope  width 0androidx.tv.foundation.lazy.list.TvLazyListScope  Card androidx.tv.material3  
CardColors androidx.tv.material3  CardDefaults androidx.tv.material3  colors "androidx.tv.material3.CardDefaults  AjTvPlayerTheme com.aj.aj_tv_player  Arrangement com.aj.aj_tv_player  Box com.aj.aj_tv_player  Bundle com.aj.aj_tv_player  Color com.aj.aj_tv_player  ComponentActivity com.aj.aj_tv_player  
Composable com.aj.aj_tv_player  FeaturedContent com.aj.aj_tv_player  
HomeScreen com.aj.aj_tv_player  MainActivity com.aj.aj_tv_player  
MaterialTheme com.aj.aj_tv_player  Modifier com.aj.aj_tv_player  MovieRow com.aj.aj_tv_player  
PaddingValues com.aj.aj_tv_player  
SampleData com.aj.aj_tv_player  Surface com.aj.aj_tv_player  TvLazyColumn com.aj.aj_tv_player  fillMaxSize com.aj.aj_tv_player  fillMaxWidth com.aj.aj_tv_player  spacedBy com.aj.aj_tv_player  AjTvPlayerTheme  com.aj.aj_tv_player.MainActivity  
HomeScreen  com.aj.aj_tv_player.MainActivity  
MaterialTheme  com.aj.aj_tv_player.MainActivity  Modifier  com.aj.aj_tv_player.MainActivity  Surface  com.aj.aj_tv_player.MainActivity  fillMaxSize  com.aj.aj_tv_player.MainActivity  
setContent  com.aj.aj_tv_player.MainActivity  	Alignment com.aj.aj_tv_player.components  Arrangement com.aj.aj_tv_player.components  Box com.aj.aj_tv_player.components  Brush com.aj.aj_tv_player.components  CardDefaults com.aj.aj_tv_player.components  Color com.aj.aj_tv_player.components  Column com.aj.aj_tv_player.components  
Composable com.aj.aj_tv_player.components  FeaturedContent com.aj.aj_tv_player.components  
FontWeight com.aj.aj_tv_player.components  List com.aj.aj_tv_player.components  Modifier com.aj.aj_tv_player.components  Movie com.aj.aj_tv_player.components  	MovieCard com.aj.aj_tv_player.components  MovieRow com.aj.aj_tv_player.components  
PaddingValues com.aj.aj_tv_player.components  String com.aj.aj_tv_player.components  Text com.aj.aj_tv_player.components  TextOverflow com.aj.aj_tv_player.components  	TvLazyRow com.aj.aj_tv_player.components  align com.aj.aj_tv_player.components  
background com.aj.aj_tv_player.components  colors com.aj.aj_tv_player.components  fillMaxSize com.aj.aj_tv_player.components  fillMaxWidth com.aj.aj_tv_player.components  height com.aj.aj_tv_player.components  horizontalGradient com.aj.aj_tv_player.components  listOf com.aj.aj_tv_player.components  padding com.aj.aj_tv_player.components  spacedBy com.aj.aj_tv_player.components  verticalGradient com.aj.aj_tv_player.components  width com.aj.aj_tv_player.components  Movie com.aj.aj_tv_player.data  
SampleData com.aj.aj_tv_player.data  String com.aj.aj_tv_player.data  listOf com.aj.aj_tv_player.data  plus com.aj.aj_tv_player.data  category com.aj.aj_tv_player.data.Movie  description com.aj.aj_tv_player.data.Movie  duration com.aj.aj_tv_player.data.Movie  id com.aj.aj_tv_player.data.Movie  rating com.aj.aj_tv_player.data.Movie  title com.aj.aj_tv_player.data.Movie  year com.aj.aj_tv_player.data.Movie  Movie #com.aj.aj_tv_player.data.SampleData  
featuredMovie #com.aj.aj_tv_player.data.SampleData  listOf #com.aj.aj_tv_player.data.SampleData  plus #com.aj.aj_tv_player.data.SampleData  sciFiMovies #com.aj.aj_tv_player.data.SampleData  AjTvPlayerTheme com.aj.aj_tv_player.ui.theme  Boolean com.aj.aj_tv_player.ui.theme  
Composable com.aj.aj_tv_player.ui.theme  DarkColorScheme com.aj.aj_tv_player.ui.theme  LightColorScheme com.aj.aj_tv_player.ui.theme  Unit com.aj.aj_tv_player.ui.theme  
BigDecimal 	java.math  
BigInteger 	java.math  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  plus kotlin  List kotlin.collections  Map kotlin.collections  Set kotlin.collections  listOf kotlin.collections  plus kotlin.collections  plus kotlin.collections.List  Sequence kotlin.sequences  plus kotlin.sequences  plus kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     