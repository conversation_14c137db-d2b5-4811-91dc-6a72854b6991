E:\1-test\aj-tv-player\gradle\libs.versions.toml:4: Warning: A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.17.0 [GradleDependency]
coreKtx = "1.10.1"
          ~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

E:\1-test\aj-tv-player\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.10 [NewerVersionAvailable]
kotlin = "2.0.21"
         ~~~~~~~~
E:\1-test\aj-tv-player\gradle\libs.versions.toml:3: Warning: A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.10 [NewerVersionAvailable]
kotlin = "2.0.21"
         ~~~~~~~~

   Explanation for issues of type "NewerVersionAvailable":
   This detector checks with a central repository to see if there are newer
   versions available for the dependencies used by this project. This is
   similar to the GradleDependency check, which checks for newer versions
   available in the Android SDK tools and libraries, but this works with any
   MavenCentral dependency, and connects to the library every time, which
   makes it more flexible but also much slower.

0 errors, 3 warnings
