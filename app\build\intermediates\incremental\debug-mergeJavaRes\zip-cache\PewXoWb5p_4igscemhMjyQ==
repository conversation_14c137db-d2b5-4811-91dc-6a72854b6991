[{"key": "androidx/compose/material/ActualJvm_jvmKt.class", "name": "androidx/compose/material/ActualJvm_jvmKt.class", "size": 426, "crc": -571700884}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogBaselineLayout$2$1.class", "size": 8697, "crc": -1653504303}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1$1.class", "size": 3118, "crc": 1842129467}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "size": 3579, "crc": -1046503131}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1$1.class", "size": 3113, "crc": 237784215}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "size": 3588, "crc": -1888635695}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogContent$1.class", "size": 11278, "crc": 508737019}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogFlowRow$1$1.class", "size": 9718, "crc": -117562007}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1$1$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1$1$1.class", "size": 2861, "crc": 25026688}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$1.class", "size": 10324, "crc": 1594339432}, {"key": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$2.class", "name": "androidx/compose/material/AlertDialogKt$AlertDialogImpl$2.class", "size": 3745, "crc": 940153039}, {"key": "androidx/compose/material/AlertDialogKt.class", "name": "androidx/compose/material/AlertDialogKt.class", "size": 30126, "crc": 1398796274}, {"key": "androidx/compose/material/AnchoredDragFinishedSignal.class", "name": "androidx/compose/material/AnchoredDragFinishedSignal.class", "size": 936, "crc": -1596056339}, {"key": "androidx/compose/material/AnchoredDragScope.class", "name": "androidx/compose/material/AnchoredDragScope.class", "size": 1007, "crc": -936946674}, {"key": "androidx/compose/material/AnchoredDraggableDefaults.class", "name": "androidx/compose/material/AnchoredDraggableDefaults.class", "size": 1560, "crc": 1397362728}, {"key": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1$1.class", "size": 3446, "crc": -1281020392}, {"key": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$anchoredDraggable$1.class", "size": 3856, "crc": -568931952}, {"key": "androidx/compose/material/AnchoredDraggableKt$animateTo$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$animateTo$2.class", "size": 5568, "crc": -354004241}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$1.class", "size": 1661, "crc": 935388840}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$2.class", "size": 3980, "crc": 1761907888}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 1881, "crc": 564968085}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2$1.class", "size": 4236, "crc": 1581358759}, {"key": "androidx/compose/material/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$restartable$2.class", "size": 4399, "crc": 2062254814}, {"key": "androidx/compose/material/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/material/AnchoredDraggableKt$snapTo$2.class", "size": 3651, "crc": 1107910891}, {"key": "androidx/compose/material/AnchoredDraggableKt.class", "name": "androidx/compose/material/AnchoredDraggableKt.class", "size": 10177, "crc": 473481458}, {"key": "androidx/compose/material/AnchoredDraggableState$Companion.class", "name": "androidx/compose/material/AnchoredDraggableState$Companion.class", "size": 4301, "crc": -2107558936}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$1.class", "size": 1892, "crc": 616803164}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4254, "crc": -849257085}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$2.class", "size": 4716, "crc": 1884196084}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$3.class", "size": 1911, "crc": 762335246}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4464, "crc": -639189928}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDrag$4.class", "size": 5062, "crc": 304780125}, {"key": "androidx/compose/material/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/material/AnchoredDraggableState$anchoredDragScope$1.class", "size": 1598, "crc": -153820570}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1$drag$2.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1$drag$2.class", "size": 5147, "crc": -788871264}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1$dragScope$1.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1$dragScope$1.class", "size": 2463, "crc": -906347249}, {"key": "androidx/compose/material/AnchoredDraggableState$draggableState$1.class", "name": "androidx/compose/material/AnchoredDraggableState$draggableState$1.class", "size": 3505, "crc": -2063498107}, {"key": "androidx/compose/material/AnchoredDraggableState.class", "name": "androidx/compose/material/AnchoredDraggableState.class", "size": 25946, "crc": -1208738873}, {"key": "androidx/compose/material/AndroidAlertDialog_androidKt.class", "name": "androidx/compose/material/AndroidAlertDialog_androidKt.class", "size": 14983, "crc": 1721066083}, {"key": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$2.class", "name": "androidx/compose/material/AndroidMenu_androidKt$DropdownMenu$2.class", "size": 4146, "crc": -2092269996}, {"key": "androidx/compose/material/AndroidMenu_androidKt.class", "name": "androidx/compose/material/AndroidMenu_androidKt.class", "size": 20571, "crc": -608121928}, {"key": "androidx/compose/material/AppBarDefaults.class", "name": "androidx/compose/material/AppBarDefaults.class", "size": 5210, "crc": 1375649949}, {"key": "androidx/compose/material/AppBarKt$AppBar$1$1.class", "name": "androidx/compose/material/AppBarKt$AppBar$1$1.class", "size": 10154, "crc": -1648446335}, {"key": "androidx/compose/material/AppBarKt$AppBar$1.class", "name": "androidx/compose/material/AppBarKt$AppBar$1.class", "size": 4189, "crc": -873983029}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1$2$1.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1$2$1.class", "size": 3257, "crc": 262427354}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1$3.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1$3.class", "size": 9264, "crc": -1588422476}, {"key": "androidx/compose/material/AppBarKt$TopAppBar$1.class", "name": "androidx/compose/material/AppBarKt$TopAppBar$1.class", "size": 14451, "crc": -1441529220}, {"key": "androidx/compose/material/AppBarKt.class", "name": "androidx/compose/material/AppBarKt.class", "size": 33527, "crc": -1986550586}, {"key": "androidx/compose/material/BackdropLayers.class", "name": "androidx/compose/material/BackdropLayers.class", "size": 1866, "crc": -976506840}, {"key": "androidx/compose/material/BackdropScaffoldDefaults.class", "name": "androidx/compose/material/BackdropScaffoldDefaults.class", "size": 6220, "crc": 1742577181}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$1$1.class", "size": 3485, "crc": 87745352}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$2$1$2$1.class", "size": 3486, "crc": -644666182}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3$1$1$1$1.class", "size": 3556, "crc": 1110406564}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$3.class", "size": 12837, "crc": 1921859009}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$WhenMappings.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1$WhenMappings.class", "size": 947, "crc": -958051655}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2$1.class", "size": 21546, "crc": -2064608743}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$2.class", "size": 9038, "crc": 30356692}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$backLayer$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropScaffold$backLayer$1.class", "size": 10804, "crc": -2126980063}, {"key": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$placeables$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$BackdropStack$1$1$placeables$1.class", "size": 3136, "crc": 371719515}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPostFling$1.class", "size": 1890, "crc": -2038255656}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1$onPreFling$1.class", "size": 1884, "crc": 1003428474}, {"key": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$ConsumeSwipeNestedScrollConnection$1.class", "size": 8463, "crc": 837322480}, {"key": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1.class", "name": "androidx/compose/material/BackdropScaffoldKt$Scrim$dismissModifier$1$1.class", "size": 2814, "crc": 1169575700}, {"key": "androidx/compose/material/BackdropScaffoldKt.class", "name": "androidx/compose/material/BackdropScaffoldKt.class", "size": 59475, "crc": -1914426324}, {"key": "androidx/compose/material/BackdropScaffoldState$Companion.class", "name": "androidx/compose/material/BackdropScaffoldState$Companion.class", "size": 4598, "crc": -2128289801}, {"key": "androidx/compose/material/BackdropScaffoldState.class", "name": "androidx/compose/material/BackdropScaffoldState.class", "size": 11287, "crc": 1214740928}, {"key": "androidx/compose/material/BackdropValue.class", "name": "androidx/compose/material/BackdropValue.class", "size": 1867, "crc": 164477253}, {"key": "androidx/compose/material/BadgeKt$Badge$1$1$1.class", "name": "androidx/compose/material/BadgeKt$Badge$1$1$1.class", "size": 2820, "crc": 2042573318}, {"key": "androidx/compose/material/BadgeKt$Badge$1$1.class", "name": "androidx/compose/material/BadgeKt$Badge$1$1.class", "size": 4524, "crc": 1994440106}, {"key": "androidx/compose/material/BadgeKt$BadgedBox$2$1.class", "name": "androidx/compose/material/BadgeKt$BadgedBox$2$1.class", "size": 7052, "crc": 2138460006}, {"key": "androidx/compose/material/BadgeKt.class", "name": "androidx/compose/material/BadgeKt.class", "size": 23885, "crc": -405120329}, {"key": "androidx/compose/material/BottomAppBarCutoutShape.class", "name": "androidx/compose/material/BottomAppBarCutoutShape.class", "size": 11314, "crc": -1331657455}, {"key": "androidx/compose/material/BottomDrawerState$Companion.class", "name": "androidx/compose/material/BottomDrawerState$Companion.class", "size": 4123, "crc": -1139193559}, {"key": "androidx/compose/material/BottomDrawerState.class", "name": "androidx/compose/material/BottomDrawerState.class", "size": 12120, "crc": -1873540186}, {"key": "androidx/compose/material/BottomDrawerValue.class", "name": "androidx/compose/material/BottomDrawerValue.class", "size": 1940, "crc": -858137912}, {"key": "androidx/compose/material/BottomNavigationDefaults.class", "name": "androidx/compose/material/BottomNavigationDefaults.class", "size": 4007, "crc": -2092348283}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigation$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigation$1.class", "size": 10441, "crc": -272414735}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$1$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$1$1.class", "size": 3461, "crc": 456396735}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$styledLabel$1$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItem$styledLabel$1$1.class", "size": 4417, "crc": -282749148}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$2$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationItemBaselineLayout$2$1.class", "size": 6000, "crc": -87939211}, {"key": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$1.class", "name": "androidx/compose/material/BottomNavigationKt$BottomNavigationTransition$1.class", "size": 3081, "crc": 1395894108}, {"key": "androidx/compose/material/BottomNavigationKt.class", "name": "androidx/compose/material/BottomNavigationKt.class", "size": 41277, "crc": 1626233236}, {"key": "androidx/compose/material/BottomSheetScaffoldDefaults.class", "name": "androidx/compose/material/BottomSheetScaffoldDefaults.class", "size": 3013, "crc": 692312527}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$1$1.class", "size": 3484, "crc": -2110949726}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$2$1$2$1.class", "size": 3486, "crc": -1332018733}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$3.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheet$3.class", "size": 9399, "crc": -1450423721}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$1.class", "size": 2996, "crc": 139778217}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$2.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$2.class", "size": 7263, "crc": -30306677}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$3.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1$3.class", "size": 3105, "crc": -1502539481}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "size": 9004, "crc": 1510193869}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$WhenMappings.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1$WhenMappings.class", "size": 906, "crc": -1530430498}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "size": 15897, "crc": -679440029}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 2101, "crc": 281393724}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 2095, "crc": 316596862}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 8744, "crc": -1209644623}, {"key": "androidx/compose/material/BottomSheetScaffoldKt$WhenMappings.class", "name": "androidx/compose/material/BottomSheetScaffoldKt$WhenMappings.class", "size": 838, "crc": 39258632}, {"key": "androidx/compose/material/BottomSheetScaffoldKt.class", "name": "androidx/compose/material/BottomSheetScaffoldKt.class", "size": 46595, "crc": 1548059219}, {"key": "androidx/compose/material/BottomSheetScaffoldState.class", "name": "androidx/compose/material/BottomSheetScaffoldState.class", "size": 1505, "crc": -2033892047}, {"key": "androidx/compose/material/BottomSheetState$Companion.class", "name": "androidx/compose/material/BottomSheetState$Companion.class", "size": 4099, "crc": -129537327}, {"key": "androidx/compose/material/BottomSheetState.class", "name": "androidx/compose/material/BottomSheetState.class", "size": 10377, "crc": 1778970312}, {"key": "androidx/compose/material/BottomSheetValue.class", "name": "androidx/compose/material/BottomSheetValue.class", "size": 1891, "crc": -1367556634}, {"key": "androidx/compose/material/ButtonColors.class", "name": "androidx/compose/material/ButtonColors.class", "size": 1127, "crc": 536078784}, {"key": "androidx/compose/material/ButtonDefaults.class", "name": "androidx/compose/material/ButtonDefaults.class", "size": 13287, "crc": 2004249057}, {"key": "androidx/compose/material/ButtonElevation.class", "name": "androidx/compose/material/ButtonElevation.class", "size": 1243, "crc": -1065004710}, {"key": "androidx/compose/material/ButtonKt$Button$2$1$1.class", "name": "androidx/compose/material/ButtonKt$Button$2$1$1.class", "size": 9927, "crc": -552827248}, {"key": "androidx/compose/material/ButtonKt$Button$2$1.class", "name": "androidx/compose/material/ButtonKt$Button$2$1.class", "size": 3461, "crc": -1213680171}, {"key": "androidx/compose/material/ButtonKt$Button$2.class", "name": "androidx/compose/material/ButtonKt$Button$2.class", "size": 4486, "crc": 1550917311}, {"key": "androidx/compose/material/ButtonKt.class", "name": "androidx/compose/material/ButtonKt.class", "size": 17520, "crc": -1887451304}, {"key": "androidx/compose/material/CardKt.class", "name": "androidx/compose/material/CardKt.class", "size": 6500, "crc": 1856297866}, {"key": "androidx/compose/material/CheckDrawingCache.class", "name": "androidx/compose/material/CheckDrawingCache.class", "size": 2128, "crc": 608815604}, {"key": "androidx/compose/material/CheckboxColors.class", "name": "androidx/compose/material/CheckboxColors.class", "size": 1683, "crc": -760740174}, {"key": "androidx/compose/material/CheckboxDefaults.class", "name": "androidx/compose/material/CheckboxDefaults.class", "size": 5252, "crc": 1944556182}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$2.class", "size": 3477, "crc": 613214528}, {"key": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkDrawFraction$2.class", "name": "androidx/compose/material/CheckboxKt$CheckboxImpl$checkDrawFraction$2.class", "size": 3467, "crc": -507572987}, {"key": "androidx/compose/material/CheckboxKt$WhenMappings.class", "name": "androidx/compose/material/CheckboxKt$WhenMappings.class", "size": 843, "crc": 1430347893}, {"key": "androidx/compose/material/CheckboxKt.class", "name": "androidx/compose/material/CheckboxKt.class", "size": 33203, "crc": 999772497}, {"key": "androidx/compose/material/ChipColors.class", "name": "androidx/compose/material/ChipColors.class", "size": 1287, "crc": -1197754270}, {"key": "androidx/compose/material/ChipDefaults.class", "name": "androidx/compose/material/ChipDefaults.class", "size": 10950, "crc": 207076522}, {"key": "androidx/compose/material/ChipKt$Chip$2$1$1.class", "name": "androidx/compose/material/ChipKt$Chip$2$1$1.class", "size": 13163, "crc": -1571751761}, {"key": "androidx/compose/material/ChipKt$Chip$2$1.class", "name": "androidx/compose/material/ChipKt$Chip$2$1.class", "size": 3785, "crc": -845853193}, {"key": "androidx/compose/material/ChipKt$Chip$2.class", "name": "androidx/compose/material/ChipKt$Chip$2.class", "size": 4766, "crc": -1041141430}, {"key": "androidx/compose/material/ChipKt$FilterChip$2$1$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$2$1$1.class", "size": 22007, "crc": 1727436098}, {"key": "androidx/compose/material/ChipKt$FilterChip$2$1.class", "name": "androidx/compose/material/ChipKt$FilterChip$2$1.class", "size": 4563, "crc": -578463114}, {"key": "androidx/compose/material/ChipKt$FilterChip$2.class", "name": "androidx/compose/material/ChipKt$FilterChip$2.class", "size": 5496, "crc": -1671518626}, {"key": "androidx/compose/material/ChipKt.class", "name": "androidx/compose/material/ChipKt.class", "size": 21868, "crc": -691636227}, {"key": "androidx/compose/material/Colors.class", "name": "androidx/compose/material/Colors.class", "size": 12243, "crc": -1837085860}, {"key": "androidx/compose/material/ColorsKt.class", "name": "androidx/compose/material/ColorsKt.class", "size": 9524, "crc": 1803355}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda$1260131259$1.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda$1260131259$1.class", "size": 2272, "crc": -1985105573}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda$269254275$1.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt$lambda$269254275$1.class", "size": 2268, "crc": 2128490762}, {"key": "androidx/compose/material/ComposableSingletons$AppBarKt.class", "name": "androidx/compose/material/ComposableSingletons$AppBarKt.class", "size": 1948, "crc": -1666341934}, {"key": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt$lambda$2057676608$1.class", "name": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt$lambda$2057676608$1.class", "size": 2827, "crc": 1811410540}, {"key": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$BackdropScaffoldKt.class", "size": 1641, "crc": -2125304043}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda$-505419337$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda$-505419337$1.class", "size": 2115, "crc": -1932027969}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda$687232378$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda$687232378$1.class", "size": 2112, "crc": -2130082732}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda$937349512$1.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt$lambda$937349512$1.class", "size": 2845, "crc": -1922329122}, {"key": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$BottomSheetScaffoldKt.class", "size": 2740, "crc": 1157753000}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1341284559$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1341284559$1.class", "size": 2041, "crc": 2133885361}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1406416085$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1406416085$1.class", "size": 2041, "crc": 985264059}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1624772335$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1624772335$1.class", "size": 2041, "crc": -1689196726}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1836397928$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$-1836397928$1.class", "size": 2773, "crc": -1287405412}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$1714259275$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$1714259275$1.class", "size": 2038, "crc": 2145677405}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$566090785$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$566090785$1.class", "size": 2035, "crc": 1531276034}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$866784315$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$866784315$1.class", "size": 2035, "crc": -686205206}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$939725476$1.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt$lambda$939725476$1.class", "size": 2768, "crc": 1141178344}, {"key": "androidx/compose/material/ComposableSingletons$ScaffoldKt.class", "name": "androidx/compose/material/ComposableSingletons$ScaffoldKt.class", "size": 4478, "crc": -1344894335}, {"key": "androidx/compose/material/ComposableSingletons$SnackbarHostKt$lambda$1890101041$1.class", "name": "androidx/compose/material/ComposableSingletons$SnackbarHostKt$lambda$1890101041$1.class", "size": 2855, "crc": 956814926}, {"key": "androidx/compose/material/ComposableSingletons$SnackbarHostKt.class", "name": "androidx/compose/material/ComposableSingletons$SnackbarHostKt.class", "size": 1611, "crc": -1266892743}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda$-1216856915$1.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda$-1216856915$1.class", "size": 2356, "crc": 1280437090}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda$-222041980$1.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt$lambda$-222041980$1.class", "size": 2354, "crc": -321298195}, {"key": "androidx/compose/material/ComposableSingletons$TabRowKt.class", "name": "androidx/compose/material/ComposableSingletons$TabRowKt.class", "size": 1866, "crc": -1768534940}, {"key": "androidx/compose/material/ContentAlpha.class", "name": "androidx/compose/material/ContentAlpha.class", "size": 4991, "crc": 1762633474}, {"key": "androidx/compose/material/ContentAlphaKt.class", "name": "androidx/compose/material/ContentAlphaKt.class", "size": 1690, "crc": -1096859834}, {"key": "androidx/compose/material/ContentColorKt$LocalContentColor$1.class", "name": "androidx/compose/material/ContentColorKt$LocalContentColor$1.class", "size": 1250, "crc": -1268091791}, {"key": "androidx/compose/material/ContentColorKt.class", "name": "androidx/compose/material/ContentColorKt.class", "size": 1476, "crc": -908432145}, {"key": "androidx/compose/material/DefaultButtonColors.class", "name": "androidx/compose/material/DefaultButtonColors.class", "size": 3948, "crc": -167445248}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$1$1$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$1$1$1.class", "size": 3397, "crc": -506847396}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$1$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$1$1.class", "size": 4199, "crc": -2092256334}, {"key": "androidx/compose/material/DefaultButtonElevation$elevation$2$1.class", "name": "androidx/compose/material/DefaultButtonElevation$elevation$2$1.class", "size": 5932, "crc": -1671790922}, {"key": "androidx/compose/material/DefaultButtonElevation.class", "name": "androidx/compose/material/DefaultButtonElevation.class", "size": 9273, "crc": -943967750}, {"key": "androidx/compose/material/DefaultCheckboxColors$WhenMappings.class", "name": "androidx/compose/material/DefaultCheckboxColors$WhenMappings.class", "size": 865, "crc": 1784017629}, {"key": "androidx/compose/material/DefaultCheckboxColors.class", "name": "androidx/compose/material/DefaultCheckboxColors.class", "size": 6729, "crc": 960780088}, {"key": "androidx/compose/material/DefaultChipColors.class", "name": "androidx/compose/material/DefaultChipColors.class", "size": 4705, "crc": -90935966}, {"key": "androidx/compose/material/DefaultElevationOverlay.class", "name": "androidx/compose/material/DefaultElevationOverlay.class", "size": 3611, "crc": 1474140176}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$1$1.class", "size": 4121, "crc": 1486758830}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1$1.class", "size": 3845, "crc": -1051749102}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1$1.class", "size": 4416, "crc": 1594147597}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation$elevation$2$1.class", "size": 4436, "crc": -851422089}, {"key": "androidx/compose/material/DefaultFloatingActionButtonElevation.class", "name": "androidx/compose/material/DefaultFloatingActionButtonElevation.class", "size": 8025, "crc": -796944883}, {"key": "androidx/compose/material/DefaultPlatformTextStyle_androidKt.class", "name": "androidx/compose/material/DefaultPlatformTextStyle_androidKt.class", "size": 1090, "crc": -662286183}, {"key": "androidx/compose/material/DefaultRadioButtonColors.class", "name": "androidx/compose/material/DefaultRadioButtonColors.class", "size": 4239, "crc": -1258072606}, {"key": "androidx/compose/material/DefaultSelectableChipColors.class", "name": "androidx/compose/material/DefaultSelectableChipColors.class", "size": 5461, "crc": -1724022185}, {"key": "androidx/compose/material/DefaultSliderColors.class", "name": "androidx/compose/material/DefaultSliderColors.class", "size": 5574, "crc": -2131153896}, {"key": "androidx/compose/material/DefaultSwitchColors.class", "name": "androidx/compose/material/DefaultSwitchColors.class", "size": 4727, "crc": -1635996989}, {"key": "androidx/compose/material/DefaultTextFieldColors.class", "name": "androidx/compose/material/DefaultTextFieldColors.class", "size": 13740, "crc": -194162484}, {"key": "androidx/compose/material/DefaultTextFieldForExposedDropdownMenusColors.class", "name": "androidx/compose/material/DefaultTextFieldForExposedDropdownMenusColors.class", "size": 14051, "crc": -724486548}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode$attachNewRipple$calculateColor$1.class", "size": 3494, "crc": -1561255511}, {"key": "androidx/compose/material/DelegatingThemeAwareRippleNode.class", "name": "androidx/compose/material/DelegatingThemeAwareRippleNode.class", "size": 6868, "crc": 1053354311}, {"key": "androidx/compose/material/DismissDirection.class", "name": "androidx/compose/material/DismissDirection.class", "size": 1889, "crc": -676146466}, {"key": "androidx/compose/material/DismissState$Companion.class", "name": "androidx/compose/material/DismissState$Companion.class", "size": 3216, "crc": -680405762}, {"key": "androidx/compose/material/DismissState.class", "name": "androidx/compose/material/DismissState.class", "size": 5025, "crc": -1959482525}, {"key": "androidx/compose/material/DismissValue.class", "name": "androidx/compose/material/DismissValue.class", "size": 1932, "crc": 1700301532}, {"key": "androidx/compose/material/DividerKt.class", "name": "androidx/compose/material/DividerKt.class", "size": 7607, "crc": 146604303}, {"key": "androidx/compose/material/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/material/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2087, "crc": 1286797633}, {"key": "androidx/compose/material/DragGestureDetectorCopyKt.class", "name": "androidx/compose/material/DragGestureDetectorCopyKt.class", "size": 16715, "crc": -340521612}, {"key": "androidx/compose/material/DraggableAnchors.class", "name": "androidx/compose/material/DraggableAnchors.class", "size": 1224, "crc": -353036371}, {"key": "androidx/compose/material/DraggableAnchorsConfig.class", "name": "androidx/compose/material/DraggableAnchorsConfig.class", "size": 1713, "crc": -1474229656}, {"key": "androidx/compose/material/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/DraggableAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "size": 3254, "crc": -1791353062}, {"key": "androidx/compose/material/DraggableAnchorsElement.class", "name": "androidx/compose/material/DraggableAnchorsElement.class", "size": 6541, "crc": 104400753}, {"key": "androidx/compose/material/DraggableAnchorsNode.class", "name": "androidx/compose/material/DraggableAnchorsNode.class", "size": 9045, "crc": -1473137170}, {"key": "androidx/compose/material/DrawerDefaults.class", "name": "androidx/compose/material/DrawerDefaults.class", "size": 5055, "crc": -479427508}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$1$1$1.class", "size": 3340, "crc": 1674752861}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$4$1$1$1.class", "size": 3348, "crc": 2134776416}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$5.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$1$5.class", "size": 9169, "crc": -738581460}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1$WhenMappings.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1$WhenMappings.class", "size": 881, "crc": -237832110}, {"key": "androidx/compose/material/DrawerKt$BottomDrawer$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawer$1.class", "size": 25680, "crc": -1540683672}, {"key": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1.class", "name": "androidx/compose/material/DrawerKt$BottomDrawerScrim$dismissModifier$1$1.class", "size": 2810, "crc": 1246277844}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 1984, "crc": -853830164}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 1978, "crc": 1095135798}, {"key": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/DrawerKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 8546, "crc": -1774409135}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$2$1$1.class", "size": 3311, "crc": 480493309}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$6$1$1$1.class", "size": 3319, "crc": 1749961038}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$7.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1$2$7.class", "size": 9376, "crc": -2104710536}, {"key": "androidx/compose/material/DrawerKt$ModalDrawer$1.class", "name": "androidx/compose/material/DrawerKt$ModalDrawer$1.class", "size": 26999, "crc": -1953889353}, {"key": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1.class", "name": "androidx/compose/material/DrawerKt$Scrim$dismissDrawer$1$1.class", "size": 2800, "crc": -2072490060}, {"key": "androidx/compose/material/DrawerKt.class", "name": "androidx/compose/material/DrawerKt.class", "size": 40602, "crc": -1564444105}, {"key": "androidx/compose/material/DrawerState$Companion.class", "name": "androidx/compose/material/DrawerState$Companion.class", "size": 3167, "crc": **********}, {"key": "androidx/compose/material/DrawerState.class", "name": "androidx/compose/material/DrawerState.class", "size": 9487, "crc": -168982901}, {"key": "androidx/compose/material/DrawerValue.class", "name": "androidx/compose/material/DrawerValue.class", "size": 1836, "crc": 951211658}, {"key": "androidx/compose/material/DropdownMenuPositionProvider.class", "name": "androidx/compose/material/DropdownMenuPositionProvider.class", "size": 12820, "crc": -981638456}, {"key": "androidx/compose/material/ElevationDefaults.class", "name": "androidx/compose/material/ElevationDefaults.class", "size": 2382, "crc": 402948155}, {"key": "androidx/compose/material/ElevationKt.class", "name": "androidx/compose/material/ElevationKt.class", "size": 4419, "crc": 700379683}, {"key": "androidx/compose/material/ElevationOverlay.class", "name": "androidx/compose/material/ElevationOverlay.class", "size": 815, "crc": **********}, {"key": "androidx/compose/material/ElevationOverlayKt.class", "name": "androidx/compose/material/ElevationOverlayKt.class", "size": 5253, "crc": -191594279}, {"key": "androidx/compose/material/ExperimentalMaterialApi.class", "name": "androidx/compose/material/ExperimentalMaterialApi.class", "size": 814, "crc": -**********}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "size": 4582, "crc": -1803510222}, {"key": "androidx/compose/material/ExposedDropdownMenuBoxScope.class", "name": "androidx/compose/material/ExposedDropdownMenuBoxScope.class", "size": 13666, "crc": 507544101}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$3.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults$TrailingIcon$3.class", "size": 3220, "crc": 1731405339}, {"key": "androidx/compose/material/ExposedDropdownMenuDefaults.class", "name": "androidx/compose/material/ExposedDropdownMenuDefaults.class", "size": 16946, "crc": 1925820148}, {"key": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$lambda$3$lambda$2$$inlined$onDispose$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_android$OnPlatformWindowBoundsChange$lambda$3$lambda$2$$inlined$onDispose$1.class", "size": 2226, "crc": -1606723506}, {"key": "androidx/compose/material/ExposedDropdownMenu_android.class", "name": "androidx/compose/material/ExposedDropdownMenu_android.class", "size": 9318, "crc": -2099483314}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "size": 2673, "crc": 148091575}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1$1.class", "size": 4550, "crc": 725430441}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt$expandable$1.class", "size": 2163, "crc": 1292339449}, {"key": "androidx/compose/material/ExposedDropdownMenu_androidKt.class", "name": "androidx/compose/material/ExposedDropdownMenu_androidKt.class", "size": 27962, "crc": -1002250785}, {"key": "androidx/compose/material/FabPlacement.class", "name": "androidx/compose/material/FabPlacement.class", "size": 1294, "crc": -1867525809}, {"key": "androidx/compose/material/FabPosition$Companion.class", "name": "androidx/compose/material/FabPosition$Companion.class", "size": 1318, "crc": -1889042329}, {"key": "androidx/compose/material/FabPosition.class", "name": "androidx/compose/material/FabPosition.class", "size": 2669, "crc": 2049424202}, {"key": "androidx/compose/material/FadeInFadeOutAnimationItem.class", "name": "androidx/compose/material/FadeInFadeOutAnimationItem.class", "size": 4388, "crc": -862242500}, {"key": "androidx/compose/material/FadeInFadeOutState.class", "name": "androidx/compose/material/FadeInFadeOutState.class", "size": 2299, "crc": -8829639}, {"key": "androidx/compose/material/FixedThreshold.class", "name": "androidx/compose/material/FixedThreshold.class", "size": 3318, "crc": 750697492}, {"key": "androidx/compose/material/FloatingActionButtonDefaults.class", "name": "androidx/compose/material/FloatingActionButtonDefaults.class", "size": 6024, "crc": 1017798497}, {"key": "androidx/compose/material/FloatingActionButtonElevation.class", "name": "androidx/compose/material/FloatingActionButtonElevation.class", "size": 1250, "crc": 703725500}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "size": 1980, "crc": -534153603}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "size": 2036, "crc": -87127670}, {"key": "androidx/compose/material/FloatingActionButtonElevationAnimatable.class", "name": "androidx/compose/material/FloatingActionButtonElevationAnimatable.class", "size": 7002, "crc": 959106079}, {"key": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$ExtendedFloatingActionButton$1.class", "size": 10890, "crc": -809115950}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1$1.class", "size": 9209, "crc": 603758398}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2$1.class", "size": 3330, "crc": 425715280}, {"key": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2.class", "name": "androidx/compose/material/FloatingActionButtonKt$FloatingActionButton$2.class", "size": 3897, "crc": 814832341}, {"key": "androidx/compose/material/FloatingActionButtonKt.class", "name": "androidx/compose/material/FloatingActionButtonKt.class", "size": 18441, "crc": -1120182727}, {"key": "androidx/compose/material/FractionalThreshold.class", "name": "androidx/compose/material/FractionalThreshold.class", "size": 2884, "crc": 1596949555}, {"key": "androidx/compose/material/HighContrastContentAlpha.class", "name": "androidx/compose/material/HighContrastContentAlpha.class", "size": 900, "crc": 580225220}, {"key": "androidx/compose/material/IconButtonKt.class", "name": "androidx/compose/material/IconButtonKt.class", "size": 19363, "crc": -1308457656}, {"key": "androidx/compose/material/IconKt.class", "name": "androidx/compose/material/IconKt.class", "size": 15835, "crc": -1470686609}, {"key": "androidx/compose/material/InputPhase.class", "name": "androidx/compose/material/InputPhase.class", "size": 1918, "crc": -1020484773}, {"key": "androidx/compose/material/InteractiveComponentSizeKt.class", "name": "androidx/compose/material/InteractiveComponentSizeKt.class", "size": 4232, "crc": -1713876502}, {"key": "androidx/compose/material/InternalMutatorMutex$Mutator.class", "name": "androidx/compose/material/InternalMutatorMutex$Mutator.class", "size": 2049, "crc": 1314000369}, {"key": "androidx/compose/material/InternalMutatorMutex$mutate$2.class", "name": "androidx/compose/material/InternalMutatorMutex$mutate$2.class", "size": 7149, "crc": -997197017}, {"key": "androidx/compose/material/InternalMutatorMutex$mutateWith$2.class", "name": "androidx/compose/material/InternalMutatorMutex$mutateWith$2.class", "size": 7310, "crc": -1291108931}, {"key": "androidx/compose/material/InternalMutatorMutex.class", "name": "androidx/compose/material/InternalMutatorMutex.class", "size": 6742, "crc": 668672281}, {"key": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1.class", "name": "androidx/compose/material/ListItemKt$BaselinesOffsetColumn$1$1.class", "size": 7370, "crc": 715357823}, {"key": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1.class", "name": "androidx/compose/material/ListItemKt$OffsetToBaselineOrCenter$1$1.class", "size": 5540, "crc": -1788743744}, {"key": "androidx/compose/material/ListItemKt$applyTextStyle$1$1.class", "name": "androidx/compose/material/ListItemKt$applyTextStyle$1$1.class", "size": 3672, "crc": -2110600005}, {"key": "androidx/compose/material/ListItemKt$applyTextStyle$1.class", "name": "androidx/compose/material/ListItemKt$applyTextStyle$1.class", "size": 3856, "crc": 815431068}, {"key": "androidx/compose/material/ListItemKt.class", "name": "androidx/compose/material/ListItemKt.class", "size": 21382, "crc": -427515183}, {"key": "androidx/compose/material/LowContrastContentAlpha.class", "name": "androidx/compose/material/LowContrastContentAlpha.class", "size": 898, "crc": -1829228869}, {"key": "androidx/compose/material/MapDraggableAnchors.class", "name": "androidx/compose/material/MapDraggableAnchors.class", "size": 5347, "crc": 1373181687}, {"key": "androidx/compose/material/MaterialTextSelectionColorsKt.class", "name": "androidx/compose/material/MaterialTextSelectionColorsKt.class", "size": 7945, "crc": 28010327}, {"key": "androidx/compose/material/MaterialTheme.class", "name": "androidx/compose/material/MaterialTheme.class", "size": 4639, "crc": 1966594622}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1$1.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1$1.class", "size": 2457, "crc": 1658010551}, {"key": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1.class", "name": "androidx/compose/material/MaterialThemeKt$MaterialTheme$1.class", "size": 3281, "crc": -92580140}, {"key": "androidx/compose/material/MaterialThemeKt.class", "name": "androidx/compose/material/MaterialThemeKt.class", "size": 9654, "crc": -918570028}, {"key": "androidx/compose/material/MaterialTheme_androidKt.class", "name": "androidx/compose/material/MaterialTheme_androidKt.class", "size": 3355, "crc": 1760344946}, {"key": "androidx/compose/material/MenuDefaults.class", "name": "androidx/compose/material/MenuDefaults.class", "size": 2075, "crc": 95671973}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$2.class", "size": 10320, "crc": 356428820}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$alpha$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$alpha$2.class", "size": 3236, "crc": -1739259561}, {"key": "androidx/compose/material/MenuKt$DropdownMenuContent$scale$2.class", "name": "androidx/compose/material/MenuKt$DropdownMenuContent$scale$2.class", "size": 3364, "crc": 604552642}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1$1.class", "size": 2887, "crc": 2042796213}, {"key": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1.class", "name": "androidx/compose/material/MenuKt$DropdownMenuItemContent$1$1.class", "size": 4439, "crc": 2082369571}, {"key": "androidx/compose/material/MenuKt.class", "name": "androidx/compose/material/MenuKt.class", "size": 29448, "crc": 1396635854}, {"key": "androidx/compose/material/MinimumInteractiveComponentSizeModifier.class", "name": "androidx/compose/material/MinimumInteractiveComponentSizeModifier.class", "size": 4435, "crc": 1417108036}, {"key": "androidx/compose/material/MinimumInteractiveModifier.class", "name": "androidx/compose/material/MinimumInteractiveModifier.class", "size": 3177, "crc": 109342217}, {"key": "androidx/compose/material/MinimumInteractiveModifierNode.class", "name": "androidx/compose/material/MinimumInteractiveModifierNode.class", "size": 4700, "crc": 1454145279}, {"key": "androidx/compose/material/ModalBottomSheetDefaults.class", "name": "androidx/compose/material/ModalBottomSheetDefaults.class", "size": 4175, "crc": 1908430319}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPostFling$1.class", "size": 2074, "crc": -342607262}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1$onPreFling$1.class", "size": 2068, "crc": 1104450714}, {"key": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 8693, "crc": -1139331778}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$1$1$1$1.class", "size": 3589, "crc": -965688580}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$1$1.class", "size": 3589, "crc": 1500812169}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$2$1.class", "size": 3600, "crc": 23616137}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$3$1$3$1.class", "size": 3604, "crc": -963606500}, {"key": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$4.class", "name": "androidx/compose/material/ModalBottomSheetKt$ModalBottomSheetLayout$1$4.class", "size": 9509, "crc": 1036749200}, {"key": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1.class", "name": "androidx/compose/material/ModalBottomSheetKt$Scrim$dismissModifier$1$1.class", "size": 2814, "crc": 581561634}, {"key": "androidx/compose/material/ModalBottomSheetKt$WhenMappings.class", "name": "androidx/compose/material/ModalBottomSheetKt$WhenMappings.class", "size": 893, "crc": 1770109654}, {"key": "androidx/compose/material/ModalBottomSheetKt.class", "name": "androidx/compose/material/ModalBottomSheetKt.class", "size": 48097, "crc": 1106043792}, {"key": "androidx/compose/material/ModalBottomSheetState$Companion.class", "name": "androidx/compose/material/ModalBottomSheetState$Companion.class", "size": 4077, "crc": -1770955277}, {"key": "androidx/compose/material/ModalBottomSheetState$WhenMappings.class", "name": "androidx/compose/material/ModalBottomSheetState$WhenMappings.class", "size": 799, "crc": -254968054}, {"key": "androidx/compose/material/ModalBottomSheetState.class", "name": "androidx/compose/material/ModalBottomSheetState.class", "size": 12173, "crc": -1796738866}, {"key": "androidx/compose/material/ModalBottomSheetValue.class", "name": "androidx/compose/material/ModalBottomSheetValue.class", "size": 1986, "crc": -229306166}, {"key": "androidx/compose/material/MutableWindowInsets.class", "name": "androidx/compose/material/MutableWindowInsets.class", "size": 4121, "crc": -1080606114}, {"key": "androidx/compose/material/NavigationRailDefaults.class", "name": "androidx/compose/material/NavigationRailDefaults.class", "size": 3972, "crc": 744712578}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRail$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRail$1.class", "size": 11510, "crc": 1388409326}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$1$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$1$1.class", "size": 3393, "crc": 736403782}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "size": 4351, "crc": -652356802}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$2$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailItemBaselineLayout$2$1.class", "size": 5946, "crc": -1231886478}, {"key": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$1.class", "name": "androidx/compose/material/NavigationRailKt$NavigationRailTransition$1.class", "size": 3059, "crc": 1459395670}, {"key": "androidx/compose/material/NavigationRailKt.class", "name": "androidx/compose/material/NavigationRailKt.class", "size": 41475, "crc": -431835795}, {"key": "androidx/compose/material/OnGlobalLayoutListener.class", "name": "androidx/compose/material/OnGlobalLayoutListener.class", "size": 3008, "crc": -632925352}, {"key": "androidx/compose/material/OneLine.class", "name": "androidx/compose/material/OneLine.class", "size": 20266, "crc": 530781313}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13$1.class", "size": 3163, "crc": -543447331}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$13.class", "size": 6828, "crc": -1995984986}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3$Decoration$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3$Decoration$1.class", "size": 3188, "crc": 370707671}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$3.class", "size": 11384, "crc": 1965130524}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7$1.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7$1.class", "size": 3160, "crc": -1453117438}, {"key": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7.class", "name": "androidx/compose/material/OutlinedTextFieldKt$OutlinedTextField$7.class", "size": 6596, "crc": -205279487}, {"key": "androidx/compose/material/OutlinedTextFieldKt$WhenMappings.class", "name": "androidx/compose/material/OutlinedTextFieldKt$WhenMappings.class", "size": 772, "crc": 891800810}, {"key": "androidx/compose/material/OutlinedTextFieldKt.class", "name": "androidx/compose/material/OutlinedTextFieldKt.class", "size": 95417, "crc": 371534081}, {"key": "androidx/compose/material/OutlinedTextFieldMeasurePolicy.class", "name": "androidx/compose/material/OutlinedTextFieldMeasurePolicy.class", "size": 27601, "crc": 923770235}, {"key": "androidx/compose/material/ProgressIndicatorDefaults.class", "name": "androidx/compose/material/ProgressIndicatorDefaults.class", "size": 2631, "crc": -653739891}, {"key": "androidx/compose/material/ProgressIndicatorKt.class", "name": "androidx/compose/material/ProgressIndicatorKt.class", "size": 54011, "crc": 1225877216}, {"key": "androidx/compose/material/RadioButtonColors.class", "name": "androidx/compose/material/RadioButtonColors.class", "size": 1066, "crc": -2001920878}, {"key": "androidx/compose/material/RadioButtonDefaults.class", "name": "androidx/compose/material/RadioButtonDefaults.class", "size": 4700, "crc": 764557388}, {"key": "androidx/compose/material/RadioButtonKt.class", "name": "androidx/compose/material/RadioButtonKt.class", "size": 13427, "crc": -483686821}, {"key": "androidx/compose/material/RangeSliderLogic$captureThumb$1.class", "name": "androidx/compose/material/RangeSliderLogic$captureThumb$1.class", "size": 3800, "crc": 485410149}, {"key": "androidx/compose/material/RangeSliderLogic.class", "name": "androidx/compose/material/RangeSliderLogic.class", "size": 5215, "crc": -1611276937}, {"key": "androidx/compose/material/ResistanceConfig.class", "name": "androidx/compose/material/ResistanceConfig.class", "size": 4014, "crc": 175804039}, {"key": "androidx/compose/material/RippleConfiguration.class", "name": "androidx/compose/material/RippleConfiguration.class", "size": 3253, "crc": 2129286850}, {"key": "androidx/compose/material/RippleDefaults.class", "name": "androidx/compose/material/RippleDefaults.class", "size": 2134, "crc": 1982278303}, {"key": "androidx/compose/material/RippleKt.class", "name": "androidx/compose/material/RippleKt.class", "size": 5342, "crc": 1171249655}, {"key": "androidx/compose/material/RippleNodeFactory$create$colorProducer$1.class", "name": "androidx/compose/material/RippleNodeFactory$create$colorProducer$1.class", "size": 1066, "crc": 202635782}, {"key": "androidx/compose/material/RippleNodeFactory.class", "name": "androidx/compose/material/RippleNodeFactory.class", "size": 4282, "crc": 166605007}, {"key": "androidx/compose/material/ScaffoldDefaults.class", "name": "androidx/compose/material/ScaffoldDefaults.class", "size": 2425, "crc": 1976990892}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$1.class", "size": 3148, "crc": 786876202}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2$1.class", "size": 3054, "crc": -359359634}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1$2.class", "size": 5030, "crc": -390207105}, {"key": "androidx/compose/material/ScaffoldKt$Scaffold$child$1.class", "name": "androidx/compose/material/ScaffoldKt$Scaffold$child$1.class", "size": 9262, "crc": 658888022}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bodyContentPlaceables$1.class", "size": 3246, "crc": 510839252}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$1$1$bottomBarPlaceables$1.class", "size": 3384, "crc": 839526349}, {"key": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$contentPadding$1$1.class", "name": "androidx/compose/material/ScaffoldKt$ScaffoldLayout$contentPadding$1$1.class", "size": 4303, "crc": 1526100274}, {"key": "androidx/compose/material/ScaffoldKt.class", "name": "androidx/compose/material/ScaffoldKt.class", "size": 49574, "crc": 93230387}, {"key": "androidx/compose/material/ScaffoldLayoutContent.class", "name": "androidx/compose/material/ScaffoldLayoutContent.class", "size": 2097, "crc": 119564634}, {"key": "androidx/compose/material/ScaffoldState.class", "name": "androidx/compose/material/ScaffoldState.class", "size": 1435, "crc": -702357111}, {"key": "androidx/compose/material/ScrollableTabData$onLaidOut$1$1.class", "name": "androidx/compose/material/ScrollableTabData$onLaidOut$1$1.class", "size": 3705, "crc": 2056596116}, {"key": "androidx/compose/material/ScrollableTabData.class", "name": "androidx/compose/material/ScrollableTabData.class", "size": 4230, "crc": 1657242500}, {"key": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3$Decoration$1.class", "name": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3$Decoration$1.class", "size": 3200, "crc": 1946211714}, {"key": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3.class", "name": "androidx/compose/material/SecureTextFieldKt$OutlinedSecureTextField$3.class", "size": 8075, "crc": 820931664}, {"key": "androidx/compose/material/SecureTextFieldKt$SecureTextField$1.class", "name": "androidx/compose/material/SecureTextFieldKt$SecureTextField$1.class", "size": 7453, "crc": 1179660717}, {"key": "androidx/compose/material/SecureTextFieldKt.class", "name": "androidx/compose/material/SecureTextFieldKt.class", "size": 28289, "crc": 1264289537}, {"key": "androidx/compose/material/SelectableChipColors.class", "name": "androidx/compose/material/SelectableChipColors.class", "size": 1306, "crc": -2134819570}, {"key": "androidx/compose/material/Shapes.class", "name": "androidx/compose/material/Shapes.class", "size": 4852, "crc": 1591733659}, {"key": "androidx/compose/material/ShapesKt.class", "name": "androidx/compose/material/ShapesKt.class", "size": 1948, "crc": -462600544}, {"key": "androidx/compose/material/SliderColors.class", "name": "androidx/compose/material/SliderColors.class", "size": 1437, "crc": -1997608830}, {"key": "androidx/compose/material/SliderDefaults.class", "name": "androidx/compose/material/SliderDefaults.class", "size": 4588, "crc": 730255966}, {"key": "androidx/compose/material/SliderDraggableState$drag$2.class", "name": "androidx/compose/material/SliderDraggableState$drag$2.class", "size": 4466, "crc": 1901307192}, {"key": "androidx/compose/material/SliderDraggableState$dragScope$1.class", "name": "androidx/compose/material/SliderDraggableState$dragScope$1.class", "size": 1337, "crc": 262873878}, {"key": "androidx/compose/material/SliderDraggableState.class", "name": "androidx/compose/material/SliderDraggableState.class", "size": 6194, "crc": 2031390654}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$2$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$2$1.class", "size": 2331, "crc": 683426095}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$3$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$3$1.class", "size": 2331, "crc": -1025750883}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2$gestureEndAction$1$1$1.class", "size": 7527, "crc": -226117566}, {"key": "androidx/compose/material/SliderKt$RangeSlider$2.class", "name": "androidx/compose/material/SliderKt$RangeSlider$2.class", "size": 22502, "crc": 1553166767}, {"key": "androidx/compose/material/SliderKt$Slider$2$2$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$2$1.class", "size": 2316, "crc": 2131198454}, {"key": "androidx/compose/material/SliderKt$Slider$2$drag$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$drag$1$1.class", "size": 3367, "crc": -662727647}, {"key": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1$1.class", "name": "androidx/compose/material/SliderKt$Slider$2$gestureEndAction$1$1$1.class", "size": 3982, "crc": -1651641758}, {"key": "androidx/compose/material/SliderKt$Slider$2.class", "name": "androidx/compose/material/SliderKt$Slider$2.class", "size": 19419, "crc": 1148452167}, {"key": "androidx/compose/material/SliderKt$SliderThumb$1$1$1$1.class", "name": "androidx/compose/material/SliderKt$SliderThumb$1$1$1$1.class", "size": 3068, "crc": -191458770}, {"key": "androidx/compose/material/SliderKt$SliderThumb$1$1$1.class", "name": "androidx/compose/material/SliderKt$SliderThumb$1$1$1.class", "size": 4266, "crc": 2101525785}, {"key": "androidx/compose/material/SliderKt$animateToTarget$2.class", "name": "androidx/compose/material/SliderKt$animateToTarget$2.class", "size": 5166, "crc": 1670106425}, {"key": "androidx/compose/material/SliderKt$awaitSlop$1.class", "name": "androidx/compose/material/SliderKt$awaitSlop$1.class", "size": 1554, "crc": 1664513951}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "size": 4282, "crc": -941316148}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "size": 13736, "crc": -1379273107}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1$1.class", "size": 5303, "crc": -376655667}, {"key": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1.class", "name": "androidx/compose/material/SliderKt$rangeSliderPressDragModifier$1.class", "size": 4390, "crc": 468075267}, {"key": "androidx/compose/material/SliderKt$slideOnKeyEvents$2.class", "name": "androidx/compose/material/SliderKt$slideOnKeyEvents$2.class", "size": 5160, "crc": -909110292}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$$inlined$debugInspectorInfo$1.class", "size": 4041, "crc": -1276619611}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$1.class", "size": 6012, "crc": 1409149834}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1$1.class", "size": 3185, "crc": -1076691828}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1$2$1.class", "size": 4361, "crc": 787313263}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2$1$1.class", "size": 4758, "crc": 1238471796}, {"key": "androidx/compose/material/SliderKt$sliderTapModifier$2.class", "name": "androidx/compose/material/SliderKt$sliderTapModifier$2.class", "size": 8395, "crc": 1410712715}, {"key": "androidx/compose/material/SliderKt.class", "name": "androidx/compose/material/SliderKt.class", "size": 83633, "crc": 1495459252}, {"key": "androidx/compose/material/SnackbarData.class", "name": "androidx/compose/material/SnackbarData.class", "size": 950, "crc": 805871436}, {"key": "androidx/compose/material/SnackbarDefaults.class", "name": "androidx/compose/material/SnackbarDefaults.class", "size": 3185, "crc": -197611789}, {"key": "androidx/compose/material/SnackbarDuration.class", "name": "androidx/compose/material/SnackbarDuration.class", "size": 1940, "crc": 781279792}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "size": 16957, "crc": -1706382535}, {"key": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "size": 3132, "crc": 677049498}, {"key": "androidx/compose/material/SnackbarHostKt$SnackbarHost$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$SnackbarHost$1$1.class", "size": 4081, "crc": -2018708137}, {"key": "androidx/compose/material/SnackbarHostKt$WhenMappings.class", "name": "androidx/compose/material/SnackbarHostKt$WhenMappings.class", "size": 859, "crc": 1115124714}, {"key": "androidx/compose/material/SnackbarHostKt$animatedOpacity$2$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedOpacity$2$1.class", "size": 4557, "crc": -1039405691}, {"key": "androidx/compose/material/SnackbarHostKt$animatedScale$1$1.class", "name": "androidx/compose/material/SnackbarHostKt$animatedScale$1$1.class", "size": 4229, "crc": 1346166419}, {"key": "androidx/compose/material/SnackbarHostKt.class", "name": "androidx/compose/material/SnackbarHostKt.class", "size": 26916, "crc": 1049908735}, {"key": "androidx/compose/material/SnackbarHostState$SnackbarDataImpl.class", "name": "androidx/compose/material/SnackbarHostState$SnackbarDataImpl.class", "size": 3036, "crc": 1903743361}, {"key": "androidx/compose/material/SnackbarHostState$showSnackbar$1.class", "name": "androidx/compose/material/SnackbarHostState$showSnackbar$1.class", "size": 2028, "crc": -1834293967}, {"key": "androidx/compose/material/SnackbarHostState.class", "name": "androidx/compose/material/SnackbarHostState.class", "size": 8195, "crc": 1203232718}, {"key": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1.class", "name": "androidx/compose/material/SnackbarKt$OneRowSnackbar$2$1.class", "size": 7861, "crc": 2134161881}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1$1$1.class", "size": 3252, "crc": -1504999360}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1$1.class", "size": 3504, "crc": -1929309301}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$1.class", "size": 3892, "crc": 1667710140}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$3.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$3.class", "size": 2845, "crc": 2094093149}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$2.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1$2.class", "size": 2918, "crc": 904108472}, {"key": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1.class", "name": "androidx/compose/material/SnackbarKt$Snackbar$actionComposable$1.class", "size": 6046, "crc": -1749670293}, {"key": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2$1.class", "name": "androidx/compose/material/SnackbarKt$TextOnlySnackbar$2$1.class", "size": 5790, "crc": -2093378574}, {"key": "androidx/compose/material/SnackbarKt.class", "name": "androidx/compose/material/SnackbarKt.class", "size": 37795, "crc": 903174265}, {"key": "androidx/compose/material/SnackbarResult.class", "name": "androidx/compose/material/SnackbarResult.class", "size": 1877, "crc": -1956088234}, {"key": "androidx/compose/material/Strings$Companion.class", "name": "androidx/compose/material/Strings$Companion.class", "size": 2274, "crc": 1412569005}, {"key": "androidx/compose/material/Strings.class", "name": "androidx/compose/material/Strings.class", "size": 3368, "crc": 1608343539}, {"key": "androidx/compose/material/Strings_androidKt.class", "name": "androidx/compose/material/Strings_androidKt.class", "size": 4327, "crc": 1350351558}, {"key": "androidx/compose/material/SurfaceKt$Surface$1$2$1.class", "name": "androidx/compose/material/SurfaceKt$Surface$1$2$1.class", "size": 1257, "crc": -588837379}, {"key": "androidx/compose/material/SurfaceKt$Surface$1.class", "name": "androidx/compose/material/SurfaceKt$Surface$1.class", "size": 12821, "crc": 2108949019}, {"key": "androidx/compose/material/SurfaceKt$Surface$3.class", "name": "androidx/compose/material/SurfaceKt$Surface$3.class", "size": 11620, "crc": 546095825}, {"key": "androidx/compose/material/SurfaceKt$Surface$5.class", "name": "androidx/compose/material/SurfaceKt$Surface$5.class", "size": 11672, "crc": -354874185}, {"key": "androidx/compose/material/SurfaceKt$Surface$7.class", "name": "androidx/compose/material/SurfaceKt$Surface$7.class", "size": 11754, "crc": -985205627}, {"key": "androidx/compose/material/SurfaceKt.class", "name": "androidx/compose/material/SurfaceKt.class", "size": 26151, "crc": 573596192}, {"key": "androidx/compose/material/SwipeProgress.class", "name": "androidx/compose/material/SwipeProgress.class", "size": 2936, "crc": 1084925596}, {"key": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2.class", "name": "androidx/compose/material/SwipeToDismissKt$SwipeToDismiss$2.class", "size": 21878, "crc": -593721948}, {"key": "androidx/compose/material/SwipeToDismissKt.class", "name": "androidx/compose/material/SwipeToDismissKt.class", "size": 13645, "crc": 54461691}, {"key": "androidx/compose/material/SwipeableDefaults.class", "name": "androidx/compose/material/SwipeableDefaults.class", "size": 4085, "crc": -632247050}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPostFling$1.class", "size": 1835, "crc": -173616641}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1$onPreFling$1.class", "size": 1829, "crc": 1454001450}, {"key": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1.class", "name": "androidx/compose/material/SwipeableKt$PreUpPostDownNestedScrollConnection$1.class", "size": 7968, "crc": -1573552881}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$1$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$1$1.class", "size": 3899, "crc": -1848923810}, {"key": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$lambda$10$lambda$9$$inlined$onDispose$1.class", "name": "androidx/compose/material/SwipeableKt$rememberSwipeableStateFor$lambda$10$lambda$9$$inlined$onDispose$1.class", "size": 1971, "crc": -1890540208}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$3$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$3$1.class", "size": 6744, "crc": 289778799}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$4$1$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$4$1$1.class", "size": 3407, "crc": -763827784}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3$4$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3$4$1.class", "size": 3607, "crc": -1426508762}, {"key": "androidx/compose/material/SwipeableKt$swipeable$3.class", "name": "androidx/compose/material/SwipeableKt$swipeable$3.class", "size": 9681, "crc": -1021675910}, {"key": "androidx/compose/material/SwipeableKt$swipeable-pPrIpRY$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/SwipeableKt$swipeable-pPrIpRY$$inlined$debugInspectorInfo$1.class", "size": 4312, "crc": 854927896}, {"key": "androidx/compose/material/SwipeableKt.class", "name": "androidx/compose/material/SwipeableKt.class", "size": 24785, "crc": -587617495}, {"key": "androidx/compose/material/SwipeableState$Companion.class", "name": "androidx/compose/material/SwipeableState$Companion.class", "size": 3439, "crc": 1026752892}, {"key": "androidx/compose/material/SwipeableState$animateInternalToOffset$2.class", "name": "androidx/compose/material/SwipeableState$animateInternalToOffset$2.class", "size": 6303, "crc": 1583784983}, {"key": "androidx/compose/material/SwipeableState$animateTo$2$emit$1.class", "name": "androidx/compose/material/SwipeableState$animateTo$2$emit$1.class", "size": 1697, "crc": 562686601}, {"key": "androidx/compose/material/SwipeableState$animateTo$2.class", "name": "androidx/compose/material/SwipeableState$animateTo$2.class", "size": 6896, "crc": -1867262762}, {"key": "androidx/compose/material/SwipeableState$performFling$2.class", "name": "androidx/compose/material/SwipeableState$performFling$2.class", "size": 3689, "crc": -1090883514}, {"key": "androidx/compose/material/SwipeableState$processNewAnchors$1.class", "name": "androidx/compose/material/SwipeableState$processNewAnchors$1.class", "size": 1912, "crc": 1275046016}, {"key": "androidx/compose/material/SwipeableState$snapInternalToOffset$2.class", "name": "androidx/compose/material/SwipeableState$snapInternalToOffset$2.class", "size": 3623, "crc": -1312074811}, {"key": "androidx/compose/material/SwipeableState$snapTo$2$emit$1.class", "name": "androidx/compose/material/SwipeableState$snapTo$2$emit$1.class", "size": 1637, "crc": -1337385558}, {"key": "androidx/compose/material/SwipeableState$snapTo$2.class", "name": "androidx/compose/material/SwipeableState$snapTo$2.class", "size": 3791, "crc": 1051514725}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2$1.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2$1.class", "size": 1714, "crc": -78443765}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1$2.class", "size": 3769, "crc": -1362827065}, {"key": "androidx/compose/material/SwipeableState$special$$inlined$filter$1.class", "name": "androidx/compose/material/SwipeableState$special$$inlined$filter$1.class", "size": 3190, "crc": 153720372}, {"key": "androidx/compose/material/SwipeableState.class", "name": "androidx/compose/material/SwipeableState.class", "size": 28683, "crc": 297651986}, {"key": "androidx/compose/material/SwitchColors.class", "name": "androidx/compose/material/SwitchColors.class", "size": 1155, "crc": 576545827}, {"key": "androidx/compose/material/SwitchDefaults.class", "name": "androidx/compose/material/SwitchDefaults.class", "size": 4267, "crc": -1691499929}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1$2.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1$2.class", "size": 4364, "crc": 737435229}, {"key": "androidx/compose/material/SwitchKt$Switch$1$1.class", "name": "androidx/compose/material/SwitchKt$Switch$1$1.class", "size": 5545, "crc": -2096459133}, {"key": "androidx/compose/material/SwitchKt$Switch$2$1.class", "name": "androidx/compose/material/SwitchKt$Switch$2$1.class", "size": 3936, "crc": 461305586}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$1$1$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$1$1$1.class", "size": 3059, "crc": -31155789}, {"key": "androidx/compose/material/SwitchKt$SwitchImpl$1$1.class", "name": "androidx/compose/material/SwitchKt$SwitchImpl$1$1.class", "size": 4208, "crc": -1699654833}, {"key": "androidx/compose/material/SwitchKt.class", "name": "androidx/compose/material/SwitchKt.class", "size": 41740, "crc": 1836080678}, {"key": "androidx/compose/material/SystemBarsDefaultInsets_androidKt.class", "name": "androidx/compose/material/SystemBarsDefaultInsets_androidKt.class", "size": 2362, "crc": -2060793357}, {"key": "androidx/compose/material/TabKt$LeadingIconTab$1.class", "name": "androidx/compose/material/TabKt$LeadingIconTab$1.class", "size": 13566, "crc": -1271754150}, {"key": "androidx/compose/material/TabKt$Tab$1.class", "name": "androidx/compose/material/TabKt$Tab$1.class", "size": 3129, "crc": 330744224}, {"key": "androidx/compose/material/TabKt$Tab$3.class", "name": "androidx/compose/material/TabKt$Tab$3.class", "size": 10661, "crc": 78586186}, {"key": "androidx/compose/material/TabKt$Tab$styledText$1$1.class", "name": "androidx/compose/material/TabKt$Tab$styledText$1$1.class", "size": 4220, "crc": 1362401008}, {"key": "androidx/compose/material/TabKt$TabBaselineLayout$2$1.class", "name": "androidx/compose/material/TabKt$TabBaselineLayout$2$1.class", "size": 8434, "crc": -15497950}, {"key": "androidx/compose/material/TabKt$TabTransition$color$2.class", "name": "androidx/compose/material/TabKt$TabTransition$color$2.class", "size": 3230, "crc": -1931194364}, {"key": "androidx/compose/material/TabKt.class", "name": "androidx/compose/material/TabKt.class", "size": 40302, "crc": 131344103}, {"key": "androidx/compose/material/TabPosition.class", "name": "androidx/compose/material/TabPosition.class", "size": 3207, "crc": -2032332082}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "size": 2684, "crc": -1390728795}, {"key": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2.class", "name": "androidx/compose/material/TabRowDefaults$tabIndicatorOffset$2.class", "size": 8248, "crc": -571299681}, {"key": "androidx/compose/material/TabRowDefaults.class", "name": "androidx/compose/material/TabRowDefaults.class", "size": 10610, "crc": -407011500}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$1.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$1.class", "size": 3032, "crc": 390160622}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2$3.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2$1$1$2$3.class", "size": 2842, "crc": 1803387932}, {"key": "androidx/compose/material/TabRowKt$ScrollableTabRow$2.class", "name": "androidx/compose/material/TabRowKt$ScrollableTabRow$2.class", "size": 16685, "crc": -1880983904}, {"key": "androidx/compose/material/TabRowKt$TabRow$1.class", "name": "androidx/compose/material/TabRowKt$TabRow$1.class", "size": 2990, "crc": -1002858712}, {"key": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1$3.class", "name": "androidx/compose/material/TabRowKt$TabRow$2$1$1$1$3.class", "size": 2801, "crc": 2088145132}, {"key": "androidx/compose/material/TabRowKt$TabRow$2.class", "name": "androidx/compose/material/TabRowKt$TabRow$2.class", "size": 12936, "crc": -832109167}, {"key": "androidx/compose/material/TabRowKt.class", "name": "androidx/compose/material/TabRowKt.class", "size": 12876, "crc": -993562415}, {"key": "androidx/compose/material/TabSlots.class", "name": "androidx/compose/material/TabSlots.class", "size": 1879, "crc": -871979448}, {"key": "androidx/compose/material/TextFieldColors.class", "name": "androidx/compose/material/TextFieldColors.class", "size": 4382, "crc": 2078297445}, {"key": "androidx/compose/material/TextFieldColorsWithIcons.class", "name": "androidx/compose/material/TextFieldColorsWithIcons.class", "size": 858, "crc": 2041801666}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "size": 3470, "crc": -1714323244}, {"key": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "name": "androidx/compose/material/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "size": 3305, "crc": -1294143800}, {"key": "androidx/compose/material/TextFieldDefaults$indicatorLine$2.class", "name": "androidx/compose/material/TextFieldDefaults$indicatorLine$2.class", "size": 3686, "crc": 647320773}, {"key": "androidx/compose/material/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "size": 3754, "crc": -1712411821}, {"key": "androidx/compose/material/TextFieldDefaults.class", "name": "androidx/compose/material/TextFieldDefaults.class", "size": 49257, "crc": -553832126}, {"key": "androidx/compose/material/TextFieldDefaultsKt.class", "name": "androidx/compose/material/TextFieldDefaultsKt.class", "size": 6210, "crc": 507414338}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "size": 860, "crc": 1386942391}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "size": 4727, "crc": 1514311762}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "size": 2807, "crc": 480791710}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "size": 10427, "crc": 1168858790}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "size": 2810, "crc": -179484762}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$drawBorder$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3$drawBorder$1.class", "size": 10063, "crc": 1083879907}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$3.class", "size": 17017, "crc": 1958846685}, {"key": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "name": "androidx/compose/material/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "size": 3635, "crc": 1338642583}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1$1.class", "size": 3408, "crc": -569531915}, {"key": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1.class", "name": "androidx/compose/material/TextFieldImplKt$Decoration$colorAndEmphasis$1.class", "size": 3675, "crc": 1943700418}, {"key": "androidx/compose/material/TextFieldImplKt.class", "name": "androidx/compose/material/TextFieldImplKt.class", "size": 23679, "crc": 237503940}, {"key": "androidx/compose/material/TextFieldKt$TextField$1.class", "name": "androidx/compose/material/TextFieldKt$TextField$1.class", "size": 10559, "crc": 1084931958}, {"key": "androidx/compose/material/TextFieldKt$TextField$3.class", "name": "androidx/compose/material/TextFieldKt$TextField$3.class", "size": 5958, "crc": 719855215}, {"key": "androidx/compose/material/TextFieldKt$TextField$7.class", "name": "androidx/compose/material/TextFieldKt$TextField$7.class", "size": 6187, "crc": -115959841}, {"key": "androidx/compose/material/TextFieldKt.class", "name": "androidx/compose/material/TextFieldKt.class", "size": 88149, "crc": 274047169}, {"key": "androidx/compose/material/TextFieldMeasurePolicy.class", "name": "androidx/compose/material/TextFieldMeasurePolicy.class", "size": 25533, "crc": -499642189}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelContentColor$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelContentColor$2.class", "size": 3069, "crc": 435118701}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelProgress$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelProgress$2.class", "size": 3023, "crc": -1782993080}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "size": 3073, "crc": -1326976030}, {"key": "androidx/compose/material/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "name": "androidx/compose/material/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "size": 3691, "crc": -508077868}, {"key": "androidx/compose/material/TextFieldTransitionScope$WhenMappings.class", "name": "androidx/compose/material/TextFieldTransitionScope$WhenMappings.class", "size": 881, "crc": 993001968}, {"key": "androidx/compose/material/TextFieldTransitionScope.class", "name": "androidx/compose/material/TextFieldTransitionScope.class", "size": 18832, "crc": -1285422827}, {"key": "androidx/compose/material/TextFieldType.class", "name": "androidx/compose/material/TextFieldType.class", "size": 1861, "crc": 1736624838}, {"key": "androidx/compose/material/TextKt$Text$1$1.class", "name": "androidx/compose/material/TextKt$Text$1$1.class", "size": 1122, "crc": -888752580}, {"key": "androidx/compose/material/TextKt$Text$6$1.class", "name": "androidx/compose/material/TextKt$Text$6$1.class", "size": 1161, "crc": -1519971186}, {"key": "androidx/compose/material/TextKt.class", "name": "androidx/compose/material/TextKt.class", "size": 45142, "crc": -1556021859}, {"key": "androidx/compose/material/ThreeLine$ListItem$1$2.class", "name": "androidx/compose/material/ThreeLine$ListItem$1$2.class", "size": 3247, "crc": -1501725911}, {"key": "androidx/compose/material/ThreeLine.class", "name": "androidx/compose/material/ThreeLine.class", "size": 19579, "crc": 53594744}, {"key": "androidx/compose/material/ThresholdConfig.class", "name": "androidx/compose/material/ThresholdConfig.class", "size": 1032, "crc": -1123134775}, {"key": "androidx/compose/material/TwoLine$ListItem$1$2.class", "name": "androidx/compose/material/TwoLine$ListItem$1$2.class", "size": 2886, "crc": 1641299887}, {"key": "androidx/compose/material/TwoLine$ListItem$1$3.class", "name": "androidx/compose/material/TwoLine$ListItem$1$3.class", "size": 2976, "crc": -1176452499}, {"key": "androidx/compose/material/TwoLine$ListItem$1$4.class", "name": "androidx/compose/material/TwoLine$ListItem$1$4.class", "size": 9209, "crc": 399257777}, {"key": "androidx/compose/material/TwoLine.class", "name": "androidx/compose/material/TwoLine.class", "size": 20693, "crc": 920439769}, {"key": "androidx/compose/material/Typography.class", "name": "androidx/compose/material/Typography.class", "size": 13002, "crc": -1971097029}, {"key": "androidx/compose/material/TypographyKt.class", "name": "androidx/compose/material/TypographyKt.class", "size": 5408, "crc": 1871136523}, {"key": "androidx/compose/material/WindowBoundsCalculator.class", "name": "androidx/compose/material/WindowBoundsCalculator.class", "size": 1328, "crc": 868000160}, {"key": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda$-1578637197$1.class", "name": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda$-1578637197$1.class", "size": 2245, "crc": 1747118933}, {"key": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "size": 1671, "crc": -1824469432}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6$1.class", "size": 2866, "crc": 389521431}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$lambda$12$lambda$11$$inlined$onDispose$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$lambda$12$lambda$11$$inlined$onDispose$1.class", "size": 2174, "crc": 2054899457}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$lambda$7$lambda$6$$inlined$onDispose$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$lambda$7$lambda$6$$inlined$onDispose$1.class", "size": 2450, "crc": -944574331}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "size": 12870, "crc": 1231277878}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1$1.class", "size": 1610, "crc": -963030211}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1$2.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1$2.class", "size": 1828, "crc": -2105773995}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1$3.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1$3.class", "size": 2234, "crc": 1292403579}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "size": 4951, "crc": -740040648}, {"key": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material/internal/ExposedDropdownMenuPopup_androidKt.class", "size": 27506, "crc": 136687784}, {"key": "androidx/compose/material/internal/Icons$Filled.class", "name": "androidx/compose/material/internal/Icons$Filled.class", "size": 6202, "crc": 2003142228}, {"key": "androidx/compose/material/internal/Icons.class", "name": "androidx/compose/material/internal/Icons.class", "size": 893, "crc": 1071562365}, {"key": "androidx/compose/material/internal/IconsKt.class", "name": "androidx/compose/material/internal/IconsKt.class", "size": 6656, "crc": -2059019612}, {"key": "androidx/compose/material/internal/LayoutUtilKt.class", "name": "androidx/compose/material/internal/LayoutUtilKt.class", "size": 643, "crc": 1818540222}, {"key": "androidx/compose/material/internal/PlatformOptimizedCancellationException.class", "name": "androidx/compose/material/internal/PlatformOptimizedCancellationException.class", "size": 1792, "crc": 278395989}, {"key": "androidx/compose/material/internal/PlatformOptimizedCancellationException_jvmKt.class", "name": "androidx/compose/material/internal/PlatformOptimizedCancellationException_jvmKt.class", "size": 842, "crc": -1496477062}, {"key": "androidx/compose/material/internal/PopupLayout$2.class", "name": "androidx/compose/material/internal/PopupLayout$2.class", "size": 1352, "crc": -894256833}, {"key": "androidx/compose/material/internal/PopupLayout$WhenMappings.class", "name": "androidx/compose/material/internal/PopupLayout$WhenMappings.class", "size": 832, "crc": -617542496}, {"key": "androidx/compose/material/internal/PopupLayout.class", "name": "androidx/compose/material/internal/PopupLayout.class", "size": 23864, "crc": -1171083546}, {"key": "androidx/compose/material/internal/System_jvmKt.class", "name": "androidx/compose/material/internal/System_jvmKt.class", "size": 656, "crc": -1099332111}, {"key": "androidx/compose/material/pullrefresh/ArrowValues.class", "name": "androidx/compose/material/pullrefresh/ArrowValues.class", "size": 1302, "crc": 825486587}, {"key": "androidx/compose/material/pullrefresh/PullRefreshDefaults.class", "name": "androidx/compose/material/pullrefresh/PullRefreshDefaults.class", "size": 2217, "crc": 1153505453}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt$PullRefreshIndicator$1$1.class", "size": 10798, "crc": 1925973177}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorKt.class", "size": 35350, "crc": -1900312880}, {"key": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshIndicatorTransformKt.class", "size": 7964, "crc": 1359751088}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$1.class", "size": 1562, "crc": 681526905}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$2.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt$pullRefresh$2.class", "size": 2048, "crc": -283569094}, {"key": "androidx/compose/material/pullrefresh/PullRefreshKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshKt.class", "size": 3899, "crc": -1853419526}, {"key": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection$onPreFling$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection$onPreFling$1.class", "size": 1941, "crc": 1605007341}, {"key": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection.class", "name": "androidx/compose/material/pullrefresh/PullRefreshNestedScrollConnection.class", "size": 7958, "crc": -552745664}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1$1.class", "size": 4227, "crc": -1058450111}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState$animateIndicatorTo$1.class", "size": 3900, "crc": -1112382932}, {"key": "androidx/compose/material/pullrefresh/PullRefreshState.class", "name": "androidx/compose/material/pullrefresh/PullRefreshState.class", "size": 11540, "crc": -395703512}, {"key": "androidx/compose/material/pullrefresh/PullRefreshStateKt.class", "name": "androidx/compose/material/pullrefresh/PullRefreshStateKt.class", "size": 10106, "crc": 1829375426}, {"key": "META-INF/androidx.compose.material_material.version", "name": "META-INF/androidx.compose.material_material.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/material.kotlin_module", "name": "META-INF/material.kotlin_module", "size": 1338, "crc": -683554494}]