{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeReleaseResources-31:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3bdf59f548fbb8efb4688937f11638bc\\transformed\\core-1.15.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "3,4,5,6,7,8,9,26", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "186,284,386,486,587,693,796,2283", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "279,381,481,582,688,791,912,2379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7c6efc5356a40c412603779ad33ac6f6\\transformed\\foundation-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,223", "endColumns": "80,86,102", "endOffsets": "131,218,321"}, "to": {"startLines": "2,30,31", "startColumns": "4,4,4", "startOffsets": "105,2662,2749", "endColumns": "80,86,102", "endOffsets": "181,2744,2847"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c4c82e9dfe5df03207c372a77c2a15c6\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "187,280,364,458,561,647,727,816,904,986,1069,1156,1228,1315,1399,1477,1553,1638,1708", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "275,359,453,556,642,722,811,899,981,1064,1151,1223,1310,1394,1472,1548,1633,1703,1826"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "917,1010,1094,1188,1291,1377,1457,1546,1634,1716,1799,1886,1958,2045,2129,2207,2384,2469,2539", "endColumns": "92,83,93,102,85,79,88,87,81,82,86,71,86,83,77,75,84,69,122", "endOffsets": "1005,1089,1183,1286,1372,1452,1541,1629,1711,1794,1881,1953,2040,2124,2202,2278,2464,2534,2657"}}]}]}