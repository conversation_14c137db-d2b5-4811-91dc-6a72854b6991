[{"key": "androidx/compose/foundation/layout/AddedInsets.class", "name": "androidx/compose/foundation/layout/AddedInsets.class", "size": 2934, "crc": -811729543}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$alignmentLineOffsetMeasure$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$alignmentLineOffsetMeasure$1.class", "size": 3004, "crc": -1352026872}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "size": 3272, "crc": 69988117}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "size": 3287, "crc": 1924138481}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt.class", "size": 9698, "crc": -798316957}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "size": 5685, "crc": 884055174}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "size": 3255, "crc": -175190270}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "size": 5188, "crc": 24464800}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "size": 3902, "crc": -**********}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "size": 3857, "crc": -586210833}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "size": 3268, "crc": -643820832}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "size": 1568, "crc": -**********}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "size": 3881, "crc": 30521240}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "size": 3718, "crc": **********}, {"key": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "name": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "size": 6131, "crc": -6775225}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "size": 1833, "crc": -442127141}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "size": 1828, "crc": **********}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "size": 1837, "crc": 518350846}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "size": 1853, "crc": 2098153500}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "size": 1857, "crc": 2119394810}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "size": 1853, "crc": 1239335962}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$aligned$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$aligned$1.class", "size": 2232, "crc": -812151617}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$1.class", "size": 2244, "crc": 152679370}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$2.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$spacedBy$2.class", "size": 2185, "crc": -2056068360}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "size": 6415, "crc": 822633287}, {"key": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "size": 1586, "crc": 1803815771}, {"key": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "size": 3147, "crc": -1912234703}, {"key": "androidx/compose/foundation/layout/Arrangement$End$1.class", "name": "androidx/compose/foundation/layout/Arrangement$End$1.class", "size": 1910, "crc": 1283330564}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "size": 915, "crc": 1118790077}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "size": 2404, "crc": 2010277796}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "size": 965, "crc": -1721520488}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "size": 2334, "crc": -719322561}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "size": 3187, "crc": 1233900327}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "size": 3195, "crc": 778134367}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "size": 3187, "crc": 1586361559}, {"key": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "name": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "size": 8460, "crc": -1538518671}, {"key": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "size": 1916, "crc": -967891837}, {"key": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "size": 1571, "crc": -699887751}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "size": 905, "crc": -236069378}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "size": 2254, "crc": -1122261376}, {"key": "androidx/compose/foundation/layout/Arrangement$aligned$1.class", "name": "androidx/compose/foundation/layout/Arrangement$aligned$1.class", "size": 2134, "crc": 1832446804}, {"key": "androidx/compose/foundation/layout/Arrangement$aligned$2.class", "name": "androidx/compose/foundation/layout/Arrangement$aligned$2.class", "size": 2075, "crc": -2021087795}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$1.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$1.class", "size": 2290, "crc": -1059302418}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$2.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$2.class", "size": 2146, "crc": 654182400}, {"key": "androidx/compose/foundation/layout/Arrangement$spacedBy$3.class", "name": "androidx/compose/foundation/layout/Arrangement$spacedBy$3.class", "size": 2087, "crc": 2139226108}, {"key": "androidx/compose/foundation/layout/Arrangement.class", "name": "androidx/compose/foundation/layout/Arrangement.class", "size": 16587, "crc": 41591813}, {"key": "androidx/compose/foundation/layout/AspectRatioElement.class", "name": "androidx/compose/foundation/layout/AspectRatioElement.class", "size": 4901, "crc": 1345388090}, {"key": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "size": 3076, "crc": 1921233545}, {"key": "androidx/compose/foundation/layout/AspectRatioKt.class", "name": "androidx/compose/foundation/layout/AspectRatioKt.class", "size": 2608, "crc": 148537336}, {"key": "androidx/compose/foundation/layout/AspectRatioNode$measure$1.class", "name": "androidx/compose/foundation/layout/AspectRatioNode$measure$1.class", "size": 1953, "crc": 1551689492}, {"key": "androidx/compose/foundation/layout/AspectRatioNode.class", "name": "androidx/compose/foundation/layout/AspectRatioNode.class", "size": 9532, "crc": 1885427224}, {"key": "androidx/compose/foundation/layout/BoxChildDataElement.class", "name": "androidx/compose/foundation/layout/BoxChildDataElement.class", "size": 4229, "crc": 365129535}, {"key": "androidx/compose/foundation/layout/BoxChildDataNode.class", "name": "androidx/compose/foundation/layout/BoxChildDataNode.class", "size": 2294, "crc": 1718351149}, {"key": "androidx/compose/foundation/layout/BoxKt$Box$$inlined$Layout$1.class", "name": "androidx/compose/foundation/layout/BoxKt$Box$$inlined$Layout$1.class", "size": 2062, "crc": 14485483}, {"key": "androidx/compose/foundation/layout/BoxKt$Box$2.class", "name": "androidx/compose/foundation/layout/BoxKt$Box$2.class", "size": 1669, "crc": -1904307695}, {"key": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1$1.class", "size": 1737, "crc": -270940039}, {"key": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "name": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "size": 2151, "crc": -2146652804}, {"key": "androidx/compose/foundation/layout/BoxKt.class", "name": "androidx/compose/foundation/layout/BoxKt.class", "size": 14751, "crc": -37527012}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$1.class", "size": 1706, "crc": 551448929}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$2.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$2.class", "size": 2853, "crc": 1458073867}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$5.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy$measure$5.class", "size": 4890, "crc": -593944097}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "size": 8425, "crc": 409370281}, {"key": "androidx/compose/foundation/layout/BoxScope.class", "name": "androidx/compose/foundation/layout/BoxScope.class", "size": 1071, "crc": 279748067}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "size": 2732, "crc": -1698017235}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "size": 2585, "crc": -1349249634}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance.class", "size": 3514, "crc": 1805578982}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "size": 3275, "crc": -991322189}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1.class", "size": 4505, "crc": -1974102435}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$2.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$2.class", "size": 2612, "crc": -2009515386}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "size": 6140, "crc": 1974806084}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "size": 1146, "crc": 2055382071}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "size": 6624, "crc": 52665767}, {"key": "androidx/compose/foundation/layout/ColumnKt.class", "name": "androidx/compose/foundation/layout/ColumnKt.class", "size": 11511, "crc": -189050670}, {"key": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "size": 605, "crc": -2079010749}, {"key": "androidx/compose/foundation/layout/ColumnScope.class", "name": "androidx/compose/foundation/layout/ColumnScope.class", "size": 2644, "crc": -1917399722}, {"key": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "size": 4455, "crc": 612941088}, {"key": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "name": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "size": 2993, "crc": -1726350625}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "size": 2739, "crc": -594006314}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "size": 1540, "crc": -1266305374}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "size": 4203, "crc": 929707196}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "size": 1629, "crc": 1800379212}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "size": 3548, "crc": -2110774609}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "size": 1635, "crc": 1567702454}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "size": 3471, "crc": 1713153342}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "size": 3879, "crc": -269121340}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$1.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$1.class", "size": 1757, "crc": -1248397845}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$2.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier$measure$2.class", "size": 1976, "crc": 635133380}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "size": 7835, "crc": -1028279573}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$1.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$1.class", "size": 1754, "crc": 1712189589}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$2.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier$measure$2.class", "size": 1973, "crc": -1086161386}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "size": 8076, "crc": -982162151}, {"key": "androidx/compose/foundation/layout/Direction.class", "name": "androidx/compose/foundation/layout/Direction.class", "size": 1480, "crc": 1502865803}, {"key": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "size": 906, "crc": 1720204429}, {"key": "androidx/compose/foundation/layout/ExcludeInsets.class", "name": "androidx/compose/foundation/layout/ExcludeInsets.class", "size": 3029, "crc": -1154971614}, {"key": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "name": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "size": 831, "crc": -1163375034}, {"key": "androidx/compose/foundation/layout/FillElement$Companion.class", "name": "androidx/compose/foundation/layout/FillElement$Companion.class", "size": 1827, "crc": 939023600}, {"key": "androidx/compose/foundation/layout/FillElement.class", "name": "androidx/compose/foundation/layout/FillElement.class", "size": 3837, "crc": 460064110}, {"key": "androidx/compose/foundation/layout/FillNode$measure$1.class", "name": "androidx/compose/foundation/layout/FillNode$measure$1.class", "size": 1925, "crc": -2026697405}, {"key": "androidx/compose/foundation/layout/FillNode.class", "name": "androidx/compose/foundation/layout/FillNode.class", "size": 3963, "crc": -1072419889}, {"key": "androidx/compose/foundation/layout/FixedDpInsets.class", "name": "androidx/compose/foundation/layout/FixedDpInsets.class", "size": 4410, "crc": 298049651}, {"key": "androidx/compose/foundation/layout/FixedIntInsets.class", "name": "androidx/compose/foundation/layout/FixedIntInsets.class", "size": 2890, "crc": -1446408832}, {"key": "androidx/compose/foundation/layout/FlowColumnScope.class", "name": "androidx/compose/foundation/layout/FlowColumnScope.class", "size": 733, "crc": -1669048165}, {"key": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "size": 3159, "crc": -963210977}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$1.class", "size": 1827, "crc": 2021401845}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$nextSize$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$breakDownItems$nextSize$1.class", "size": 1793, "crc": 1044265341}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$1.class", "size": 1908, "crc": -192852496}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$2.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$intrinsicCrossAxisSize$2.class", "size": 1909, "crc": -836001151}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt.class", "size": 31137, "crc": 1582095604}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$1.class", "size": 2424, "crc": -1365425697}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxCrossAxisIntrinsicItemSize$2.class", "size": 2423, "crc": 579256002}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$1.class", "size": 2421, "crc": -1760351199}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$maxMainAxisIntrinsicItemSize$2.class", "size": 2422, "crc": -407303510}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$1.class", "size": 1716, "crc": 1001436406}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$6.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$measure$6.class", "size": 4296, "crc": 2015875684}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$1.class", "size": 2424, "crc": 555456705}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minCrossAxisIntrinsicItemSize$2.class", "size": 2423, "crc": -14989866}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$1.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$1.class", "size": 2421, "crc": 971238559}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$2.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy$minMainAxisIntrinsicItemSize$2.class", "size": 2422, "crc": 2041762223}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "size": 19722, "crc": 1818833741}, {"key": "androidx/compose/foundation/layout/FlowResult.class", "name": "androidx/compose/foundation/layout/FlowResult.class", "size": 2123, "crc": -1981646909}, {"key": "androidx/compose/foundation/layout/FlowRowScope.class", "name": "androidx/compose/foundation/layout/FlowRowScope.class", "size": 721, "crc": -649624993}, {"key": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "size": 3360, "crc": -1503589447}, {"key": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "name": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "size": 3416, "crc": -924138708}, {"key": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "name": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "size": 3158, "crc": 1886672712}, {"key": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "size": 5331, "crc": -1705331841}, {"key": "androidx/compose/foundation/layout/InsetsListener.class", "name": "androidx/compose/foundation/layout/InsetsListener.class", "size": 5810, "crc": -1680132943}, {"key": "androidx/compose/foundation/layout/InsetsPaddingModifier$measure$1.class", "name": "androidx/compose/foundation/layout/InsetsPaddingModifier$measure$1.class", "size": 2064, "crc": 2057443435}, {"key": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "name": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "size": 8452, "crc": 1080258992}, {"key": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "name": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "size": 4215, "crc": -966126219}, {"key": "androidx/compose/foundation/layout/InsetsValues.class", "name": "androidx/compose/foundation/layout/InsetsValues.class", "size": 2350, "crc": -24157792}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "size": 4263, "crc": 794606058}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "size": 3517, "crc": 911523577}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "size": 2952, "crc": 432770804}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "size": 2994, "crc": 687580605}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "size": 2987, "crc": -2010607287}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "size": 2947, "crc": 1599641938}, {"key": "androidx/compose/foundation/layout/IntrinsicKt.class", "name": "androidx/compose/foundation/layout/IntrinsicKt.class", "size": 3872, "crc": -1600289280}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxHeight$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxHeight$1$1.class", "size": 1913, "crc": 1491594858}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxHeight$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxHeight$1$2.class", "size": 1912, "crc": 1988970381}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxHeight$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxHeight$1.class", "size": 2901, "crc": 1820458018}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxWidth$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxWidth$1$1.class", "size": 1909, "crc": -1160679904}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxWidth$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxWidth$1$2.class", "size": 1910, "crc": 1517405539}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxWidth$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMaxWidth$1.class", "size": 2875, "crc": -390763927}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinHeight$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinHeight$1$1.class", "size": 1913, "crc": 1001255343}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinHeight$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinHeight$1$2.class", "size": 1912, "crc": -1873291125}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinHeight$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinHeight$1.class", "size": 2901, "crc": 772043111}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinWidth$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinWidth$1$1.class", "size": 1909, "crc": -1018423712}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinWidth$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinWidth$1$2.class", "size": 1910, "crc": -1423467175}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinWidth$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$HorizontalMinWidth$1.class", "size": 2875, "crc": 274884730}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxHeight$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxHeight$1$1.class", "size": 1907, "crc": -599954106}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxHeight$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxHeight$1$2.class", "size": 1906, "crc": 84323955}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxHeight$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxHeight$1.class", "size": 2866, "crc": 59579871}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxWidth$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxWidth$1$1.class", "size": 1903, "crc": 1010094266}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxWidth$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxWidth$1$2.class", "size": 1904, "crc": -950537832}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxWidth$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMaxWidth$1.class", "size": 2884, "crc": 1198161552}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinHeight$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinHeight$1$1.class", "size": 1907, "crc": -2038402214}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinHeight$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinHeight$1$2.class", "size": 1906, "crc": 460213758}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinHeight$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinHeight$1.class", "size": 2866, "crc": -1247170606}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinWidth$1$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinWidth$1$1.class", "size": 1903, "crc": -1801462616}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinWidth$1$2.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinWidth$1$2.class", "size": 1904, "crc": 613244222}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinWidth$1.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks$VerticalMinWidth$1.class", "size": 2884, "crc": -934278091}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "size": 4650, "crc": -1832221513}, {"key": "androidx/compose/foundation/layout/IntrinsicSize.class", "name": "androidx/compose/foundation/layout/IntrinsicSize.class", "size": 1443, "crc": 358141639}, {"key": "androidx/compose/foundation/layout/IntrinsicSizeModifier$measure$1.class", "name": "androidx/compose/foundation/layout/IntrinsicSizeModifier$measure$1.class", "size": 2183, "crc": -865433512}, {"key": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "name": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "size": 3942, "crc": 1627977476}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "size": 4253, "crc": -527285212}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "size": 3508, "crc": -1146736258}, {"key": "androidx/compose/foundation/layout/LayoutOrientation.class", "name": "androidx/compose/foundation/layout/LayoutOrientation.class", "size": 1487, "crc": -468359287}, {"key": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "name": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "size": 628, "crc": -1348536247}, {"key": "androidx/compose/foundation/layout/LayoutWeightElement.class", "name": "androidx/compose/foundation/layout/LayoutWeightElement.class", "size": 3679, "crc": 341344901}, {"key": "androidx/compose/foundation/layout/LayoutWeightNode.class", "name": "androidx/compose/foundation/layout/LayoutWeightNode.class", "size": 2706, "crc": -1728143810}, {"key": "androidx/compose/foundation/layout/LimitInsets.class", "name": "androidx/compose/foundation/layout/LimitInsets.class", "size": 4583, "crc": -905006986}, {"key": "androidx/compose/foundation/layout/MutableWindowInsets.class", "name": "androidx/compose/foundation/layout/MutableWindowInsets.class", "size": 4218, "crc": -1252222458}, {"key": "androidx/compose/foundation/layout/OffsetElement.class", "name": "androidx/compose/foundation/layout/OffsetElement.class", "size": 5071, "crc": 1106817153}, {"key": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$1.class", "name": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$1.class", "size": 1986, "crc": -562145818}, {"key": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$2.class", "name": "androidx/compose/foundation/layout/OffsetKt$absoluteOffset$2.class", "size": 2139, "crc": 1799264784}, {"key": "androidx/compose/foundation/layout/OffsetKt$offset$1.class", "name": "androidx/compose/foundation/layout/OffsetKt$offset$1.class", "size": 1954, "crc": -514628315}, {"key": "androidx/compose/foundation/layout/OffsetKt$offset$2.class", "name": "androidx/compose/foundation/layout/OffsetKt$offset$2.class", "size": 2103, "crc": 974397725}, {"key": "androidx/compose/foundation/layout/OffsetKt.class", "name": "androidx/compose/foundation/layout/OffsetKt.class", "size": 4215, "crc": -2007950371}, {"key": "androidx/compose/foundation/layout/OffsetNode$measure$1.class", "name": "androidx/compose/foundation/layout/OffsetNode$measure$1.class", "size": 2532, "crc": -2052193526}, {"key": "androidx/compose/foundation/layout/OffsetNode.class", "name": "androidx/compose/foundation/layout/OffsetNode.class", "size": 3426, "crc": -945287518}, {"key": "androidx/compose/foundation/layout/OffsetPxElement.class", "name": "androidx/compose/foundation/layout/OffsetPxElement.class", "size": 4992, "crc": -754477686}, {"key": "androidx/compose/foundation/layout/OffsetPxNode$measure$1.class", "name": "androidx/compose/foundation/layout/OffsetPxNode$measure$1.class", "size": 2657, "crc": 1697003229}, {"key": "androidx/compose/foundation/layout/OffsetPxNode.class", "name": "androidx/compose/foundation/layout/OffsetPxNode.class", "size": 3739, "crc": 1728286375}, {"key": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "name": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "size": 7037, "crc": 487625445}, {"key": "androidx/compose/foundation/layout/PaddingElement.class", "name": "androidx/compose/foundation/layout/PaddingElement.class", "size": 7295, "crc": -766788696}, {"key": "androidx/compose/foundation/layout/PaddingKt$absolutePadding$1.class", "name": "androidx/compose/foundation/layout/PaddingKt$absolutePadding$1.class", "size": 2157, "crc": 1321197085}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$1.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$1.class", "size": 2123, "crc": -1979813335}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$2.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$2.class", "size": 1994, "crc": 78383553}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$3.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$3.class", "size": 1735, "crc": -1153679880}, {"key": "androidx/compose/foundation/layout/PaddingKt$padding$4.class", "name": "androidx/compose/foundation/layout/PaddingKt$padding$4.class", "size": 1956, "crc": -45568200}, {"key": "androidx/compose/foundation/layout/PaddingKt.class", "name": "androidx/compose/foundation/layout/PaddingKt.class", "size": 8158, "crc": 456228301}, {"key": "androidx/compose/foundation/layout/PaddingNode$measure$1.class", "name": "androidx/compose/foundation/layout/PaddingNode$measure$1.class", "size": 2544, "crc": -619997898}, {"key": "androidx/compose/foundation/layout/PaddingNode.class", "name": "androidx/compose/foundation/layout/PaddingNode.class", "size": 5610, "crc": -1431386526}, {"key": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "name": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "size": 5138, "crc": 1368294868}, {"key": "androidx/compose/foundation/layout/PaddingValues.class", "name": "androidx/compose/foundation/layout/PaddingValues.class", "size": 1301, "crc": -424052114}, {"key": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "size": 2351, "crc": -705392374}, {"key": "androidx/compose/foundation/layout/PaddingValuesElement.class", "name": "androidx/compose/foundation/layout/PaddingValuesElement.class", "size": 3952, "crc": -1541020161}, {"key": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "name": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "size": 5499, "crc": -1232138010}, {"key": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "name": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "size": 3914, "crc": -2033768269}, {"key": "androidx/compose/foundation/layout/PaddingValuesModifier$measure$2.class", "name": "androidx/compose/foundation/layout/PaddingValuesModifier$measure$2.class", "size": 2760, "crc": -739948517}, {"key": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "size": 5082, "crc": 579389996}, {"key": "androidx/compose/foundation/layout/RowColumnImplKt.class", "name": "androidx/compose/foundation/layout/RowColumnImplKt.class", "size": 10553, "crc": -107871290}, {"key": "androidx/compose/foundation/layout/RowColumnMeasureHelperResult.class", "name": "androidx/compose/foundation/layout/RowColumnMeasureHelperResult.class", "size": 2008, "crc": -2015293680}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicy$measure$1.class", "size": 2602, "crc": -874033615}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "size": 11412, "crc": 620431346}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurementHelper.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurementHelper.class", "size": 18453, "crc": 889394874}, {"key": "androidx/compose/foundation/layout/RowColumnParentData.class", "name": "androidx/compose/foundation/layout/RowColumnParentData.class", "size": 4440, "crc": -1013208311}, {"key": "androidx/compose/foundation/layout/RowKt.class", "name": "androidx/compose/foundation/layout/RowKt.class", "size": 11402, "crc": -401466143}, {"key": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "size": 593, "crc": 1922927788}, {"key": "androidx/compose/foundation/layout/RowScope.class", "name": "androidx/compose/foundation/layout/RowScope.class", "size": 2763, "crc": -1024677644}, {"key": "androidx/compose/foundation/layout/RowScopeInstance.class", "name": "androidx/compose/foundation/layout/RowScopeInstance.class", "size": 4715, "crc": -1562956652}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "size": 3459, "crc": -746736251}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "size": 3064, "crc": 988941840}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "size": 1965, "crc": -189002249}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "size": 2503, "crc": 555122046}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "size": 2498, "crc": 2120317566}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "size": 2501, "crc": -1608513612}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "size": 2496, "crc": -756432457}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "size": 3490, "crc": -57778062}, {"key": "androidx/compose/foundation/layout/SideCalculator.class", "name": "androidx/compose/foundation/layout/SideCalculator.class", "size": 2041, "crc": -1395001170}, {"key": "androidx/compose/foundation/layout/SizeElement.class", "name": "androidx/compose/foundation/layout/SizeElement.class", "size": 4853, "crc": 1970514275}, {"key": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2717, "crc": -1399381236}, {"key": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2961, "crc": -1000233414}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2767, "crc": -51216707}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 3010, "crc": 615420439}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2753, "crc": 1580233733}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2996, "crc": -452402934}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3181, "crc": -43294760}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2760, "crc": -97463681}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 3004, "crc": -807669816}, {"key": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2705, "crc": 435782872}, {"key": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2947, "crc": 689649463}, {"key": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3132, "crc": 599616470}, {"key": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2710, "crc": -960801581}, {"key": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2955, "crc": 1215877456}, {"key": "androidx/compose/foundation/layout/SizeKt.class", "name": "androidx/compose/foundation/layout/SizeKt.class", "size": 17986, "crc": 64966965}, {"key": "androidx/compose/foundation/layout/SizeMode.class", "name": "androidx/compose/foundation/layout/SizeMode.class", "size": 1425, "crc": -103665901}, {"key": "androidx/compose/foundation/layout/SizeNode$measure$1.class", "name": "androidx/compose/foundation/layout/SizeNode$measure$1.class", "size": 1925, "crc": 368458913}, {"key": "androidx/compose/foundation/layout/SizeNode.class", "name": "androidx/compose/foundation/layout/SizeNode.class", "size": 7944, "crc": 1662491018}, {"key": "androidx/compose/foundation/layout/SpacerKt$Spacer$$inlined$Layout$1.class", "name": "androidx/compose/foundation/layout/SpacerKt$Spacer$$inlined$Layout$1.class", "size": 2080, "crc": -517201246}, {"key": "androidx/compose/foundation/layout/SpacerKt.class", "name": "androidx/compose/foundation/layout/SpacerKt.class", "size": 5937, "crc": 206521142}, {"key": "androidx/compose/foundation/layout/SpacerMeasurePolicy$measure$1$1.class", "name": "androidx/compose/foundation/layout/SpacerMeasurePolicy$measure$1$1.class", "size": 1722, "crc": -1063229416}, {"key": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "name": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "size": 2543, "crc": -1677704817}, {"key": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "name": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "size": 3347, "crc": -434370327}, {"key": "androidx/compose/foundation/layout/UnionInsets.class", "name": "androidx/compose/foundation/layout/UnionInsets.class", "size": 3004, "crc": -1673419114}, {"key": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "size": 2149, "crc": 434888969}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "size": 4101, "crc": 1976972896}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode$measure$1.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode$measure$1.class", "size": 1979, "crc": -703199809}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "size": 5623, "crc": -1284078528}, {"key": "androidx/compose/foundation/layout/ValueInsets.class", "name": "androidx/compose/foundation/layout/ValueInsets.class", "size": 5089, "crc": 222124637}, {"key": "androidx/compose/foundation/layout/VerticalAlignElement.class", "name": "androidx/compose/foundation/layout/VerticalAlignElement.class", "size": 3388, "crc": -503432897}, {"key": "androidx/compose/foundation/layout/VerticalAlignNode.class", "name": "androidx/compose/foundation/layout/VerticalAlignNode.class", "size": 3132, "crc": -666395038}, {"key": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "size": 757, "crc": -1615628904}, {"key": "androidx/compose/foundation/layout/WindowInsets.class", "name": "androidx/compose/foundation/layout/WindowInsets.class", "size": 1370, "crc": -1634164549}, {"key": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "name": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "size": 2092, "crc": 995044726}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "size": 2733, "crc": 660604170}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "size": 4049, "crc": -88982095}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1$invoke$$inlined$onDispose$1.class", "size": 2486, "crc": 17656415}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection$1.class", "size": 3369, "crc": -1826026167}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "size": 9863, "crc": 1737949508}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1$invoke$$inlined$onDispose$1.class", "size": 2382, "crc": -886361222}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$1.class", "size": 3214, "crc": -1499013597}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "size": 8204, "crc": -2006214984}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "size": 12498, "crc": -1701649347}, {"key": "androidx/compose/foundation/layout/WindowInsetsKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsKt.class", "size": 7151, "crc": 1788556395}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$animationEnded$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$animationEnded$1.class", "size": 1500, "crc": 461441919}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$dispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$dispose$1.class", "size": 1479, "crc": -1950673793}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "size": 2269, "crc": -626836277}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1$1.class", "size": 3245, "crc": -806344051}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "size": 5163, "crc": 1474448533}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "size": 5805, "crc": -1503853626}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1$1.class", "size": 2395, "crc": -1477046305}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "size": 5230, "crc": 441049972}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "size": 4745, "crc": 1324487137}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$onReady$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$onReady$1.class", "size": 1532, "crc": 517385037}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "size": 18475, "crc": -1303471089}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$ModifierLocalConsumedWindowInsets$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$ModifierLocalConsumedWindowInsets$1.class", "size": 1503, "crc": -62019626}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "size": 3080, "crc": -381916217}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "size": 3099, "crc": 1450774781}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "size": 4767, "crc": 800368314}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "size": 4786, "crc": -906571861}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "size": 3079, "crc": 1010384955}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "size": 5006, "crc": 964523191}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "size": 3080, "crc": 1824149945}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "size": 4760, "crc": -227923695}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "size": 6206, "crc": -1165271468}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "size": 2713, "crc": -1764170236}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "size": 6065, "crc": -944515504}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "size": 2727, "crc": 1394183137}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "size": 6082, "crc": 2001924554}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "size": 2678, "crc": 2064050952}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "size": 6023, "crc": -2042113639}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2778, "crc": 2029158647}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 6143, "crc": 1353251956}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2733, "crc": -2078004312}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 6089, "crc": 627304972}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "size": 2715, "crc": -1809511463}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "size": 6005, "crc": 318024136}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "size": 2715, "crc": -2145943754}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "size": 6005, "crc": -500448780}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2720, "crc": -787927875}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 6011, "crc": 1729677669}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2713, "crc": -93795338}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 6065, "crc": -140279428}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2710, "crc": -1109758887}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 6062, "crc": 649827012}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2733, "crc": 1999797926}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 6089, "crc": -1518129886}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "size": 2708, "crc": -1730742571}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "size": 6051, "crc": -1535749139}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "size": 5847, "crc": 134584636}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "size": 10606, "crc": -1761367235}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "size": 3081, "crc": 569296784}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides.class", "size": 5167, "crc": -1288636488}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "size": 3104, "crc": 664614372}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$2.class", "size": 2085, "crc": 762147538}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "size": 3078, "crc": -1372391579}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$2.class", "size": 2480, "crc": 79819505}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "size": 3090, "crc": -2271417}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$2.class", "size": 2486, "crc": 1095429760}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "size": 3084, "crc": 274641706}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$2.class", "size": 2073, "crc": 1204688320}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "size": 5128, "crc": 1828333658}, {"key": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "size": 18431, "crc": -371559013}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "size": 3916, "crc": 1147649334}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "size": 3555, "crc": 896897839}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$height$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$height$1.class", "size": 2358, "crc": -535907828}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$size$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$size$1.class", "size": 2341, "crc": 1745619964}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion$width$1.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion$width$1.class", "size": 2409, "crc": -820130886}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "size": 3113, "crc": 1099188765}, {"key": "androidx/compose/foundation/layout/WrapContentElement.class", "name": "androidx/compose/foundation/layout/WrapContentElement.class", "size": 5173, "crc": -1732916634}, {"key": "androidx/compose/foundation/layout/WrapContentNode$measure$1.class", "name": "androidx/compose/foundation/layout/WrapContentNode$measure$1.class", "size": 3028, "crc": 1085189657}, {"key": "androidx/compose/foundation/layout/WrapContentNode.class", "name": "androidx/compose/foundation/layout/WrapContentNode.class", "size": 5292, "crc": 446139296}, {"key": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 580, "crc": 843432404}, {"key": "META-INF/androidx.compose.foundation_foundation-layout.version", "name": "META-INF/androidx.compose.foundation_foundation-layout.version", "size": 6, "crc": -1055996171}, {"key": "META-INF/foundation-layout_release.kotlin_module", "name": "META-INF/foundation-layout_release.kotlin_module", "size": 454, "crc": 408808975}]