[{"key": "androidx/activity/compose/ActivityComposeUtilsKt.class", "name": "androidx/activity/compose/ActivityComposeUtilsKt.class", "size": 1046, "crc": 467388989}, {"key": "androidx/activity/compose/ActivityResultLauncherHolder.class", "name": "androidx/activity/compose/ActivityResultLauncherHolder.class", "size": 2462, "crc": 1757590439}, {"key": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1.class", "size": 2368, "crc": 408222219}, {"key": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1.class", "name": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1.class", "size": 5410, "crc": 994601678}, {"key": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1.class", "name": "androidx/activity/compose/ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1.class", "size": 1508, "crc": -2091786058}, {"key": "androidx/activity/compose/ActivityResultRegistryKt.class", "name": "androidx/activity/compose/ActivityResultRegistryKt.class", "size": 7875, "crc": -1051054704}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$1$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$1$1.class", "size": 1475, "crc": 537234556}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1.class", "size": 2186, "crc": 226383809}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$2$1.class", "size": 3321, "crc": 462225052}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$3.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$3.class", "size": 1837, "crc": 1963666429}, {"key": "androidx/activity/compose/BackHandlerKt$BackHandler$backCallback$1$1.class", "name": "androidx/activity/compose/BackHandlerKt$BackHandler$backCallback$1$1.class", "size": 1522, "crc": 123387424}, {"key": "androidx/activity/compose/BackHandlerKt.class", "name": "androidx/activity/compose/BackHandlerKt.class", "size": 8681, "crc": -1298123814}, {"key": "androidx/activity/compose/ComponentActivityKt.class", "name": "androidx/activity/compose/ComponentActivityKt.class", "size": 4335, "crc": -86892061}, {"key": "androidx/activity/compose/LocalActivityKt$LocalActivity$1.class", "name": "androidx/activity/compose/LocalActivityKt$LocalActivity$1.class", "size": 2946, "crc": 240596245}, {"key": "androidx/activity/compose/LocalActivityKt.class", "name": "androidx/activity/compose/LocalActivityKt.class", "size": 1390, "crc": -890747949}, {"key": "androidx/activity/compose/LocalActivityResultRegistryOwner$LocalComposition$1.class", "name": "androidx/activity/compose/LocalActivityResultRegistryOwner$LocalComposition$1.class", "size": 1271, "crc": -285929590}, {"key": "androidx/activity/compose/LocalActivityResultRegistryOwner.class", "name": "androidx/activity/compose/LocalActivityResultRegistryOwner.class", "size": 5697, "crc": -213182420}, {"key": "androidx/activity/compose/LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1.class", "name": "androidx/activity/compose/LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1.class", "size": 1239, "crc": -324331023}, {"key": "androidx/activity/compose/LocalFullyDrawnReporterOwner.class", "name": "androidx/activity/compose/LocalFullyDrawnReporterOwner.class", "size": 6012, "crc": 1735708464}, {"key": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1.class", "name": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1.class", "size": 1279, "crc": -226188259}, {"key": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner.class", "name": "androidx/activity/compose/LocalOnBackPressedDispatcherOwner.class", "size": 6109, "crc": 2081469393}, {"key": "androidx/activity/compose/ManagedActivityResultLauncher.class", "name": "androidx/activity/compose/ManagedActivityResultLauncher.class", "size": 3241, "crc": 1001893617}, {"key": "androidx/activity/compose/OnBackInstance$job$1$1.class", "name": "androidx/activity/compose/OnBackInstance$job$1$1.class", "size": 3136, "crc": 1084951892}, {"key": "androidx/activity/compose/OnBackInstance$job$1.class", "name": "androidx/activity/compose/OnBackInstance$job$1.class", "size": 5487, "crc": -1922697640}, {"key": "androidx/activity/compose/OnBackInstance.class", "name": "androidx/activity/compose/OnBackInstance.class", "size": 4649, "crc": 1968541243}, {"key": "androidx/activity/compose/PredictiveBackHandlerCallback.class", "name": "androidx/activity/compose/PredictiveBackHandlerCallback.class", "size": 4879, "crc": 171277987}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$2$1.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$2$1.class", "size": 3416, "crc": 1048368238}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1.class", "size": 2287, "crc": 665786675}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$3$1.class", "size": 3430, "crc": -405638110}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$4.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt$PredictiveBackHandler$4.class", "size": 2147, "crc": 384580669}, {"key": "androidx/activity/compose/PredictiveBackHandlerKt.class", "name": "androidx/activity/compose/PredictiveBackHandlerKt.class", "size": 12518, "crc": -1645583311}, {"key": "androidx/activity/compose/ReportDrawnComposition$checkReporter$1.class", "name": "androidx/activity/compose/ReportDrawnComposition$checkReporter$1.class", "size": 1711, "crc": 916217771}, {"key": "androidx/activity/compose/ReportDrawnComposition$observeReporter$1.class", "name": "androidx/activity/compose/ReportDrawnComposition$observeReporter$1.class", "size": 1718, "crc": -2003642051}, {"key": "androidx/activity/compose/ReportDrawnComposition$snapshotStateObserver$1.class", "name": "androidx/activity/compose/ReportDrawnComposition$snapshotStateObserver$1.class", "size": 1594, "crc": -171044002}, {"key": "androidx/activity/compose/ReportDrawnComposition.class", "name": "androidx/activity/compose/ReportDrawnComposition.class", "size": 4584, "crc": -1700541888}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$1.class", "size": 1205, "crc": 1209377914}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawn$2.class", "size": 1414, "crc": 769023837}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$1$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$1$1.class", "size": 4918, "crc": 764708412}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$2.class", "size": 1851, "crc": -1968829909}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$fullyDrawnReporter$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnAfter$fullyDrawnReporter$1.class", "size": 1889, "crc": -845125759}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1.class", "size": 1915, "crc": -684014249}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2.class", "size": 2162, "crc": 612421313}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$1$1.class", "size": 3516, "crc": 1716567251}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$2.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$2.class", "size": 1756, "crc": -607015806}, {"key": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1.class", "name": "androidx/activity/compose/ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1.class", "size": 1794, "crc": 270340850}, {"key": "androidx/activity/compose/ReportDrawnKt.class", "name": "androidx/activity/compose/ReportDrawnKt.class", "size": 7717, "crc": 523671779}, {"key": "META-INF/activity-compose_release.kotlin_module", "name": "META-INF/activity-compose_release.kotlin_module", "size": 197, "crc": 1103507813}, {"key": "META-INF/androidx.activity_activity-compose.version", "name": "META-INF/androidx.activity_activity-compose.version", "size": 7, "crc": -688916209}]