{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeDebugResources-32:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3bdf59f548fbb8efb4688937f11638bc\\transformed\\core-1.15.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "3,4,5,6,7,8,9,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "191,289,392,492,595,700,803,2390", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "284,387,487,590,695,798,917,2486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7c6efc5356a40c412603779ad33ac6f6\\transformed\\foundation-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,227", "endColumns": "85,85,88", "endOffsets": "136,222,311"}, "to": {"startLines": "2,31,32", "startColumns": "4,4,4", "startOffsets": "105,2756,2842", "endColumns": "85,85,88", "endOffsets": "186,2837,2926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c4c82e9dfe5df03207c372a77c2a15c6\\transformed\\ui-release\\res\\values-eu\\values-eu.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "192,283,364,465,569,661,737,824,913,997,1085,1175,1249,1334,1411,1493,1571,1648,1716", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "278,359,460,564,656,732,819,908,992,1080,1170,1244,1329,1406,1488,1566,1643,1711,1831"}, "to": {"startLines": "10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "922,1013,1094,1195,1299,1391,1467,1643,1732,1816,1904,1994,2068,2153,2230,2312,2491,2568,2636", "endColumns": "90,80,100,103,91,75,86,88,83,87,89,73,84,76,81,77,76,67,119", "endOffsets": "1008,1089,1190,1294,1386,1462,1549,1727,1811,1899,1989,2063,2148,2225,2307,2385,2563,2631,2751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55a66032b6022265c5288acd5a936a98\\transformed\\material\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1554", "endColumns": "88", "endOffsets": "1638"}}]}]}