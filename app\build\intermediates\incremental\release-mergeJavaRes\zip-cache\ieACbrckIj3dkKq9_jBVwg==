[{"key": "androidx/compose/animation/core/ActualJvm_jvmKt.class", "name": "androidx/compose/animation/core/ActualJvm_jvmKt.class", "size": 430, "crc": -1341513997}, {"key": "androidx/compose/animation/core/Animatable$runAnimation$2.class", "name": "androidx/compose/animation/core/Animatable$runAnimation$2.class", "size": 8257, "crc": -157211934}, {"key": "androidx/compose/animation/core/Animatable$snapTo$2.class", "name": "androidx/compose/animation/core/Animatable$snapTo$2.class", "size": 3473, "crc": -270958758}, {"key": "androidx/compose/animation/core/Animatable$stop$2.class", "name": "androidx/compose/animation/core/Animatable$stop$2.class", "size": 2826, "crc": -748855447}, {"key": "androidx/compose/animation/core/Animatable.class", "name": "androidx/compose/animation/core/Animatable.class", "size": 20636, "crc": 1066231678}, {"key": "androidx/compose/animation/core/AnimatableKt.class", "name": "androidx/compose/animation/core/AnimatableKt.class", "size": 4052, "crc": -646147544}, {"key": "androidx/compose/animation/core/AnimateAsStateKt$animateValueAsState$3$1$1.class", "name": "androidx/compose/animation/core/AnimateAsStateKt$animateValueAsState$3$1$1.class", "size": 4825, "crc": -1717568696}, {"key": "androidx/compose/animation/core/AnimateAsStateKt$animateValueAsState$3$1.class", "name": "androidx/compose/animation/core/AnimateAsStateKt$animateValueAsState$3$1.class", "size": 5685, "crc": -1521813373}, {"key": "androidx/compose/animation/core/AnimateAsStateKt.class", "name": "androidx/compose/animation/core/AnimateAsStateKt.class", "size": 33758, "crc": 1770685314}, {"key": "androidx/compose/animation/core/Animation$DefaultImpls.class", "name": "androidx/compose/animation/core/Animation$DefaultImpls.class", "size": 962, "crc": -128634405}, {"key": "androidx/compose/animation/core/Animation.class", "name": "androidx/compose/animation/core/Animation.class", "size": 1999, "crc": 15428625}, {"key": "androidx/compose/animation/core/AnimationConstants.class", "name": "androidx/compose/animation/core/AnimationConstants.class", "size": 1000, "crc": 2017784072}, {"key": "androidx/compose/animation/core/AnimationEndReason.class", "name": "androidx/compose/animation/core/AnimationEndReason.class", "size": 1955, "crc": -796177602}, {"key": "androidx/compose/animation/core/AnimationKt.class", "name": "androidx/compose/animation/core/AnimationKt.class", "size": 7416, "crc": -1067814983}, {"key": "androidx/compose/animation/core/AnimationResult.class", "name": "androidx/compose/animation/core/AnimationResult.class", "size": 2456, "crc": 136097751}, {"key": "androidx/compose/animation/core/AnimationScope.class", "name": "androidx/compose/animation/core/AnimationScope.class", "size": 7404, "crc": 318415444}, {"key": "androidx/compose/animation/core/AnimationSpec.class", "name": "androidx/compose/animation/core/AnimationSpec.class", "size": 1201, "crc": -1152688688}, {"key": "androidx/compose/animation/core/AnimationSpecKt.class", "name": "androidx/compose/animation/core/AnimationSpecKt.class", "size": 11888, "crc": -792645666}, {"key": "androidx/compose/animation/core/AnimationState.class", "name": "androidx/compose/animation/core/AnimationState.class", "size": 7074, "crc": 779000172}, {"key": "androidx/compose/animation/core/AnimationStateKt.class", "name": "androidx/compose/animation/core/AnimationStateKt.class", "size": 7615, "crc": 903495111}, {"key": "androidx/compose/animation/core/AnimationVector.class", "name": "androidx/compose/animation/core/AnimationVector.class", "size": 1694, "crc": 1747887204}, {"key": "androidx/compose/animation/core/AnimationVector1D.class", "name": "androidx/compose/animation/core/AnimationVector1D.class", "size": 3031, "crc": -1064978342}, {"key": "androidx/compose/animation/core/AnimationVector2D.class", "name": "androidx/compose/animation/core/AnimationVector2D.class", "size": 3439, "crc": -646169808}, {"key": "androidx/compose/animation/core/AnimationVector3D.class", "name": "androidx/compose/animation/core/AnimationVector3D.class", "size": 3809, "crc": -834926519}, {"key": "androidx/compose/animation/core/AnimationVector4D.class", "name": "androidx/compose/animation/core/AnimationVector4D.class", "size": 4196, "crc": 1770344685}, {"key": "androidx/compose/animation/core/AnimationVectorsKt.class", "name": "androidx/compose/animation/core/AnimationVectorsKt.class", "size": 3416, "crc": -309366385}, {"key": "androidx/compose/animation/core/Animations.class", "name": "androidx/compose/animation/core/Animations.class", "size": 672, "crc": 837862669}, {"key": "androidx/compose/animation/core/ArcAnimationSpec.class", "name": "androidx/compose/animation/core/ArcAnimationSpec.class", "size": 5367, "crc": 1534603748}, {"key": "androidx/compose/animation/core/ArcMode$Companion.class", "name": "androidx/compose/animation/core/ArcMode$Companion.class", "size": 1380, "crc": -94505632}, {"key": "androidx/compose/animation/core/ArcMode.class", "name": "androidx/compose/animation/core/ArcMode.class", "size": 2728, "crc": -199878427}, {"key": "androidx/compose/animation/core/ArcSpline$Arc.class", "name": "androidx/compose/animation/core/ArcSpline$Arc.class", "size": 7695, "crc": 803656686}, {"key": "androidx/compose/animation/core/ArcSpline.class", "name": "androidx/compose/animation/core/ArcSpline.class", "size": 6615, "crc": -1666169892}, {"key": "androidx/compose/animation/core/ArcSplineKt.class", "name": "androidx/compose/animation/core/ArcSplineKt.class", "size": 1480, "crc": 1529162491}, {"key": "androidx/compose/animation/core/ArcSpline_jvmKt.class", "name": "androidx/compose/animation/core/ArcSpline_jvmKt.class", "size": 966, "crc": -1371970448}, {"key": "androidx/compose/animation/core/CubicBezierEasing.class", "name": "androidx/compose/animation/core/CubicBezierEasing.class", "size": 5628, "crc": -2140863494}, {"key": "androidx/compose/animation/core/DecayAnimation.class", "name": "androidx/compose/animation/core/DecayAnimation.class", "size": 6948, "crc": 1168930065}, {"key": "androidx/compose/animation/core/DecayAnimationSpec.class", "name": "androidx/compose/animation/core/DecayAnimationSpec.class", "size": 1235, "crc": 566015737}, {"key": "androidx/compose/animation/core/DecayAnimationSpecImpl.class", "name": "androidx/compose/animation/core/DecayAnimationSpecImpl.class", "size": 2001, "crc": -2140359840}, {"key": "androidx/compose/animation/core/DecayAnimationSpecKt.class", "name": "androidx/compose/animation/core/DecayAnimationSpecKt.class", "size": 4777, "crc": 933874917}, {"key": "androidx/compose/animation/core/DeferredTargetAnimation$updateTarget$1.class", "name": "androidx/compose/animation/core/DeferredTargetAnimation$updateTarget$1.class", "size": 4724, "crc": -809771972}, {"key": "androidx/compose/animation/core/DeferredTargetAnimation.class", "name": "androidx/compose/animation/core/DeferredTargetAnimation.class", "size": 6786, "crc": -447430352}, {"key": "androidx/compose/animation/core/DurationBasedAnimationSpec.class", "name": "androidx/compose/animation/core/DurationBasedAnimationSpec.class", "size": 1441, "crc": -496461066}, {"key": "androidx/compose/animation/core/Easing.class", "name": "androidx/compose/animation/core/Easing.class", "size": 541, "crc": 1810524850}, {"key": "androidx/compose/animation/core/EasingFunctionsKt.class", "name": "androidx/compose/animation/core/EasingFunctionsKt.class", "size": 8532, "crc": -1388460196}, {"key": "androidx/compose/animation/core/EasingKt.class", "name": "androidx/compose/animation/core/EasingKt.class", "size": 1945, "crc": -1843980259}, {"key": "androidx/compose/animation/core/ExperimentalAnimatableApi.class", "name": "androidx/compose/animation/core/ExperimentalAnimatableApi.class", "size": 829, "crc": -595347678}, {"key": "androidx/compose/animation/core/ExperimentalAnimationSpecApi.class", "name": "androidx/compose/animation/core/ExperimentalAnimationSpecApi.class", "size": 843, "crc": -79858795}, {"key": "androidx/compose/animation/core/ExperimentalTransitionApi.class", "name": "androidx/compose/animation/core/ExperimentalTransitionApi.class", "size": 831, "crc": 1635794966}, {"key": "androidx/compose/animation/core/FiniteAnimationSpec.class", "name": "androidx/compose/animation/core/FiniteAnimationSpec.class", "size": 1388, "crc": 257666629}, {"key": "androidx/compose/animation/core/FloatAnimationSpec$DefaultImpls.class", "name": "androidx/compose/animation/core/FloatAnimationSpec$DefaultImpls.class", "size": 1604, "crc": -1201962283}, {"key": "androidx/compose/animation/core/FloatAnimationSpec.class", "name": "androidx/compose/animation/core/FloatAnimationSpec.class", "size": 3021, "crc": -811262228}, {"key": "androidx/compose/animation/core/FloatDecayAnimationSpec.class", "name": "androidx/compose/animation/core/FloatDecayAnimationSpec.class", "size": 879, "crc": -784099285}, {"key": "androidx/compose/animation/core/FloatDecayAnimationSpecKt.class", "name": "androidx/compose/animation/core/FloatDecayAnimationSpecKt.class", "size": 1797, "crc": -1136279060}, {"key": "androidx/compose/animation/core/FloatExponentialDecaySpec.class", "name": "androidx/compose/animation/core/FloatExponentialDecaySpec.class", "size": 2706, "crc": -2036386954}, {"key": "androidx/compose/animation/core/FloatSpringSpec.class", "name": "androidx/compose/animation/core/FloatSpringSpec.class", "size": 4823, "crc": 1589913425}, {"key": "androidx/compose/animation/core/FloatTweenSpec.class", "name": "androidx/compose/animation/core/FloatTweenSpec.class", "size": 5387, "crc": -1875695253}, {"key": "androidx/compose/animation/core/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameMillis$2.class", "name": "androidx/compose/animation/core/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameMillis$2.class", "size": 1596, "crc": 860181085}, {"key": "androidx/compose/animation/core/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "name": "androidx/compose/animation/core/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "size": 3158, "crc": -1639148609}, {"key": "androidx/compose/animation/core/InfiniteAnimationPolicyKt.class", "name": "androidx/compose/animation/core/InfiniteAnimationPolicyKt.class", "size": 2897, "crc": 375408516}, {"key": "androidx/compose/animation/core/InfiniteRepeatableSpec.class", "name": "androidx/compose/animation/core/InfiniteRepeatableSpec.class", "size": 5694, "crc": 1409167793}, {"key": "androidx/compose/animation/core/InfiniteTransition$TransitionAnimationState.class", "name": "androidx/compose/animation/core/InfiniteTransition$TransitionAnimationState.class", "size": 8160, "crc": 2141515840}, {"key": "androidx/compose/animation/core/InfiniteTransition$run$1$1$3.class", "name": "androidx/compose/animation/core/InfiniteTransition$run$1$1$3.class", "size": 3024, "crc": -497531473}, {"key": "androidx/compose/animation/core/InfiniteTransition$run$1$1.class", "name": "androidx/compose/animation/core/InfiniteTransition$run$1$1.class", "size": 8918, "crc": 413087693}, {"key": "androidx/compose/animation/core/InfiniteTransition.class", "name": "androidx/compose/animation/core/InfiniteTransition.class", "size": 11837, "crc": 36891552}, {"key": "androidx/compose/animation/core/InfiniteTransitionKt$animateValue$lambda$6$lambda$5$$inlined$onDispose$1.class", "name": "androidx/compose/animation/core/InfiniteTransitionKt$animateValue$lambda$6$lambda$5$$inlined$onDispose$1.class", "size": 2691, "crc": 427426011}, {"key": "androidx/compose/animation/core/InfiniteTransitionKt.class", "name": "androidx/compose/animation/core/InfiniteTransitionKt.class", "size": 14339, "crc": -596665924}, {"key": "androidx/compose/animation/core/InternalAnimationApi.class", "name": "androidx/compose/animation/core/InternalAnimationApi.class", "size": 1042, "crc": -1969445813}, {"key": "androidx/compose/animation/core/KeyframeBaseEntity.class", "name": "androidx/compose/animation/core/KeyframeBaseEntity.class", "size": 2894, "crc": -956746759}, {"key": "androidx/compose/animation/core/KeyframesSpec$KeyframeEntity.class", "name": "androidx/compose/animation/core/KeyframesSpec$KeyframeEntity.class", "size": 3518, "crc": -1421336430}, {"key": "androidx/compose/animation/core/KeyframesSpec$KeyframesSpecConfig.class", "name": "androidx/compose/animation/core/KeyframesSpec$KeyframesSpecConfig.class", "size": 6197, "crc": -2041031443}, {"key": "androidx/compose/animation/core/KeyframesSpec.class", "name": "androidx/compose/animation/core/KeyframesSpec.class", "size": 8100, "crc": 2136239573}, {"key": "androidx/compose/animation/core/KeyframesSpecBaseConfig.class", "name": "androidx/compose/animation/core/KeyframesSpecBaseConfig.class", "size": 5094, "crc": -432457316}, {"key": "androidx/compose/animation/core/KeyframesWithSplineSpec$KeyframesWithSplineSpecConfig.class", "name": "androidx/compose/animation/core/KeyframesWithSplineSpec$KeyframesWithSplineSpecConfig.class", "size": 2273, "crc": -1951630048}, {"key": "androidx/compose/animation/core/KeyframesWithSplineSpec.class", "name": "androidx/compose/animation/core/KeyframesWithSplineSpec.class", "size": 8093, "crc": 741039196}, {"key": "androidx/compose/animation/core/MonoSpline.class", "name": "androidx/compose/animation/core/MonoSpline.class", "size": 7784, "crc": -365047126}, {"key": "androidx/compose/animation/core/MonoSplineKt.class", "name": "androidx/compose/animation/core/MonoSplineKt.class", "size": 1060, "crc": -727480467}, {"key": "androidx/compose/animation/core/Motion.class", "name": "androidx/compose/animation/core/Motion.class", "size": 3725, "crc": 780954989}, {"key": "androidx/compose/animation/core/MutableTransitionState.class", "name": "androidx/compose/animation/core/MutableTransitionState.class", "size": 4463, "crc": 1276900827}, {"key": "androidx/compose/animation/core/MutatePriority.class", "name": "androidx/compose/animation/core/MutatePriority.class", "size": 1995, "crc": 1192406871}, {"key": "androidx/compose/animation/core/MutationInterruptedException.class", "name": "androidx/compose/animation/core/MutationInterruptedException.class", "size": 971, "crc": 123890589}, {"key": "androidx/compose/animation/core/MutatorMutex$Mutator.class", "name": "androidx/compose/animation/core/MutatorMutex$Mutator.class", "size": 2067, "crc": 222359834}, {"key": "androidx/compose/animation/core/MutatorMutex$mutate$2.class", "name": "androidx/compose/animation/core/MutatorMutex$mutate$2.class", "size": 7125, "crc": 1492637080}, {"key": "androidx/compose/animation/core/MutatorMutex$mutateWith$2.class", "name": "androidx/compose/animation/core/MutatorMutex$mutateWith$2.class", "size": 7286, "crc": -222252551}, {"key": "androidx/compose/animation/core/MutatorMutex.class", "name": "androidx/compose/animation/core/MutatorMutex.class", "size": 5929, "crc": 846295477}, {"key": "androidx/compose/animation/core/PathEasing.class", "name": "androidx/compose/animation/core/PathEasing.class", "size": 6617, "crc": -1197596501}, {"key": "androidx/compose/animation/core/PreconditionsKt.class", "name": "androidx/compose/animation/core/PreconditionsKt.class", "size": 2538, "crc": 344040594}, {"key": "androidx/compose/animation/core/PreventExhaustiveWhenTransitionState.class", "name": "androidx/compose/animation/core/PreventExhaustiveWhenTransitionState.class", "size": 2117, "crc": -892718248}, {"key": "androidx/compose/animation/core/RepeatMode.class", "name": "androidx/compose/animation/core/RepeatMode.class", "size": 1888, "crc": -1369592263}, {"key": "androidx/compose/animation/core/RepeatableSpec.class", "name": "androidx/compose/animation/core/RepeatableSpec.class", "size": 6166, "crc": 110318785}, {"key": "androidx/compose/animation/core/SeekableTransitionState$Companion.class", "name": "androidx/compose/animation/core/SeekableTransitionState$Companion.class", "size": 1393, "crc": -2017572404}, {"key": "androidx/compose/animation/core/SeekableTransitionState$SeekingAnimationState.class", "name": "androidx/compose/animation/core/SeekableTransitionState$SeekingAnimationState.class", "size": 4806, "crc": 359562411}, {"key": "androidx/compose/animation/core/SeekableTransitionState$animateTo$2$1.class", "name": "androidx/compose/animation/core/SeekableTransitionState$animateTo$2$1.class", "size": 11100, "crc": -1741056045}, {"key": "androidx/compose/animation/core/SeekableTransitionState$animateTo$2.class", "name": "androidx/compose/animation/core/SeekableTransitionState$animateTo$2.class", "size": 4363, "crc": -246920598}, {"key": "androidx/compose/animation/core/SeekableTransitionState$runAnimations$1.class", "name": "androidx/compose/animation/core/SeekableTransitionState$runAnimations$1.class", "size": 2022, "crc": 1926952280}, {"key": "androidx/compose/animation/core/SeekableTransitionState$seekTo$3$1$1.class", "name": "androidx/compose/animation/core/SeekableTransitionState$seekTo$3$1$1.class", "size": 3580, "crc": -1172839519}, {"key": "androidx/compose/animation/core/SeekableTransitionState$seekTo$3$1.class", "name": "androidx/compose/animation/core/SeekableTransitionState$seekTo$3$1.class", "size": 6015, "crc": -1555848693}, {"key": "androidx/compose/animation/core/SeekableTransitionState$seekTo$3.class", "name": "androidx/compose/animation/core/SeekableTransitionState$seekTo$3.class", "size": 3796, "crc": -1213457506}, {"key": "androidx/compose/animation/core/SeekableTransitionState$snapTo$2.class", "name": "androidx/compose/animation/core/SeekableTransitionState$snapTo$2.class", "size": 4627, "crc": 2024090904}, {"key": "androidx/compose/animation/core/SeekableTransitionState$waitForComposition$1.class", "name": "androidx/compose/animation/core/SeekableTransitionState$waitForComposition$1.class", "size": 2100, "crc": -334141753}, {"key": "androidx/compose/animation/core/SeekableTransitionState$waitForCompositionAfterTargetStateChange$1.class", "name": "androidx/compose/animation/core/SeekableTransitionState$waitForCompositionAfterTargetStateChange$1.class", "size": 2210, "crc": 666457065}, {"key": "androidx/compose/animation/core/SeekableTransitionState.class", "name": "androidx/compose/animation/core/SeekableTransitionState.class", "size": 29880, "crc": 737721209}, {"key": "androidx/compose/animation/core/SnapSpec.class", "name": "androidx/compose/animation/core/SnapSpec.class", "size": 3150, "crc": -1759564300}, {"key": "androidx/compose/animation/core/Spring.class", "name": "androidx/compose/animation/core/Spring.class", "size": 1494, "crc": -394081750}, {"key": "androidx/compose/animation/core/SpringEstimationKt.class", "name": "androidx/compose/animation/core/SpringEstimationKt.class", "size": 8903, "crc": 120368326}, {"key": "androidx/compose/animation/core/SpringSimulation.class", "name": "androidx/compose/animation/core/SpringSimulation.class", "size": 4904, "crc": -2085413480}, {"key": "androidx/compose/animation/core/SpringSimulationKt.class", "name": "androidx/compose/animation/core/SpringSimulationKt.class", "size": 1656, "crc": 1071987345}, {"key": "androidx/compose/animation/core/SpringSpec.class", "name": "androidx/compose/animation/core/SpringSpec.class", "size": 4156, "crc": 1542424754}, {"key": "androidx/compose/animation/core/StartDelayAnimationSpec.class", "name": "androidx/compose/animation/core/StartDelayAnimationSpec.class", "size": 3192, "crc": 1595062928}, {"key": "androidx/compose/animation/core/StartDelayVectorizedAnimationSpec.class", "name": "androidx/compose/animation/core/StartDelayVectorizedAnimationSpec.class", "size": 3989, "crc": 381726010}, {"key": "androidx/compose/animation/core/StartOffset.class", "name": "androidx/compose/animation/core/StartOffset.class", "size": 3127, "crc": -540130910}, {"key": "androidx/compose/animation/core/StartOffsetType$Companion.class", "name": "androidx/compose/animation/core/StartOffsetType$Companion.class", "size": 1244, "crc": -1314287842}, {"key": "androidx/compose/animation/core/StartOffsetType.class", "name": "androidx/compose/animation/core/StartOffsetType.class", "size": 2657, "crc": -495445830}, {"key": "androidx/compose/animation/core/SuspendAnimationKt$animate$4.class", "name": "androidx/compose/animation/core/SuspendAnimationKt$animate$4.class", "size": 1949, "crc": -1455720755}, {"key": "androidx/compose/animation/core/SuspendAnimationKt.class", "name": "androidx/compose/animation/core/SuspendAnimationKt.class", "size": 25050, "crc": 45140696}, {"key": "androidx/compose/animation/core/TargetBasedAnimation.class", "name": "androidx/compose/animation/core/TargetBasedAnimation.class", "size": 10798, "crc": -1836120069}, {"key": "androidx/compose/animation/core/Transition$DeferredAnimation$DeferredAnimationData.class", "name": "androidx/compose/animation/core/Transition$DeferredAnimation$DeferredAnimationData.class", "size": 5856, "crc": -1516658637}, {"key": "androidx/compose/animation/core/Transition$DeferredAnimation.class", "name": "androidx/compose/animation/core/Transition$DeferredAnimation.class", "size": 8506, "crc": -85782384}, {"key": "androidx/compose/animation/core/Transition$Segment$DefaultImpls.class", "name": "androidx/compose/animation/core/Transition$Segment$DefaultImpls.class", "size": 1096, "crc": -205894979}, {"key": "androidx/compose/animation/core/Transition$Segment.class", "name": "androidx/compose/animation/core/Transition$Segment.class", "size": 1539, "crc": -645722926}, {"key": "androidx/compose/animation/core/Transition$SegmentImpl.class", "name": "androidx/compose/animation/core/Transition$SegmentImpl.class", "size": 2071, "crc": -2030730186}, {"key": "androidx/compose/animation/core/Transition$TransitionAnimationState.class", "name": "androidx/compose/animation/core/Transition$TransitionAnimationState.class", "size": 18577, "crc": -1216496685}, {"key": "androidx/compose/animation/core/Transition$animateTo$1$1$1.class", "name": "androidx/compose/animation/core/Transition$animateTo$1$1$1.class", "size": 4784, "crc": 1409874847}, {"key": "androidx/compose/animation/core/Transition$animateTo$lambda$17$lambda$16$$inlined$onDispose$1.class", "name": "androidx/compose/animation/core/Transition$animateTo$lambda$17$lambda$16$$inlined$onDispose$1.class", "size": 1831, "crc": 1405346898}, {"key": "androidx/compose/animation/core/Transition.class", "name": "androidx/compose/animation/core/Transition.class", "size": 37721, "crc": -1729991518}, {"key": "androidx/compose/animation/core/TransitionKt$animateDp$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateDp$1.class", "size": 3179, "crc": 1157396732}, {"key": "androidx/compose/animation/core/TransitionKt$animateFloat$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateFloat$1.class", "size": 2816, "crc": 133253956}, {"key": "androidx/compose/animation/core/TransitionKt$animateInt$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateInt$1.class", "size": 2882, "crc": -360145635}, {"key": "androidx/compose/animation/core/TransitionKt$animateIntOffset$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateIntOffset$1.class", "size": 4092, "crc": 1315926015}, {"key": "androidx/compose/animation/core/TransitionKt$animateIntSize$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateIntSize$1.class", "size": 4067, "crc": 1363656389}, {"key": "androidx/compose/animation/core/TransitionKt$animateOffset$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateOffset$1.class", "size": 3251, "crc": -937715149}, {"key": "androidx/compose/animation/core/TransitionKt$animateRect$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateRect$1.class", "size": 3198, "crc": -1149308965}, {"key": "androidx/compose/animation/core/TransitionKt$animateSize$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateSize$1.class", "size": 3229, "crc": 776506781}, {"key": "androidx/compose/animation/core/TransitionKt$animateValue$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateValue$1.class", "size": 2837, "crc": 358643039}, {"key": "androidx/compose/animation/core/TransitionKt$animateValue$animationSpec$1$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateValue$animationSpec$1$1.class", "size": 1762, "crc": 203822574}, {"key": "androidx/compose/animation/core/TransitionKt$animateValue$targetValue$1$1.class", "name": "androidx/compose/animation/core/TransitionKt$animateValue$targetValue$1$1.class", "size": 1452, "crc": -1817163997}, {"key": "androidx/compose/animation/core/TransitionKt$createChildTransitionInternal$lambda$22$lambda$21$$inlined$onDispose$1.class", "name": "androidx/compose/animation/core/TransitionKt$createChildTransitionInternal$lambda$22$lambda$21$$inlined$onDispose$1.class", "size": 2439, "crc": -1161454012}, {"key": "androidx/compose/animation/core/TransitionKt$createDeferredAnimation$lambda$17$lambda$16$$inlined$onDispose$1.class", "name": "androidx/compose/animation/core/TransitionKt$createDeferredAnimation$lambda$17$lambda$16$$inlined$onDispose$1.class", "size": 2538, "crc": -637279447}, {"key": "androidx/compose/animation/core/TransitionKt$createTransitionAnimation$lambda$31$lambda$30$$inlined$onDispose$1.class", "name": "androidx/compose/animation/core/TransitionKt$createTransitionAnimation$lambda$31$lambda$30$$inlined$onDispose$1.class", "size": 2638, "crc": -763978170}, {"key": "androidx/compose/animation/core/TransitionKt$rememberTransition$1$1.class", "name": "androidx/compose/animation/core/TransitionKt$rememberTransition$1$1.class", "size": 5782, "crc": 21183443}, {"key": "androidx/compose/animation/core/TransitionKt$rememberTransition$lambda$13$lambda$12$$inlined$onDispose$1.class", "name": "androidx/compose/animation/core/TransitionKt$rememberTransition$lambda$13$lambda$12$$inlined$onDispose$1.class", "size": 2198, "crc": 493407755}, {"key": "androidx/compose/animation/core/TransitionKt$updateTransition$lambda$3$lambda$2$$inlined$onDispose$1.class", "name": "androidx/compose/animation/core/TransitionKt$updateTransition$lambda$3$lambda$2$$inlined$onDispose$1.class", "size": 2153, "crc": 656578310}, {"key": "androidx/compose/animation/core/TransitionKt.class", "name": "androidx/compose/animation/core/TransitionKt.class", "size": 72910, "crc": 826374662}, {"key": "androidx/compose/animation/core/TransitionState.class", "name": "androidx/compose/animation/core/TransitionState.class", "size": 3984, "crc": -1504978851}, {"key": "androidx/compose/animation/core/TweenSpec.class", "name": "androidx/compose/animation/core/TweenSpec.class", "size": 4173, "crc": 1765483076}, {"key": "androidx/compose/animation/core/TwoWayConverter.class", "name": "androidx/compose/animation/core/TwoWayConverter.class", "size": 1072, "crc": 900952109}, {"key": "androidx/compose/animation/core/TwoWayConverterImpl.class", "name": "androidx/compose/animation/core/TwoWayConverterImpl.class", "size": 2007, "crc": -539094696}, {"key": "androidx/compose/animation/core/VectorConvertersKt.class", "name": "androidx/compose/animation/core/VectorConvertersKt.class", "size": 20133, "crc": -2014297777}, {"key": "androidx/compose/animation/core/VectorizedAnimationSpec$DefaultImpls.class", "name": "androidx/compose/animation/core/VectorizedAnimationSpec$DefaultImpls.class", "size": 1376, "crc": 216341976}, {"key": "androidx/compose/animation/core/VectorizedAnimationSpec.class", "name": "androidx/compose/animation/core/VectorizedAnimationSpec.class", "size": 2682, "crc": 1965903825}, {"key": "androidx/compose/animation/core/VectorizedAnimationSpecKt$createSpringAnimations$1.class", "name": "androidx/compose/animation/core/VectorizedAnimationSpecKt$createSpringAnimations$1.class", "size": 2085, "crc": -822116594}, {"key": "androidx/compose/animation/core/VectorizedAnimationSpecKt$createSpringAnimations$2.class", "name": "androidx/compose/animation/core/VectorizedAnimationSpecKt$createSpringAnimations$2.class", "size": 1655, "crc": 568321922}, {"key": "androidx/compose/animation/core/VectorizedAnimationSpecKt.class", "name": "androidx/compose/animation/core/VectorizedAnimationSpecKt.class", "size": 6076, "crc": -698831422}, {"key": "androidx/compose/animation/core/VectorizedDecayAnimationSpec.class", "name": "androidx/compose/animation/core/VectorizedDecayAnimationSpec.class", "size": 1792, "crc": -1249339251}, {"key": "androidx/compose/animation/core/VectorizedDurationBasedAnimationSpec$DefaultImpls.class", "name": "androidx/compose/animation/core/VectorizedDurationBasedAnimationSpec$DefaultImpls.class", "size": 2380, "crc": -1174556481}, {"key": "androidx/compose/animation/core/VectorizedDurationBasedAnimationSpec.class", "name": "androidx/compose/animation/core/VectorizedDurationBasedAnimationSpec.class", "size": 2911, "crc": -968811095}, {"key": "androidx/compose/animation/core/VectorizedFiniteAnimationSpec$DefaultImpls.class", "name": "androidx/compose/animation/core/VectorizedFiniteAnimationSpec$DefaultImpls.class", "size": 1748, "crc": 1509833244}, {"key": "androidx/compose/animation/core/VectorizedFiniteAnimationSpec.class", "name": "androidx/compose/animation/core/VectorizedFiniteAnimationSpec.class", "size": 1941, "crc": 2012077906}, {"key": "androidx/compose/animation/core/VectorizedFloatAnimationSpec$1.class", "name": "androidx/compose/animation/core/VectorizedFloatAnimationSpec$1.class", "size": 1147, "crc": -1927824981}, {"key": "androidx/compose/animation/core/VectorizedFloatAnimationSpec.class", "name": "androidx/compose/animation/core/VectorizedFloatAnimationSpec.class", "size": 5150, "crc": 1154445457}, {"key": "androidx/compose/animation/core/VectorizedFloatDecaySpec.class", "name": "androidx/compose/animation/core/VectorizedFloatDecaySpec.class", "size": 4564, "crc": -1867820482}, {"key": "androidx/compose/animation/core/VectorizedInfiniteRepeatableSpec.class", "name": "androidx/compose/animation/core/VectorizedInfiniteRepeatableSpec.class", "size": 6025, "crc": 1825213729}, {"key": "androidx/compose/animation/core/VectorizedKeyframeSpecElementInfo.class", "name": "androidx/compose/animation/core/VectorizedKeyframeSpecElementInfo.class", "size": 4700, "crc": -1427213303}, {"key": "androidx/compose/animation/core/VectorizedKeyframesSpec.class", "name": "androidx/compose/animation/core/VectorizedKeyframesSpec.class", "size": 15160, "crc": 215386303}, {"key": "androidx/compose/animation/core/VectorizedMonoSplineKeyframesSpec.class", "name": "androidx/compose/animation/core/VectorizedMonoSplineKeyframesSpec.class", "size": 9980, "crc": -238408855}, {"key": "androidx/compose/animation/core/VectorizedRepeatableSpec.class", "name": "androidx/compose/animation/core/VectorizedRepeatableSpec.class", "size": 6267, "crc": -184089046}, {"key": "androidx/compose/animation/core/VectorizedSnapSpec.class", "name": "androidx/compose/animation/core/VectorizedSnapSpec.class", "size": 2613, "crc": 1792110810}, {"key": "androidx/compose/animation/core/VectorizedSpringSpec.class", "name": "androidx/compose/animation/core/VectorizedSpringSpec.class", "size": 4512, "crc": -245273379}, {"key": "androidx/compose/animation/core/VectorizedTweenSpec.class", "name": "androidx/compose/animation/core/VectorizedTweenSpec.class", "size": 3660, "crc": 2004584507}, {"key": "androidx/compose/animation/core/VisibilityThresholdsKt.class", "name": "androidx/compose/animation/core/VisibilityThresholdsKt.class", "size": 8678, "crc": 2060084737}, {"key": "androidx/compose/animation/core/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/animation/core/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 455, "crc": 952261444}, {"key": "androidx/compose/animation/core/internal/PlatformOptimizedCancellationException.class", "name": "androidx/compose/animation/core/internal/PlatformOptimizedCancellationException.class", "size": 1816, "crc": 1597689250}, {"key": "androidx/compose/animation/core/internal/PlatformOptimizedCancellationException_jvmKt.class", "name": "androidx/compose/animation/core/internal/PlatformOptimizedCancellationException_jvmKt.class", "size": 854, "crc": 1973015842}, {"key": "META-INF/androidx.compose.animation_animation-core.version", "name": "META-INF/androidx.compose.animation_animation-core.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/animation-core.kotlin_module", "name": "META-INF/animation-core.kotlin_module", "size": 635, "crc": 1363241478}]