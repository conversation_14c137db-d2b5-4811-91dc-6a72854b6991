1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aj.aj_tv_player"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <permission
11-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:22:5-24:47
12        android:name="com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:4:5-9:62
18        android:allowBackup="true"
18-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:5:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.10.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6ed63fdfcc9ed92046670fc9c013b712\transformed\core-1.10.1\AndroidManifest.xml:28:18-86
20        android:debuggable="true"
21        android:extractNativeLibs="true"
22        android:icon="@mipmap/ic_launcher"
22-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:6:9-43
23        android:label="@string/app_name"
23-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:7:9-41
24        android:supportsRtl="true"
24-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:8:9-35
25        android:testOnly="true"
26        android:theme="@android:style/Theme.DeviceDefault" />
26-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:9:9-59
27
28</manifest>
