1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.aj.aj_tv_player"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="36" />
10
11    <uses-feature
11-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:4:5-6:35
12        android:name="android.software.leanback"
12-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:5:9-49
13        android:required="true" />
13-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:6:9-32
14    <uses-feature
14-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:8:5-10:36
15        android:name="android.hardware.touchscreen"
15-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:9:9-52
16        android:required="false" />
16-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:10:9-33
17
18    <permission
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.aj.aj_tv_player.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
23
24    <application
24-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:12:5-31:19
25        android:allowBackup="true"
25-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:13:9-35
26        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
26-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3bdf59f548fbb8efb4688937f11638bc\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
27        android:banner="@mipmap/ic_launcher"
27-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:18:9-45
28        android:debuggable="true"
29        android:extractNativeLibs="true"
30        android:icon="@mipmap/ic_launcher"
30-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:14:9-43
31        android:label="@string/app_name"
31-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:15:9-41
32        android:supportsRtl="true"
32-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:16:9-35
33        android:theme="@android:style/Theme.DeviceDefault" >
33-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:17:9-59
34        <activity
34-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:20:9-29:20
35            android:name="com.aj.aj_tv_player.MainActivity"
35-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:21:13-41
36            android:exported="true"
36-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:22:13-36
37            android:theme="@android:style/Theme.DeviceDefault.NoActionBar" >
37-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:23:13-75
38            <intent-filter>
38-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:24:13-28:29
39                <action android:name="android.intent.action.MAIN" />
39-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:25:17-69
39-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:25:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:26:17-77
41-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:26:27-74
42                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
42-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:27:17-86
42-->E:\1-test\aj-tv-player\app\src\main\AndroidManifest.xml:27:27-83
43            </intent-filter>
44        </activity>
45        <activity
45-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:23:9-25:39
46            android:name="androidx.compose.ui.tooling.PreviewActivity"
46-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:24:13-71
47            android:exported="true" />
47-->[androidx.compose.ui:ui-tooling-android:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5509ff9d32281c2477de783c770e403b\transformed\ui-tooling\AndroidManifest.xml:25:13-36
48
49        <provider
49-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
50            android:name="androidx.startup.InitializationProvider"
50-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
51            android:authorities="com.aj.aj_tv_player.androidx-startup"
51-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
52            android:exported="false" >
52-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
53            <meta-data
53-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.emoji2.text.EmojiCompatInitializer"
54-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
55                android:value="androidx.startup" />
55-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c3ea82ce2e56ea320f294cbb7b06669\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
56            <meta-data
56-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
57                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
57-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
58                android:value="androidx.startup" />
58-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c07f0aa0694f6817d0e5a930ae5f1a93\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
59            <meta-data
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
61                android:value="androidx.startup" />
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
62        </provider>
63
64        <receiver
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
65            android:name="androidx.profileinstaller.ProfileInstallReceiver"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
66            android:directBootAware="false"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
67            android:enabled="true"
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
68            android:exported="true"
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
69            android:permission="android.permission.DUMP" >
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
71                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
74                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
77                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
80                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42af16794a268bc24cdd558670b9f150\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
81            </intent-filter>
82        </receiver>
83    </application>
84
85</manifest>
