<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.activity:activity:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a42c81bcb2e35211401c1f2fe6980d8e\transformed\activity-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity:1.8.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a42c81bcb2e35211401c1f2fe6980d8e\transformed\activity-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\aea4c83d1361f67d534cdd7ba37b025c\transformed\activity-compose-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.8.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\aea4c83d1361f67d534cdd7ba37b025c\transformed\activity-compose-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.8.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\00394ecf5d1532defe86d1574d34f45c\transformed\activity-ktx-1.8.2\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.8.2"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\00394ecf5d1532defe86d1574d34f45c\transformed\activity-ktx-1.8.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ec574ed7885005f565e5ac87c79b40\transformed\core-1.10.1\jars\classes.jar"
      resolved="androidx.core:core:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a6ec574ed7885005f565e5ac87c79b40\transformed\core-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\87a569a5fa7557287a13fed63913a50b\transformed\lifecycle-livedata-core-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\87a569a5fa7557287a13fed63913a50b\transformed\lifecycle-livedata-core-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common\2.6.1\10f354fdb64868baecd67128560c5a0d6312c495\lifecycle-common-2.6.1.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.6.1"
      provided="true"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e9c9c07df5a5ad3e8006585377172978\transformed\lifecycle-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e9c9c07df5a5ad3e8006585377172978\transformed\lifecycle-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\74856161b2e6b6a22da47b2994329566\transformed\lifecycle-viewmodel-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\74856161b2e6b6a22da47b2994329566\transformed\lifecycle-viewmodel-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e2486e8617d70c283b1c0a25ff0d926\transformed\lifecycle-runtime-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e2486e8617d70c283b1c0a25ff0d926\transformed\lifecycle-runtime-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\dbd4fac5fb9fff7d261caf0b90992fbb\transformed\lifecycle-viewmodel-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\dbd4fac5fb9fff7d261caf0b90992fbb\transformed\lifecycle-viewmodel-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\6bcb147f690fa97bed95826166b94336\transformed\lifecycle-viewmodel-savedstate-2.6.1\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\6bcb147f690fa97bed95826166b94336\transformed\lifecycle-viewmodel-savedstate-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.10.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\276c20983d5dfd1def6a03943548ce2c\transformed\core-ktx-1.10.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.10.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\276c20983d5dfd1def6a03943548ce2c\transformed\core-ktx-1.10.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tv:tv-material:1.0.0-alpha10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e46e30b1c360378b1c1802d2326a01\transformed\tv-material-1.0.0-alpha10\jars\classes.jar"
      resolved="androidx.tv:tv-material:1.0.0-alpha10"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\e4e46e30b1c360378b1c1802d2326a01\transformed\tv-material-1.0.0-alpha10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tv:tv-foundation:1.0.0-alpha10@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\bd6522d20f8b6dfa76f7cd7d16f79d17\transformed\tv-foundation-1.0.0-alpha10\jars\classes.jar"
      resolved="androidx.tv:tv-foundation:1.0.0-alpha10"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\bd6522d20f8b6dfa76f7cd7d16f79d17\transformed\tv-foundation-1.0.0-alpha10"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\76b465850f473eb35fd4e1ae963ff4af\transformed\savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\76b465850f473eb35fd4e1ae963ff4af\transformed\savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7f77bf1bc6bdf13e7c294554c9da082d\transformed\savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7f77bf1bc6bdf13e7c294554c9da082d\transformed\savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\19b642e7465edfc9a64434f74e28ca0a\transformed\material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.2.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\19b642e7465edfc9a64434f74e28ca0a\transformed\material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3c4f0f754bfd89964743496d5d59046d\transformed\annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3c4f0f754bfd89964743496d5d59046d\transformed\annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae71269e315d84a21a7185fcf9ff9008\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\ae71269e315d84a21a7185fcf9ff9008\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\a834be713ae3c20c638b26aaee0284a5\transformed\animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\a834be713ae3c20c638b26aaee0284a5\transformed\animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5d06031303b7022a710c2e2a560821c9\transformed\animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5d06031303b7022a710c2e2a560821c9\transformed\animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\714eaeb5dd9dfb52bbf86eef57e5f298\transformed\foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\714eaeb5dd9dfb52bbf86eef57e5f298\transformed\foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\54472b3010e5914273826d227d6f1b9b\transformed\material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\54472b3010e5914273826d227d6f1b9b\transformed\material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3e1e96417bdb40bf9b585eff4310c680\transformed\material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3e1e96417bdb40bf9b585eff4310c680\transformed\material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\f608d021a4f1e83e8e85d7b0e5006a22\transformed\foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\f608d021a4f1e83e8e85d7b0e5006a22\transformed\foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.0\e209fb7bd1183032f55a0408121c6251a81acb49\collection-jvm-1.4.0.jar"
      resolved="androidx.collection:collection-jvm:1.4.0"
      provided="true"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7a403e619c637632766809eea9782a68\transformed\ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7a403e619c637632766809eea9782a68\transformed\ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e0ca9553a7ac3b5d7bc5c26e7213da4\transformed\ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\7e0ca9553a7ac3b5d7bc5c26e7213da4\transformed\ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\d2f417d29f03b4ec9bfdc84aa5bc6204\transformed\ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\d2f417d29f03b4ec9bfdc84aa5bc6204\transformed\ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\97aceb26a8adfcaab205be10b177f90a\transformed\ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\97aceb26a8adfcaab205be10b177f90a\transformed\ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\3bb763f91a252eaee209da395caf2d98\transformed\ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\3bb763f91a252eaee209da395caf2d98\transformed\ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\20432fd77a8f9cbcb70a196ae255ca47\transformed\ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\20432fd77a8f9cbcb70a196ae255ca47\transformed\ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f5c30a60ac2f77c8b695e1c6755d29b\transformed\ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\5f5c30a60ac2f77c8b695e1c6755d29b\transformed\ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\62f35d7bdb9f40920fe06c2287c5bbc3\transformed\ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\62f35d7bdb9f40920fe06c2287c5bbc3\transformed\ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f4bac79cd19e915e2ef6950290d243e\transformed\ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\0f4bac79cd19e915e2ef6950290d243e\transformed\ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\62dfcb013325aa073fb7063f811d8f76\transformed\runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\62dfcb013325aa073fb7063f811d8f76\transformed\runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.13\transforms\96b3545257870c994295e612c3b2f5aa\transformed\runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.13\transforms\96b3545257870c994295e612c3b2f5aa\transformed\runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"
      provided="true"/>
  <library
      name="androidx.annotation:annotation-jvm:1.7.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.7.0\920472d40adcdef5e18708976b3e314f9a636fcd\annotation-jvm-1.7.0.jar"
      resolved="androidx.annotation:annotation-jvm:1.7.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.1\63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4\kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"
      provided="true"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.1\c2d86b569f10b7fc7e28d3f50c0eed97897d77a7\kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.8.20\73576ddf378c5b4f1f6b449fe6b119b8183fc078\kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.8.20\3aa51faf20aae8b31e1a4bc54f8370673d7b7df4\kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:2.0.21@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\2.0.21\618b539767b4899b4660a83006e052b63f1db551\kotlin-stdlib-2.0.21.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:2.0.21"
      provided="true"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"
      provided="true"/>
</libraries>
