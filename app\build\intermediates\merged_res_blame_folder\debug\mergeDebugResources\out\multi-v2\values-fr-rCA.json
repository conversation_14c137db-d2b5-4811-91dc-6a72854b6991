{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeDebugResources-32:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\55a66032b6022265c5288acd5a936a98\\transformed\\material\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "88", "endOffsets": "139"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1570", "endColumns": "88", "endOffsets": "1654"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\3bdf59f548fbb8efb4688937f11638bc\\transformed\\core-1.15.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "3,4,5,6,7,8,9,27", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "195,293,395,494,596,700,804,2392", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "288,390,489,591,695,799,913,2488"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7c6efc5356a40c412603779ad33ac6f6\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,145,233", "endColumns": "89,87,94", "endOffsets": "140,228,323"}, "to": {"startLines": "2,31,32", "startColumns": "4,4,4", "startOffsets": "105,2763,2851", "endColumns": "89,87,94", "endOffsets": "190,2846,2941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c4c82e9dfe5df03207c372a77c2a15c6\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "196,295,383,481,587,674,754,848,940,1027,1108,1193,1269,1354,1429,1507,1581,1660,1729", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "290,378,476,582,669,749,843,935,1022,1103,1188,1264,1349,1424,1502,1576,1655,1724,1846"}, "to": {"startLines": "10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "918,1017,1105,1203,1309,1396,1476,1659,1751,1838,1919,2004,2080,2165,2240,2318,2493,2572,2641", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,84,74,77,73,78,68,121", "endOffsets": "1012,1100,1198,1304,1391,1471,1565,1746,1833,1914,1999,2075,2160,2235,2313,2387,2567,2636,2758"}}]}]}