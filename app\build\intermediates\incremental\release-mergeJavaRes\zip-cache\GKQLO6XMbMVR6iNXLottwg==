[{"key": "androidx/compose/animation/AndroidActualDefaultDecayAnimationSpec_androidKt.class", "name": "androidx/compose/animation/AndroidActualDefaultDecayAnimationSpec_androidKt.class", "size": 2051, "crc": -2004351917}, {"key": "androidx/compose/animation/AndroidFlingSpline$FlingResult.class", "name": "androidx/compose/animation/AndroidFlingSpline$FlingResult.class", "size": 2808, "crc": 636079068}, {"key": "androidx/compose/animation/AndroidFlingSpline.class", "name": "androidx/compose/animation/AndroidFlingSpline.class", "size": 2280, "crc": -1992774482}, {"key": "androidx/compose/animation/AnimateBoundsModifierKt$animateBounds$1.class", "name": "androidx/compose/animation/AnimateBoundsModifierKt$animateBounds$1.class", "size": 1844, "crc": 1021966047}, {"key": "androidx/compose/animation/AnimateBoundsModifierKt$animateBounds$2.class", "name": "androidx/compose/animation/AnimateBoundsModifierKt$animateBounds$2.class", "size": 3329, "crc": 1552127904}, {"key": "androidx/compose/animation/AnimateBoundsModifierKt.class", "name": "androidx/compose/animation/AnimateBoundsModifierKt.class", "size": 3943, "crc": -833687653}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$1$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$1$1.class", "size": 2930, "crc": 16483068}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$2$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$2$1.class", "size": 1237, "crc": 1091692142}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$3.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$3.class", "size": 3302, "crc": 1231888262}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$4$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$4$1.class", "size": 2938, "crc": -1195520017}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$5$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$5$1.class", "size": 1245, "crc": -54192058}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1$1.class", "size": 2132, "crc": -1237245676}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$1$1.class", "size": 2767, "crc": 429339865}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$3$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$3$1.class", "size": 1524, "crc": 1348359122}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$4$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$4$1.class", "size": 2022, "crc": -188575576}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1$invoke$$inlined$onDispose$1.class", "size": 2790, "crc": 1221966461}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5$1$1.class", "size": 3464, "crc": 97062034}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1$5.class", "size": 7886, "crc": 1721707235}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$6$1.class", "size": 11878, "crc": -865144126}, {"key": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$9.class", "name": "androidx/compose/animation/AnimatedContentKt$AnimatedContent$9.class", "size": 3392, "crc": -1186170974}, {"key": "androidx/compose/animation/AnimatedContentKt$SizeTransform$1.class", "name": "androidx/compose/animation/AnimatedContentKt$SizeTransform$1.class", "size": 2319, "crc": 1718782487}, {"key": "androidx/compose/animation/AnimatedContentKt.class", "name": "androidx/compose/animation/AnimatedContentKt.class", "size": 27363, "crc": -1393617886}, {"key": "androidx/compose/animation/AnimatedContentMeasurePolicy$measure$3.class", "name": "androidx/compose/animation/AnimatedContentMeasurePolicy$measure$3.class", "size": 4808, "crc": 1155407376}, {"key": "androidx/compose/animation/AnimatedContentMeasurePolicy.class", "name": "androidx/compose/animation/AnimatedContentMeasurePolicy.class", "size": 12830, "crc": -1547672976}, {"key": "androidx/compose/animation/AnimatedContentScope.class", "name": "androidx/compose/animation/AnimatedContentScope.class", "size": 585, "crc": 1653817509}, {"key": "androidx/compose/animation/AnimatedContentScopeImpl.class", "name": "androidx/compose/animation/AnimatedContentScopeImpl.class", "size": 2101, "crc": 1003279711}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection$Companion.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection$Companion.class", "size": 1974, "crc": 642589620}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$SlideDirection.class", "size": 3322, "crc": 423013750}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$slideIntoContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$slideIntoContainer$1.class", "size": 1704, "crc": 190916130}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope$slideOutOfContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope$slideOutOfContainer$1.class", "size": 1706, "crc": -405054809}, {"key": "androidx/compose/animation/AnimatedContentTransitionScope.class", "name": "androidx/compose/animation/AnimatedContentTransitionScope.class", "size": 6171, "crc": -1420923334}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$ChildData.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$ChildData.class", "size": 3482, "crc": -1765094924}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierElement.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierElement.class", "size": 6396, "crc": -891518739}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode$measure$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode$measure$1.class", "size": 4328, "crc": -754669584}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode$measure$size$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode$measure$size$1.class", "size": 4190, "crc": 1994233152}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode$measure$size$2.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode$measure$size$2.class", "size": 2975, "crc": 1706497830}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$SizeModifierNode.class", "size": 9886, "crc": -181302821}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$1.class", "size": 4093, "crc": -359849584}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$2.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$2.class", "size": 3764, "crc": 1287805559}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$3.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$3.class", "size": 4095, "crc": -1125453634}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$4.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideIntoContainer$4.class", "size": 3764, "crc": 1336578837}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$1.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$1.class", "size": 4328, "crc": 1686901098}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$2.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$2.class", "size": 4626, "crc": 439705619}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$3.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$3.class", "size": 4328, "crc": -1764169692}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$4.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl$slideOutOfContainer$4.class", "size": 4628, "crc": 1757777700}, {"key": "androidx/compose/animation/AnimatedContentTransitionScopeImpl.class", "name": "androidx/compose/animation/AnimatedContentTransitionScopeImpl.class", "size": 19629, "crc": -85561561}, {"key": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy$measure$1.class", "name": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy$measure$1.class", "size": 3254, "crc": -951578556}, {"key": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy.class", "name": "androidx/compose/animation/AnimatedEnterExitMeasurePolicy.class", "size": 9465, "crc": 1033111577}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1$1$1.class", "size": 1928, "crc": -320121392}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$2$1.class", "size": 4619, "crc": 735136875}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$4.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$4.class", "size": 3947, "crc": -2029389292}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$1.class", "size": 1766, "crc": 1307911218}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$2.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1$2.class", "size": 3517, "crc": 1401395802}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedEnterExitImpl$shouldDisposeAfterExit$2$1.class", "size": 5290, "crc": -2101419983}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$1$1.class", "size": 1542, "crc": 1259608184}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$10.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$10.class", "size": 3438, "crc": 1066895302}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$11$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$11$1.class", "size": 1647, "crc": -943133926}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$12.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$12.class", "size": 3450, "crc": 587488322}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$13.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$13.class", "size": 3259, "crc": 1517170006}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$2.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$2.class", "size": 2842, "crc": 1718736897}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$3$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$3$1.class", "size": 1587, "crc": -1572224128}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$4.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$4.class", "size": 3102, "crc": 1088324121}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$5$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$5$1.class", "size": 1590, "crc": -2082347703}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$6.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$6.class", "size": 3114, "crc": 277187871}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$7$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$7$1.class", "size": 1597, "crc": 764519161}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$8.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$8.class", "size": 3176, "crc": -1561431632}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$9$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibility$9$1.class", "size": 1642, "crc": -1569267766}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1$1.class", "size": 1916, "crc": 689044683}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$1$1.class", "size": 5711, "crc": 942373277}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$2$1.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$2$1.class", "size": 1988, "crc": -2082769905}, {"key": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$3.class", "name": "androidx/compose/animation/AnimatedVisibilityKt$AnimatedVisibilityImpl$3.class", "size": 3191, "crc": -247971913}, {"key": "androidx/compose/animation/AnimatedVisibilityKt.class", "name": "androidx/compose/animation/AnimatedVisibilityKt.class", "size": 50970, "crc": 1577942360}, {"key": "androidx/compose/animation/AnimatedVisibilityScope$DefaultImpls.class", "name": "androidx/compose/animation/AnimatedVisibilityScope$DefaultImpls.class", "size": 1607, "crc": -2118439106}, {"key": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$$inlined$debugInspectorInfo$1.class", "size": 3244, "crc": -847542885}, {"key": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$2.class", "name": "androidx/compose/animation/AnimatedVisibilityScope$animateEnterExit$2.class", "size": 3468, "crc": 1838540849}, {"key": "androidx/compose/animation/AnimatedVisibilityScope.class", "name": "androidx/compose/animation/AnimatedVisibilityScope.class", "size": 5021, "crc": 395091746}, {"key": "androidx/compose/animation/AnimatedVisibilityScopeImpl.class", "name": "androidx/compose/animation/AnimatedVisibilityScopeImpl.class", "size": 2830, "crc": 1352770747}, {"key": "androidx/compose/animation/AnimationModifierKt.class", "name": "androidx/compose/animation/AnimationModifierKt.class", "size": 5786, "crc": -800881223}, {"key": "androidx/compose/animation/BoundsAnimation$animate$1.class", "name": "androidx/compose/animation/BoundsAnimation$animate$1.class", "size": 2107, "crc": 1209628800}, {"key": "androidx/compose/animation/BoundsAnimation$animate$2.class", "name": "androidx/compose/animation/BoundsAnimation$animate$2.class", "size": 1913, "crc": 1557871353}, {"key": "androidx/compose/animation/BoundsAnimation.class", "name": "androidx/compose/animation/BoundsAnimation.class", "size": 10072, "crc": 100512088}, {"key": "androidx/compose/animation/BoundsAnimationElement.class", "name": "androidx/compose/animation/BoundsAnimationElement.class", "size": 7936, "crc": -1362249991}, {"key": "androidx/compose/animation/BoundsAnimationKt.class", "name": "androidx/compose/animation/BoundsAnimationKt.class", "size": 1428, "crc": -614045262}, {"key": "androidx/compose/animation/BoundsAnimationModifierNode$approachMeasure$1.class", "name": "androidx/compose/animation/BoundsAnimationModifierNode$approachMeasure$1.class", "size": 6591, "crc": -164114506}, {"key": "androidx/compose/animation/BoundsAnimationModifierNode.class", "name": "androidx/compose/animation/BoundsAnimationModifierNode.class", "size": 10000, "crc": 556177804}, {"key": "androidx/compose/animation/BoundsProvider.class", "name": "androidx/compose/animation/BoundsProvider.class", "size": 680, "crc": 74441606}, {"key": "androidx/compose/animation/BoundsTransform.class", "name": "androidx/compose/animation/BoundsTransform.class", "size": 1138, "crc": 611648211}, {"key": "androidx/compose/animation/BoundsTransformDeferredAnimation$animate$1.class", "name": "androidx/compose/animation/BoundsTransformDeferredAnimation$animate$1.class", "size": 4849, "crc": -500317918}, {"key": "androidx/compose/animation/BoundsTransformDeferredAnimation.class", "name": "androidx/compose/animation/BoundsTransformDeferredAnimation.class", "size": 12936, "crc": 377263385}, {"key": "androidx/compose/animation/ChangeSize$1.class", "name": "androidx/compose/animation/ChangeSize$1.class", "size": 2603, "crc": **********}, {"key": "androidx/compose/animation/ChangeSize.class", "name": "androidx/compose/animation/ChangeSize.class", "size": 5785, "crc": **********}, {"key": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$1.class", "name": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$1.class", "size": 3226, "crc": 1385354884}, {"key": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$2.class", "name": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1$2.class", "size": 4403, "crc": -1106913111}, {"key": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1.class", "name": "androidx/compose/animation/ColorVectorConverterKt$ColorToVector$1.class", "size": 2479, "crc": -1476627518}, {"key": "androidx/compose/animation/ColorVectorConverterKt.class", "name": "androidx/compose/animation/ColorVectorConverterKt.class", "size": 2228, "crc": -1313548493}, {"key": "androidx/compose/animation/ContentScaleTransitionEffect$Key.class", "name": "androidx/compose/animation/ContentScaleTransitionEffect$Key.class", "size": 1184, "crc": 399408858}, {"key": "androidx/compose/animation/ContentScaleTransitionEffect.class", "name": "androidx/compose/animation/ContentScaleTransitionEffect.class", "size": 3979, "crc": 1244904238}, {"key": "androidx/compose/animation/ContentTransform.class", "name": "androidx/compose/animation/ContentTransform.class", "size": 4427, "crc": 1935041616}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$1.class", "size": 2656, "crc": -441836617}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$2.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$2.class", "size": 2534, "crc": -1299886590}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$3$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$3$1.class", "size": 1193, "crc": -1579891266}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$4$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$4$1.class", "size": 1968, "crc": -1578039228}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$1$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$1$1.class", "size": 1742, "crc": 971128546}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$alpha$2.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$alpha$2.class", "size": 3055, "crc": -1498363491}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$invoke$$inlined$animateFloat$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$invoke$$inlined$animateFloat$1.class", "size": 1454, "crc": 1585777531}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$invoke$$inlined$animateFloat$2.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1$invoke$$inlined$animateFloat$2.class", "size": 1760, "crc": 1850558450}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$5$1.class", "size": 19110, "crc": -1303709620}, {"key": "androidx/compose/animation/CrossfadeKt$Crossfade$7.class", "name": "androidx/compose/animation/CrossfadeKt$Crossfade$7.class", "size": 3014, "crc": -649406358}, {"key": "androidx/compose/animation/CrossfadeKt.class", "name": "androidx/compose/animation/CrossfadeKt.class", "size": 21397, "crc": -1389416534}, {"key": "androidx/compose/animation/EnterExitState.class", "name": "androidx/compose/animation/EnterExitState.class", "size": 1944, "crc": -482080492}, {"key": "androidx/compose/animation/EnterExitTransitionElement.class", "name": "androidx/compose/animation/EnterExitTransitionElement.class", "size": 14634, "crc": 28755121}, {"key": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$1.class", "size": 1713, "crc": 1488048151}, {"key": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$TransformOriginVectorConverter$2.class", "size": 1784, "crc": 35575523}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$1.class", "size": 3345, "crc": 1218622514}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2$WhenMappings.class", "size": 965, "crc": 842748528}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$alpha$2.class", "size": 2692, "crc": 1032635989}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$block$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$block$1.class", "size": 3100, "crc": -1239270120}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$1.class", "size": 3348, "crc": 281627276}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2$WhenMappings.class", "size": 965, "crc": -1764957883}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$scale$2.class", "size": 2684, "crc": -1554569226}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$1.class", "size": 2602, "crc": -1423425648}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2$WhenMappings.class", "size": 985, "crc": 1294321756}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createGraphicsLayerBlock$1$1$transformOrigin$2.class", "size": 3279, "crc": -1757816694}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createModifier$1$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createModifier$1$1.class", "size": 1373, "crc": -404188077}, {"key": "androidx/compose/animation/EnterExitTransitionKt$createModifier$2$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$createModifier$2$1.class", "size": 2160, "crc": -789304790}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$1.class", "size": 1655, "crc": 1603836416}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandHorizontally$2.class", "size": 3748, "crc": -597934464}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandIn$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandIn$1.class", "size": 2768, "crc": -724295458}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$1.class", "size": 1643, "crc": -1113427088}, {"key": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$expandVertically$2.class", "size": 3728, "crc": 213121497}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$1.class", "size": 1654, "crc": -1010742050}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkHorizontally$2.class", "size": 3746, "crc": -1386717776}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkOut$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkOut$1.class", "size": 2774, "crc": -1536127071}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$1.class", "size": 1642, "crc": 615195011}, {"key": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$shrinkVertically$2.class", "size": 3726, "crc": 30007427}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$1.class", "size": 1516, "crc": -1314003028}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInHorizontally$2.class", "size": 3519, "crc": -2018039422}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$1.class", "size": 1510, "crc": -485218280}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideInVertically$2.class", "size": 3508, "crc": -2102298225}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$1.class", "size": 1518, "crc": -1274441482}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutHorizontally$2.class", "size": 3524, "crc": -1552157468}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$1.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$1.class", "size": 1512, "crc": -2115373447}, {"key": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$2.class", "name": "androidx/compose/animation/EnterExitTransitionKt$slideOutVertically$2.class", "size": 3513, "crc": 703295318}, {"key": "androidx/compose/animation/EnterExitTransitionKt.class", "name": "androidx/compose/animation/EnterExitTransitionKt.class", "size": 47944, "crc": 1157771596}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$WhenMappings.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$WhenMappings.class", "size": 903, "crc": 1067847276}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$1.class", "size": 1878, "crc": -1535054321}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$2.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$2.class", "size": 2455, "crc": -2080832972}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$3$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$3$1.class", "size": 1893, "crc": 1372145400}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$animSize$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$animSize$1.class", "size": 1948, "crc": -619247481}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$1.class", "size": 2402, "crc": 636369154}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$2.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$offsetDelta$2.class", "size": 1970, "crc": 206117034}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$slideOffset$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$measure$slideOffset$1.class", "size": 1974, "crc": 1659022175}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$sizeTransitionSpec$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$sizeTransitionSpec$1.class", "size": 3749, "crc": -885045540}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode$slideSpec$1.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode$slideSpec$1.class", "size": 3739, "crc": 231317424}, {"key": "androidx/compose/animation/EnterExitTransitionModifierNode.class", "name": "androidx/compose/animation/EnterExitTransitionModifierNode.class", "size": 21102, "crc": -472448026}, {"key": "androidx/compose/animation/EnterTransition$Companion.class", "name": "androidx/compose/animation/EnterTransition$Companion.class", "size": 1165, "crc": -1046422733}, {"key": "androidx/compose/animation/EnterTransition.class", "name": "androidx/compose/animation/EnterTransition.class", "size": 4442, "crc": 14095708}, {"key": "androidx/compose/animation/EnterTransitionImpl.class", "name": "androidx/compose/animation/EnterTransitionImpl.class", "size": 1174, "crc": -1639684661}, {"key": "androidx/compose/animation/ExitTransition$Companion.class", "name": "androidx/compose/animation/ExitTransition$Companion.class", "size": 1392, "crc": -1831788299}, {"key": "androidx/compose/animation/ExitTransition.class", "name": "androidx/compose/animation/ExitTransition.class", "size": 5151, "crc": -751370654}, {"key": "androidx/compose/animation/ExitTransitionImpl.class", "name": "androidx/compose/animation/ExitTransitionImpl.class", "size": 1170, "crc": -1839494581}, {"key": "androidx/compose/animation/ExperimentalAnimationApi.class", "name": "androidx/compose/animation/ExperimentalAnimationApi.class", "size": 1040, "crc": 1348765484}, {"key": "androidx/compose/animation/ExperimentalSharedTransitionApi.class", "name": "androidx/compose/animation/ExperimentalSharedTransitionApi.class", "size": 1074, "crc": -1780686804}, {"key": "androidx/compose/animation/Fade.class", "name": "androidx/compose/animation/Fade.class", "size": 3405, "crc": -670785956}, {"key": "androidx/compose/animation/FlingCalculator$FlingInfo.class", "name": "androidx/compose/animation/FlingCalculator$FlingInfo.class", "size": 3987, "crc": -770745767}, {"key": "androidx/compose/animation/FlingCalculator.class", "name": "androidx/compose/animation/FlingCalculator.class", "size": 2904, "crc": 1142457758}, {"key": "androidx/compose/animation/FlingCalculatorKt.class", "name": "androidx/compose/animation/FlingCalculatorKt.class", "size": 1064, "crc": 1528949389}, {"key": "androidx/compose/animation/GraphicsLayerBlockForEnterExit.class", "name": "androidx/compose/animation/GraphicsLayerBlockForEnterExit.class", "size": 857, "crc": 51604138}, {"key": "androidx/compose/animation/LayerRenderer.class", "name": "androidx/compose/animation/LayerRenderer.class", "size": 1038, "crc": 2115284660}, {"key": "androidx/compose/animation/LayoutModifierNodeWithPassThroughIntrinsics.class", "name": "androidx/compose/animation/LayoutModifierNodeWithPassThroughIntrinsics.class", "size": 2262, "crc": 1953023009}, {"key": "androidx/compose/animation/OnLookaheadMeasured.class", "name": "androidx/compose/animation/OnLookaheadMeasured.class", "size": 557, "crc": -1344024692}, {"key": "androidx/compose/animation/RemeasureImpl.class", "name": "androidx/compose/animation/RemeasureImpl.class", "size": 1003, "crc": -1296629257}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNode$LayerWithRenderer.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNode$LayerWithRenderer.class", "size": 9437, "crc": -1265952743}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNode$draw$1.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNode$draw$1.class", "size": 1657, "crc": 1327191853}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNode.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNode.class", "size": 9854, "crc": 591298507}, {"key": "androidx/compose/animation/RenderInTransitionOverlayNodeElement.class", "name": "androidx/compose/animation/RenderInTransitionOverlayNodeElement.class", "size": 8409, "crc": -1478648837}, {"key": "androidx/compose/animation/Scale.class", "name": "androidx/compose/animation/Scale.class", "size": 4376, "crc": -461088351}, {"key": "androidx/compose/animation/ScaleToBoundsImpl.class", "name": "androidx/compose/animation/ScaleToBoundsImpl.class", "size": 1704, "crc": -1984702257}, {"key": "androidx/compose/animation/SharedBoundsNode$approachPlace$1.class", "name": "androidx/compose/animation/SharedBoundsNode$approachPlace$1.class", "size": 1839, "crc": -1018705744}, {"key": "androidx/compose/animation/SharedBoundsNode$approachPlace$2.class", "name": "androidx/compose/animation/SharedBoundsNode$approachPlace$2.class", "size": 2040, "crc": 2104039358}, {"key": "androidx/compose/animation/SharedBoundsNode$approachPlace$3.class", "name": "androidx/compose/animation/SharedBoundsNode$approachPlace$3.class", "size": 2032, "crc": 648148090}, {"key": "androidx/compose/animation/SharedBoundsNode$draw$1.class", "name": "androidx/compose/animation/SharedBoundsNode$draw$1.class", "size": 1916, "crc": -1037783261}, {"key": "androidx/compose/animation/SharedBoundsNode$measure$1.class", "name": "androidx/compose/animation/SharedBoundsNode$measure$1.class", "size": 2403, "crc": 1148132458}, {"key": "androidx/compose/animation/SharedBoundsNode.class", "name": "androidx/compose/animation/SharedBoundsNode.class", "size": 22952, "crc": 1880485851}, {"key": "androidx/compose/animation/SharedBoundsNodeElement.class", "name": "androidx/compose/animation/SharedBoundsNodeElement.class", "size": 4426, "crc": -251541220}, {"key": "androidx/compose/animation/SharedContentNodeKt$ModifierLocalSharedElementInternalState$1.class", "name": "androidx/compose/animation/SharedContentNodeKt$ModifierLocalSharedElementInternalState$1.class", "size": 1274, "crc": -1937004883}, {"key": "androidx/compose/animation/SharedContentNodeKt.class", "name": "androidx/compose/animation/SharedContentNodeKt.class", "size": 1575, "crc": -1044926163}, {"key": "androidx/compose/animation/SharedElement$observingVisibilityChange$1.class", "name": "androidx/compose/animation/SharedElement$observingVisibilityChange$1.class", "size": 1357, "crc": 1835740402}, {"key": "androidx/compose/animation/SharedElement$updateMatch$1.class", "name": "androidx/compose/animation/SharedElement$updateMatch$1.class", "size": 1403, "crc": -1903673303}, {"key": "androidx/compose/animation/SharedElement.class", "name": "androidx/compose/animation/SharedElement.class", "size": 17728, "crc": 1499134046}, {"key": "androidx/compose/animation/SharedElementInternalState.class", "name": "androidx/compose/animation/SharedElementInternalState.class", "size": 18693, "crc": -1223260156}, {"key": "androidx/compose/animation/SharedElementKt.class", "name": "androidx/compose/animation/SharedElementKt.class", "size": 1677, "crc": 700585632}, {"key": "androidx/compose/animation/SharedTransitionScope$OverlayClip.class", "name": "androidx/compose/animation/SharedTransitionScope$OverlayClip.class", "size": 1469, "crc": 245110826}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$animatedSize$1.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$animatedSize$1.class", "size": 1197, "crc": 1904809143}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$contentSize$1.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion$contentSize$1.class", "size": 1194, "crc": 1413438696}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize$Companion.class", "size": 1887, "crc": 855051844}, {"key": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize.class", "name": "androidx/compose/animation/SharedTransitionScope$PlaceHolderSize.class", "size": 1102, "crc": 832299907}, {"key": "androidx/compose/animation/SharedTransitionScope$ResizeMode$Companion.class", "name": "androidx/compose/animation/SharedTransitionScope$ResizeMode$Companion.class", "size": 2890, "crc": 1716256885}, {"key": "androidx/compose/animation/SharedTransitionScope$ResizeMode.class", "name": "androidx/compose/animation/SharedTransitionScope$ResizeMode.class", "size": 1008, "crc": -173134166}, {"key": "androidx/compose/animation/SharedTransitionScope$SharedContentState.class", "name": "androidx/compose/animation/SharedTransitionScope$SharedContentState.class", "size": 4939, "crc": -47114210}, {"key": "androidx/compose/animation/SharedTransitionScope$renderInSharedTransitionScopeOverlay$1.class", "name": "androidx/compose/animation/SharedTransitionScope$renderInSharedTransitionScopeOverlay$1.class", "size": 1592, "crc": -1571500780}, {"key": "androidx/compose/animation/SharedTransitionScope.class", "name": "androidx/compose/animation/SharedTransitionScope.class", "size": 13542, "crc": -198137220}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$Companion$SharedTransitionObserver$2$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$Companion$SharedTransitionObserver$2$1.class", "size": 1668, "crc": 719704419}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$Companion$SharedTransitionObserver$2.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$Companion$SharedTransitionObserver$2.class", "size": 2416, "crc": -1639059554}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$Companion.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$Companion.class", "size": 1681, "crc": 695447881}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$ShapeBasedClip.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$ShapeBasedClip.class", "size": 2995, "crc": -496206013}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$drawInOverlay$$inlined$sortBy$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$drawInOverlay$$inlined$sortBy$1.class", "size": 2763, "crc": 1029752951}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$observeAnimatingBlock$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$observeAnimatingBlock$1.class", "size": 4438, "crc": 1235517498}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$onStateRemoved$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$onStateRemoved$1$1.class", "size": 3742, "crc": 634351272}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$1.class", "size": 2475, "crc": 150082377}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$1$1.class", "size": 1629, "crc": 1081897977}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$2$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2$2$1.class", "size": 1629, "crc": -1914856990}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBounds$2.class", "size": 8318, "crc": -1351523224}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsImpl$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsImpl$1.class", "size": 14925, "crc": -452754342}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsWithCallerManagedVisibility$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedBoundsWithCallerManagedVisibility$1.class", "size": 2068, "crc": -2130057906}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElement$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElement$1.class", "size": 2244, "crc": 1228732090}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElementWithCallerManagedVisibility$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$sharedElementWithCallerManagedVisibility$1.class", "size": 2061, "crc": -1998698401}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl$updateTransitionActiveness$1.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl$updateTransitionActiveness$1.class", "size": 1698, "crc": -1240802654}, {"key": "androidx/compose/animation/SharedTransitionScopeImpl.class", "name": "androidx/compose/animation/SharedTransitionScopeImpl.class", "size": 38066, "crc": 772781966}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$DefaultClipInOverlayDuringTransition$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$DefaultClipInOverlayDuringTransition$1.class", "size": 1438, "crc": -1181843521}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$ParentClip$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$ParentClip$1.class", "size": 1993, "crc": 1803080356}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$1.class", "size": 10292, "crc": 783309900}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$2.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionLayout$2.class", "size": 2237, "crc": 1419200693}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1$1.class", "size": 2859, "crc": -287133979}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$1$1.class", "size": 2917, "crc": -611596512}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$2$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$2$1.class", "size": 1843, "crc": 1237443807}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1$invoke$$inlined$onDispose$1.class", "size": 2295, "crc": -425806709}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1$3$1.class", "size": 2916, "crc": 591294389}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$1.class", "size": 9343, "crc": 2011039653}, {"key": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$2.class", "name": "androidx/compose/animation/SharedTransitionScopeKt$SharedTransitionScope$2.class", "size": 2070, "crc": -793472010}, {"key": "androidx/compose/animation/SharedTransitionScopeKt.class", "name": "androidx/compose/animation/SharedTransitionScopeKt.class", "size": 12922, "crc": 468684836}, {"key": "androidx/compose/animation/SingleValueAnimationKt.class", "name": "androidx/compose/animation/SingleValueAnimationKt.class", "size": 7516, "crc": -522232496}, {"key": "androidx/compose/animation/SizeAnimationModifierElement.class", "name": "androidx/compose/animation/SizeAnimationModifierElement.class", "size": 7340, "crc": -1510239993}, {"key": "androidx/compose/animation/SizeAnimationModifierNode$AnimData.class", "name": "androidx/compose/animation/SizeAnimationModifierNode$AnimData.class", "size": 4586, "crc": -597124278}, {"key": "androidx/compose/animation/SizeAnimationModifierNode$animateTo$data$1$1.class", "name": "androidx/compose/animation/SizeAnimationModifierNode$animateTo$data$1$1.class", "size": 4905, "crc": 2064417681}, {"key": "androidx/compose/animation/SizeAnimationModifierNode$measure$2.class", "name": "androidx/compose/animation/SizeAnimationModifierNode$measure$2.class", "size": 3873, "crc": 243761950}, {"key": "androidx/compose/animation/SizeAnimationModifierNode.class", "name": "androidx/compose/animation/SizeAnimationModifierNode.class", "size": 12555, "crc": -1297914887}, {"key": "androidx/compose/animation/SizeTransform.class", "name": "androidx/compose/animation/SizeTransform.class", "size": 976, "crc": -1078388125}, {"key": "androidx/compose/animation/SizeTransformImpl.class", "name": "androidx/compose/animation/SizeTransformImpl.class", "size": 3040, "crc": 1911307251}, {"key": "androidx/compose/animation/SkipToLookaheadElement.class", "name": "androidx/compose/animation/SkipToLookaheadElement.class", "size": 5982, "crc": 1427421915}, {"key": "androidx/compose/animation/SkipToLookaheadNode$measure$1$1.class", "name": "androidx/compose/animation/SkipToLookaheadNode$measure$1$1.class", "size": 1857, "crc": 285770881}, {"key": "androidx/compose/animation/SkipToLookaheadNode$measure$2$1.class", "name": "androidx/compose/animation/SkipToLookaheadNode$measure$2$1.class", "size": 3534, "crc": -1639588207}, {"key": "androidx/compose/animation/SkipToLookaheadNode$measure$2.class", "name": "androidx/compose/animation/SkipToLookaheadNode$measure$2.class", "size": 7450, "crc": 1978833679}, {"key": "androidx/compose/animation/SkipToLookaheadNode.class", "name": "androidx/compose/animation/SkipToLookaheadNode.class", "size": 10127, "crc": -1853500190}, {"key": "androidx/compose/animation/SkipToLookaheadNodeKt$DefaultEnabled$1.class", "name": "androidx/compose/animation/SkipToLookaheadNodeKt$DefaultEnabled$1.class", "size": 1180, "crc": 1561858759}, {"key": "androidx/compose/animation/SkipToLookaheadNodeKt$createContentScaleModifier$1.class", "name": "androidx/compose/animation/SkipToLookaheadNodeKt$createContentScaleModifier$1.class", "size": 1968, "crc": -243907694}, {"key": "androidx/compose/animation/SkipToLookaheadNodeKt.class", "name": "androidx/compose/animation/SkipToLookaheadNodeKt.class", "size": 3071, "crc": -678167303}, {"key": "androidx/compose/animation/Slide.class", "name": "androidx/compose/animation/Slide.class", "size": 4341, "crc": 97559544}, {"key": "androidx/compose/animation/SplineBasedDecayKt.class", "name": "androidx/compose/animation/SplineBasedDecayKt.class", "size": 2749, "crc": -1949179969}, {"key": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec.class", "name": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec.class", "size": 2848, "crc": -1510640079}, {"key": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec_androidKt.class", "name": "androidx/compose/animation/SplineBasedFloatDecayAnimationSpec_androidKt.class", "size": 5644, "crc": 66585680}, {"key": "androidx/compose/animation/TargetData.class", "name": "androidx/compose/animation/TargetData.class", "size": 4844, "crc": -1143831329}, {"key": "androidx/compose/animation/TransitionData.class", "name": "androidx/compose/animation/TransitionData.class", "size": 6968, "crc": -1597506088}, {"key": "androidx/compose/animation/TransitionEffect.class", "name": "androidx/compose/animation/TransitionEffect.class", "size": 1290, "crc": 601545677}, {"key": "androidx/compose/animation/TransitionEffectKey.class", "name": "androidx/compose/animation/TransitionEffectKey.class", "size": 576, "crc": 68330044}, {"key": "androidx/compose/animation/TransitionKt$animateColor$$inlined$animateValue$1.class", "name": "androidx/compose/animation/TransitionKt$animateColor$$inlined$animateValue$1.class", "size": 1600, "crc": 1455171810}, {"key": "androidx/compose/animation/TransitionKt$animateColor$$inlined$animateValue$2.class", "name": "androidx/compose/animation/TransitionKt$animateColor$$inlined$animateValue$2.class", "size": 1906, "crc": -789985996}, {"key": "androidx/compose/animation/TransitionKt$animateColor$1.class", "name": "androidx/compose/animation/TransitionKt$animateColor$1.class", "size": 2858, "crc": -1236308959}, {"key": "androidx/compose/animation/TransitionKt.class", "name": "androidx/compose/animation/TransitionKt.class", "size": 14766, "crc": 1471378365}, {"key": "androidx/compose/animation/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/animation/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 445, "crc": 572114726}, {"key": "META-INF/androidx.compose.animation_animation.version", "name": "META-INF/androidx.compose.animation_animation.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/animation.kotlin_module", "name": "META-INF/animation.kotlin_module", "size": 554, "crc": -300200145}]