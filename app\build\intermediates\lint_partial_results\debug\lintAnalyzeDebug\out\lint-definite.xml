<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.12.1" type="incidents">

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core-ktx than 1.10.1 is available: 1.17.0">
        <fix-replace
            description="Change to 1.17.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.17.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="4"
            column="11"
            startOffset="54"
            endLine="4"
            endColumn="19"
            endOffset="62"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.compose:compose-bom than 2024.02.00 is available: 2025.08.00">
        <fix-replace
            description="Change to 2025.08.00"
            family="Update versions"
            oldString="2024.02.00"
            replacement="2025.08.00"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="5"
            column="15"
            startOffset="77"
            endLine="5"
            endColumn="27"
            endOffset="89"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.tv:tv-foundation than 1.0.0-alpha10 is available: 1.0.0-alpha12">
        <fix-replace
            description="Change to 1.0.0-alpha12"
            family="Update versions"
            oldString="1.0.0-alpha10"
            replacement="1.0.0-alpha12"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="14"
            startOffset="103"
            endLine="6"
            endColumn="29"
            endOffset="118"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.tv:tv-material than 1.0.0-alpha10 is available: 1.0.1">
        <fix-replace
            description="Change to 1.0.1"
            family="Update versions"
            oldString="1.0.0-alpha10"
            replacement="1.0.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="14"
            startOffset="103"
            endLine="6"
            endColumn="29"
            endOffset="118"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.activity:activity-compose than 1.8.2 is available: 1.10.1">
        <fix-replace
            description="Change to 1.10.1"
            family="Update versions"
            oldString="1.8.2"
            replacement="1.10.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="7"
            column="20"
            startOffset="138"
            endLine="7"
            endColumn="27"
            endOffset="145"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.android than 2.0.21 is available: 2.2.10">
        <fix-replace
            description="Change to 2.2.10"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.2.10"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

    <incident
        id="NewerVersionAvailable"
        severity="warning"
        message="A newer version of org.jetbrains.kotlin.plugin.compose than 2.0.21 is available: 2.2.10">
        <fix-replace
            description="Change to 2.2.10"
            family="Update versions"
            oldString="2.0.21"
            replacement="2.2.10"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="3"
            column="10"
            startOffset="35"
            endLine="3"
            endColumn="18"
            endOffset="43"/>
    </incident>

</incidents>
