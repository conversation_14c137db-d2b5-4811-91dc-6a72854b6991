package com.aj.aj_tv_player

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.tv.material3.Card
import androidx.tv.material3.CardDefaults
import androidx.tv.material3.MaterialTheme
import androidx.tv.material3.Text
import kotlinx.coroutines.delay

data class MediaItem(
    val id: Int,
    val title: String,
    val description: String,
    val imageUrl: String,
    val backgroundImageUrl: String
)

@Composable
fun HomeScreen() {
    val mediaItems = remember {
        listOf(
            MediaItem(
                id = 1,
                title = "Movie 1",
                description = "An exciting adventure movie with stunning visuals and compelling storyline.",
                imageUrl = "https://via.placeholder.com/300x450/FF5722/FFFFFF?text=Movie+1",
                backgroundImageUrl = "https://via.placeholder.com/1920x1080/FF5722/FFFFFF?text=Movie+1+BG"
            ),
            MediaItem(
                id = 2,
                title = "Movie 2", 
                description = "A thrilling drama that will keep you on the edge of your seat.",
                imageUrl = "https://via.placeholder.com/300x450/2196F3/FFFFFF?text=Movie+2",
                backgroundImageUrl = "https://via.placeholder.com/1920x1080/2196F3/FFFFFF?text=Movie+2+BG"
            ),
            MediaItem(
                id = 3,
                title = "Movie 3",
                description = "A heartwarming story about friendship and courage.",
                imageUrl = "https://via.placeholder.com/300x450/4CAF50/FFFFFF?text=Movie+3",
                backgroundImageUrl = "https://via.placeholder.com/1920x1080/4CAF50/FFFFFF?text=Movie+3+BG"
            ),
            MediaItem(
                id = 4,
                title = "Movie 4",
                description = "An action-packed thriller with amazing special effects.",
                imageUrl = "https://via.placeholder.com/300x450/9C27B0/FFFFFF?text=Movie+4",
                backgroundImageUrl = "https://via.placeholder.com/1920x1080/9C27B0/FFFFFF?text=Movie+4+BG"
            ),
            MediaItem(
                id = 5,
                title = "Movie 5",
                description = "A romantic comedy that will make you laugh and cry.",
                imageUrl = "https://via.placeholder.com/300x450/FF9800/FFFFFF?text=Movie+5",
                backgroundImageUrl = "https://via.placeholder.com/1920x1080/FF9800/FFFFFF?text=Movie+5+BG"
            )
        )
    }

    ImmersiveList(mediaItems = mediaItems)
}

@Composable
fun ImmersiveList(mediaItems: List<MediaItem>) {
    var selectedIndex by remember { mutableIntStateOf(0) }
    val selectedItem = mediaItems.getOrNull(selectedIndex)

    Box(modifier = Modifier.fillMaxSize()) {
        // Background Image
        selectedItem?.let { item ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color.Black.copy(alpha = 0.3f),
                                Color.Black.copy(alpha = 0.7f)
                            )
                        )
                    )
            )
        }

        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            // Content area with background info
            selectedItem?.let { item ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(48.dp),
                    verticalArrangement = Arrangement.Bottom
                ) {
                    Text(
                        text = item.title,
                        style = MaterialTheme.typography.displayMedium,
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = item.description,
                        style = MaterialTheme.typography.bodyLarge,
                        color = Color.White.copy(alpha = 0.9f),
                        modifier = Modifier.widthIn(max = 600.dp)
                    )
                }
            }

            // Content grid (horizontal list)
            LazyRow(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 48.dp, vertical = 32.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                state = rememberLazyListState()
            ) {
                itemsIndexed(mediaItems) { index, item ->
                    MediaCard(
                        item = item,
                        isSelected = index == selectedIndex,
                        onFocusChanged = { hasFocus ->
                            if (hasFocus) {
                                selectedIndex = index
                            }
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun MediaCard(
    item: MediaItem,
    isSelected: Boolean,
    onFocusChanged: (Boolean) -> Unit
) {
    var isFocused by remember { mutableStateOf(false) }
    
    Card(
        onClick = { /* Handle click */ },
        modifier = Modifier
            .width(200.dp)
            .height(300.dp)
            .scale(if (isFocused) 1.1f else 1.0f)
            .border(
                width = if (isFocused) 4.dp else 0.dp,
                color = if (isFocused) Color.White else Color.Transparent,
                shape = RoundedCornerShape(12.dp)
            )
            .focusable()
            .onFocusChanged { focusState ->
                isFocused = focusState.isFocused
                onFocusChanged(focusState.isFocused)
            },
        shape = CardDefaults.shape(shape = RoundedCornerShape(12.dp)),
        colors = CardDefaults.colors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            // Placeholder for image
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.verticalGradient(
                            colors = listOf(
                                Color.Gray.copy(alpha = 0.3f),
                                Color.Gray.copy(alpha = 0.8f)
                            )
                        )
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = item.title,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}
