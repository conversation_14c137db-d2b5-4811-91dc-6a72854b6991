[{"key": "androidx/compose/foundation/layout/AddedInsets.class", "name": "androidx/compose/foundation/layout/AddedInsets.class", "size": 2918, "crc": -1003543971}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-4j6BHR0$$inlined$debugInspectorInfo$1.class", "size": 3173, "crc": 1674491871}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt$paddingFrom-Y_r0B1c$$inlined$debugInspectorInfo$1.class", "size": 3188, "crc": -1113363989}, {"key": "androidx/compose/foundation/layout/AlignmentLineKt.class", "name": "androidx/compose/foundation/layout/AlignmentLineKt.class", "size": 12056, "crc": -2054513003}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpElement.class", "size": 6791, "crc": 926094770}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetDpNode.class", "size": 3213, "crc": 25727633}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitElement.class", "size": 5135, "crc": -**********}, {"key": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "name": "androidx/compose/foundation/layout/AlignmentLineOffsetTextUnitNode.class", "size": 4911, "crc": -62495587}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Block.class", "size": 3854, "crc": **********}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider$Value.class", "size": 3265, "crc": **********}, {"key": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "name": "androidx/compose/foundation/layout/AlignmentLineProvider.class", "size": 1565, "crc": **********}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline$FlingResult.class", "size": 4081, "crc": -978363245}, {"key": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "name": "androidx/compose/foundation/layout/AndroidFlingSpline.class", "size": 3690, "crc": **********}, {"key": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "name": "androidx/compose/foundation/layout/AndroidWindowInsets.class", "size": 6144, "crc": **********}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Center$1.class", "size": 1665, "crc": 531631259}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Left$1.class", "size": 1660, "crc": 464675703}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$Right$1.class", "size": 1669, "crc": 1397267347}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceAround$1.class", "size": 1685, "crc": -1928044713}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceBetween$1.class", "size": 1689, "crc": 222453425}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute$SpaceEvenly$1.class", "size": 1685, "crc": -1726495465}, {"key": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "name": "androidx/compose/foundation/layout/Arrangement$Absolute.class", "size": 7362, "crc": 1994151511}, {"key": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Bottom$1.class", "size": 1424, "crc": -221128445}, {"key": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Center$1.class", "size": 2957, "crc": -565348133}, {"key": "androidx/compose/foundation/layout/Arrangement$End$1.class", "name": "androidx/compose/foundation/layout/Arrangement$End$1.class", "size": 1734, "crc": -1692633826}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal$DefaultImpls.class", "size": 910, "crc": 1653755226}, {"key": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "name": "androidx/compose/foundation/layout/Arrangement$Horizontal.class", "size": 2376, "crc": -755516046}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical$DefaultImpls.class", "size": 960, "crc": -1808695606}, {"key": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "name": "androidx/compose/foundation/layout/Arrangement$HorizontalOrVertical.class", "size": 2306, "crc": -1412928207}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceAround$1.class", "size": 2997, "crc": -601915673}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceBetween$1.class", "size": 3005, "crc": -531591680}, {"key": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "name": "androidx/compose/foundation/layout/Arrangement$SpaceEvenly$1.class", "size": 2997, "crc": -1377354812}, {"key": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "name": "androidx/compose/foundation/layout/Arrangement$SpacedAligned.class", "size": 8410, "crc": -948022825}, {"key": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Start$1.class", "size": 1740, "crc": -1592777732}, {"key": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "name": "androidx/compose/foundation/layout/Arrangement$Top$1.class", "size": 1409, "crc": -1150992194}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical$DefaultImpls.class", "size": 900, "crc": -1746101510}, {"key": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "name": "androidx/compose/foundation/layout/Arrangement$Vertical.class", "size": 2226, "crc": -431721741}, {"key": "androidx/compose/foundation/layout/Arrangement.class", "name": "androidx/compose/foundation/layout/Arrangement.class", "size": 18529, "crc": 960121441}, {"key": "androidx/compose/foundation/layout/AspectRatioElement.class", "name": "androidx/compose/foundation/layout/AspectRatioElement.class", "size": 5317, "crc": 1516619698}, {"key": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/AspectRatioKt$aspectRatio$$inlined$debugInspectorInfo$1.class", "size": 2977, "crc": 1650990497}, {"key": "androidx/compose/foundation/layout/AspectRatioKt.class", "name": "androidx/compose/foundation/layout/AspectRatioKt.class", "size": 3158, "crc": -806853902}, {"key": "androidx/compose/foundation/layout/AspectRatioNode.class", "name": "androidx/compose/foundation/layout/AspectRatioNode.class", "size": 12029, "crc": 95627727}, {"key": "androidx/compose/foundation/layout/BoxChildDataElement.class", "name": "androidx/compose/foundation/layout/BoxChildDataElement.class", "size": 4226, "crc": 1273671647}, {"key": "androidx/compose/foundation/layout/BoxChildDataNode.class", "name": "androidx/compose/foundation/layout/BoxChildDataNode.class", "size": 2283, "crc": 1374442660}, {"key": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "name": "androidx/compose/foundation/layout/BoxKt$EmptyBoxMeasurePolicy$1.class", "size": 2327, "crc": 1689603056}, {"key": "androidx/compose/foundation/layout/BoxKt.class", "name": "androidx/compose/foundation/layout/BoxKt.class", "size": 17698, "crc": 596277678}, {"key": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "name": "androidx/compose/foundation/layout/BoxMeasurePolicy.class", "size": 10941, "crc": 342519627}, {"key": "androidx/compose/foundation/layout/BoxScope.class", "name": "androidx/compose/foundation/layout/BoxScope.class", "size": 1063, "crc": -1107154240}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$align$$inlined$debugInspectorInfo$1.class", "size": 2633, "crc": 61221657}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance$matchParentSize$$inlined$debugInspectorInfo$1.class", "size": 2477, "crc": 2011084838}, {"key": "androidx/compose/foundation/layout/BoxScopeInstance.class", "name": "androidx/compose/foundation/layout/BoxScopeInstance.class", "size": 3511, "crc": 1364044421}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt$BoxWithConstraints$1$1$measurables$1.class", "size": 3112, "crc": 665744507}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsKt.class", "size": 9427, "crc": -421763012}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScope.class", "size": 1082, "crc": -1247238250}, {"key": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "name": "androidx/compose/foundation/layout/BoxWithConstraintsScopeImpl.class", "size": 6546, "crc": 1760341598}, {"key": "androidx/compose/foundation/layout/ColumnKt.class", "name": "androidx/compose/foundation/layout/ColumnKt.class", "size": 11368, "crc": -1987868248}, {"key": "androidx/compose/foundation/layout/ColumnMeasurePolicy.class", "name": "androidx/compose/foundation/layout/ColumnMeasurePolicy.class", "size": 13509, "crc": 1543774075}, {"key": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/ColumnScope$DefaultImpls.class", "size": 600, "crc": -1936914151}, {"key": "androidx/compose/foundation/layout/ColumnScope.class", "name": "androidx/compose/foundation/layout/ColumnScope.class", "size": 2634, "crc": 1373553410}, {"key": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/ColumnScopeInstance.class", "size": 4619, "crc": 1462510555}, {"key": "androidx/compose/foundation/layout/ComposeFoundationLayoutFlags.class", "name": "androidx/compose/foundation/layout/ComposeFoundationLayoutFlags.class", "size": 1214, "crc": -393665769}, {"key": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "name": "androidx/compose/foundation/layout/ConsumedInsetsModifier.class", "size": 2990, "crc": 1655029900}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3516, "crc": 676653542}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3664, "crc": 1379321453}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3660, "crc": 1404139475}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow$Companion.class", "size": 10892, "crc": -1918092646}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflow.class", "size": 3896, "crc": -1815765552}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScope.class", "size": 901, "crc": -1666918467}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnOverflowScopeImpl.class", "size": 4083, "crc": 552396459}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnScope.class", "size": 2096, "crc": -614547488}, {"key": "androidx/compose/foundation/layout/ContextualFlowColumnScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowColumnScopeImpl.class", "size": 6132, "crc": -1513229284}, {"key": "androidx/compose/foundation/layout/ContextualFlowItemIterator.class", "name": "androidx/compose/foundation/layout/ContextualFlowItemIterator.class", "size": 4957, "crc": 1713513391}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$measurePolicy$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowColumn$measurePolicy$1.class", "size": 4189, "crc": -2065150060}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$measurePolicy$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt$ContextualFlowRow$measurePolicy$1.class", "size": 4106, "crc": -420005876}, {"key": "androidx/compose/foundation/layout/ContextualFlowLayoutKt.class", "name": "androidx/compose/foundation/layout/ContextualFlowLayoutKt.class", "size": 25057, "crc": -635008034}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3486, "crc": 970765226}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3634, "crc": 2067723741}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3630, "crc": 640631970}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow$Companion.class", "size": 10818, "crc": -201223582}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflow.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflow.class", "size": 3881, "crc": 299811354}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScope.class", "size": 889, "crc": 1650801203}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowOverflowScopeImpl.class", "size": 4279, "crc": -257002012}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowScope.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowScope.class", "size": 2075, "crc": -864154965}, {"key": "androidx/compose/foundation/layout/ContextualFlowRowScopeImpl.class", "name": "androidx/compose/foundation/layout/ContextualFlowRowScopeImpl.class", "size": 6246, "crc": -1898746649}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$AlignmentLineCrossAxisAlignment.class", "size": 2712, "crc": -1190399102}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$CenterCrossAxisAlignment.class", "size": 1529, "crc": -697365131}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$Companion.class", "size": 4176, "crc": -1108318554}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$EndCrossAxisAlignment.class", "size": 1618, "crc": -684002218}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$HorizontalCrossAxisAlignment.class", "size": 3533, "crc": -236514613}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$StartCrossAxisAlignment.class", "size": 1624, "crc": -946223532}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment$VerticalCrossAxisAlignment.class", "size": 3456, "crc": 1537930456}, {"key": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "name": "androidx/compose/foundation/layout/CrossAxisAlignment.class", "size": 3848, "crc": -1359883294}, {"key": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "name": "androidx/compose/foundation/layout/DerivedHeightModifier.class", "size": 8833, "crc": 734579512}, {"key": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "name": "androidx/compose/foundation/layout/DerivedWidthModifier.class", "size": 9077, "crc": 1571002677}, {"key": "androidx/compose/foundation/layout/Direction.class", "name": "androidx/compose/foundation/layout/Direction.class", "size": 1958, "crc": -1592374988}, {"key": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/DoNothingNestedScrollConnection.class", "size": 903, "crc": -1652824538}, {"key": "androidx/compose/foundation/layout/ExcludeInsets.class", "name": "androidx/compose/foundation/layout/ExcludeInsets.class", "size": 3026, "crc": 1847976979}, {"key": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "name": "androidx/compose/foundation/layout/ExperimentalLayoutApi.class", "size": 823, "crc": 1257761694}, {"key": "androidx/compose/foundation/layout/FillCrossAxisSizeElement.class", "name": "androidx/compose/foundation/layout/FillCrossAxisSizeElement.class", "size": 3369, "crc": 368218120}, {"key": "androidx/compose/foundation/layout/FillCrossAxisSizeNode.class", "name": "androidx/compose/foundation/layout/FillCrossAxisSizeNode.class", "size": 2932, "crc": -1686126535}, {"key": "androidx/compose/foundation/layout/FillElement$Companion.class", "name": "androidx/compose/foundation/layout/FillElement$Companion.class", "size": 1824, "crc": -557670228}, {"key": "androidx/compose/foundation/layout/FillElement.class", "name": "androidx/compose/foundation/layout/FillElement.class", "size": 3834, "crc": 1920399821}, {"key": "androidx/compose/foundation/layout/FillNode.class", "name": "androidx/compose/foundation/layout/FillNode.class", "size": 6474, "crc": 643257686}, {"key": "androidx/compose/foundation/layout/FixedDpInsets.class", "name": "androidx/compose/foundation/layout/FixedDpInsets.class", "size": 4363, "crc": -217251045}, {"key": "androidx/compose/foundation/layout/FixedIntInsets.class", "name": "androidx/compose/foundation/layout/FixedIntInsets.class", "size": 2894, "crc": 1072115509}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3416, "crc": -533252790}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3564, "crc": 641274279}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3560, "crc": -1801381700}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow$Companion.class", "size": 10643, "crc": -1980295266}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflow.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflow.class", "size": 3843, "crc": 1105117386}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScope.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScope.class", "size": 1120, "crc": 1453630110}, {"key": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/FlowColumnOverflowScopeImpl.class", "size": 5629, "crc": 169441981}, {"key": "androidx/compose/foundation/layout/FlowColumnScope.class", "name": "androidx/compose/foundation/layout/FlowColumnScope.class", "size": 1589, "crc": -2042346634}, {"key": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowColumnScopeInstance.class", "size": 5043, "crc": -308947590}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapEllipsisInfo.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapEllipsisInfo.class", "size": 2818, "crc": 803921036}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapInfo.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks$WrapInfo.class", "size": 1464, "crc": -733261057}, {"key": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks.class", "name": "androidx/compose/foundation/layout/FlowLayoutBuildingBlocks.class", "size": 7081, "crc": -571410435}, {"key": "androidx/compose/foundation/layout/FlowLayoutData.class", "name": "androidx/compose/foundation/layout/FlowLayoutData.class", "size": 2427, "crc": -1325026764}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$list$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowColumn$list$1$1.class", "size": 3477, "crc": -1439378725}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$list$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$FlowRow$list$1$1.class", "size": 3445, "crc": 702690550}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$columnMeasurementHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$columnMeasurementHelper$1$1.class", "size": 2608, "crc": -270127767}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt$rowMeasurementHelper$1$1.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt$rowMeasurementHelper$1$1.class", "size": 2592, "crc": 793179386}, {"key": "androidx/compose/foundation/layout/FlowLayoutKt.class", "name": "androidx/compose/foundation/layout/FlowLayoutKt.class", "size": 74902, "crc": -630237890}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow$OverflowType.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow$OverflowType.class", "size": 2314, "crc": -1277428789}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow$WhenMappings.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow$WhenMappings.class", "size": 967, "crc": -1984487110}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflow.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflow.class", "size": 6840, "crc": -946195542}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowKt.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowKt.class", "size": 1388, "crc": -1263025071}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState$WhenMappings.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState$WhenMappings.class", "size": 1127, "crc": -1115950834}, {"key": "androidx/compose/foundation/layout/FlowLayoutOverflowState.class", "name": "androidx/compose/foundation/layout/FlowLayoutOverflowState.class", "size": 15646, "crc": 1998952176}, {"key": "androidx/compose/foundation/layout/FlowLineInfo.class", "name": "androidx/compose/foundation/layout/FlowLineInfo.class", "size": 3767, "crc": 519367868}, {"key": "androidx/compose/foundation/layout/FlowLineMeasurePolicy.class", "name": "androidx/compose/foundation/layout/FlowLineMeasurePolicy.class", "size": 8403, "crc": 510027794}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1$1.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy$measure$measurablesIterator$1$1.class", "size": 2894, "crc": -96948657}, {"key": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy.class", "name": "androidx/compose/foundation/layout/FlowMeasureLazyPolicy.class", "size": 18012, "crc": -1589550164}, {"key": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "name": "androidx/compose/foundation/layout/FlowMeasurePolicy.class", "size": 26681, "crc": 1046772941}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandIndicator$seeMoreGetter$1$1.class", "size": 3383, "crc": 1612131929}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$collapseGetter$1$1.class", "size": 3533, "crc": -1876524975}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion$expandOrCollapseIndicator$1$seeMoreGetter$1$1.class", "size": 3529, "crc": 1010029362}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow$Companion.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow$Companion.class", "size": 10473, "crc": 1403169527}, {"key": "androidx/compose/foundation/layout/FlowRowOverflow.class", "name": "androidx/compose/foundation/layout/FlowRowOverflow.class", "size": 3828, "crc": -833026344}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScope.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScope.class", "size": 1108, "crc": 1476363822}, {"key": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl.class", "name": "androidx/compose/foundation/layout/FlowRowOverflowScopeImpl.class", "size": 5819, "crc": -1154066389}, {"key": "androidx/compose/foundation/layout/FlowRowScope.class", "name": "androidx/compose/foundation/layout/FlowRowScope.class", "size": 1568, "crc": 1839107757}, {"key": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "name": "androidx/compose/foundation/layout/FlowRowScopeInstance.class", "size": 5157, "crc": -517805506}, {"key": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "name": "androidx/compose/foundation/layout/HorizontalAlignElement.class", "size": 3408, "crc": 1360234687}, {"key": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "name": "androidx/compose/foundation/layout/HorizontalAlignNode.class", "size": 3194, "crc": 1096402380}, {"key": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/InsetsConsumingModifier.class", "size": 5331, "crc": -2095868060}, {"key": "androidx/compose/foundation/layout/InsetsListener.class", "name": "androidx/compose/foundation/layout/InsetsListener.class", "size": 5803, "crc": 460268114}, {"key": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "name": "androidx/compose/foundation/layout/InsetsPaddingModifier.class", "size": 9408, "crc": 1410659886}, {"key": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "name": "androidx/compose/foundation/layout/InsetsPaddingValues.class", "size": 4618, "crc": -950438990}, {"key": "androidx/compose/foundation/layout/InsetsValues.class", "name": "androidx/compose/foundation/layout/InsetsValues.class", "size": 2343, "crc": -1886446930}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightElement.class", "size": 4260, "crc": -687407094}, {"key": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "name": "androidx/compose/foundation/layout/IntrinsicHeightNode.class", "size": 3505, "crc": 1287372582}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$height$$inlined$debugInspectorInfo$1.class", "size": 2853, "crc": 541429578}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredHeight$$inlined$debugInspectorInfo$1.class", "size": 2895, "crc": 1573405845}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$requiredWidth$$inlined$debugInspectorInfo$1.class", "size": 2890, "crc": -1172159607}, {"key": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/IntrinsicKt$width$$inlined$debugInspectorInfo$1.class", "size": 2848, "crc": -1847003648}, {"key": "androidx/compose/foundation/layout/IntrinsicKt.class", "name": "androidx/compose/foundation/layout/IntrinsicKt.class", "size": 3888, "crc": -233790942}, {"key": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "name": "androidx/compose/foundation/layout/IntrinsicMeasureBlocks.class", "size": 16653, "crc": -476989277}, {"key": "androidx/compose/foundation/layout/IntrinsicSize.class", "name": "androidx/compose/foundation/layout/IntrinsicSize.class", "size": 1921, "crc": -1362361417}, {"key": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "name": "androidx/compose/foundation/layout/IntrinsicSizeModifier.class", "size": 5091, "crc": -1432046184}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthElement.class", "size": 4250, "crc": -370593719}, {"key": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "name": "androidx/compose/foundation/layout/IntrinsicWidthNode.class", "size": 3488, "crc": 1994794632}, {"key": "androidx/compose/foundation/layout/LayoutOrientation.class", "name": "androidx/compose/foundation/layout/LayoutOrientation.class", "size": 1965, "crc": -1222651367}, {"key": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "name": "androidx/compose/foundation/layout/LayoutScopeMarker.class", "size": 620, "crc": 1332095256}, {"key": "androidx/compose/foundation/layout/LayoutWeightElement.class", "name": "androidx/compose/foundation/layout/LayoutWeightElement.class", "size": 3660, "crc": 1436429247}, {"key": "androidx/compose/foundation/layout/LayoutWeightNode.class", "name": "androidx/compose/foundation/layout/LayoutWeightNode.class", "size": 2746, "crc": 756873930}, {"key": "androidx/compose/foundation/layout/LazyImpl$Companion.class", "name": "androidx/compose/foundation/layout/LazyImpl$Companion.class", "size": 905, "crc": -1868951617}, {"key": "androidx/compose/foundation/layout/LazyImpl.class", "name": "androidx/compose/foundation/layout/LazyImpl.class", "size": 2952, "crc": -875012540}, {"key": "androidx/compose/foundation/layout/LimitInsets.class", "name": "androidx/compose/foundation/layout/LimitInsets.class", "size": 4503, "crc": 634193165}, {"key": "androidx/compose/foundation/layout/MutableWindowInsets.class", "name": "androidx/compose/foundation/layout/MutableWindowInsets.class", "size": 4210, "crc": 2126746328}, {"key": "androidx/compose/foundation/layout/OffsetElement.class", "name": "androidx/compose/foundation/layout/OffsetElement.class", "size": 4970, "crc": 848140184}, {"key": "androidx/compose/foundation/layout/OffsetKt.class", "name": "androidx/compose/foundation/layout/OffsetKt.class", "size": 5668, "crc": 1301892481}, {"key": "androidx/compose/foundation/layout/OffsetNode.class", "name": "androidx/compose/foundation/layout/OffsetNode.class", "size": 5118, "crc": 516094196}, {"key": "androidx/compose/foundation/layout/OffsetPxElement.class", "name": "androidx/compose/foundation/layout/OffsetPxElement.class", "size": 4790, "crc": 848150800}, {"key": "androidx/compose/foundation/layout/OffsetPxNode.class", "name": "androidx/compose/foundation/layout/OffsetPxNode.class", "size": 5574, "crc": 108349971}, {"key": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "name": "androidx/compose/foundation/layout/OrientationIndependentConstraints.class", "size": 6926, "crc": -842543826}, {"key": "androidx/compose/foundation/layout/PaddingElement.class", "name": "androidx/compose/foundation/layout/PaddingElement.class", "size": 7966, "crc": 1989161850}, {"key": "androidx/compose/foundation/layout/PaddingKt.class", "name": "androidx/compose/foundation/layout/PaddingKt.class", "size": 9900, "crc": 1030291}, {"key": "androidx/compose/foundation/layout/PaddingNode.class", "name": "androidx/compose/foundation/layout/PaddingNode.class", "size": 6638, "crc": 1466098470}, {"key": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "name": "androidx/compose/foundation/layout/PaddingValues$Absolute.class", "size": 5781, "crc": -109345113}, {"key": "androidx/compose/foundation/layout/PaddingValues$Companion.class", "name": "androidx/compose/foundation/layout/PaddingValues$Companion.class", "size": 1404, "crc": -2135410580}, {"key": "androidx/compose/foundation/layout/PaddingValues.class", "name": "androidx/compose/foundation/layout/PaddingValues.class", "size": 1508, "crc": -196844513}, {"key": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesConsumingModifier.class", "size": 2344, "crc": 1419383839}, {"key": "androidx/compose/foundation/layout/PaddingValuesElement.class", "name": "androidx/compose/foundation/layout/PaddingValuesElement.class", "size": 3949, "crc": 225417445}, {"key": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "name": "androidx/compose/foundation/layout/PaddingValuesImpl.class", "size": 6121, "crc": -150821988}, {"key": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "name": "androidx/compose/foundation/layout/PaddingValuesInsets.class", "size": 4378, "crc": -973759821}, {"key": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "name": "androidx/compose/foundation/layout/PaddingValuesModifier.class", "size": 6558, "crc": 1907221551}, {"key": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierElement.class", "name": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierElement.class", "size": 2733, "crc": -1020780557}, {"key": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode.class", "name": "androidx/compose/foundation/layout/RecalculateWindowInsetsModifierNode.class", "size": 13854, "crc": 528332217}, {"key": "androidx/compose/foundation/layout/RowColumnImplKt.class", "name": "androidx/compose/foundation/layout/RowColumnImplKt.class", "size": 8195, "crc": 1368163341}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicy.class", "size": 2421, "crc": -185588796}, {"key": "androidx/compose/foundation/layout/RowColumnMeasurePolicyKt.class", "name": "androidx/compose/foundation/layout/RowColumnMeasurePolicyKt.class", "size": 12501, "crc": 1446542444}, {"key": "androidx/compose/foundation/layout/RowColumnParentData.class", "name": "androidx/compose/foundation/layout/RowColumnParentData.class", "size": 5461, "crc": 52300167}, {"key": "androidx/compose/foundation/layout/RowKt.class", "name": "androidx/compose/foundation/layout/RowKt.class", "size": 11263, "crc": -1142116033}, {"key": "androidx/compose/foundation/layout/RowMeasurePolicy.class", "name": "androidx/compose/foundation/layout/RowMeasurePolicy.class", "size": 13266, "crc": 109930704}, {"key": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "name": "androidx/compose/foundation/layout/RowScope$DefaultImpls.class", "size": 588, "crc": 941840807}, {"key": "androidx/compose/foundation/layout/RowScope.class", "name": "androidx/compose/foundation/layout/RowScope.class", "size": 2753, "crc": 254138446}, {"key": "androidx/compose/foundation/layout/RowScopeInstance.class", "name": "androidx/compose/foundation/layout/RowScopeInstance.class", "size": 4867, "crc": 1362305955}, {"key": "androidx/compose/foundation/layout/RulerAlignmentKt.class", "name": "androidx/compose/foundation/layout/RulerAlignmentKt.class", "size": 9514, "crc": -898230298}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineBlockNode.class", "size": 3495, "crc": -1014074307}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode$WithAlignmentLineNode.class", "size": 3096, "crc": 1940747577}, {"key": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "name": "androidx/compose/foundation/layout/SiblingsAlignedNode.class", "size": 1983, "crc": 319308615}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$BottomSideCalculator$1.class", "size": 4281, "crc": -982661050}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$LeftSideCalculator$1.class", "size": 4265, "crc": -1123476328}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$RightSideCalculator$1.class", "size": 4272, "crc": 806659732}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion$TopSideCalculator$1.class", "size": 4262, "crc": 1775508301}, {"key": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "name": "androidx/compose/foundation/layout/SideCalculator$Companion.class", "size": 3478, "crc": 1412309533}, {"key": "androidx/compose/foundation/layout/SideCalculator.class", "name": "androidx/compose/foundation/layout/SideCalculator.class", "size": 2016, "crc": 1305748503}, {"key": "androidx/compose/foundation/layout/SizeElement.class", "name": "androidx/compose/foundation/layout/SizeElement.class", "size": 4797, "crc": 1495140799}, {"key": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$height-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2618, "crc": -501916769}, {"key": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$heightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2862, "crc": 1322475767}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeight-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2668, "crc": -1016786106}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredHeightIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2911, "crc": -1579150198}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2654, "crc": -1456892541}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSize-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2897, "crc": -1132752005}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredSizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3082, "crc": -545364550}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidth-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2661, "crc": -1231819787}, {"key": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$requiredWidthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2905, "crc": 1847562847}, {"key": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2606, "crc": 1136207923}, {"key": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$size-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2848, "crc": 359581783}, {"key": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$sizeIn-qDBjuR0$$inlined$debugInspectorInfo$1.class", "size": 3033, "crc": -245569168}, {"key": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$width-3ABfNKs$$inlined$debugInspectorInfo$1.class", "size": 2611, "crc": -1328390776}, {"key": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/SizeKt$widthIn-VpY3zN4$$inlined$debugInspectorInfo$1.class", "size": 2856, "crc": -2108996658}, {"key": "androidx/compose/foundation/layout/SizeKt.class", "name": "androidx/compose/foundation/layout/SizeKt.class", "size": 17897, "crc": -1905496546}, {"key": "androidx/compose/foundation/layout/SizeNode.class", "name": "androidx/compose/foundation/layout/SizeNode.class", "size": 12064, "crc": 1764756466}, {"key": "androidx/compose/foundation/layout/SpacerKt.class", "name": "androidx/compose/foundation/layout/SpacerKt.class", "size": 5914, "crc": -2001687305}, {"key": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "name": "androidx/compose/foundation/layout/SpacerMeasurePolicy.class", "size": 3159, "crc": 1281601145}, {"key": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "name": "androidx/compose/foundation/layout/SplineBasedFloatDecayAnimationSpec.class", "size": 3352, "crc": 1040577073}, {"key": "androidx/compose/foundation/layout/UnionInsets.class", "name": "androidx/compose/foundation/layout/UnionInsets.class", "size": 2988, "crc": 492247524}, {"key": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "name": "androidx/compose/foundation/layout/UnionInsetsConsumingModifier.class", "size": 2142, "crc": -944941756}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsElement.class", "size": 4048, "crc": 649946106}, {"key": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "name": "androidx/compose/foundation/layout/UnspecifiedConstraintsNode.class", "size": 9078, "crc": 1598609264}, {"key": "androidx/compose/foundation/layout/ValueInsets.class", "name": "androidx/compose/foundation/layout/ValueInsets.class", "size": 5066, "crc": -2018578698}, {"key": "androidx/compose/foundation/layout/VerticalAlignElement.class", "name": "androidx/compose/foundation/layout/VerticalAlignElement.class", "size": 3380, "crc": 1820881322}, {"key": "androidx/compose/foundation/layout/VerticalAlignNode.class", "name": "androidx/compose/foundation/layout/VerticalAlignNode.class", "size": 3168, "crc": 616791466}, {"key": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsets$Companion.class", "size": 754, "crc": -1435159330}, {"key": "androidx/compose/foundation/layout/WindowInsets.class", "name": "androidx/compose/foundation/layout/WindowInsets.class", "size": 1361, "crc": -1829161745}, {"key": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "name": "androidx/compose/foundation/layout/WindowInsetsAnimationCancelledException.class", "size": 1160, "crc": -121220960}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$$inlined$debugInspectorInfo$1.class", "size": 2625, "crc": -2046939069}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$imeNestedScroll$2.class", "size": 3782, "crc": -1419801339}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection_VRgvIgI$lambda$4$lambda$3$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt$rememberWindowInsetsConnection_VRgvIgI$lambda$4$lambda$3$$inlined$onDispose$1.class", "size": 2493, "crc": -71973334}, {"key": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsConnection_androidKt.class", "size": 11311, "crc": 186878483}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$lambda$2$lambda$1$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion$current$lambda$2$lambda$1$$inlined$onDispose$1.class", "size": 2346, "crc": 1168618275}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder$Companion.class", "size": 10301, "crc": -2064896074}, {"key": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "name": "androidx/compose/foundation/layout/WindowInsetsHolder.class", "size": 12676, "crc": -1881282137}, {"key": "androidx/compose/foundation/layout/WindowInsetsKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsKt.class", "size": 7445, "crc": -668755738}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$1.class", "size": 2223, "crc": -517975611}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2$1.class", "size": 6537, "crc": 1563948642}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$2.class", "size": 5565, "crc": -1366853436}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3$1.class", "size": 5723, "crc": 218895158}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection$fling$3.class", "size": 4505, "crc": -944839794}, {"key": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "name": "androidx/compose/foundation/layout/WindowInsetsNestedScrollConnection.class", "size": 20229, "crc": 1654227109}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$1.class", "size": 2981, "crc": -1087174503}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$$inlined$debugInspectorInfo$2.class", "size": 3000, "crc": 542394445}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$2.class", "size": 4390, "crc": 787620830}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$consumeWindowInsets$4.class", "size": 4403, "crc": 728342736}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$$inlined$debugInspectorInfo$1.class", "size": 2980, "crc": -2143777784}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$onConsumedWindowInsetsChanged$2.class", "size": 4623, "crc": -817124011}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$$inlined$debugInspectorInfo$1.class", "size": 2981, "crc": -50367596}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt$windowInsetsPadding$2.class", "size": 4379, "crc": -1407001105}, {"key": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPaddingKt.class", "size": 7052, "crc": -1410787428}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$debugInspectorInfo$1.class", "size": 2614, "crc": -325292037}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$captionBarPadding$$inlined$windowInsetsPadding$1.class", "size": 5625, "crc": -1408233348}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$debugInspectorInfo$1.class", "size": 2628, "crc": -1301816816}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$displayCutoutPadding$$inlined$windowInsetsPadding$1.class", "size": 5642, "crc": 1167966648}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$debugInspectorInfo$1.class", "size": 2579, "crc": 1309614059}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$imePadding$$inlined$windowInsetsPadding$1.class", "size": 5583, "crc": 314708894}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2679, "crc": -1521385626}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$mandatorySystemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 5703, "crc": 618937123}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2634, "crc": -2045431289}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$navigationBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 5649, "crc": 1916625120}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$debugInspectorInfo$1.class", "size": 2616, "crc": 1180356675}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeContentPadding$$inlined$windowInsetsPadding$1.class", "size": 5565, "crc": 235365160}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$debugInspectorInfo$1.class", "size": 2616, "crc": 1740512928}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeDrawingPadding$$inlined$windowInsetsPadding$1.class", "size": 5565, "crc": 1210511267}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2621, "crc": -831732813}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$safeGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 5571, "crc": 1999858118}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2614, "crc": 1181841460}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$statusBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 5625, "crc": 171281430}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$debugInspectorInfo$1.class", "size": 2611, "crc": 1971907935}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemBarsPadding$$inlined$windowInsetsPadding$1.class", "size": 5622, "crc": -1655111087}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$debugInspectorInfo$1.class", "size": 2634, "crc": 1330284540}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$systemGesturesPadding$$inlined$windowInsetsPadding$1.class", "size": 5649, "crc": -202148424}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$debugInspectorInfo$1.class", "size": 2609, "crc": -1723736069}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$waterfallPadding$$inlined$windowInsetsPadding$1.class", "size": 5611, "crc": -423975166}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt$windowInsetsPadding$1.class", "size": 5500, "crc": 807068759}, {"key": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsPadding_androidKt.class", "size": 10588, "crc": -124496177}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides$Companion.class", "size": 2922, "crc": 2022855106}, {"key": "androidx/compose/foundation/layout/WindowInsetsSides.class", "name": "androidx/compose/foundation/layout/WindowInsetsSides.class", "size": 5070, "crc": -1697937407}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsBottomHeight$$inlined$debugInspectorInfo$1.class", "size": 3005, "crc": -2137137584}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsEndWidth$$inlined$debugInspectorInfo$1.class", "size": 2979, "crc": 2125427317}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsStartWidth$$inlined$debugInspectorInfo$1.class", "size": 2991, "crc": -442528865}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt$windowInsetsTopHeight$$inlined$debugInspectorInfo$1.class", "size": 2987, "crc": -1031366323}, {"key": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "name": "androidx/compose/foundation/layout/WindowInsetsSizeKt.class", "size": 7332, "crc": 1436181960}, {"key": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "name": "androidx/compose/foundation/layout/WindowInsets_androidKt.class", "size": 19064, "crc": -1256242641}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineBlockElement.class", "size": 3822, "crc": -2011521218}, {"key": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "name": "androidx/compose/foundation/layout/WithAlignmentLineElement.class", "size": 3547, "crc": 2138864692}, {"key": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "name": "androidx/compose/foundation/layout/WrapContentElement$Companion.class", "size": 6568, "crc": -1981243463}, {"key": "androidx/compose/foundation/layout/WrapContentElement.class", "name": "androidx/compose/foundation/layout/WrapContentElement.class", "size": 5151, "crc": 921956620}, {"key": "androidx/compose/foundation/layout/WrapContentNode.class", "name": "androidx/compose/foundation/layout/WrapContentNode.class", "size": 7855, "crc": 1039649755}, {"key": "androidx/compose/foundation/layout/internal/InlineClassHelperKt.class", "name": "androidx/compose/foundation/layout/internal/InlineClassHelperKt.class", "size": 3198, "crc": 222503257}, {"key": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/layout/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 461, "crc": 881186553}, {"key": "META-INF/androidx.compose.foundation_foundation-layout.version", "name": "META-INF/androidx.compose.foundation_foundation-layout.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/foundation-layout.kotlin_module", "name": "META-INF/foundation-layout.kotlin_module", "size": 565, "crc": -1680936563}]