{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeReleaseResources-40:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f608d021a4f1e83e8e85d7b0e5006a22\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,89", "endOffsets": "138,228"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8551,8639", "endColumns": "87,89", "endOffsets": "8634,8724"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19b642e7465edfc9a64434f74e28ca0a\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4657,4743,4850,4930,5013,5110,5213,5306,5404,5491,5599,5696,5798,5931,6011,6118", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4652,4738,4845,4925,5008,5105,5208,5301,5399,5486,5594,5691,5793,5926,6006,6113,6210"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1467,1583,1699,1826,1942,2040,2134,2245,2381,2500,2642,2727,2827,2922,3020,3136,3261,3366,3507,3647,3780,3960,4085,4205,4330,4452,4548,4646,4763,4893,4993,5095,5204,5346,5495,5604,5707,5784,5882,5980,6069,6155,6262,6342,6425,6522,6625,6718,6816,6903,7011,7108,7210,7343,7423,7530", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "1578,1694,1821,1937,2035,2129,2240,2376,2495,2637,2722,2822,2917,3015,3131,3256,3361,3502,3642,3775,3955,4080,4200,4325,4447,4543,4641,4758,4888,4988,5090,5199,5341,5490,5599,5702,5779,5877,5975,6064,6150,6257,6337,6420,6517,6620,6713,6811,6898,7006,7103,7205,7338,7418,7525,7622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\71219f197f28274676b3874ecf964996\\transformed\\core-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,8183", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,8279"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62f35d7bdb9f40920fe06c2287c5bbc3\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,989,1059,1139,1224,1297,1376,1446", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,984,1054,1134,1219,1292,1371,1441,1559"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,928,1015,1112,1213,1299,1376,7627,7719,7804,7875,7945,8025,8110,8284,8363,8433", "endColumns": "96,86,96,100,85,76,90,91,84,70,69,79,84,72,78,69,117", "endOffsets": "923,1010,1107,1208,1294,1371,1462,7714,7799,7870,7940,8020,8105,8178,8358,8428,8546"}}]}]}