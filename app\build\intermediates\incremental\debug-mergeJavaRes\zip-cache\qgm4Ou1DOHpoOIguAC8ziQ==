[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "META-INF/annotation.kotlin_module", "name": "META-INF/annotation.kotlin_module", "size": 24, "crc": 1613429616}, {"key": "androidx/annotation/AnimRes.class", "name": "androidx/annotation/AnimRes.class", "size": 998, "crc": 1805675076}, {"key": "androidx/annotation/AnimatorRes.class", "name": "androidx/annotation/AnimatorRes.class", "size": 1010, "crc": -21670366}, {"key": "androidx/annotation/AnyRes.class", "name": "androidx/annotation/AnyRes.class", "size": 995, "crc": -1558869655}, {"key": "androidx/annotation/AnyThread.class", "name": "androidx/annotation/AnyThread.class", "size": 1047, "crc": -292959457}, {"key": "androidx/annotation/ArrayRes.class", "name": "androidx/annotation/ArrayRes.class", "size": 1001, "crc": -1364959277}, {"key": "androidx/annotation/AttrRes.class", "name": "androidx/annotation/AttrRes.class", "size": 998, "crc": 336978531}, {"key": "androidx/annotation/BinderThread.class", "name": "androidx/annotation/BinderThread.class", "size": 1056, "crc": -1438044114}, {"key": "androidx/annotation/BoolRes.class", "name": "androidx/annotation/BoolRes.class", "size": 998, "crc": 1086348174}, {"key": "androidx/annotation/CallSuper.class", "name": "androidx/annotation/CallSuper.class", "size": 919, "crc": -1796011780}, {"key": "androidx/annotation/CheckResult.class", "name": "androidx/annotation/CheckResult.class", "size": 1039, "crc": 1042113953}, {"key": "androidx/annotation/ChecksSdkIntAtLeast.class", "name": "androidx/annotation/ChecksSdkIntAtLeast.class", "size": 1337, "crc": 1242654976}, {"key": "androidx/annotation/ColorInt.class", "name": "androidx/annotation/ColorInt.class", "size": 1001, "crc": 799609820}, {"key": "androidx/annotation/ColorLong.class", "name": "androidx/annotation/ColorLong.class", "size": 996, "crc": -965017695}, {"key": "androidx/annotation/ColorRes.class", "name": "androidx/annotation/ColorRes.class", "size": 1001, "crc": 1387302471}, {"key": "androidx/annotation/ContentView.class", "name": "androidx/annotation/ContentView.class", "size": 790, "crc": 1840561130}, {"key": "androidx/annotation/DeprecatedSinceApi.class", "name": "androidx/annotation/DeprecatedSinceApi.class", "size": 1210, "crc": -197722093}, {"key": "androidx/annotation/DimenRes.class", "name": "androidx/annotation/DimenRes.class", "size": 1001, "crc": 1200789212}, {"key": "androidx/annotation/Dimension$Companion.class", "name": "androidx/annotation/Dimension$Companion.class", "size": 850, "crc": -597474804}, {"key": "androidx/annotation/Dimension.class", "name": "androidx/annotation/Dimension.class", "size": 1551, "crc": 1192463037}, {"key": "androidx/annotation/Discouraged.class", "name": "androidx/annotation/Discouraged.class", "size": 1071, "crc": -1502727256}, {"key": "androidx/annotation/DisplayContext.class", "name": "androidx/annotation/DisplayContext.class", "size": 973, "crc": -1030231984}, {"key": "androidx/annotation/DoNotInline.class", "name": "androidx/annotation/DoNotInline.class", "size": 842, "crc": -784511386}, {"key": "androidx/annotation/DrawableRes.class", "name": "androidx/annotation/DrawableRes.class", "size": 1010, "crc": 1762719637}, {"key": "androidx/annotation/EmptySuper.class", "name": "androidx/annotation/EmptySuper.class", "size": 876, "crc": -1971548453}, {"key": "androidx/annotation/FloatRange.class", "name": "androidx/annotation/FloatRange.class", "size": 1365, "crc": 624310496}, {"key": "androidx/annotation/FontRes.class", "name": "androidx/annotation/FontRes.class", "size": 998, "crc": -641216705}, {"key": "androidx/annotation/FractionRes.class", "name": "androidx/annotation/FractionRes.class", "size": 1010, "crc": 1499853784}, {"key": "androidx/annotation/GravityInt.class", "name": "androidx/annotation/GravityInt.class", "size": 1007, "crc": -1848783872}, {"key": "androidx/annotation/GuardedBy.class", "name": "androidx/annotation/GuardedBy.class", "size": 927, "crc": 1952837113}, {"key": "androidx/annotation/HalfFloat.class", "name": "androidx/annotation/HalfFloat.class", "size": 996, "crc": -1673008440}, {"key": "androidx/annotation/IdRes.class", "name": "androidx/annotation/IdRes.class", "size": 992, "crc": 1613555030}, {"key": "androidx/annotation/InspectableProperty$EnumEntry.class", "name": "androidx/annotation/InspectableProperty$EnumEntry.class", "size": 1098, "crc": 1662655193}, {"key": "androidx/annotation/InspectableProperty$FlagEntry.class", "name": "androidx/annotation/InspectableProperty$FlagEntry.class", "size": 1182, "crc": -428253226}, {"key": "androidx/annotation/InspectableProperty$ValueType.class", "name": "androidx/annotation/InspectableProperty$ValueType.class", "size": 1854, "crc": 1544860775}, {"key": "androidx/annotation/InspectableProperty.class", "name": "androidx/annotation/InspectableProperty.class", "size": 2118, "crc": 1292687511}, {"key": "androidx/annotation/IntDef.class", "name": "androidx/annotation/IntDef.class", "size": 1018, "crc": -2084495046}, {"key": "androidx/annotation/IntRange.class", "name": "androidx/annotation/IntRange.class", "size": 1213, "crc": -1057240409}, {"key": "androidx/annotation/IntegerRes.class", "name": "androidx/annotation/IntegerRes.class", "size": 1007, "crc": 1443210290}, {"key": "androidx/annotation/InterpolatorRes.class", "name": "androidx/annotation/InterpolatorRes.class", "size": 1022, "crc": 1441158230}, {"key": "androidx/annotation/Keep.class", "name": "androidx/annotation/Keep.class", "size": 959, "crc": -614514485}, {"key": "androidx/annotation/LayoutRes.class", "name": "androidx/annotation/LayoutRes.class", "size": 1004, "crc": -1845378728}, {"key": "androidx/annotation/LongDef.class", "name": "androidx/annotation/LongDef.class", "size": 1021, "crc": -1882350390}, {"key": "androidx/annotation/MainThread.class", "name": "androidx/annotation/MainThread.class", "size": 1050, "crc": 850348188}, {"key": "androidx/annotation/MenuRes.class", "name": "androidx/annotation/MenuRes.class", "size": 998, "crc": 1561717034}, {"key": "androidx/annotation/NavigationRes.class", "name": "androidx/annotation/NavigationRes.class", "size": 1016, "crc": 264753535}, {"key": "androidx/annotation/NonNull.class", "name": "androidx/annotation/NonNull.class", "size": 1072, "crc": 1890549879}, {"key": "androidx/annotation/NonUiContext.class", "name": "androidx/annotation/NonUiContext.class", "size": 967, "crc": -287470527}, {"key": "androidx/annotation/Nullable.class", "name": "androidx/annotation/Nullable.class", "size": 1075, "crc": 1363795669}, {"key": "androidx/annotation/OpenForTesting.class", "name": "androidx/annotation/OpenForTesting.class", "size": 951, "crc": -1077337476}, {"key": "androidx/annotation/PluralsRes.class", "name": "androidx/annotation/PluralsRes.class", "size": 1007, "crc": -16773091}, {"key": "androidx/annotation/Px.class", "name": "androidx/annotation/Px.class", "size": 1071, "crc": 181286199}, {"key": "androidx/annotation/RawRes.class", "name": "androidx/annotation/RawRes.class", "size": 995, "crc": -734924532}, {"key": "androidx/annotation/RequiresApi.class", "name": "androidx/annotation/RequiresApi.class", "size": 1176, "crc": 702703466}, {"key": "androidx/annotation/RequiresExtension$Container.class", "name": "androidx/annotation/RequiresExtension$Container.class", "size": 1055, "crc": -1982377015}, {"key": "androidx/annotation/RequiresExtension.class", "name": "androidx/annotation/RequiresExtension.class", "size": 1402, "crc": 1188583388}, {"key": "androidx/annotation/RequiresFeature.class", "name": "androidx/annotation/RequiresFeature.class", "size": 1091, "crc": -586912630}, {"key": "androidx/annotation/RequiresPermission$Read.class", "name": "androidx/annotation/RequiresPermission$Read.class", "size": 1092, "crc": -606956352}, {"key": "androidx/annotation/RequiresPermission$Write.class", "name": "androidx/annotation/RequiresPermission$Write.class", "size": 1095, "crc": 2005504804}, {"key": "androidx/annotation/RequiresPermission.class", "name": "androidx/annotation/RequiresPermission.class", "size": 1597, "crc": -40562329}, {"key": "androidx/annotation/RestrictTo$Scope.class", "name": "androidx/annotation/RestrictTo$Scope.class", "size": 1818, "crc": 170557603}, {"key": "androidx/annotation/RestrictTo.class", "name": "androidx/annotation/RestrictTo.class", "size": 1308, "crc": -1930201174}, {"key": "androidx/annotation/ReturnThis.class", "name": "androidx/annotation/ReturnThis.class", "size": 893, "crc": -1928044719}, {"key": "androidx/annotation/Size.class", "name": "androidx/annotation/Size.class", "size": 1326, "crc": 302039339}, {"key": "androidx/annotation/StringDef.class", "name": "androidx/annotation/StringDef.class", "size": 1005, "crc": -1165630304}, {"key": "androidx/annotation/StringRes.class", "name": "androidx/annotation/StringRes.class", "size": 1004, "crc": 284093704}, {"key": "androidx/annotation/StyleRes.class", "name": "androidx/annotation/StyleRes.class", "size": 1001, "crc": 1701184168}, {"key": "androidx/annotation/StyleableRes.class", "name": "androidx/annotation/StyleableRes.class", "size": 1013, "crc": 1642461597}, {"key": "androidx/annotation/TransitionRes.class", "name": "androidx/annotation/TransitionRes.class", "size": 981, "crc": -1441671359}, {"key": "androidx/annotation/UiContext.class", "name": "androidx/annotation/UiContext.class", "size": 958, "crc": 1604163313}, {"key": "androidx/annotation/UiThread.class", "name": "androidx/annotation/UiThread.class", "size": 1044, "crc": -490984382}, {"key": "androidx/annotation/VisibleForTesting$Companion.class", "name": "androidx/annotation/VisibleForTesting$Companion.class", "size": 967, "crc": 599658159}, {"key": "androidx/annotation/VisibleForTesting.class", "name": "androidx/annotation/VisibleForTesting.class", "size": 1196, "crc": -761906278}, {"key": "androidx/annotation/WorkerThread.class", "name": "androidx/annotation/WorkerThread.class", "size": 1056, "crc": 2033881368}, {"key": "androidx/annotation/XmlRes.class", "name": "androidx/annotation/XmlRes.class", "size": 995, "crc": 1134232451}, {"key": "META-INF/proguard/androidx-annotations.pro", "name": "META-INF/proguard/androidx-annotations.pro", "size": 433, "crc": 1618759247}]