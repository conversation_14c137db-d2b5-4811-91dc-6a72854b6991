[{"key": "androidx/compose/runtime/AbstractApplier.class", "name": "androidx/compose/runtime/AbstractApplier.class", "size": 4213, "crc": -2062088913}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt$DefaultMonotonicFrameClock$2.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt$DefaultMonotonicFrameClock$2.class", "size": 1753, "crc": 868946224}, {"key": "androidx/compose/runtime/ActualAndroid_androidKt.class", "name": "androidx/compose/runtime/ActualAndroid_androidKt.class", "size": 5140, "crc": -1571080916}, {"key": "androidx/compose/runtime/ActualJvm_jvmKt.class", "name": "androidx/compose/runtime/ActualJvm_jvmKt.class", "size": 4010, "crc": -1103166639}, {"key": "androidx/compose/runtime/Anchor.class", "name": "androidx/compose/runtime/Anchor.class", "size": 2201, "crc": -582422162}, {"key": "androidx/compose/runtime/Applier$DefaultImpls.class", "name": "androidx/compose/runtime/Applier$DefaultImpls.class", "size": 967, "crc": -252894882}, {"key": "androidx/compose/runtime/Applier.class", "name": "androidx/compose/runtime/Applier.class", "size": 1647, "crc": 1287229015}, {"key": "androidx/compose/runtime/AtomicInt.class", "name": "androidx/compose/runtime/AtomicInt.class", "size": 2103, "crc": 419453120}, {"key": "androidx/compose/runtime/BitwiseOperatorsKt.class", "name": "androidx/compose/runtime/BitwiseOperatorsKt.class", "size": 760, "crc": 466936685}, {"key": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "name": "androidx/compose/runtime/BroadcastFrameClock$FrameAwaiter.class", "size": 3242, "crc": 538881965}, {"key": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/runtime/BroadcastFrameClock$withFrameNanos$2$1.class", "size": 3796, "crc": 1698997535}, {"key": "androidx/compose/runtime/BroadcastFrameClock.class", "name": "androidx/compose/runtime/BroadcastFrameClock.class", "size": 12516, "crc": 1898579351}, {"key": "androidx/compose/runtime/Composable.class", "name": "androidx/compose/runtime/Composable.class", "size": 969, "crc": 1238586231}, {"key": "androidx/compose/runtime/ComposableInferredTarget.class", "name": "androidx/compose/runtime/ComposableInferredTarget.class", "size": 1045, "crc": 667039861}, {"key": "androidx/compose/runtime/ComposableOpenTarget.class", "name": "androidx/compose/runtime/ComposableOpenTarget.class", "size": 980, "crc": -1602434713}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-1$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-1$1.class", "size": 2207, "crc": 950875106}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-2$1.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt$lambda-2$1.class", "size": 2207, "crc": -1608502329}, {"key": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "name": "androidx/compose/runtime/ComposableSingletons$CompositionKt.class", "size": 1838, "crc": -1189782696}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda-1$1.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt$lambda-1$1.class", "size": 2200, "crc": -697141777}, {"key": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "name": "androidx/compose/runtime/ComposableSingletons$RecomposerKt.class", "size": 1493, "crc": -1815456581}, {"key": "androidx/compose/runtime/ComposableTarget.class", "name": "androidx/compose/runtime/ComposableTarget.class", "size": 1009, "crc": 757223313}, {"key": "androidx/compose/runtime/ComposableTargetMarker.class", "name": "androidx/compose/runtime/ComposableTargetMarker.class", "size": 979, "crc": -758062925}, {"key": "androidx/compose/runtime/ComposablesKt$ComposeNode$1.class", "name": "androidx/compose/runtime/ComposablesKt$ComposeNode$1.class", "size": 1773, "crc": -75754762}, {"key": "androidx/compose/runtime/ComposablesKt$ReusableComposeNode$1.class", "name": "androidx/compose/runtime/ComposablesKt$ReusableComposeNode$1.class", "size": 1813, "crc": -1843667375}, {"key": "androidx/compose/runtime/ComposablesKt.class", "name": "androidx/compose/runtime/ComposablesKt.class", "size": 18397, "crc": -1261855982}, {"key": "androidx/compose/runtime/ComposeCompilerApi.class", "name": "androidx/compose/runtime/ComposeCompilerApi.class", "size": 798, "crc": -1144562057}, {"key": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "name": "androidx/compose/runtime/ComposeNodeLifecycleCallback.class", "size": 556, "crc": -1240631825}, {"key": "androidx/compose/runtime/ComposeRuntimeError.class", "name": "androidx/compose/runtime/ComposeRuntimeError.class", "size": 1155, "crc": 34079145}, {"key": "androidx/compose/runtime/ComposeVersion.class", "name": "androidx/compose/runtime/ComposeVersion.class", "size": 882, "crc": 1117552146}, {"key": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "name": "androidx/compose/runtime/Composer$Companion$Empty$1.class", "size": 912, "crc": 650151059}, {"key": "androidx/compose/runtime/Composer$Companion.class", "name": "androidx/compose/runtime/Composer$Companion.class", "size": 1495, "crc": 1611939652}, {"key": "androidx/compose/runtime/Composer.class", "name": "androidx/compose/runtime/Composer.class", "size": 9612, "crc": 25004424}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextHolder.class", "size": 1751, "crc": 614853536}, {"key": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "name": "androidx/compose/runtime/ComposerImpl$CompositionContextImpl.class", "size": 13315, "crc": -1158092511}, {"key": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "name": "androidx/compose/runtime/ComposerImpl$derivedStateObserver$1.class", "size": 1907, "crc": -1622696139}, {"key": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$1$1.class", "name": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$1$1.class", "size": 6058, "crc": 200936308}, {"key": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$2$1$1$1$1.class", "name": "androidx/compose/runtime/ComposerImpl$insertMovableContentGuarded$1$1$2$1$1$1$1.class", "size": 1947, "crc": 1379581419}, {"key": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "name": "androidx/compose/runtime/ComposerImpl$invokeMovableContentLambda$1.class", "size": 2942, "crc": -1329987571}, {"key": "androidx/compose/runtime/ComposerImpl.class", "name": "androidx/compose/runtime/ComposerImpl.class", "size": 89976, "crc": 1917551022}, {"key": "androidx/compose/runtime/ComposerKt.class", "name": "androidx/compose/runtime/ComposerKt.class", "size": 26136, "crc": -969422843}, {"key": "androidx/compose/runtime/Composition.class", "name": "androidx/compose/runtime/Composition.class", "size": 991, "crc": -1493225744}, {"key": "androidx/compose/runtime/CompositionContext.class", "name": "androidx/compose/runtime/CompositionContext.class", "size": 5574, "crc": -839218501}, {"key": "androidx/compose/runtime/CompositionContextKt.class", "name": "androidx/compose/runtime/CompositionContextKt.class", "size": 1044, "crc": 1676118894}, {"key": "androidx/compose/runtime/CompositionImpl$RememberEventDispatcher.class", "name": "androidx/compose/runtime/CompositionImpl$RememberEventDispatcher.class", "size": 8444, "crc": -1528813782}, {"key": "androidx/compose/runtime/CompositionImpl$observe$2.class", "name": "androidx/compose/runtime/CompositionImpl$observe$2.class", "size": 3088, "crc": -1665459125}, {"key": "androidx/compose/runtime/CompositionImpl.class", "name": "androidx/compose/runtime/CompositionImpl.class", "size": 77312, "crc": -1814370975}, {"key": "androidx/compose/runtime/CompositionKt$CompositionImplServiceKey$1.class", "name": "androidx/compose/runtime/CompositionKt$CompositionImplServiceKey$1.class", "size": 966, "crc": 533379872}, {"key": "androidx/compose/runtime/CompositionKt.class", "name": "androidx/compose/runtime/CompositionKt.class", "size": 8925, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocal.class", "name": "androidx/compose/runtime/CompositionLocal.class", "size": 3254, "crc": **********}, {"key": "androidx/compose/runtime/CompositionLocalContext.class", "name": "androidx/compose/runtime/CompositionLocalContext.class", "size": 1199, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$1.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$1.class", "size": 2360, "crc": -210389942}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$2.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$2.class", "size": 2255, "crc": 998739605}, {"key": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$4.class", "name": "androidx/compose/runtime/CompositionLocalKt$CompositionLocalProvider$4.class", "size": 2240, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalKt.class", "name": "androidx/compose/runtime/CompositionLocalKt.class", "size": 9766, "crc": 37589361}, {"key": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "name": "androidx/compose/runtime/CompositionLocalMap$Companion.class", "size": 1289, "crc": -609937834}, {"key": "androidx/compose/runtime/CompositionLocalMap.class", "name": "androidx/compose/runtime/CompositionLocalMap.class", "size": 1183, "crc": -**********}, {"key": "androidx/compose/runtime/CompositionLocalMapKt.class", "name": "androidx/compose/runtime/CompositionLocalMapKt.class", "size": 6362, "crc": -302533250}, {"key": "androidx/compose/runtime/CompositionObserverHolder.class", "name": "androidx/compose/runtime/CompositionObserverHolder.class", "size": 2030, "crc": 847812585}, {"key": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "name": "androidx/compose/runtime/CompositionScopedCoroutineScopeCanceller.class", "size": 1878, "crc": 2120706359}, {"key": "androidx/compose/runtime/CompositionServiceKey.class", "name": "androidx/compose/runtime/CompositionServiceKey.class", "size": 488, "crc": -496338052}, {"key": "androidx/compose/runtime/CompositionServices.class", "name": "androidx/compose/runtime/CompositionServices.class", "size": 905, "crc": 460722935}, {"key": "androidx/compose/runtime/CompositionTracer.class", "name": "androidx/compose/runtime/CompositionTracer.class", "size": 863, "crc": -824089828}, {"key": "androidx/compose/runtime/ControlledComposition.class", "name": "androidx/compose/runtime/ControlledComposition.class", "size": 3102, "crc": -641543961}, {"key": "androidx/compose/runtime/DataIterator.class", "name": "androidx/compose/runtime/DataIterator.class", "size": 3448, "crc": 156687941}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$choreographer$1.class", "size": 3336, "crc": 240263098}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$1.class", "size": 1829, "crc": -2109445933}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock$withFrameNanos$2$callback$1.class", "size": 3021, "crc": 2035933371}, {"key": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "name": "androidx/compose/runtime/DefaultChoreographerFrameClock.class", "size": 7014, "crc": 540874096}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord$Companion.class", "size": 1200, "crc": -1521236967}, {"key": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "name": "androidx/compose/runtime/DerivedSnapshotState$ResultRecord.class", "size": 12891, "crc": 1113073190}, {"key": "androidx/compose/runtime/DerivedSnapshotState$currentRecord$result$1$1$result$1.class", "name": "androidx/compose/runtime/DerivedSnapshotState$currentRecord$result$1$1$result$1.class", "size": 3131, "crc": -1653165992}, {"key": "androidx/compose/runtime/DerivedSnapshotState.class", "name": "androidx/compose/runtime/DerivedSnapshotState.class", "size": 18159, "crc": 1193150121}, {"key": "androidx/compose/runtime/DerivedState$Record.class", "name": "androidx/compose/runtime/DerivedState$Record.class", "size": 1079, "crc": -682744111}, {"key": "androidx/compose/runtime/DerivedState.class", "name": "androidx/compose/runtime/DerivedState.class", "size": 1319, "crc": -1235172193}, {"key": "androidx/compose/runtime/DerivedStateObserver.class", "name": "androidx/compose/runtime/DerivedStateObserver.class", "size": 793, "crc": -1880963137}, {"key": "androidx/compose/runtime/DisallowComposableCalls.class", "name": "androidx/compose/runtime/DisallowComposableCalls.class", "size": 928, "crc": 757151631}, {"key": "androidx/compose/runtime/DisposableEffectImpl.class", "name": "androidx/compose/runtime/DisposableEffectImpl.class", "size": 2205, "crc": 482750958}, {"key": "androidx/compose/runtime/DisposableEffectResult.class", "name": "androidx/compose/runtime/DisposableEffectResult.class", "size": 453, "crc": -1829342907}, {"key": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "name": "androidx/compose/runtime/DisposableEffectScope$onDispose$1.class", "size": 1696, "crc": 651467134}, {"key": "androidx/compose/runtime/DisposableEffectScope.class", "name": "androidx/compose/runtime/DisposableEffectScope.class", "size": 1527, "crc": -682631942}, {"key": "androidx/compose/runtime/DontMemoize.class", "name": "androidx/compose/runtime/DontMemoize.class", "size": 754, "crc": -893898696}, {"key": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/DoubleState$DefaultImpls.class", "size": 1033, "crc": -1157124520}, {"key": "androidx/compose/runtime/DoubleState.class", "name": "androidx/compose/runtime/DoubleState.class", "size": 1494, "crc": 1682501546}, {"key": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "name": "androidx/compose/runtime/DynamicProvidableCompositionLocal.class", "size": 2548, "crc": -1074869131}, {"key": "androidx/compose/runtime/EffectsKt$LaunchedEffect$1.class", "name": "androidx/compose/runtime/EffectsKt$LaunchedEffect$1.class", "size": 1990, "crc": -711302787}, {"key": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "name": "androidx/compose/runtime/EffectsKt$rememberCoroutineScope$1.class", "size": 1713, "crc": -229799156}, {"key": "androidx/compose/runtime/EffectsKt.class", "name": "androidx/compose/runtime/EffectsKt.class", "size": 20238, "crc": 608010889}, {"key": "androidx/compose/runtime/ExpectKt$ThreadLocal$1.class", "name": "androidx/compose/runtime/ExpectKt$ThreadLocal$1.class", "size": 1101, "crc": 105420100}, {"key": "androidx/compose/runtime/ExpectKt.class", "name": "androidx/compose/runtime/ExpectKt.class", "size": 1340, "crc": 972662272}, {"key": "androidx/compose/runtime/ExperimentalComposeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeApi.class", "size": 1223, "crc": 656735070}, {"key": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "name": "androidx/compose/runtime/ExperimentalComposeRuntimeApi.class", "size": 1247, "crc": -2074507129}, {"key": "androidx/compose/runtime/ExplicitGroupsComposable.class", "name": "androidx/compose/runtime/ExplicitGroupsComposable.class", "size": 865, "crc": -252095505}, {"key": "androidx/compose/runtime/FloatState$DefaultImpls.class", "name": "androidx/compose/runtime/FloatState$DefaultImpls.class", "size": 1023, "crc": -2066494080}, {"key": "androidx/compose/runtime/FloatState.class", "name": "androidx/compose/runtime/FloatState.class", "size": 1483, "crc": 1991470395}, {"key": "androidx/compose/runtime/GroupInfo.class", "name": "androidx/compose/runtime/GroupInfo.class", "size": 1349, "crc": 1322359628}, {"key": "androidx/compose/runtime/GroupIterator.class", "name": "androidx/compose/runtime/GroupIterator.class", "size": 2830, "crc": 1070422165}, {"key": "androidx/compose/runtime/GroupKind$Companion.class", "name": "androidx/compose/runtime/GroupKind$Companion.class", "size": 1372, "crc": 1749233824}, {"key": "androidx/compose/runtime/GroupKind.class", "name": "androidx/compose/runtime/GroupKind.class", "size": 3210, "crc": -1499621130}, {"key": "androidx/compose/runtime/GroupSourceInformation.class", "name": "androidx/compose/runtime/GroupSourceInformation.class", "size": 7901, "crc": 468668809}, {"key": "androidx/compose/runtime/HotReloader$Companion.class", "name": "androidx/compose/runtime/HotReloader$Companion.class", "size": 2558, "crc": -279726275}, {"key": "androidx/compose/runtime/HotReloader.class", "name": "androidx/compose/runtime/HotReloader.class", "size": 883, "crc": -397004248}, {"key": "androidx/compose/runtime/HotReloaderKt.class", "name": "androidx/compose/runtime/HotReloaderKt.class", "size": 3594, "crc": -1594220297}, {"key": "androidx/compose/runtime/Immutable.class", "name": "androidx/compose/runtime/Immutable.class", "size": 959, "crc": -1811685330}, {"key": "androidx/compose/runtime/IntStack.class", "name": "androidx/compose/runtime/IntStack.class", "size": 2315, "crc": -1199474951}, {"key": "androidx/compose/runtime/IntState$DefaultImpls.class", "name": "androidx/compose/runtime/IntState$DefaultImpls.class", "size": 1015, "crc": 33389163}, {"key": "androidx/compose/runtime/IntState.class", "name": "androidx/compose/runtime/IntState.class", "size": 1477, "crc": 1267204505}, {"key": "androidx/compose/runtime/InternalComposeApi.class", "name": "androidx/compose/runtime/InternalComposeApi.class", "size": 1189, "crc": -938449997}, {"key": "androidx/compose/runtime/InternalComposeTracingApi.class", "name": "androidx/compose/runtime/InternalComposeTracingApi.class", "size": 955, "crc": -61782969}, {"key": "androidx/compose/runtime/Invalidation.class", "name": "androidx/compose/runtime/Invalidation.class", "size": 2426, "crc": -425051269}, {"key": "androidx/compose/runtime/InvalidationResult.class", "name": "androidx/compose/runtime/InvalidationResult.class", "size": 1540, "crc": -859519987}, {"key": "androidx/compose/runtime/JoinedKey.class", "name": "androidx/compose/runtime/JoinedKey.class", "size": 3031, "crc": -881333858}, {"key": "androidx/compose/runtime/KeyInfo.class", "name": "androidx/compose/runtime/KeyInfo.class", "size": 1702, "crc": -2100250827}, {"key": "androidx/compose/runtime/Latch$await$2$2.class", "name": "androidx/compose/runtime/Latch$await$2$2.class", "size": 2962, "crc": -1523857224}, {"key": "androidx/compose/runtime/Latch.class", "name": "androidx/compose/runtime/Latch.class", "size": 6613, "crc": 1584156839}, {"key": "androidx/compose/runtime/LaunchedEffectImpl.class", "name": "androidx/compose/runtime/LaunchedEffectImpl.class", "size": 3227, "crc": -726645999}, {"key": "androidx/compose/runtime/LazyValueHolder.class", "name": "androidx/compose/runtime/LazyValueHolder.class", "size": 1698, "crc": 219076917}, {"key": "androidx/compose/runtime/LeftCompositionCancellationException.class", "name": "androidx/compose/runtime/LeftCompositionCancellationException.class", "size": 1850, "crc": -1068847271}, {"key": "androidx/compose/runtime/LongState$DefaultImpls.class", "name": "androidx/compose/runtime/LongState$DefaultImpls.class", "size": 1013, "crc": -584322641}, {"key": "androidx/compose/runtime/LongState.class", "name": "androidx/compose/runtime/LongState.class", "size": 1472, "crc": 2070576272}, {"key": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "name": "androidx/compose/runtime/MonotonicFrameClock$DefaultImpls.class", "size": 3397, "crc": 1235685219}, {"key": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "name": "androidx/compose/runtime/MonotonicFrameClock$Key.class", "size": 1051, "crc": -1986932794}, {"key": "androidx/compose/runtime/MonotonicFrameClock.class", "name": "androidx/compose/runtime/MonotonicFrameClock.class", "size": 2118, "crc": 2086433001}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt$withFrameMillis$2.class", "size": 2140, "crc": -1274335771}, {"key": "androidx/compose/runtime/MonotonicFrameClockKt.class", "name": "androidx/compose/runtime/MonotonicFrameClockKt.class", "size": 4524, "crc": 1179733883}, {"key": "androidx/compose/runtime/MovableContent.class", "name": "androidx/compose/runtime/MovableContent.class", "size": 1792, "crc": 782316045}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$1.class", "size": 2561, "crc": 326832563}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$2.class", "size": 2847, "crc": 143930908}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$3.class", "size": 3143, "crc": -1777349958}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$4.class", "size": 3362, "crc": 461261697}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$5.class", "size": 3587, "crc": -197922178}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$1.class", "size": 2874, "crc": 808470396}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$2.class", "size": 3310, "crc": 4831884}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$3.class", "size": 3212, "crc": 981520258}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentOf$movableContent$4.class", "size": 3323, "crc": -1431504101}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$1.class", "size": 2897, "crc": -582642093}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$2.class", "size": 3193, "crc": 1302032997}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$3.class", "size": 3411, "crc": 545409063}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$4.class", "size": 3636, "crc": 673569069}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$1.class", "size": 3015, "crc": -1602018793}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$2.class", "size": 3350, "crc": -2131155124}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$3.class", "size": 3255, "crc": -969122153}, {"key": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "name": "androidx/compose/runtime/MovableContentKt$movableContentWithReceiverOf$movableContent$4.class", "size": 3366, "crc": -461498619}, {"key": "androidx/compose/runtime/MovableContentKt.class", "name": "androidx/compose/runtime/MovableContentKt.class", "size": 8581, "crc": -1747431980}, {"key": "androidx/compose/runtime/MovableContentState.class", "name": "androidx/compose/runtime/MovableContentState.class", "size": 1212, "crc": -522487259}, {"key": "androidx/compose/runtime/MovableContentStateReference.class", "name": "androidx/compose/runtime/MovableContentStateReference.class", "size": 4730, "crc": 1129208435}, {"key": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableDoubleState$DefaultImpls.class", "size": 1296, "crc": -1067044414}, {"key": "androidx/compose/runtime/MutableDoubleState.class", "name": "androidx/compose/runtime/MutableDoubleState.class", "size": 2173, "crc": 1397055746}, {"key": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableFloatState$DefaultImpls.class", "size": 1285, "crc": 1690377782}, {"key": "androidx/compose/runtime/MutableFloatState.class", "name": "androidx/compose/runtime/MutableFloatState.class", "size": 2157, "crc": -1223683742}, {"key": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableIntState$DefaultImpls.class", "size": 1275, "crc": -172430677}, {"key": "androidx/compose/runtime/MutableIntState.class", "name": "androidx/compose/runtime/MutableIntState.class", "size": 2141, "crc": -303146133}, {"key": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "name": "androidx/compose/runtime/MutableLongState$DefaultImpls.class", "size": 1274, "crc": 551835724}, {"key": "androidx/compose/runtime/MutableLongState.class", "name": "androidx/compose/runtime/MutableLongState.class", "size": 2141, "crc": 890262603}, {"key": "androidx/compose/runtime/MutableState.class", "name": "androidx/compose/runtime/MutableState.class", "size": 1124, "crc": 1883532302}, {"key": "androidx/compose/runtime/NeverEqualPolicy.class", "name": "androidx/compose/runtime/NeverEqualPolicy.class", "size": 1398, "crc": -633449108}, {"key": "androidx/compose/runtime/NoLiveLiterals.class", "name": "androidx/compose/runtime/NoLiveLiterals.class", "size": 865, "crc": -1997887906}, {"key": "androidx/compose/runtime/NonRestartableComposable.class", "name": "androidx/compose/runtime/NonRestartableComposable.class", "size": 865, "crc": 524130731}, {"key": "androidx/compose/runtime/NonSkippableComposable.class", "name": "androidx/compose/runtime/NonSkippableComposable.class", "size": 859, "crc": -318800196}, {"key": "androidx/compose/runtime/OffsetApplier.class", "name": "androidx/compose/runtime/OffsetApplier.class", "size": 4280, "crc": -1158571605}, {"key": "androidx/compose/runtime/OpaqueKey.class", "name": "androidx/compose/runtime/OpaqueKey.class", "size": 2287, "crc": -380305036}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion$CREATOR$1.class", "size": 2023, "crc": -1002366417}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState$Companion.class", "size": 1162, "crc": 231433740}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableDoubleState.class", "size": 2282, "crc": 766277014}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion$CREATOR$1.class", "size": 2014, "crc": -529458880}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState$Companion.class", "size": 1157, "crc": -1971679964}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableFloatState.class", "size": 2271, "crc": 308437552}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion$CREATOR$1.class", "size": 1996, "crc": 1245760980}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState$Companion.class", "size": 1147, "crc": -828468465}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableIntState.class", "size": 2229, "crc": 169229765}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion$CREATOR$1.class", "size": 2005, "crc": 1946890528}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState$Companion.class", "size": 1152, "crc": -1223816640}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableLongState.class", "size": 2260, "crc": -1075727579}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion$CREATOR$1.class", "size": 4148, "crc": -487554878}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState$Companion.class", "size": 1292, "crc": 1511734866}, {"key": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "name": "androidx/compose/runtime/ParcelableSnapshotMutableState.class", "size": 3546, "crc": -1196789295}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock$withFrameNanos$1.class", "size": 1977, "crc": 1745961350}, {"key": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "name": "androidx/compose/runtime/PausableMonotonicFrameClock.class", "size": 5448, "crc": -1905374807}, {"key": "androidx/compose/runtime/Pending$keyMap$2.class", "name": "androidx/compose/runtime/Pending$keyMap$2.class", "size": 2299, "crc": -654728326}, {"key": "androidx/compose/runtime/Pending.class", "name": "androidx/compose/runtime/Pending.class", "size": 9411, "crc": -293233338}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap$Builder.class", "size": 1438, "crc": -10538314}, {"key": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "name": "androidx/compose/runtime/PersistentCompositionLocalMap.class", "size": 1974, "crc": -595759301}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt.class", "size": 1641, "crc": 244497337}, {"key": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "name": "androidx/compose/runtime/PrimitiveSnapshotStateKt__SnapshotFloatStateKt.class", "size": 2164, "crc": -505770364}, {"key": "androidx/compose/runtime/PrioritySet.class", "name": "androidx/compose/runtime/PrioritySet.class", "size": 4741, "crc": -301509998}, {"key": "androidx/compose/runtime/ProduceFrameSignal.class", "name": "androidx/compose/runtime/ProduceFrameSignal.class", "size": 5859, "crc": -1911517716}, {"key": "androidx/compose/runtime/ProduceStateScope.class", "name": "androidx/compose/runtime/ProduceStateScope.class", "size": 1244, "crc": -450016262}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl$awaitDispose$1.class", "size": 1864, "crc": 279138295}, {"key": "androidx/compose/runtime/ProduceStateScopeImpl.class", "name": "androidx/compose/runtime/ProduceStateScopeImpl.class", "size": 5622, "crc": -2043181431}, {"key": "androidx/compose/runtime/ProvidableCompositionLocal.class", "name": "androidx/compose/runtime/ProvidableCompositionLocal.class", "size": 1890, "crc": -1928459488}, {"key": "androidx/compose/runtime/ProvidedValue.class", "name": "androidx/compose/runtime/ProvidedValue.class", "size": 1874, "crc": -442136201}, {"key": "androidx/compose/runtime/ReadOnlyComposable.class", "name": "androidx/compose/runtime/ReadOnlyComposable.class", "size": 938, "crc": -2111181013}, {"key": "androidx/compose/runtime/RecomposeScope.class", "name": "androidx/compose/runtime/RecomposeScope.class", "size": 451, "crc": 189986874}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$Companion.class", "size": 5009, "crc": -129455688}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$end$1$2.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$end$1$2.class", "size": 6566, "crc": -1391124906}, {"key": "androidx/compose/runtime/RecomposeScopeImpl$observe$2.class", "name": "androidx/compose/runtime/RecomposeScopeImpl$observe$2.class", "size": 2988, "crc": 257311819}, {"key": "androidx/compose/runtime/RecomposeScopeImpl.class", "name": "androidx/compose/runtime/RecomposeScopeImpl.class", "size": 19122, "crc": -1623946150}, {"key": "androidx/compose/runtime/RecomposeScopeImplKt.class", "name": "androidx/compose/runtime/RecomposeScopeImplKt.class", "size": 1593, "crc": 939409895}, {"key": "androidx/compose/runtime/RecomposeScopeOwner.class", "name": "androidx/compose/runtime/RecomposeScopeOwner.class", "size": 1116, "crc": 1856969226}, {"key": "androidx/compose/runtime/Recomposer$Companion.class", "name": "androidx/compose/runtime/Recomposer$Companion.class", "size": 10923, "crc": 1613802641}, {"key": "androidx/compose/runtime/Recomposer$HotReloadable.class", "name": "androidx/compose/runtime/Recomposer$HotReloadable.class", "size": 2060, "crc": 325027292}, {"key": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "name": "androidx/compose/runtime/Recomposer$RecomposerErrorState.class", "size": 1378, "crc": -288962911}, {"key": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "name": "androidx/compose/runtime/Recomposer$RecomposerInfoImpl.class", "size": 9167, "crc": 1006153252}, {"key": "androidx/compose/runtime/Recomposer$State.class", "name": "androidx/compose/runtime/Recomposer$State.class", "size": 1749, "crc": 550768993}, {"key": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "name": "androidx/compose/runtime/Recomposer$awaitIdle$2.class", "size": 3426, "crc": -830797726}, {"key": "androidx/compose/runtime/Recomposer$broadcastFrameClock$1.class", "name": "androidx/compose/runtime/Recomposer$broadcastFrameClock$1.class", "size": 3807, "crc": 164545141}, {"key": "androidx/compose/runtime/Recomposer$effectJob$1$1$1$1.class", "name": "androidx/compose/runtime/Recomposer$effectJob$1$1$1$1.class", "size": 3882, "crc": 642274646}, {"key": "androidx/compose/runtime/Recomposer$effectJob$1$1.class", "name": "androidx/compose/runtime/Recomposer$effectJob$1$1.class", "size": 4769, "crc": 282513207}, {"key": "androidx/compose/runtime/Recomposer$join$2.class", "name": "androidx/compose/runtime/Recomposer$join$2.class", "size": 3335, "crc": 1026426525}, {"key": "androidx/compose/runtime/Recomposer$performRecompose$1$1.class", "name": "androidx/compose/runtime/Recomposer$performRecompose$1$1.class", "size": 3319, "crc": -1250862821}, {"key": "androidx/compose/runtime/Recomposer$readObserverOf$1.class", "name": "androidx/compose/runtime/Recomposer$readObserverOf$1.class", "size": 1567, "crc": 2095173429}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2$3.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2$3.class", "size": 4069, "crc": -37836820}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2$unregisterApplyObserver$1.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2$unregisterApplyObserver$1.class", "size": 7255, "crc": -718621380}, {"key": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "name": "androidx/compose/runtime/Recomposer$recompositionRunner$2.class", "size": 9441, "crc": -2016799461}, {"key": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runFrameLoop$1.class", "size": 2165, "crc": -1622762442}, {"key": "androidx/compose/runtime/Recomposer$runFrameLoop$2.class", "name": "androidx/compose/runtime/Recomposer$runFrameLoop$2.class", "size": 8808, "crc": -1025843621}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2$1.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2$1.class", "size": 14178, "crc": 1322699412}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeAndApplyChanges$2.class", "size": 9643, "crc": -1511073528}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$2$2.class", "size": 6274, "crc": 1632983978}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2$frameLoop$1.class", "size": 4146, "crc": 1605589667}, {"key": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "name": "androidx/compose/runtime/Recomposer$runRecomposeConcurrentlyAndApplyChanges$2.class", "size": 12662, "crc": 12818299}, {"key": "androidx/compose/runtime/Recomposer$writeObserverOf$1.class", "name": "androidx/compose/runtime/Recomposer$writeObserverOf$1.class", "size": 2160, "crc": 1111057308}, {"key": "androidx/compose/runtime/Recomposer.class", "name": "androidx/compose/runtime/Recomposer.class", "size": 63715, "crc": -1675415870}, {"key": "androidx/compose/runtime/RecomposerErrorInfo.class", "name": "androidx/compose/runtime/RecomposerErrorInfo.class", "size": 781, "crc": 824971784}, {"key": "androidx/compose/runtime/RecomposerInfo.class", "name": "androidx/compose/runtime/RecomposerInfo.class", "size": 1076, "crc": 1345748937}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2$1.class", "size": 3519, "crc": -1259936557}, {"key": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "name": "androidx/compose/runtime/RecomposerKt$withRunningRecomposer$2.class", "size": 4974, "crc": 1650848960}, {"key": "androidx/compose/runtime/RecomposerKt.class", "name": "androidx/compose/runtime/RecomposerKt.class", "size": 4782, "crc": 699818102}, {"key": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "name": "androidx/compose/runtime/ReferentialEqualityPolicy.class", "size": 1461, "crc": 1435341165}, {"key": "androidx/compose/runtime/RememberManager.class", "name": "androidx/compose/runtime/RememberManager.class", "size": 1166, "crc": -1753012157}, {"key": "androidx/compose/runtime/RememberObserver.class", "name": "androidx/compose/runtime/RememberObserver.class", "size": 526, "crc": 928327424}, {"key": "androidx/compose/runtime/RememberObserverHolder.class", "name": "androidx/compose/runtime/RememberObserverHolder.class", "size": 1274, "crc": -486134344}, {"key": "androidx/compose/runtime/ReusableComposition.class", "name": "androidx/compose/runtime/ReusableComposition.class", "size": 1019, "crc": 149615240}, {"key": "androidx/compose/runtime/ReusableRememberObserver.class", "name": "androidx/compose/runtime/ReusableRememberObserver.class", "size": 502, "crc": -413308931}, {"key": "androidx/compose/runtime/ScopeUpdateScope.class", "name": "androidx/compose/runtime/ScopeUpdateScope.class", "size": 869, "crc": 478004799}, {"key": "androidx/compose/runtime/SdkStubsFallbackFrameClock$withFrameNanos$2.class", "name": "androidx/compose/runtime/SdkStubsFallbackFrameClock$withFrameNanos$2.class", "size": 3848, "crc": -1699212202}, {"key": "androidx/compose/runtime/SdkStubsFallbackFrameClock.class", "name": "androidx/compose/runtime/SdkStubsFallbackFrameClock.class", "size": 4211, "crc": -1607941878}, {"key": "androidx/compose/runtime/SkippableUpdater.class", "name": "androidx/compose/runtime/SkippableUpdater.class", "size": 3961, "crc": -944471724}, {"key": "androidx/compose/runtime/SlotReader.class", "name": "androidx/compose/runtime/SlotReader.class", "size": 15927, "crc": 485535535}, {"key": "androidx/compose/runtime/SlotTable.class", "name": "androidx/compose/runtime/SlotTable.class", "size": 32305, "crc": -413071646}, {"key": "androidx/compose/runtime/SlotTableGroup.class", "name": "androidx/compose/runtime/SlotTableGroup.class", "size": 7964, "crc": 2144990647}, {"key": "androidx/compose/runtime/SlotTableKt.class", "name": "androidx/compose/runtime/SlotTableKt.class", "size": 17788, "crc": 1064793711}, {"key": "androidx/compose/runtime/SlotWriter$Companion.class", "name": "androidx/compose/runtime/SlotWriter$Companion.class", "size": 11869, "crc": 1513899906}, {"key": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "name": "androidx/compose/runtime/SlotWriter$groupSlots$1.class", "size": 2137, "crc": 2123584067}, {"key": "androidx/compose/runtime/SlotWriter.class", "name": "androidx/compose/runtime/SlotWriter.class", "size": 58536, "crc": -1450109789}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt.class", "size": 1645, "crc": 1577886748}, {"key": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "name": "androidx/compose/runtime/SnapshotDoubleStateKt__SnapshotDoubleStateKt.class", "size": 2173, "crc": 1442149848}, {"key": "androidx/compose/runtime/SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt.class", "size": 1609, "crc": 480514241}, {"key": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "name": "androidx/compose/runtime/SnapshotIntStateKt__SnapshotIntStateKt.class", "size": 2118, "crc": -1816137362}, {"key": "androidx/compose/runtime/SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt.class", "size": 1621, "crc": 1222965091}, {"key": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "name": "androidx/compose/runtime/SnapshotLongStateKt__SnapshotLongStateKt.class", "size": 2139, "crc": -1494893383}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$DoubleStateStateRecord.class", "size": 1796, "crc": 1337362134}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl$component2$1.class", "size": 1499, "crc": 1694338483}, {"key": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableDoubleStateImpl.class", "size": 9170, "crc": 829899365}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$FloatStateStateRecord.class", "size": 1787, "crc": -695946249}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl$component2$1.class", "size": 1490, "crc": 2057060753}, {"key": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableFloatStateImpl.class", "size": 9127, "crc": 968550102}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl$IntStateStateRecord.class", "size": 1769, "crc": -122483080}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl$component2$1.class", "size": 1469, "crc": 521629655}, {"key": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableIntStateImpl.class", "size": 8766, "crc": -648158685}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl$LongStateStateRecord.class", "size": 1778, "crc": -373267988}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl$component2$1.class", "size": 1481, "crc": -204102629}, {"key": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableLongStateImpl.class", "size": 8303, "crc": -1449414356}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl$StateStateRecord.class", "size": 2037, "crc": -2146361985}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl$component2$1.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl$component2$1.class", "size": 1561, "crc": 1747411334}, {"key": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "name": "androidx/compose/runtime/SnapshotMutableStateImpl.class", "size": 9513, "crc": 1632674086}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy$DefaultImpls.class", "size": 1143, "crc": 1886515562}, {"key": "androidx/compose/runtime/SnapshotMutationPolicy.class", "name": "androidx/compose/runtime/SnapshotMutationPolicy.class", "size": 1462, "crc": 986125056}, {"key": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "name": "androidx/compose/runtime/SnapshotStateExtensionsKt.class", "size": 2671, "crc": -901759764}, {"key": "androidx/compose/runtime/SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt.class", "size": 11204, "crc": -2072084221}, {"key": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__DerivedStateKt.class", "size": 8030, "crc": 2039093170}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$1.class", "size": 4342, "crc": 1557463636}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$2.class", "size": 4360, "crc": -1729950004}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$3.class", "size": 4378, "crc": -1061453397}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$4.class", "size": 4396, "crc": 1265650569}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt$produceState$5.class", "size": 4361, "crc": 45758650}, {"key": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__ProduceStateKt.class", "size": 11393, "crc": 1281888780}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$1.class", "size": 1903, "crc": -1770427679}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$2$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$2$1.class", "size": 1996, "crc": 806085482}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$2.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1$2.class", "size": 4132, "crc": -240993622}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$collectAsState$1.class", "size": 5037, "crc": 579415563}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$readObserver$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$readObserver$1.class", "size": 3126, "crc": -337258903}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$unregisterApplyObserver$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1$unregisterApplyObserver$1.class", "size": 4634, "crc": -25088438}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt$snapshotFlow$1.class", "size": 9893, "crc": -398440847}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotFlowKt.class", "size": 7892, "crc": -1426513171}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotMutationPolicyKt.class", "size": 2137, "crc": -1658047304}, {"key": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "name": "androidx/compose/runtime/SnapshotStateKt__SnapshotStateKt.class", "size": 9413, "crc": -1405060174}, {"key": "androidx/compose/runtime/SnapshotThreadLocal.class", "name": "androidx/compose/runtime/SnapshotThreadLocal.class", "size": 3711, "crc": -2070861579}, {"key": "androidx/compose/runtime/SourceInformationGroupIterator.class", "name": "androidx/compose/runtime/SourceInformationGroupIterator.class", "size": 3921, "crc": -731366447}, {"key": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "name": "androidx/compose/runtime/SourceInformationSlotTableGroup.class", "size": 3857, "crc": -1164712752}, {"key": "androidx/compose/runtime/Stable.class", "name": "androidx/compose/runtime/Stable.class", "size": 1019, "crc": -797317208}, {"key": "androidx/compose/runtime/StableMarker.class", "name": "androidx/compose/runtime/StableMarker.class", "size": 931, "crc": 1906422324}, {"key": "androidx/compose/runtime/Stack.class", "name": "androidx/compose/runtime/Stack.class", "size": 2409, "crc": 1131432970}, {"key": "androidx/compose/runtime/State.class", "name": "androidx/compose/runtime/State.class", "size": 637, "crc": 245308741}, {"key": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "name": "androidx/compose/runtime/StaticProvidableCompositionLocal.class", "size": 2107, "crc": 1469488920}, {"key": "androidx/compose/runtime/StaticValueHolder.class", "name": "androidx/compose/runtime/StaticValueHolder.class", "size": 2705, "crc": -1529479637}, {"key": "androidx/compose/runtime/StructuralEqualityPolicy.class", "name": "androidx/compose/runtime/StructuralEqualityPolicy.class", "size": 1483, "crc": 2102532343}, {"key": "androidx/compose/runtime/ThreadLocal.class", "name": "androidx/compose/runtime/ThreadLocal.class", "size": 1878, "crc": -799730340}, {"key": "androidx/compose/runtime/Trace.class", "name": "androidx/compose/runtime/Trace.class", "size": 1343, "crc": 1794700392}, {"key": "androidx/compose/runtime/TraceKt.class", "name": "androidx/compose/runtime/TraceKt.class", "size": 1508, "crc": 1362521038}, {"key": "androidx/compose/runtime/UnboxedDoubleState.class", "name": "androidx/compose/runtime/UnboxedDoubleState.class", "size": 2167, "crc": 783037081}, {"key": "androidx/compose/runtime/UnboxedFloatState.class", "name": "androidx/compose/runtime/UnboxedFloatState.class", "size": 2156, "crc": 837039512}, {"key": "androidx/compose/runtime/UnboxedIntState.class", "name": "androidx/compose/runtime/UnboxedIntState.class", "size": 2144, "crc": -801134785}, {"key": "androidx/compose/runtime/UnboxedLongState.class", "name": "androidx/compose/runtime/UnboxedLongState.class", "size": 2145, "crc": -1230904531}, {"key": "androidx/compose/runtime/Updater$init$1.class", "name": "androidx/compose/runtime/Updater$init$1.class", "size": 1736, "crc": 1395960723}, {"key": "androidx/compose/runtime/Updater$reconcile$1.class", "name": "androidx/compose/runtime/Updater$reconcile$1.class", "size": 1751, "crc": 1278383569}, {"key": "androidx/compose/runtime/Updater.class", "name": "androidx/compose/runtime/Updater.class", "size": 6382, "crc": 514969655}, {"key": "androidx/compose/runtime/WeakReference.class", "name": "androidx/compose/runtime/WeakReference.class", "size": 1055, "crc": -2037166261}, {"key": "androidx/compose/runtime/changelist/ChangeList.class", "name": "androidx/compose/runtime/changelist/ChangeList.class", "size": 51473, "crc": 724912547}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter$Companion.class", "size": 956, "crc": -957541656}, {"key": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "name": "androidx/compose/runtime/changelist/ComposerChangeListWriter.class", "size": 18547, "crc": 1597051709}, {"key": "androidx/compose/runtime/changelist/FixupList.class", "name": "androidx/compose/runtime/changelist/FixupList.class", "size": 14099, "crc": 995783924}, {"key": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "name": "androidx/compose/runtime/changelist/Operation$AdvanceSlotsBy.class", "size": 3563, "crc": 998164591}, {"key": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "name": "androidx/compose/runtime/changelist/Operation$ApplyChangeList.class", "size": 4769, "crc": -1616478002}, {"key": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopyNodesToNewAnchorLocation.class", "size": 5316, "crc": 279993343}, {"key": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "name": "androidx/compose/runtime/changelist/Operation$CopySlotTableToAnchorLocation.class", "size": 6795, "crc": 106227130}, {"key": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$DeactivateCurrentGroup.class", "size": 2118, "crc": -1584555759}, {"key": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "name": "androidx/compose/runtime/changelist/Operation$DetermineMovableContentNodeIndex.class", "size": 4743, "crc": -1746832692}, {"key": "androidx/compose/runtime/changelist/Operation$Downs.class", "name": "androidx/compose/runtime/changelist/Operation$Downs.class", "size": 4019, "crc": 1497766859}, {"key": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "name": "androidx/compose/runtime/changelist/Operation$EndCompositionScope.class", "size": 4270, "crc": -1377342305}, {"key": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$EndCurrentGroup.class", "size": 2003, "crc": 2123277560}, {"key": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "name": "androidx/compose/runtime/changelist/Operation$EndMovableContentPlacement.class", "size": 2424, "crc": -1720546986}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureGroupStarted.class", "size": 3754, "crc": 2010121154}, {"key": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "name": "androidx/compose/runtime/changelist/Operation$EnsureRootGroupStarted.class", "size": 2030, "crc": -666912192}, {"key": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$InsertNodeFixup.class", "size": 5500, "crc": 94482530}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlots.class", "size": 4396, "crc": -655144352}, {"key": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "name": "androidx/compose/runtime/changelist/Operation$InsertSlotsWithFixups.class", "size": 5884, "crc": -1130093231}, {"key": "androidx/compose/runtime/changelist/Operation$IntParameter.class", "name": "androidx/compose/runtime/changelist/Operation$IntParameter.class", "size": 2280, "crc": 415032738}, {"key": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$MoveCurrentGroup.class", "size": 3569, "crc": -1390355667}, {"key": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$MoveNode.class", "size": 4209, "crc": 1020774757}, {"key": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "name": "androidx/compose/runtime/changelist/Operation$ObjectParameter.class", "size": 2412, "crc": -422186996}, {"key": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "name": "androidx/compose/runtime/changelist/Operation$PostInsertNodeFixup.class", "size": 4993, "crc": 265306288}, {"key": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "name": "androidx/compose/runtime/changelist/Operation$ReleaseMovableGroupAtCurrent.class", "size": 5130, "crc": -995026444}, {"key": "androidx/compose/runtime/changelist/Operation$Remember.class", "name": "androidx/compose/runtime/changelist/Operation$Remember.class", "size": 3708, "crc": 1055764484}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveCurrentGroup.class", "size": 2102, "crc": -1107027991}, {"key": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "name": "androidx/compose/runtime/changelist/Operation$RemoveNode.class", "size": 3910, "crc": 352498046}, {"key": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "name": "androidx/compose/runtime/changelist/Operation$ResetSlots.class", "size": 1978, "crc": -528523387}, {"key": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "name": "androidx/compose/runtime/changelist/Operation$SideEffect.class", "size": 3692, "crc": -849009637}, {"key": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "name": "androidx/compose/runtime/changelist/Operation$SkipToEndOfCurrentGroup.class", "size": 2026, "crc": 911688825}, {"key": "androidx/compose/runtime/changelist/Operation$TestOperation$1.class", "name": "androidx/compose/runtime/changelist/Operation$TestOperation$1.class", "size": 2274, "crc": -796359865}, {"key": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "name": "androidx/compose/runtime/changelist/Operation$TestOperation.class", "size": 6292, "crc": 1962481316}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateAuxData.class", "size": 3619, "crc": -1303803950}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateNode.class", "size": 4264, "crc": 1925443983}, {"key": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "name": "androidx/compose/runtime/changelist/Operation$UpdateValue.class", "size": 5018, "crc": 1027013618}, {"key": "androidx/compose/runtime/changelist/Operation$Ups.class", "name": "androidx/compose/runtime/changelist/Operation$Ups.class", "size": 3660, "crc": -1533357265}, {"key": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "name": "androidx/compose/runtime/changelist/Operation$UseCurrentNode.class", "size": 2309, "crc": -1632770386}, {"key": "androidx/compose/runtime/changelist/Operation.class", "name": "androidx/compose/runtime/changelist/Operation.class", "size": 9250, "crc": 984685071}, {"key": "androidx/compose/runtime/changelist/OperationArgContainer.class", "name": "androidx/compose/runtime/changelist/OperationArgContainer.class", "size": 1133, "crc": -1452498303}, {"key": "androidx/compose/runtime/changelist/OperationKt$releaseMovableGroupAtCurrent$movableContentRecomposeScopeOwner$1.class", "name": "androidx/compose/runtime/changelist/OperationKt$releaseMovableGroupAtCurrent$movableContentRecomposeScopeOwner$1.class", "size": 4287, "crc": 175933720}, {"key": "androidx/compose/runtime/changelist/OperationKt.class", "name": "androidx/compose/runtime/changelist/OperationKt.class", "size": 7748, "crc": 1533076447}, {"key": "androidx/compose/runtime/changelist/Operations$Companion.class", "name": "androidx/compose/runtime/changelist/Operations$Companion.class", "size": 936, "crc": -1530331674}, {"key": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "name": "androidx/compose/runtime/changelist/Operations$OpIterator.class", "size": 3033, "crc": 1422318079}, {"key": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "name": "androidx/compose/runtime/changelist/Operations$WriteScope.class", "size": 5689, "crc": 1922452327}, {"key": "androidx/compose/runtime/changelist/Operations$toCollectionString$1.class", "name": "androidx/compose/runtime/changelist/Operations$toCollectionString$1.class", "size": 1785, "crc": -1070988641}, {"key": "androidx/compose/runtime/changelist/Operations.class", "name": "androidx/compose/runtime/changelist/Operations.class", "size": 19776, "crc": 145512996}, {"key": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "name": "androidx/compose/runtime/changelist/OperationsDebugStringFormattable.class", "size": 1354, "crc": 240386371}, {"key": "androidx/compose/runtime/collection/ActualIntMap_androidKt.class", "name": "androidx/compose/runtime/collection/ActualIntMap_androidKt.class", "size": 452, "crc": 940011615}, {"key": "androidx/compose/runtime/collection/IdentityArrayIntMap.class", "name": "androidx/compose/runtime/collection/IdentityArrayIntMap.class", "size": 6533, "crc": -1414973262}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$entries$1$iterator$1$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$entries$1$iterator$1$1.class", "size": 2560, "crc": -29511598}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$entries$1$iterator$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$entries$1$iterator$1.class", "size": 4629, "crc": 1952262336}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$entries$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$entries$1.class", "size": 5802, "crc": -1108541334}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$keys$1$iterator$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$keys$1$iterator$1.class", "size": 4547, "crc": -823106761}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$keys$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$keys$1.class", "size": 4144, "crc": -1493836167}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$values$1$iterator$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$values$1$iterator$1.class", "size": 4352, "crc": 1505206372}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$values$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1$values$1.class", "size": 4467, "crc": -413975770}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap$asMap$1.class", "size": 5925, "crc": -1473473561}, {"key": "androidx/compose/runtime/collection/IdentityArrayMap.class", "name": "androidx/compose/runtime/collection/IdentityArrayMap.class", "size": 8823, "crc": 288859009}, {"key": "androidx/compose/runtime/collection/IdentityArraySet$iterator$1.class", "name": "androidx/compose/runtime/collection/IdentityArraySet$iterator$1.class", "size": 2342, "crc": -143415664}, {"key": "androidx/compose/runtime/collection/IdentityArraySet$toString$1.class", "name": "androidx/compose/runtime/collection/IdentityArraySet$toString$1.class", "size": 1524, "crc": 7770870}, {"key": "androidx/compose/runtime/collection/IdentityArraySet.class", "name": "androidx/compose/runtime/collection/IdentityArraySet.class", "size": 11920, "crc": -1027869895}, {"key": "androidx/compose/runtime/collection/IdentityArraySetKt.class", "name": "androidx/compose/runtime/collection/IdentityArraySetKt.class", "size": 3105, "crc": -1252364817}, {"key": "androidx/compose/runtime/collection/IntMap.class", "name": "androidx/compose/runtime/collection/IntMap.class", "size": 2639, "crc": -84108942}, {"key": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "name": "androidx/compose/runtime/collection/MutableVector$MutableVectorList.class", "size": 6883, "crc": 1185450767}, {"key": "androidx/compose/runtime/collection/MutableVector$SubList.class", "name": "androidx/compose/runtime/collection/MutableVector$SubList.class", "size": 8230, "crc": -282584805}, {"key": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "name": "androidx/compose/runtime/collection/MutableVector$VectorListIterator.class", "size": 2723, "crc": 2019867414}, {"key": "androidx/compose/runtime/collection/MutableVector.class", "name": "androidx/compose/runtime/collection/MutableVector.class", "size": 27069, "crc": -338738155}, {"key": "androidx/compose/runtime/collection/MutableVectorKt.class", "name": "androidx/compose/runtime/collection/MutableVectorKt.class", "size": 5133, "crc": -1612959218}, {"key": "androidx/compose/runtime/collection/ScopeMap.class", "name": "androidx/compose/runtime/collection/ScopeMap.class", "size": 13624, "crc": 749205976}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt.class", "size": 44671, "crc": 871634668}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableCollection.class", "size": 724, "crc": -1928243005}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList$SubList.class", "size": 2887, "crc": -2145684073}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableList.class", "size": 1844, "crc": -2034045203}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableMap.class", "size": 1799, "crc": 2050427298}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet.class", "size": 975, "crc": 782172065}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection$Builder.class", "size": 1289, "crc": -249050980}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentCollection.class", "size": 3016, "crc": -503479167}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList$Builder.class", "size": 1634, "crc": 911618663}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList.class", "size": 4131, "crc": -106511572}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder.class", "size": 1264, "crc": 1103560205}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap.class", "size": 2703, "crc": -317623062}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet$Builder.class", "size": 1623, "crc": -596983889}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet.class", "size": 3179, "crc": 732318457}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableCollectionAdapter.class", "size": 4299, "crc": -1386972210}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableListAdapter.class", "size": 6270, "crc": 1852452552}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableMapAdapter.class", "size": 7711, "crc": 1526954162}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/adapters/ImmutableSetAdapter.class", "size": 1721, "crc": 2273184}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractListIterator.class", "size": 2990, "crc": 1838372787}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$removeAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$removeAll$1.class", "size": 1902, "crc": 87550251}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$retainAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList$retainAll$1.class", "size": 1937, "crc": 1070919301}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList.class", "size": 8659, "crc": 978785767}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/BufferIterator.class", "size": 2135, "crc": 881186825}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/ObjectRef.class", "size": 1297, "crc": -1211415870}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVector.class", "size": 15853, "crc": -1956048225}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder$removeAll$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder$removeAll$1.class", "size": 1827, "crc": 1122785253}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorBuilder.class", "size": 29545, "crc": -220185909}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorIterator.class", "size": 3183, "crc": -1554422731}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/PersistentVectorMutableIterator.class", "size": 5890, "crc": -1961032283}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SingleElementListIterator.class", "size": 1905, "crc": -11087310}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion.class", "size": 1549, "crc": -2008563037}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector.class", "size": 12958, "crc": -1879553808}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/TrieIterator.class", "size": 4194, "crc": 1683868701}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt.class", "size": 2508, "crc": -617376571}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/AbstractMapBuilderEntries.class", "size": 2251, "crc": 2061539731}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry.class", "size": 3697, "crc": -979692211}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MutableMapEntry.class", "size": 3085, "crc": 421999246}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion.class", "size": 2323, "crc": 259626929}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap.class", "size": 13005, "crc": -378948757}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator.class", "size": 5682, "crc": 173452538}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder.class", "size": 11551, "crc": 1631507063}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderBaseIterator.class", "size": 7506, "crc": 2076585778}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntries.class", "size": 4976, "crc": 1577746967}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderEntriesIterator.class", "size": 4196, "crc": -580561192}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeys.class", "size": 3333, "crc": 1812292338}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderKeysIterator.class", "size": 2775, "crc": -276552489}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValues.class", "size": 3171, "crc": 22354424}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilderValuesIterator.class", "size": 2781, "crc": -591056866}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapContentIteratorsKt.class", "size": 515, "crc": -1996005144}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries.class", "size": 4646, "crc": 1811032799}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator.class", "size": 2792, "crc": 1181019558}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeys.class", "size": 3119, "crc": -168332770}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapKeysIterator.class", "size": 2650, "crc": -62409802}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValues.class", "size": 3167, "crc": 2108044832}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapValuesIterator.class", "size": 2656, "crc": -315869199}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion.class", "size": 1492, "crc": 440882718}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult.class", "size": 4371, "crc": 1914532034}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode.class", "size": 43722, "crc": -556305475}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator.class", "size": 4783, "crc": -57188897}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator.class", "size": 2244, "crc": 354892509}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKeysIterator.class", "size": 1749, "crc": 2059705273}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt.class", "size": 4364, "crc": -752521707}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeMutableEntriesIterator.class", "size": 3188, "crc": 777449415}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeValuesIterator.class", "size": 1752, "crc": 940979941}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet$Companion.class", "size": 1972, "crc": 1692139379}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSet.class", "size": 11252, "crc": 1657885459}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetBuilder.class", "size": 10465, "crc": 103244544}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetIterator.class", "size": 5603, "crc": 1764774349}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/PersistentHashSetMutableIterator.class", "size": 6258, "crc": 271923600}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode$Companion.class", "size": 1486, "crc": 629664787}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNode.class", "size": 38469, "crc": -953162557}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeIterator.class", "size": 3909, "crc": 1185127987}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt$filterTo$1.class", "size": 2777, "crc": -189832842}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableSet/TrieNodeKt.class", "size": 3942, "crc": -2000177510}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/LinkedValue.class", "size": 3575, "crc": 335419055}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/MutableMapEntry.class", "size": 3457, "crc": -1801938329}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap$Companion.class", "size": 2425, "crc": 719452645}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMap.class", "size": 14518, "crc": -945778448}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilder.class", "size": 9543, "crc": 7245491}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntries.class", "size": 5100, "crc": -652661666}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderEntriesIterator.class", "size": 4293, "crc": 722385342}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeys.class", "size": 3424, "crc": 1546463599}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderKeysIterator.class", "size": 3237, "crc": 1484205499}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderLinksIterator.class", "size": 6585, "crc": -1290283123}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValues.class", "size": 3262, "crc": -1128305648}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapBuilderValuesIterator.class", "size": 3323, "crc": -2144642758}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntries.class", "size": 4518, "crc": -1863002464}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapEntriesIterator.class", "size": 3944, "crc": -1443778485}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeys.class", "size": 2954, "crc": 812845929}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapKeysIterator.class", "size": 3355, "crc": 619265929}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapLinksIterator.class", "size": 4437, "crc": 2005812120}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValues.class", "size": 3002, "crc": 3421047}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedMap/PersistentOrderedMapValuesIterator.class", "size": 3402, "crc": 116485536}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links.class", "size": 2611, "crc": -1922564181}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion.class", "size": 2030, "crc": -1739252680}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet.class", "size": 13264, "crc": 542254362}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetBuilder.class", "size": 8267, "crc": 1754261541}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetIterator.class", "size": 4026, "crc": -200115814}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSetMutableIterator.class", "size": 4239, "crc": 1912694572}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt.class", "size": 548, "crc": 244407076}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter.class", "size": 2806, "crc": -1969764508}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain.class", "size": 887, "crc": 600339257}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ForEachOneBitKt.class", "size": 1387, "crc": 981379766}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation.class", "size": 3528, "crc": -286554118}, {"key": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "name": "androidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership.class", "size": 817, "crc": -591097335}, {"key": "androidx/compose/runtime/internal/ComposableLambda.class", "name": "androidx/compose/runtime/internal/ComposableLambda.class", "size": 9084, "crc": 621267890}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$1.class", "size": 2022, "crc": -718195717}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$10.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$10.class", "size": 2658, "crc": -1927806707}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$11.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$11.class", "size": 2937, "crc": -1330991907}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$12.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$12.class", "size": 3026, "crc": 129801193}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$13.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$13.class", "size": 3111, "crc": -800993976}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$14.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$14.class", "size": 3196, "crc": -1197528401}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$15.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$15.class", "size": 3281, "crc": 505449138}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$16.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$16.class", "size": 3366, "crc": -639840462}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$17.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$17.class", "size": 3451, "crc": 331455212}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$18.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$18.class", "size": 3536, "crc": 2109226588}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$2.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$2.class", "size": 2102, "crc": 1146736659}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$3.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$3.class", "size": 2182, "crc": -2076501991}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$4.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$4.class", "size": 2262, "crc": 68090437}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$5.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$5.class", "size": 2342, "crc": -711171389}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$6.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$6.class", "size": 2422, "crc": 1103902397}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$7.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$7.class", "size": 2502, "crc": 419483073}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$8.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$8.class", "size": 2582, "crc": 267254581}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$9.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl$invoke$9.class", "size": 2662, "crc": -679419553}, {"key": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaImpl.class", "size": 50080, "crc": -246394984}, {"key": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaKt.class", "size": 4447, "crc": -1280490854}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN.class", "size": 739, "crc": 1833348103}, {"key": "androidx/compose/runtime/internal/ComposableLambdaNImpl$invoke$1.class", "name": "androidx/compose/runtime/internal/ComposableLambdaNImpl$invoke$1.class", "size": 4012, "crc": -1943246053}, {"key": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "name": "androidx/compose/runtime/internal/ComposableLambdaNImpl.class", "size": 6901, "crc": -1151194925}, {"key": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "name": "androidx/compose/runtime/internal/ComposableLambdaN_jvmKt.class", "size": 3124, "crc": -469510257}, {"key": "androidx/compose/runtime/internal/Decoy.class", "name": "androidx/compose/runtime/internal/Decoy.class", "size": 1029, "crc": -2078636242}, {"key": "androidx/compose/runtime/internal/DecoyImplementation.class", "name": "androidx/compose/runtime/internal/DecoyImplementation.class", "size": 1006, "crc": -496063648}, {"key": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "name": "androidx/compose/runtime/internal/DecoyImplementationDefaultsBitMask.class", "size": 961, "crc": 1076525739}, {"key": "androidx/compose/runtime/internal/DecoyKt.class", "name": "androidx/compose/runtime/internal/DecoyKt.class", "size": 1079, "crc": 1261849177}, {"key": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "name": "androidx/compose/runtime/internal/FloatingPointEquality_androidKt.class", "size": 1501, "crc": -859740172}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta$Container.class", "size": 913, "crc": 870267617}, {"key": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "name": "androidx/compose/runtime/internal/FunctionKeyMeta.class", "size": 1297, "crc": 216572539}, {"key": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "name": "androidx/compose/runtime/internal/FunctionKeyMetaClass.class", "size": 971, "crc": -366835767}, {"key": "androidx/compose/runtime/internal/IntRef.class", "name": "androidx/compose/runtime/internal/IntRef.class", "size": 1916, "crc": 1216340085}, {"key": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/runtime/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 560, "crc": -1863150101}, {"key": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralFileInfo.class", "size": 965, "crc": -84332509}, {"key": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "name": "androidx/compose/runtime/internal/LiveLiteralInfo.class", "size": 1026, "crc": -693514666}, {"key": "androidx/compose/runtime/internal/LiveLiteralKt.class", "name": "androidx/compose/runtime/internal/LiveLiteralKt.class", "size": 4360, "crc": -1177433970}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Builder.class", "size": 6521, "crc": 193498037}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap$Companion.class", "size": 1382, "crc": -381514527}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalHashMap.class", "size": 9486, "crc": 1983705263}, {"key": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "name": "androidx/compose/runtime/internal/PersistentCompositionLocalMapKt.class", "size": 3321, "crc": 896486289}, {"key": "androidx/compose/runtime/internal/StabilityInferred.class", "name": "androidx/compose/runtime/internal/StabilityInferred.class", "size": 955, "crc": -177704314}, {"key": "androidx/compose/runtime/internal/ThreadMap.class", "name": "androidx/compose/runtime/internal/ThreadMap.class", "size": 3914, "crc": 505637417}, {"key": "androidx/compose/runtime/internal/ThreadMap_jvmKt.class", "name": "androidx/compose/runtime/internal/ThreadMap_jvmKt.class", "size": 1601, "crc": 1730516084}, {"key": "androidx/compose/runtime/reflect/ComposableInfo.class", "name": "androidx/compose/runtime/reflect/ComposableInfo.class", "size": 3352, "crc": 2093087670}, {"key": "androidx/compose/runtime/reflect/ComposableMethod.class", "name": "androidx/compose/runtime/reflect/ComposableMethod.class", "size": 8239, "crc": 15479675}, {"key": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "name": "androidx/compose/runtime/reflect/ComposableMethodKt.class", "size": 10619, "crc": 950071042}, {"key": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "name": "androidx/compose/runtime/snapshots/AutoboxingStateValueProperty.class", "size": 1009, "crc": 494427556}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$1$1$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$1$1$1.class", "size": 2793, "crc": -1868137528}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedMutableSnapshot$1.class", "size": 3917, "crc": -939002956}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot$takeNestedSnapshot$1.class", "size": 3627, "crc": -1465749018}, {"key": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "name": "androidx/compose/runtime/snapshots/GlobalSnapshot.class", "size": 6528, "crc": 2020473054}, {"key": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "name": "androidx/compose/runtime/snapshots/ListUtilsKt.class", "size": 12852, "crc": -1481692209}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot$Companion.class", "size": 903, "crc": 2073477342}, {"key": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/MutableSnapshot.class", "size": 31774, "crc": -1096998720}, {"key": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedMutableSnapshot.class", "size": 7812, "crc": -461096905}, {"key": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot$readObserver$1$1$1.class", "name": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot$readObserver$1$1$1.class", "size": 1888, "crc": 1505967255}, {"key": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/NestedReadonlySnapshot.class", "size": 6383, "crc": 1397808928}, {"key": "androidx/compose/runtime/snapshots/ObserverHandle.class", "name": "androidx/compose/runtime/snapshots/ObserverHandle.class", "size": 462, "crc": 449216628}, {"key": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "name": "androidx/compose/runtime/snapshots/ReaderKind$Companion.class", "size": 1543, "crc": 2008060952}, {"key": "androidx/compose/runtime/snapshots/ReaderKind.class", "name": "androidx/compose/runtime/snapshots/ReaderKind.class", "size": 3098, "crc": -1047107092}, {"key": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "name": "androidx/compose/runtime/snapshots/ReadonlySnapshot.class", "size": 5417, "crc": 1387585740}, {"key": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "name": "androidx/compose/runtime/snapshots/Snapshot$Companion.class", "size": 15019, "crc": -532811165}, {"key": "androidx/compose/runtime/snapshots/Snapshot.class", "name": "androidx/compose/runtime/snapshots/Snapshot.class", "size": 11356, "crc": 1660787855}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyConflictException.class", "size": 1259, "crc": 756699082}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Failure.class", "size": 1775, "crc": 884631732}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult$Success.class", "size": 1261, "crc": 1812863540}, {"key": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "name": "androidx/compose/runtime/snapshots/SnapshotApplyResult.class", "size": 1403, "crc": -1806712236}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$DefaultImpls.class", "size": 3157, "crc": 575677703}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement$Key.class", "size": 1119, "crc": -1327373666}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElement.class", "size": 1112, "crc": -1077834103}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElementImpl.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElementImpl.class", "size": 4901, "crc": -892362260}, {"key": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotContextElementKt.class", "size": 1140, "crc": -624753673}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeap.class", "size": 4866, "crc": -47690800}, {"key": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotDoubleIndexHeapKt.class", "size": 438, "crc": 1851865193}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$Companion.class", "size": 1193, "crc": -1304696973}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet$iterator$1.class", "size": 5032, "crc": 1987839504}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSet.class", "size": 12883, "crc": 1991102334}, {"key": "androidx/compose/runtime/snapshots/SnapshotIdSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotIdSetKt.class", "size": 1387, "crc": 1521471772}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$advanceGlobalSnapshot$3.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$advanceGlobalSnapshot$3.class", "size": 1543, "crc": 131243348}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$emptyLambda$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$emptyLambda$1.class", "size": 1494, "crc": -1656490764}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$mergedReadObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$mergedReadObserver$1.class", "size": 1857, "crc": 1941039571}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$mergedWriteObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$mergedWriteObserver$1.class", "size": 1860, "crc": -1445753658}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt$takeNewSnapshot$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt$takeNewSnapshot$1.class", "size": 3734, "crc": -101378455}, {"key": "androidx/compose/runtime/snapshots/SnapshotKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotKt.class", "size": 38939, "crc": -1790840465}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapEntrySet.class", "size": 13660, "crc": -1382314610}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapKeySet.class", "size": 12583, "crc": -2105629841}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapSet.class", "size": 2513, "crc": 2109474761}, {"key": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotMapValueSet.class", "size": 14890, "crc": -1366954459}, {"key": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "name": "androidx/compose/runtime/snapshots/SnapshotMutableState.class", "size": 979, "crc": 1303726931}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$StateListStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$StateListStateRecord.class", "size": 4707, "crc": -1147884279}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$addAll$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$addAll$1.class", "size": 1749, "crc": -578824460}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList$retainAll$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList$retainAll$1.class", "size": 1705, "crc": -1731757658}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateList.class", "size": 54137, "crc": 1480482151}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateListKt.class", "size": 2004, "crc": -430202560}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap$StateMapStateRecord.class", "size": 4513, "crc": 100340851}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMap.class", "size": 29787, "crc": 1922027254}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateMapKt.class", "size": 852, "crc": -390358347}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap$derivedStateObserver$1.class", "size": 2027, "crc": 1353676956}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$ObservedScopeMap.class", "size": 41058, "crc": 1378246311}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$applyObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$applyObserver$1.class", "size": 2256, "crc": 897480259}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$readObserver$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$readObserver$1.class", "size": 2454, "crc": 115356602}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver$sendNotifications$1.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver$sendNotifications$1.class", "size": 3730, "crc": -870797524}, {"key": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "name": "androidx/compose/runtime/snapshots/SnapshotStateObserver.class", "size": 19389, "crc": -1891043899}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSet.class", "size": 6036, "crc": -301883879}, {"key": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "name": "androidx/compose/runtime/snapshots/SnapshotWeakSetKt.class", "size": 422, "crc": 1539315218}, {"key": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "name": "androidx/compose/runtime/snapshots/StateFactoryMarker.class", "size": 935, "crc": -589967162}, {"key": "androidx/compose/runtime/snapshots/StateListIterator.class", "name": "androidx/compose/runtime/snapshots/StateListIterator.class", "size": 4446, "crc": -306168111}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator$next$1.class", "size": 4051, "crc": 1347582476}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableEntriesIterator.class", "size": 2286, "crc": -1429820393}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableIterator.class", "size": 5988, "crc": -53817572}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableKeysIterator.class", "size": 1980, "crc": 1373249618}, {"key": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "name": "androidx/compose/runtime/snapshots/StateMapMutableValuesIterator.class", "size": 1985, "crc": -165947506}, {"key": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "name": "androidx/compose/runtime/snapshots/StateObject$DefaultImpls.class", "size": 1192, "crc": -77497598}, {"key": "androidx/compose/runtime/snapshots/StateObject.class", "name": "androidx/compose/runtime/snapshots/StateObject.class", "size": 1859, "crc": 1733916894}, {"key": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "name": "androidx/compose/runtime/snapshots/StateObjectImpl.class", "size": 2717, "crc": -898159930}, {"key": "androidx/compose/runtime/snapshots/StateRecord.class", "name": "androidx/compose/runtime/snapshots/StateRecord.class", "size": 1886, "crc": -1816388120}, {"key": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "name": "androidx/compose/runtime/snapshots/SubList$listIterator$1.class", "size": 3338, "crc": -1362964791}, {"key": "androidx/compose/runtime/snapshots/SubList.class", "name": "androidx/compose/runtime/snapshots/SubList.class", "size": 9578, "crc": 174545210}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot.class", "size": 8964, "crc": 1365336372}, {"key": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "name": "androidx/compose/runtime/snapshots/TransparentObserverSnapshot.class", "size": 7707, "crc": -1677518678}, {"key": "androidx/compose/runtime/tooling/CompositionData.class", "name": "androidx/compose/runtime/tooling/CompositionData.class", "size": 1211, "crc": 376254531}, {"key": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "name": "androidx/compose/runtime/tooling/CompositionGroup$DefaultImpls.class", "size": 1561, "crc": 578238820}, {"key": "androidx/compose/runtime/tooling/CompositionGroup.class", "name": "androidx/compose/runtime/tooling/CompositionGroup.class", "size": 2403, "crc": 1994065739}, {"key": "androidx/compose/runtime/tooling/CompositionObserver.class", "name": "androidx/compose/runtime/tooling/CompositionObserver.class", "size": 1194, "crc": 987247663}, {"key": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "name": "androidx/compose/runtime/tooling/CompositionObserverHandle.class", "size": 588, "crc": 247969004}, {"key": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "name": "androidx/compose/runtime/tooling/CompositionObserverKt.class", "size": 2489, "crc": 168388353}, {"key": "androidx/compose/runtime/tooling/InspectionTablesKt$LocalInspectionTables$1.class", "name": "androidx/compose/runtime/tooling/InspectionTablesKt$LocalInspectionTables$1.class", "size": 1401, "crc": 1470652688}, {"key": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "name": "androidx/compose/runtime/tooling/InspectionTablesKt.class", "size": 1567, "crc": 1708835644}, {"key": "androidx/compose/runtime/tooling/RecomposeScopeObserver.class", "name": "androidx/compose/runtime/tooling/RecomposeScopeObserver.class", "size": 929, "crc": 1824493179}, {"key": "META-INF/androidx.compose.runtime_runtime.version", "name": "META-INF/androidx.compose.runtime_runtime.version", "size": 6, "crc": -1055996171}, {"key": "META-INF/runtime_release.kotlin_module", "name": "META-INF/runtime_release.kotlin_module", "size": 2113, "crc": -1540992198}]