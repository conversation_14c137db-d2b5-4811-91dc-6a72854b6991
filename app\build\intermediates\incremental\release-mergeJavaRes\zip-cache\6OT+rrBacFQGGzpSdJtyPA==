[{"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$1.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$1.class", "size": 1931, "crc": -1640808297}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$2.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$2.class", "size": 1977, "crc": -1225768483}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$3.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$3.class", "size": 1963, "crc": 460228250}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$4.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$4.class", "size": 2482, "crc": -542943736}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$1.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$1.class", "size": 2151, "crc": -318782002}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$2.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$2.class", "size": 2183, "crc": 1495941665}, {"key": "androidx/activity/ActivityViewModelLazyKt.class", "name": "androidx/activity/ActivityViewModelLazyKt.class", "size": 4266, "crc": -1420144971}, {"key": "androidx/activity/Api19Impl.class", "name": "androidx/activity/Api19Impl.class", "size": 1021, "crc": -1649969737}, {"key": "androidx/activity/Api26Impl.class", "name": "androidx/activity/Api26Impl.class", "size": 1528, "crc": -2107521099}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$2.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$2.class", "size": 2073, "crc": -1500449956}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$1.class", "size": 2639, "crc": -292907574}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1.class", "size": 3072, "crc": 603466455}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1.class", "size": 7119, "crc": 1481922311}, {"key": "androidx/activity/PipHintTrackerKt.class", "name": "androidx/activity/PipHintTrackerKt.class", "size": 2709, "crc": 1409474831}, {"key": "androidx/activity/result/ActivityResultCallerKt.class", "name": "androidx/activity/result/ActivityResultCallerKt.class", "size": 4225, "crc": -501355442}, {"key": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1.class", "name": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1.class", "size": 2603, "crc": -538903450}, {"key": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2.class", "name": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2.class", "size": 1931, "crc": -2145364518}, {"key": "androidx/activity/result/ActivityResultCallerLauncher.class", "name": "androidx/activity/result/ActivityResultCallerLauncher.class", "size": 4006, "crc": -1175388349}, {"key": "androidx/activity/result/ActivityResultKt.class", "name": "androidx/activity/result/ActivityResultKt.class", "size": 1120, "crc": -1039745402}, {"key": "androidx/activity/result/ActivityResultLauncherKt.class", "name": "androidx/activity/result/ActivityResultLauncherKt.class", "size": 1891, "crc": 2108970990}, {"key": "META-INF/activity-ktx_release.kotlin_module", "name": "META-INF/activity-ktx_release.kotlin_module", "size": 184, "crc": 164021924}, {"key": "META-INF/androidx.activity_activity-ktx.version", "name": "META-INF/androidx.activity_activity-ktx.version", "size": 6, "crc": 184345093}]