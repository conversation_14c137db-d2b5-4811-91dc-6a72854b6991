{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeReleaseResources-40:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\71219f197f28274676b3874ecf964996\\transformed\\core-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,202,304,402,499,601,707,8052", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "197,299,397,494,596,702,813,8148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19b642e7465edfc9a64434f74e28ca0a\\transformed\\material3-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,283,401,516,612,708,821,954,1076,1216,1301,1399,1488,1585,1700,1821,1924,2061,2197,2319,2490,2608,2724,2842,2957,3047,3145,3269,3398,3499,3601,3707,3843,3983,4095,4197,4273,4370,4468,4554,4639,4756,4836,4920,5020,5120,5216,5311,5399,5505,5605,5704,5825,5905,6012", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "165,278,396,511,607,703,816,949,1071,1211,1296,1394,1483,1580,1695,1816,1919,2056,2192,2314,2485,2603,2719,2837,2952,3042,3140,3264,3393,3494,3596,3702,3838,3978,4090,4192,4268,4365,4463,4549,4634,4751,4831,4915,5015,5115,5211,5306,5394,5500,5600,5699,5820,5900,6007,6100"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1457,1572,1685,1803,1918,2014,2110,2223,2356,2478,2618,2703,2801,2890,2987,3102,3223,3326,3463,3599,3721,3892,4010,4126,4244,4359,4449,4547,4671,4800,4901,5003,5109,5245,5385,5497,5599,5675,5772,5870,5956,6041,6158,6238,6322,6422,6522,6618,6713,6801,6907,7007,7106,7227,7307,7414", "endColumns": "114,112,117,114,95,95,112,132,121,139,84,97,88,96,114,120,102,136,135,121,170,117,115,117,114,89,97,123,128,100,101,105,135,139,111,101,75,96,97,85,84,116,79,83,99,99,95,94,87,105,99,98,120,79,106,92", "endOffsets": "1567,1680,1798,1913,2009,2105,2218,2351,2473,2613,2698,2796,2885,2982,3097,3218,3321,3458,3594,3716,3887,4005,4121,4239,4354,4444,4542,4666,4795,4896,4998,5104,5240,5380,5492,5594,5670,5767,5865,5951,6036,6153,6233,6317,6417,6517,6613,6708,6796,6902,7002,7101,7222,7302,7409,7502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f608d021a4f1e83e8e85d7b0e5006a22\\transformed\\foundation-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,86", "endOffsets": "134,221"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8417,8501", "endColumns": "83,86", "endOffsets": "8496,8583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62f35d7bdb9f40920fe06c2287c5bbc3\\transformed\\ui-release\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,377,477,561,644,744,832,916,984,1050,1130,1218,1289,1367,1435", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "193,277,372,472,556,639,739,827,911,979,1045,1125,1213,1284,1362,1430,1548"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "818,911,995,1090,1190,1274,1357,7507,7595,7679,7747,7813,7893,7981,8153,8231,8299", "endColumns": "92,83,94,99,83,82,99,87,83,67,65,79,87,70,77,67,117", "endOffsets": "906,990,1085,1185,1269,1352,1452,7590,7674,7742,7808,7888,7976,8047,8226,8294,8412"}}]}]}