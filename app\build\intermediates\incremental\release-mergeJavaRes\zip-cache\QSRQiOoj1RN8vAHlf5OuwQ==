[{"key": "androidx/compose/foundation/AbstractClickableNode$TraverseKey.class", "name": "androidx/compose/foundation/AbstractClickableNode$TraverseKey.class", "size": 867, "crc": 687958378}, {"key": "androidx/compose/foundation/AbstractClickableNode$emitHoverEnter$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$emitHoverEnter$1$1.class", "size": 3901, "crc": -530235059}, {"key": "androidx/compose/foundation/AbstractClickableNode$emitHoverExit$1$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$emitHoverExit$1$1$1.class", "size": 3899, "crc": 1771595250}, {"key": "androidx/compose/foundation/AbstractClickableNode$focusableNode$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$focusableNode$1.class", "size": 1632, "crc": 165308630}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1$delayJob$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1$delayJob$1.class", "size": 4858, "crc": -643313882}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteraction$2$1.class", "size": 7330, "crc": -2099880082}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionCancel$1$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionCancel$1$1$1.class", "size": 4259, "crc": -1294529389}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionRelease$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionRelease$1$1.class", "size": 4959, "crc": 1784338016}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionRelease$1$2$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionRelease$1$2$1.class", "size": 4276, "crc": 463873926}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionStart$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionStart$1$1.class", "size": 4613, "crc": 1657390992}, {"key": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionStart$1$2.class", "name": "androidx/compose/foundation/AbstractClickableNode$handlePressInteractionStart$1$2.class", "size": 3969, "crc": 230778616}, {"key": "androidx/compose/foundation/AbstractClickableNode$onFocusChange$1$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onFocusChange$1$1.class", "size": 4196, "crc": 1632199517}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "size": 4033, "crc": 407761893}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2.class", "size": 4212, "crc": -100583815}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$1.class", "size": 3320, "crc": -267722776}, {"key": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/AbstractClickableNode$onPointerEvent$2.class", "size": 3319, "crc": 1857841184}, {"key": "androidx/compose/foundation/AbstractClickableNode.class", "name": "androidx/compose/foundation/AbstractClickableNode.class", "size": 32274, "crc": 1536545833}, {"key": "androidx/compose/foundation/ActualJvm_jvmKt.class", "name": "androidx/compose/foundation/ActualJvm_jvmKt.class", "size": 514, "crc": -1590977975}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "size": 1958, "crc": -1600933472}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1$1.class", "size": 8372, "crc": 1661840208}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$pointerInputNode$1.class", "size": 2196, "crc": 1398112687}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "size": 35508, "crc": 1796997071}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollFactory.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollFactory.class", "size": 3803, "crc": -1334756441}, {"key": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "size": 5353, "crc": 1274615064}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "size": 1276, "crc": -756679761}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "size": 2699, "crc": 1681149577}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "size": 1453, "crc": 1637033630}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "size": 2956, "crc": -1635159226}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "size": 23883, "crc": 1251604261}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "size": 8898, "crc": 1367123318}, {"key": "androidx/compose/foundation/Api31Impl.class", "name": "androidx/compose/foundation/Api31Impl.class", "size": 2120, "crc": 2040870564}, {"key": "androidx/compose/foundation/BackgroundElement.class", "name": "androidx/compose/foundation/BackgroundElement.class", "size": 5502, "crc": 1977656735}, {"key": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "size": 3108, "crc": 400123323}, {"key": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "size": 3073, "crc": 402946096}, {"key": "androidx/compose/foundation/BackgroundKt.class", "name": "androidx/compose/foundation/BackgroundKt.class", "size": 4185, "crc": -588723834}, {"key": "androidx/compose/foundation/BackgroundNode.class", "name": "androidx/compose/foundation/BackgroundNode.class", "size": 9444, "crc": 176248352}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "size": 2805, "crc": 1433394073}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "size": 4890, "crc": -886621248}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "size": 6165, "crc": 586301206}, {"key": "androidx/compose/foundation/BasicMarqueeKt.class", "name": "androidx/compose/foundation/BasicMarqueeKt.class", "size": 6669, "crc": 1061045830}, {"key": "androidx/compose/foundation/BasicTooltipDefaults.class", "name": "androidx/compose/foundation/BasicTooltipDefaults.class", "size": 1337, "crc": -1535513292}, {"key": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$lambda$3$lambda$2$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$BasicTooltipBox$lambda$3$lambda$2$$inlined$onDispose$1.class", "size": 2230, "crc": -1651545619}, {"key": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$1$1$1.class", "size": 3350, "crc": -1894208135}, {"key": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$2.class", "name": "androidx/compose/foundation/BasicTooltipKt$TooltipPopup$2.class", "size": 11454, "crc": -1695539388}, {"key": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$anchorSemantics$1$1$1.class", "size": 3563, "crc": -301429470}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1$1.class", "size": 3600, "crc": -1213024105}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1$1.class", "size": 6276, "crc": -727695412}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1$1.class", "size": 4000, "crc": 2048719602}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$1.class", "size": 2013, "crc": 398760677}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1$1.class", "size": 3600, "crc": 1163675960}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1$1.class", "size": 5657, "crc": 266763546}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2$1.class", "size": 3953, "crc": -1347625127}, {"key": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2.class", "name": "androidx/compose/foundation/BasicTooltipKt$handleGestures$2.class", "size": 2013, "crc": 403690112}, {"key": "androidx/compose/foundation/BasicTooltipKt.class", "name": "androidx/compose/foundation/BasicTooltipKt.class", "size": 30128, "crc": -1625905147}, {"key": "androidx/compose/foundation/BasicTooltipState.class", "name": "androidx/compose/foundation/BasicTooltipState.class", "size": 1805, "crc": -1343807975}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "size": 3319, "crc": -624928203}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "size": 3848, "crc": 428607413}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "size": 5054, "crc": 781586685}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl.class", "size": 5601, "crc": -2029498824}, {"key": "androidx/compose/foundation/BasicTooltipStrings.class", "name": "androidx/compose/foundation/BasicTooltipStrings.class", "size": 2484, "crc": 279299915}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt.class", "size": 5643, "crc": -1961270949}, {"key": "androidx/compose/foundation/BorderCache.class", "name": "androidx/compose/foundation/BorderCache.class", "size": 13658, "crc": 1188360514}, {"key": "androidx/compose/foundation/BorderKt.class", "name": "androidx/compose/foundation/BorderKt.class", "size": 11858, "crc": 1052687626}, {"key": "androidx/compose/foundation/BorderModifierNode.class", "name": "androidx/compose/foundation/BorderModifierNode.class", "size": 32361, "crc": -922304312}, {"key": "androidx/compose/foundation/BorderModifierNodeElement.class", "name": "androidx/compose/foundation/BorderModifierNodeElement.class", "size": 6367, "crc": -459011370}, {"key": "androidx/compose/foundation/BorderStroke.class", "name": "androidx/compose/foundation/BorderStroke.class", "size": 3241, "crc": -418005005}, {"key": "androidx/compose/foundation/BorderStrokeKt.class", "name": "androidx/compose/foundation/BorderStrokeKt.class", "size": 1170, "crc": 1069940557}, {"key": "androidx/compose/foundation/CanvasKt.class", "name": "androidx/compose/foundation/CanvasKt.class", "size": 8016, "crc": -174856937}, {"key": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "name": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "size": 4038, "crc": 164767264}, {"key": "androidx/compose/foundation/ClickableElement.class", "name": "androidx/compose/foundation/ClickableElement.class", "size": 5817, "crc": -1248451215}, {"key": "androidx/compose/foundation/ClickableKt$clickable$2.class", "name": "androidx/compose/foundation/ClickableKt$clickable$2.class", "size": 6116, "crc": 1636753938}, {"key": "androidx/compose/foundation/ClickableKt$clickable$4.class", "name": "androidx/compose/foundation/ClickableKt$clickable$4.class", "size": 6371, "crc": -285482732}, {"key": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6445, "crc": -1313408343}, {"key": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3279, "crc": -1711752382}, {"key": "androidx/compose/foundation/ClickableKt$clickable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "size": 3573, "crc": -1451501924}, {"key": "androidx/compose/foundation/ClickableKt$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$clickableWithIndicationIfNeeded$1.class", "size": 5683, "crc": 342931851}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "size": 6821, "crc": -1190007682}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable$4.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable$4.class", "size": 7074, "crc": -1214538907}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable$6.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable$6.class", "size": 6756, "crc": -1668006348}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6911, "crc": -1108677414}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-auXiCPI$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-auXiCPI$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6973, "crc": -2064940446}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "size": 3748, "crc": -1221241237}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-f5TDLPQ$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-f5TDLPQ$$inlined$debugInspectorInfo$1.class", "size": 3856, "crc": 1049575880}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-hoGz1lA$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-hoGz1lA$$inlined$debugInspectorInfo$1.class", "size": 3922, "crc": 2107015663}, {"key": "androidx/compose/foundation/ClickableKt.class", "name": "androidx/compose/foundation/ClickableKt.class", "size": 27569, "crc": -44676458}, {"key": "androidx/compose/foundation/ClickableNode$createPointerInputNodeIfNeeded$1$1.class", "name": "androidx/compose/foundation/ClickableNode$createPointerInputNodeIfNeeded$1$1.class", "size": 3654, "crc": 2123441464}, {"key": "androidx/compose/foundation/ClickableNode$createPointerInputNodeIfNeeded$1.class", "name": "androidx/compose/foundation/ClickableNode$createPointerInputNodeIfNeeded$1.class", "size": 3030, "crc": -1631121932}, {"key": "androidx/compose/foundation/ClickableNode.class", "name": "androidx/compose/foundation/ClickableNode.class", "size": 13554, "crc": -1597600337}, {"key": "androidx/compose/foundation/Clickable_androidKt.class", "name": "androidx/compose/foundation/Clickable_androidKt.class", "size": 1682, "crc": 2029999833}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt.class", "size": 3003, "crc": -2119288956}, {"key": "androidx/compose/foundation/CombinedClickableElement.class", "name": "androidx/compose/foundation/CombinedClickableElement.class", "size": 7771, "crc": 202964780}, {"key": "androidx/compose/foundation/CombinedClickableNode$DoubleKeyClickState.class", "name": "androidx/compose/foundation/CombinedClickableNode$DoubleKeyClickState.class", "size": 1585, "crc": -437208449}, {"key": "androidx/compose/foundation/CombinedClickableNode$createPointerInputNodeIfNeeded$1$3.class", "name": "androidx/compose/foundation/CombinedClickableNode$createPointerInputNodeIfNeeded$1$3.class", "size": 3726, "crc": 524382660}, {"key": "androidx/compose/foundation/CombinedClickableNode$createPointerInputNodeIfNeeded$1.class", "name": "androidx/compose/foundation/CombinedClickableNode$createPointerInputNodeIfNeeded$1.class", "size": 4871, "crc": -1907836456}, {"key": "androidx/compose/foundation/CombinedClickableNode$onClickKeyDownEvent$1.class", "name": "androidx/compose/foundation/CombinedClickableNode$onClickKeyDownEvent$1.class", "size": 4161, "crc": 1977354455}, {"key": "androidx/compose/foundation/CombinedClickableNode$onClickKeyUpEvent$2.class", "name": "androidx/compose/foundation/CombinedClickableNode$onClickKeyUpEvent$2.class", "size": 4961, "crc": -1515017933}, {"key": "androidx/compose/foundation/CombinedClickableNode.class", "name": "androidx/compose/foundation/CombinedClickableNode.class", "size": 15915, "crc": -436239939}, {"key": "androidx/compose/foundation/ComposeFoundationFlags.class", "name": "androidx/compose/foundation/ComposeFoundationFlags.class", "size": 3293, "crc": -1532180238}, {"key": "androidx/compose/foundation/DarkThemeKt.class", "name": "androidx/compose/foundation/DarkThemeKt.class", "size": 1443, "crc": 1390011040}, {"key": "androidx/compose/foundation/DarkTheme_androidKt.class", "name": "androidx/compose/foundation/DarkTheme_androidKt.class", "size": 2803, "crc": -1427124047}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1$1.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1$1.class", "size": 4104, "crc": -1991013664}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance$onAttach$1.class", "size": 4605, "crc": 963090207}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "size": 4204, "crc": 124543365}, {"key": "androidx/compose/foundation/DefaultDebugIndication.class", "name": "androidx/compose/foundation/DefaultDebugIndication.class", "size": 1903, "crc": 742254048}, {"key": "androidx/compose/foundation/EdgeEffectCompat.class", "name": "androidx/compose/foundation/EdgeEffectCompat.class", "size": 3654, "crc": -1050524161}, {"key": "androidx/compose/foundation/EdgeEffectCompat_androidKt.class", "name": "androidx/compose/foundation/EdgeEffectCompat_androidKt.class", "size": 1650, "crc": 1823399957}, {"key": "androidx/compose/foundation/EdgeEffectWrapper.class", "name": "androidx/compose/foundation/EdgeEffectWrapper.class", "size": 12446, "crc": -1502966857}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "size": 3921, "crc": 1079931560}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "size": 1774, "crc": 225862703}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "size": 3679, "crc": -1085931597}, {"key": "androidx/compose/foundation/ExperimentalFoundationApi.class", "name": "androidx/compose/foundation/ExperimentalFoundationApi.class", "size": 833, "crc": -242853409}, {"key": "androidx/compose/foundation/FixedMotionDurationScale.class", "name": "androidx/compose/foundation/FixedMotionDurationScale.class", "size": 3084, "crc": 315613475}, {"key": "androidx/compose/foundation/FocusGroupElement.class", "name": "androidx/compose/foundation/FocusGroupElement.class", "size": 2547, "crc": -77756727}, {"key": "androidx/compose/foundation/FocusGroupNode.class", "name": "androidx/compose/foundation/FocusGroupNode.class", "size": 1251, "crc": 1510979209}, {"key": "androidx/compose/foundation/FocusableElement.class", "name": "androidx/compose/foundation/FocusableElement.class", "size": 3445, "crc": 1060751190}, {"key": "androidx/compose/foundation/FocusableKt.class", "name": "androidx/compose/foundation/FocusableKt.class", "size": 2122, "crc": 860534037}, {"key": "androidx/compose/foundation/FocusableNode$TraverseKey.class", "name": "androidx/compose/foundation/FocusableNode$TraverseKey.class", "size": 843, "crc": -875768661}, {"key": "androidx/compose/foundation/FocusableNode$applySemantics$1.class", "name": "androidx/compose/foundation/FocusableNode$applySemantics$1.class", "size": 1305, "crc": 1787628238}, {"key": "androidx/compose/foundation/FocusableNode$emitWithFallback$1.class", "name": "androidx/compose/foundation/FocusableNode$emitWithFallback$1.class", "size": 4015, "crc": -384534439}, {"key": "androidx/compose/foundation/FocusableNode$focusTargetNode$1.class", "name": "androidx/compose/foundation/FocusableNode$focusTargetNode$1.class", "size": 1865, "crc": 275572876}, {"key": "androidx/compose/foundation/FocusableNode$onFocusStateChange$1.class", "name": "androidx/compose/foundation/FocusableNode$onFocusStateChange$1.class", "size": 3524, "crc": -1974774065}, {"key": "androidx/compose/foundation/FocusableNode.class", "name": "androidx/compose/foundation/FocusableNode.class", "size": 15608, "crc": 1879245952}, {"key": "androidx/compose/foundation/FocusedBoundsKt.class", "name": "androidx/compose/foundation/FocusedBoundsKt.class", "size": 1374, "crc": 853854853}, {"key": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "name": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "size": 3638, "crc": -352461079}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode$TraverseKey.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode$TraverseKey.class", "size": 883, "crc": -1308539541}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "size": 3057, "crc": -1919511740}, {"key": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "name": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "size": 3188, "crc": 1276265876}, {"key": "androidx/compose/foundation/GlowOverscrollNode.class", "name": "androidx/compose/foundation/GlowOverscrollNode.class", "size": 10582, "crc": -1626915383}, {"key": "androidx/compose/foundation/HorizontalScrollableClipShape.class", "name": "androidx/compose/foundation/HorizontalScrollableClipShape.class", "size": 3862, "crc": 1400788159}, {"key": "androidx/compose/foundation/HoverableElement.class", "name": "androidx/compose/foundation/HoverableElement.class", "size": 3216, "crc": -318714103}, {"key": "androidx/compose/foundation/HoverableKt.class", "name": "androidx/compose/foundation/HoverableKt.class", "size": 1772, "crc": 815883322}, {"key": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "name": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "size": 1826, "crc": -576085204}, {"key": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "name": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "size": 1784, "crc": 370151077}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "size": 3366, "crc": -1710963601}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "size": 3365, "crc": 700548160}, {"key": "androidx/compose/foundation/HoverableNode.class", "name": "androidx/compose/foundation/HoverableNode.class", "size": 6692, "crc": 395840800}, {"key": "androidx/compose/foundation/ImageKt$Image$1$1.class", "name": "androidx/compose/foundation/ImageKt$Image$1$1.class", "size": 2544, "crc": 1242105819}, {"key": "androidx/compose/foundation/ImageKt.class", "name": "androidx/compose/foundation/ImageKt.class", "size": 19146, "crc": 1753200837}, {"key": "androidx/compose/foundation/Indication.class", "name": "androidx/compose/foundation/Indication.class", "size": 2473, "crc": -1725421889}, {"key": "androidx/compose/foundation/IndicationInstance.class", "name": "androidx/compose/foundation/IndicationInstance.class", "size": 1099, "crc": 1795619116}, {"key": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "size": 3068, "crc": 978036304}, {"key": "androidx/compose/foundation/IndicationKt$indication$2.class", "name": "androidx/compose/foundation/IndicationKt$indication$2.class", "size": 4667, "crc": -310496685}, {"key": "androidx/compose/foundation/IndicationKt.class", "name": "androidx/compose/foundation/IndicationKt.class", "size": 5508, "crc": -164906752}, {"key": "androidx/compose/foundation/IndicationModifier.class", "name": "androidx/compose/foundation/IndicationModifier.class", "size": 2019, "crc": 1910471267}, {"key": "androidx/compose/foundation/IndicationModifierElement.class", "name": "androidx/compose/foundation/IndicationModifierElement.class", "size": 3672, "crc": -1977123788}, {"key": "androidx/compose/foundation/IndicationModifierNode.class", "name": "androidx/compose/foundation/IndicationModifierNode.class", "size": 1249, "crc": 1431318907}, {"key": "androidx/compose/foundation/IndicationNodeFactory.class", "name": "androidx/compose/foundation/IndicationNodeFactory.class", "size": 1176, "crc": -1490251937}, {"key": "androidx/compose/foundation/InternalFoundationApi.class", "name": "androidx/compose/foundation/InternalFoundationApi.class", "size": 1046, "crc": 1011450810}, {"key": "androidx/compose/foundation/MagnifierElement.class", "name": "androidx/compose/foundation/MagnifierElement.class", "size": 7832, "crc": -279434069}, {"key": "androidx/compose/foundation/MagnifierNode$onAttach$1.class", "name": "androidx/compose/foundation/MagnifierNode$onAttach$1.class", "size": 4370, "crc": -2052756403}, {"key": "androidx/compose/foundation/MagnifierNode.class", "name": "androidx/compose/foundation/MagnifierNode.class", "size": 21100, "crc": 956213985}, {"key": "androidx/compose/foundation/Magnifier_androidKt.class", "name": "androidx/compose/foundation/Magnifier_androidKt.class", "size": 7197, "crc": 1335470650}, {"key": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "name": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "size": 1272, "crc": 384180930}, {"key": "androidx/compose/foundation/MarqueeAnimationMode.class", "name": "androidx/compose/foundation/MarqueeAnimationMode.class", "size": 2880, "crc": -116807945}, {"key": "androidx/compose/foundation/MarqueeDefaults.class", "name": "androidx/compose/foundation/MarqueeDefaults.class", "size": 2869, "crc": 155986587}, {"key": "androidx/compose/foundation/MarqueeModifierElement.class", "name": "androidx/compose/foundation/MarqueeModifierElement.class", "size": 6679, "crc": -248425356}, {"key": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "name": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "size": 814, "crc": 443581553}, {"key": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "size": 3622, "crc": 1629356065}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "size": 5306, "crc": -203304821}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "size": 4951, "crc": -313616141}, {"key": "androidx/compose/foundation/MarqueeModifierNode.class", "name": "androidx/compose/foundation/MarqueeModifierNode.class", "size": 25224, "crc": -1715986963}, {"key": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "name": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "size": 1769, "crc": 838702932}, {"key": "androidx/compose/foundation/MarqueeSpacing.class", "name": "androidx/compose/foundation/MarqueeSpacing.class", "size": 1051, "crc": 1028648952}, {"key": "androidx/compose/foundation/MutatePriority.class", "name": "androidx/compose/foundation/MutatePriority.class", "size": 1963, "crc": 946288664}, {"key": "androidx/compose/foundation/MutationInterruptedException.class", "name": "androidx/compose/foundation/MutationInterruptedException.class", "size": 951, "crc": -97152326}, {"key": "androidx/compose/foundation/MutatorMutex$Mutator.class", "name": "androidx/compose/foundation/MutatorMutex$Mutator.class", "size": 2027, "crc": 1326458056}, {"key": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "size": 6977, "crc": -182495832}, {"key": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "size": 7138, "crc": 242424195}, {"key": "androidx/compose/foundation/MutatorMutex.class", "name": "androidx/compose/foundation/MutatorMutex.class", "size": 7020, "crc": -504730587}, {"key": "androidx/compose/foundation/NoIndicationInstance.class", "name": "androidx/compose/foundation/NoIndicationInstance.class", "size": 1232, "crc": -334615313}, {"key": "androidx/compose/foundation/OverscrollConfiguration.class", "name": "androidx/compose/foundation/OverscrollConfiguration.class", "size": 4261, "crc": 944447343}, {"key": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "name": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "size": 3067, "crc": 829524185}, {"key": "androidx/compose/foundation/OverscrollEffect$node$1.class", "name": "androidx/compose/foundation/OverscrollEffect$node$1.class", "size": 814, "crc": -1789195842}, {"key": "androidx/compose/foundation/OverscrollEffect.class", "name": "androidx/compose/foundation/OverscrollEffect.class", "size": 3199, "crc": 586179285}, {"key": "androidx/compose/foundation/OverscrollFactory.class", "name": "androidx/compose/foundation/OverscrollFactory.class", "size": 871, "crc": 509464292}, {"key": "androidx/compose/foundation/OverscrollKt.class", "name": "androidx/compose/foundation/OverscrollKt.class", "size": 7114, "crc": 815104511}, {"key": "androidx/compose/foundation/OverscrollModifierElement.class", "name": "androidx/compose/foundation/OverscrollModifierElement.class", "size": 3475, "crc": 1578168351}, {"key": "androidx/compose/foundation/OverscrollModifierNode.class", "name": "androidx/compose/foundation/OverscrollModifierNode.class", "size": 2684, "crc": -239001558}, {"key": "androidx/compose/foundation/PlatformMagnifier.class", "name": "androidx/compose/foundation/PlatformMagnifier.class", "size": 831, "crc": 481885990}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "size": 1883, "crc": -1538879597}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory.class", "size": 1622, "crc": 1305891639}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "size": 4392, "crc": 962033855}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "size": 2704, "crc": 208936955}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "size": 4607, "crc": -1929099998}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "size": 5896, "crc": -918898683}, {"key": "androidx/compose/foundation/PreferKeepClearElement.class", "name": "androidx/compose/foundation/PreferKeepClearElement.class", "size": 3813, "crc": -1168712287}, {"key": "androidx/compose/foundation/PreferKeepClearNode.class", "name": "androidx/compose/foundation/PreferKeepClearNode.class", "size": 3552, "crc": -983323257}, {"key": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "name": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "size": 2962, "crc": -1371588254}, {"key": "androidx/compose/foundation/ProgressSemanticsKt.class", "name": "androidx/compose/foundation/ProgressSemanticsKt.class", "size": 3944, "crc": 2023179925}, {"key": "androidx/compose/foundation/RectListNode.class", "name": "androidx/compose/foundation/RectListNode.class", "size": 9377, "crc": 544064244}, {"key": "androidx/compose/foundation/ScrollKt.class", "name": "androidx/compose/foundation/ScrollKt.class", "size": 9089, "crc": 62001675}, {"key": "androidx/compose/foundation/ScrollNode.class", "name": "androidx/compose/foundation/ScrollNode.class", "size": 10298, "crc": -354263417}, {"key": "androidx/compose/foundation/ScrollState$Companion.class", "name": "androidx/compose/foundation/ScrollState$Companion.class", "size": 1320, "crc": -2030719968}, {"key": "androidx/compose/foundation/ScrollState.class", "name": "androidx/compose/foundation/ScrollState.class", "size": 13765, "crc": -1202095090}, {"key": "androidx/compose/foundation/ScrollingContainerElement.class", "name": "androidx/compose/foundation/ScrollingContainerElement.class", "size": 6141, "crc": -64307782}, {"key": "androidx/compose/foundation/ScrollingContainerKt.class", "name": "androidx/compose/foundation/ScrollingContainerKt.class", "size": 3287, "crc": 229205928}, {"key": "androidx/compose/foundation/ScrollingContainerNode.class", "name": "androidx/compose/foundation/ScrollingContainerNode.class", "size": 10372, "crc": 1009058568}, {"key": "androidx/compose/foundation/ScrollingLayoutElement.class", "name": "androidx/compose/foundation/ScrollingLayoutElement.class", "size": 4073, "crc": -131413211}, {"key": "androidx/compose/foundation/StretchOverscrollNode.class", "name": "androidx/compose/foundation/StretchOverscrollNode.class", "size": 14876, "crc": -1213473600}, {"key": "androidx/compose/foundation/SurfaceCoroutineScope.class", "name": "androidx/compose/foundation/SurfaceCoroutineScope.class", "size": 615, "crc": 1921278495}, {"key": "androidx/compose/foundation/SurfaceScope.class", "name": "androidx/compose/foundation/SurfaceScope.class", "size": 1306, "crc": 1403300417}, {"key": "androidx/compose/foundation/SystemGestureExclusionKt.class", "name": "androidx/compose/foundation/SystemGestureExclusionKt.class", "size": 3105, "crc": 636408691}, {"key": "androidx/compose/foundation/VerticalScrollableClipShape.class", "name": "androidx/compose/foundation/VerticalScrollableClipShape.class", "size": 3848, "crc": -90909604}, {"key": "androidx/compose/foundation/WrappedOverscrollEffect$node$1.class", "name": "androidx/compose/foundation/WrappedOverscrollEffect$node$1.class", "size": 832, "crc": 264910214}, {"key": "androidx/compose/foundation/WrappedOverscrollEffect.class", "name": "androidx/compose/foundation/WrappedOverscrollEffect.class", "size": 4600, "crc": -1439088918}, {"key": "androidx/compose/foundation/content/MediaType$Companion.class", "name": "androidx/compose/foundation/content/MediaType$Companion.class", "size": 1798, "crc": 1943327730}, {"key": "androidx/compose/foundation/content/MediaType.class", "name": "androidx/compose/foundation/content/MediaType.class", "size": 2883, "crc": 907452954}, {"key": "androidx/compose/foundation/content/PlatformTransferableContent.class", "name": "androidx/compose/foundation/content/PlatformTransferableContent.class", "size": 2538, "crc": 513987771}, {"key": "androidx/compose/foundation/content/ReceiveContentElement.class", "name": "androidx/compose/foundation/content/ReceiveContentElement.class", "size": 4299, "crc": -847888206}, {"key": "androidx/compose/foundation/content/ReceiveContentKt.class", "name": "androidx/compose/foundation/content/ReceiveContentKt.class", "size": 1249, "crc": -455772997}, {"key": "androidx/compose/foundation/content/ReceiveContentListener.class", "name": "androidx/compose/foundation/content/ReceiveContentListener.class", "size": 1352, "crc": 253066510}, {"key": "androidx/compose/foundation/content/ReceiveContentNode.class", "name": "androidx/compose/foundation/content/ReceiveContentNode.class", "size": 4672, "crc": 838178257}, {"key": "androidx/compose/foundation/content/TransferableContent$Source$Companion.class", "name": "androidx/compose/foundation/content/TransferableContent$Source$Companion.class", "size": 1559, "crc": -71026888}, {"key": "androidx/compose/foundation/content/TransferableContent$Source.class", "name": "androidx/compose/foundation/content/TransferableContent$Source.class", "size": 3240, "crc": 2115968217}, {"key": "androidx/compose/foundation/content/TransferableContent.class", "name": "androidx/compose/foundation/content/TransferableContent.class", "size": 3087, "crc": 44747793}, {"key": "androidx/compose/foundation/content/TransferableContent_androidKt.class", "name": "androidx/compose/foundation/content/TransferableContent_androidKt.class", "size": 6712, "crc": -1303546183}, {"key": "androidx/compose/foundation/content/internal/DragAndDropRequestPermission_androidKt.class", "name": "androidx/compose/foundation/content/internal/DragAndDropRequestPermission_androidKt.class", "size": 3317, "crc": 1021383379}, {"key": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration$receiveContentListener$1.class", "name": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration$receiveContentListener$1.class", "size": 3037, "crc": 2079934821}, {"key": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration.class", "name": "androidx/compose/foundation/content/internal/DynamicReceiveContentConfiguration.class", "size": 2845, "crc": 390248115}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration$Companion.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration$Companion.class", "size": 1675, "crc": -934303004}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfiguration.class", "size": 2184, "crc": 1076175149}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationImpl.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationImpl.class", "size": 2910, "crc": -1250774232}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentConfigurationKt.class", "size": 2973, "crc": -2109600732}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$2.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt$ReceiveContentDragAndDropNode$2.class", "size": 3672, "crc": 1925011708}, {"key": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt.class", "name": "androidx/compose/foundation/content/internal/ReceiveContentDragAndDropNode_androidKt.class", "size": 4530, "crc": 2096923128}, {"key": "androidx/compose/foundation/contextmenu/ComposableSingletons$ContextMenuUi_androidKt$lambda$-355168742$1.class", "name": "androidx/compose/foundation/contextmenu/ComposableSingletons$ContextMenuUi_androidKt$lambda$-355168742$1.class", "size": 4000, "crc": 341834118}, {"key": "androidx/compose/foundation/contextmenu/ComposableSingletons$ContextMenuUi_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ComposableSingletons$ContextMenuUi_androidKt.class", "size": 1760, "crc": 42560295}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuArea_androidKt.class", "size": 19365, "crc": 1604558354}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuColors.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuColors.class", "size": 3447, "crc": 752976321}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt$contextMenuGestures$2.class", "size": 2100, "crc": -**********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuGestures_androidKt.class", "size": 3435, "crc": -**********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuKey.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuKey.class", "size": 746, "crc": **********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProvider.class", "size": 7212, "crc": 598750418}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProviderKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuPopupPositionProviderKt.class", "size": 2219, "crc": -252184671}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope$item$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope$item$1.class", "size": 5806, "crc": -**********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuScope.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuScope.class", "size": 8581, "crc": 51293471}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuSpec.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuSpec.class", "size": 7401, "crc": 807357279}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Closed.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Closed.class", "size": 1305, "crc": **********}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Open.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status$Open.class", "size": 4135, "crc": -1063463299}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState$Status.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState$Status.class", "size": 1425, "crc": 773076672}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState.class", "size": 4449, "crc": 470907041}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuState_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuState_androidKt.class", "size": 1472, "crc": -2085252383}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuColumnBuilder$1.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuColumnBuilder$1.class", "size": 5634, "crc": -163784672}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt$ContextMenuPopup$2.class", "size": 3341, "crc": -1563441105}, {"key": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt.class", "name": "androidx/compose/foundation/contextmenu/ContextMenuUi_androidKt.class", "size": 39673, "crc": 1674113630}, {"key": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "name": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "size": 1566, "crc": 788345959}, {"key": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt__LegacyDragAndDropSourceWithDefaultPainter_androidKt.class", "name": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt__LegacyDragAndDropSourceWithDefaultPainter_androidKt.class", "size": 2176, "crc": -1680160454}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "size": 5090, "crc": 2113990958}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults$DefaultStartDetector$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults$DefaultStartDetector$1.class", "size": 4660, "crc": -441361862}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceDefaults.class", "size": 2032, "crc": 926341831}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "size": 6108, "crc": -2134170185}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "size": 2720, "crc": 1454497355}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__DragAndDropSourceKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__DragAndDropSourceKt.class", "size": 3136, "crc": -456776011}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__LegacyDragAndDropSource_androidKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt__LegacyDragAndDropSource_androidKt.class", "size": 2472, "crc": 1695755715}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1$1.class", "size": 6534, "crc": 615482108}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$onAttach$1.class", "size": 2002, "crc": 1974499464}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "size": 9687, "crc": -1953516563}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "size": 1132, "crc": -1686237677}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "size": 6012, "crc": 797678769}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropStartDetectorScope.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropStartDetectorScope.class", "size": 1484, "crc": -290795812}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "size": 1739, "crc": 1681923788}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "size": 4700, "crc": 2084742561}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "size": 1774, "crc": -1928899760}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "size": 6549, "crc": -1554085836}, {"key": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "name": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "size": 4358, "crc": 378664126}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceElement.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceElement.class", "size": 5267, "crc": -1261800899}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1$1.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1$1.class", "size": 6834, "crc": -1388888243}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode$1.class", "size": 2337, "crc": 943044666}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceNode.class", "size": 5249, "crc": -758203901}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceWithDefaultShadowElement.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragAndDropSourceWithDefaultShadowElement.class", "size": 4525, "crc": 1069739932}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$2.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$2.class", "size": 3742, "crc": 2005836791}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "size": 1792, "crc": -1365271677}, {"key": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter.class", "name": "androidx/compose/foundation/draganddrop/LegacyDragSourceNodeWithDefaultPainter.class", "size": 4674, "crc": 408101140}, {"key": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "name": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "size": 972, "crc": -2083548921}, {"key": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "name": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "size": 954, "crc": **********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableDefaults.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableDefaults.class", "size": 8420, "crc": **********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableElement.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableElement.class", "size": 7127, "crc": -871804527}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$AnchoredDraggableLayoutInfoProvider$1.class", "size": 2904, "crc": **********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$NoOpDecayAnimationSpec$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$NoOpDecayAnimationSpec$1.class", "size": 1677, "crc": 97362807}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$4.class", "size": 4725, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$1.class", "size": 1917, "crc": 494320807}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateToWithDecay$2.class", "size": 9907, "crc": **********}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "size": 1716, "crc": -2109111021}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4057, "crc": -789683329}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 1991, "crc": -1619383386}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "size": 4313, "crc": -520511513}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "size": 4465, "crc": -905361692}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "size": 3838, "crc": 291264}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "size": 36689, "crc": 1776491584}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$drag$2.class", "size": 7648, "crc": 752861736}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$1.class", "size": 2069, "crc": -1778946677}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2$scrollScope$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2$scrollScope$1.class", "size": 2250, "crc": -1818606612}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$fling$2.class", "size": 5007, "crc": -666002834}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1$1.class", "size": 4756, "crc": 1714829255}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode$onDragStopped$1.class", "size": 4676, "crc": 363472785}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableNode.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableNode.class", "size": 16893, "crc": -1872280962}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "size": 8184, "crc": 1252920052}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4603, "crc": -797353722}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "size": 5792, "crc": -1940319680}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "size": 2010, "crc": -941013520}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4796, "crc": -697159696}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "size": 6096, "crc": -121121751}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "size": 4472, "crc": -692309494}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "size": 28990, "crc": 2142168347}, {"key": "androidx/compose/foundation/gestures/AndroidConfig.class", "name": "androidx/compose/foundation/gestures/AndroidConfig.class", "size": 7096, "crc": 1252940040}, {"key": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "name": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "size": 1476, "crc": 1438833891}, {"key": "androidx/compose/foundation/gestures/AnimationData.class", "name": "androidx/compose/foundation/gestures/AnimationData.class", "size": 3375, "crc": -732576516}, {"key": "androidx/compose/foundation/gestures/AnimationDataConverter.class", "name": "androidx/compose/foundation/gestures/AnimationDataConverter.class", "size": 5228, "crc": -1122848423}, {"key": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "name": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "size": 10649, "crc": 2006505721}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "size": 896, "crc": 2130770186}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "size": 2579, "crc": 461821050}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "size": 1825, "crc": -896697503}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$PivotBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt$PivotBringIntoViewSpec$1.class", "size": 1922, "crc": 2024773967}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec_androidKt.class", "size": 3655, "crc": -937632180}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "size": 4346, "crc": -173239819}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "size": 869, "crc": 1623624136}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "size": 11476, "crc": 1603901785}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "size": 5899, "crc": 1459498504}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode.class", "size": 22365, "crc": 1953660064}, {"key": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "name": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "size": 665, "crc": -587076113}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "size": 4509, "crc": -1877080725}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "size": 1543, "crc": -1198748568}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "size": 4474, "crc": -1537012606}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableAnchors.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableAnchors.class", "size": 7921, "crc": -1746076376}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "size": 4479, "crc": -1263537309}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "size": 1410, "crc": -1347597044}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "size": 4256, "crc": 2123346273}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "size": 6736, "crc": 354127747}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "size": 4143, "crc": -955141318}, {"key": "androidx/compose/foundation/gestures/DefaultScrollable2DState$scroll$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollable2DState$scroll$2$1.class", "size": 4358, "crc": -663070861}, {"key": "androidx/compose/foundation/gestures/DefaultScrollable2DState$scroll$2.class", "name": "androidx/compose/foundation/gestures/DefaultScrollable2DState$scroll$2.class", "size": 4787, "crc": -1871162566}, {"key": "androidx/compose/foundation/gestures/DefaultScrollable2DState$scrollScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollable2DState$scrollScope$1.class", "size": 3523, "crc": -663993777}, {"key": "androidx/compose/foundation/gestures/DefaultScrollable2DState.class", "name": "androidx/compose/foundation/gestures/DefaultScrollable2DState.class", "size": 5688, "crc": 1027365870}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "size": 4322, "crc": 1792664972}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "size": 4755, "crc": 1221083693}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "size": 2024, "crc": -2002409842}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "size": 5991, "crc": 1545348200}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "size": 4394, "crc": -1073644351}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "size": 4830, "crc": 1834820123}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "size": 1791, "crc": -5556007}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "size": 5444, "crc": 977496216}, {"key": "androidx/compose/foundation/gestures/DelegatingAnimationSpec$vectorize$1.class", "name": "androidx/compose/foundation/gestures/DelegatingAnimationSpec$vectorize$1.class", "size": 10112, "crc": -2048447311}, {"key": "androidx/compose/foundation/gestures/DelegatingAnimationSpec.class", "name": "androidx/compose/foundation/gestures/DelegatingAnimationSpec.class", "size": 3902, "crc": 370949817}, {"key": "androidx/compose/foundation/gestures/Drag2DScope.class", "name": "androidx/compose/foundation/gestures/Drag2DScope.class", "size": 564, "crc": 1944938871}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "size": 1030, "crc": 847943572}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "size": 1349, "crc": 308995765}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "size": 1365, "crc": 486280154}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "size": 1359, "crc": 1653187290}, {"key": "androidx/compose/foundation/gestures/DragEvent.class", "name": "androidx/compose/foundation/gestures/DragEvent.class", "size": 1587, "crc": -2125229800}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitAllPointersUpWithSlopDetection$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitAllPointersUpWithSlopDetection$1.class", "size": 2073, "crc": 376214226}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "size": 1766, "crc": -521591008}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "size": 1806, "crc": 42796785}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2127, "crc": 1437651539}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "size": 2115, "crc": -618464918}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "size": 1798, "crc": -2069381391}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "size": 11250, "crc": 1273523787}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitPointerSlopOrCancellation$1.class", "size": 2114, "crc": 1648319572}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "size": 2075, "crc": 834766065}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "size": 1798, "crc": -102829288}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "size": 2119, "crc": -1662498045}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "size": 2107, "crc": -1570121042}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$13.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$13.class", "size": 29774, "crc": -70317918}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "size": 9032, "crc": -22612433}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "size": 9440, "crc": 1063139691}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "size": 9426, "crc": 1662146489}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "size": 1707, "crc": 1186444709}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "size": 1963, "crc": 1477727616}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "size": 1891, "crc": 1360159448}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "size": 1883, "crc": 311856116}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "size": 93912, "crc": 546179459}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1$1.class", "size": 7059, "crc": 1532753430}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$initializePointerInputNode$1.class", "size": 10645, "crc": -1210674757}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragCancel$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragCancel$1.class", "size": 1928, "crc": 313184151}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragStart$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragStart$1.class", "size": 2272, "crc": -2005880598}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$processDragStop$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$processDragStop$1.class", "size": 2208, "crc": 1341208137}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1$1.class", "size": 5026, "crc": -1140656110}, {"key": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1.class", "name": "androidx/compose/foundation/gestures/DragGestureNode$startListeningForEvents$1.class", "size": 6552, "crc": 1326149663}, {"key": "androidx/compose/foundation/gestures/DragGestureNode.class", "name": "androidx/compose/foundation/gestures/DragGestureNode.class", "size": 18247, "crc": -533634475}, {"key": "androidx/compose/foundation/gestures/DragScope.class", "name": "androidx/compose/foundation/gestures/DragScope.class", "size": 486, "crc": 727360465}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement$Companion.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement$Companion.class", "size": 1391, "crc": -580095976}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement.class", "size": 7151, "crc": -41920720}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt.class", "size": 8266, "crc": -213765603}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "size": 6463, "crc": 1198773704}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode.class", "size": 7627, "crc": 1331546840}, {"key": "androidx/compose/foundation/gestures/Draggable2DState.class", "name": "androidx/compose/foundation/gestures/Draggable2DState.class", "size": 2150, "crc": 1611717168}, {"key": "androidx/compose/foundation/gestures/DraggableAnchors.class", "name": "androidx/compose/foundation/gestures/DraggableAnchors.class", "size": 1362, "crc": 145687969}, {"key": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "name": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "size": 2851, "crc": 1752779664}, {"key": "androidx/compose/foundation/gestures/DraggableElement$Companion.class", "name": "androidx/compose/foundation/gestures/DraggableElement$Companion.class", "size": 1383, "crc": -1285040030}, {"key": "androidx/compose/foundation/gestures/DraggableElement.class", "name": "androidx/compose/foundation/gestures/DraggableElement.class", "size": 8135, "crc": -731894303}, {"key": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStarted$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStarted$1.class", "size": 2821, "crc": 261855395}, {"key": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStopped$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$NoOpOnDragStopped$1.class", "size": 2724, "crc": 1680930044}, {"key": "androidx/compose/foundation/gestures/DraggableKt.class", "name": "androidx/compose/foundation/gestures/DraggableKt.class", "size": 11515, "crc": 2074684026}, {"key": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "name": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "size": 5460, "crc": 1371260906}, {"key": "androidx/compose/foundation/gestures/DraggableNode$onDragStarted$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$onDragStarted$1.class", "size": 3699, "crc": 632389699}, {"key": "androidx/compose/foundation/gestures/DraggableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$onDragStopped$1.class", "size": 4082, "crc": -709584561}, {"key": "androidx/compose/foundation/gestures/DraggableNode.class", "name": "androidx/compose/foundation/gestures/DraggableNode.class", "size": 9374, "crc": 291157844}, {"key": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "size": 680, "crc": 1788601235}, {"key": "androidx/compose/foundation/gestures/DraggableState.class", "name": "androidx/compose/foundation/gestures/DraggableState.class", "size": 2172, "crc": 1037586032}, {"key": "androidx/compose/foundation/gestures/ExperimentalTapGestureDetectorBehaviorApi.class", "name": "androidx/compose/foundation/gestures/ExperimentalTapGestureDetectorBehaviorApi.class", "size": 1408, "crc": 1397936803}, {"key": "androidx/compose/foundation/gestures/FlingBehavior.class", "name": "androidx/compose/foundation/gestures/FlingBehavior.class", "size": 1051, "crc": -1524487987}, {"key": "androidx/compose/foundation/gestures/FlingCancellationException.class", "name": "androidx/compose/foundation/gestures/FlingCancellationException.class", "size": 976, "crc": 1392743272}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "size": 3566, "crc": 768003853}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "size": 1744, "crc": -515122305}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "size": 4920, "crc": -112673969}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "size": 1810, "crc": 1840175910}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "size": 10062, "crc": 593396820}, {"key": "androidx/compose/foundation/gestures/GestureCancellationException.class", "name": "androidx/compose/foundation/gestures/GestureCancellationException.class", "size": 1317, "crc": -1457898937}, {"key": "androidx/compose/foundation/gestures/LongPressResult$Canceled.class", "name": "androidx/compose/foundation/gestures/LongPressResult$Canceled.class", "size": 1048, "crc": -1315161992}, {"key": "androidx/compose/foundation/gestures/LongPressResult$Released.class", "name": "androidx/compose/foundation/gestures/LongPressResult$Released.class", "size": 1412, "crc": -585456436}, {"key": "androidx/compose/foundation/gestures/LongPressResult$Success.class", "name": "androidx/compose/foundation/gestures/LongPressResult$Success.class", "size": 1045, "crc": 1669379719}, {"key": "androidx/compose/foundation/gestures/LongPressResult.class", "name": "androidx/compose/foundation/gestures/LongPressResult.class", "size": 1453, "crc": 1747226852}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollableKt.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollableKt.class", "size": 2346, "crc": 1811338888}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$MouseWheelScrollDelta.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$MouseWheelScrollDelta.class", "size": 4209, "crc": 791515419}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2$job$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2$job$1.class", "size": 4106, "crc": 670640728}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$busyReceive$2.class", "size": 4957, "crc": 1107309488}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$1.class", "size": 2596, "crc": -2099706057}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$3.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$3.class", "size": 10520, "crc": -1416626868}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$1.class", "size": 2341, "crc": -568405243}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$2.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$dispatchMouseWheelScroll$waitNextScrollDelta$2.class", "size": 4542, "crc": 1769806081}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$startReceivingMouseWheelEvents$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$startReceivingMouseWheelEvents$1.class", "size": 6631, "crc": -193015715}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$untilNull$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$untilNull$1.class", "size": 4483, "crc": -2043384450}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$1.class", "size": 2157, "crc": -62294031}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$2.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic$userScroll$2.class", "size": 4165, "crc": -186789789}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollingLogic.class", "size": 30416, "crc": -1673792848}, {"key": "androidx/compose/foundation/gestures/MouseWheelVelocityTracker.class", "name": "androidx/compose/foundation/gestures/MouseWheelVelocityTracker.class", "size": 3320, "crc": 183627311}, {"key": "androidx/compose/foundation/gestures/NestedScrollScope.class", "name": "androidx/compose/foundation/gestures/NestedScrollScope.class", "size": 769, "crc": 1037638439}, {"key": "androidx/compose/foundation/gestures/OnScrollChangedDispatcher.class", "name": "androidx/compose/foundation/gestures/OnScrollChangedDispatcher.class", "size": 624, "crc": -816850672}, {"key": "androidx/compose/foundation/gestures/Orientation.class", "name": "androidx/compose/foundation/gestures/Orientation.class", "size": 1936, "crc": 1482433823}, {"key": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "size": 3546, "crc": -1088306653}, {"key": "androidx/compose/foundation/gestures/PressGestureScope.class", "name": "androidx/compose/foundation/gestures/PressGestureScope.class", "size": 3573, "crc": 300892468}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "size": 1824, "crc": 1957005036}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "size": 1796, "crc": 665713619}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "size": 1836, "crc": 1724462348}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "size": 7493, "crc": 745357386}, {"key": "androidx/compose/foundation/gestures/Scroll2DScope.class", "name": "androidx/compose/foundation/gestures/Scroll2DScope.class", "size": 567, "crc": 1801699886}, {"key": "androidx/compose/foundation/gestures/ScrollConfig.class", "name": "androidx/compose/foundation/gestures/ScrollConfig.class", "size": 1312, "crc": -673544281}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "size": 1696, "crc": -2056743733}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "size": 4991, "crc": -377971505}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$3.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$3.class", "size": 1706, "crc": -623496098}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$4.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$4.class", "size": 5876, "crc": 342836542}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "size": 1615, "crc": 1467135503}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "size": 3495, "crc": 574682193}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$3.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$3.class", "size": 1625, "crc": -221324154}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$4.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$4.class", "size": 3530, "crc": 1546145610}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "size": 3138, "crc": 1585473067}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$4.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$4.class", "size": 3150, "crc": -291779226}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "size": 10296, "crc": -169027549}, {"key": "androidx/compose/foundation/gestures/ScrollLogic.class", "name": "androidx/compose/foundation/gestures/ScrollLogic.class", "size": 1137, "crc": -388338293}, {"key": "androidx/compose/foundation/gestures/ScrollScope.class", "name": "androidx/compose/foundation/gestures/ScrollScope.class", "size": 489, "crc": -47981120}, {"key": "androidx/compose/foundation/gestures/Scrollable2DElement.class", "name": "androidx/compose/foundation/gestures/Scrollable2DElement.class", "size": 5578, "crc": -1769996446}, {"key": "androidx/compose/foundation/gestures/Scrollable2DKt$NoOpScrollScope$1.class", "name": "androidx/compose/foundation/gestures/Scrollable2DKt$NoOpScrollScope$1.class", "size": 1012, "crc": -1624394069}, {"key": "androidx/compose/foundation/gestures/Scrollable2DKt$semanticsScrollBy$1.class", "name": "androidx/compose/foundation/gestures/Scrollable2DKt$semanticsScrollBy$1.class", "size": 1681, "crc": -110456744}, {"key": "androidx/compose/foundation/gestures/Scrollable2DKt$semanticsScrollBy$2.class", "name": "androidx/compose/foundation/gestures/Scrollable2DKt$semanticsScrollBy$2.class", "size": 5832, "crc": 992159184}, {"key": "androidx/compose/foundation/gestures/Scrollable2DKt.class", "name": "androidx/compose/foundation/gestures/Scrollable2DKt.class", "size": 6437, "crc": 127468142}, {"key": "androidx/compose/foundation/gestures/Scrollable2DNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/Scrollable2DNode$drag$2$1.class", "size": 5107, "crc": 2000240059}, {"key": "androidx/compose/foundation/gestures/Scrollable2DNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/Scrollable2DNode$onDragStopped$1.class", "size": 3601, "crc": -1890137029}, {"key": "androidx/compose/foundation/gestures/Scrollable2DNode$setScrollSemanticsActions$1$1.class", "name": "androidx/compose/foundation/gestures/Scrollable2DNode$setScrollSemanticsActions$1$1.class", "size": 5157, "crc": -363932782}, {"key": "androidx/compose/foundation/gestures/Scrollable2DNode$setScrollSemanticsActions$2.class", "name": "androidx/compose/foundation/gestures/Scrollable2DNode$setScrollSemanticsActions$2.class", "size": 3822, "crc": -2119661258}, {"key": "androidx/compose/foundation/gestures/Scrollable2DNode.class", "name": "androidx/compose/foundation/gestures/Scrollable2DNode.class", "size": 16539, "crc": -863594838}, {"key": "androidx/compose/foundation/gestures/Scrollable2DState.class", "name": "androidx/compose/foundation/gestures/Scrollable2DState.class", "size": 2339, "crc": 1145236686}, {"key": "androidx/compose/foundation/gestures/Scrollable2DStateKt.class", "name": "androidx/compose/foundation/gestures/Scrollable2DStateKt.class", "size": 5331, "crc": -370808754}, {"key": "androidx/compose/foundation/gestures/ScrollableContainerNode$TraverseKey.class", "name": "androidx/compose/foundation/gestures/ScrollableContainerNode$TraverseKey.class", "size": 901, "crc": 499593074}, {"key": "androidx/compose/foundation/gestures/ScrollableContainerNode.class", "name": "androidx/compose/foundation/gestures/ScrollableContainerNode.class", "size": 1887, "crc": 776436368}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaultFlingBehavior.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaultFlingBehavior.class", "size": 921, "crc": -219950712}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect$node$1.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect$node$1.class", "size": 1002, "crc": -1407616465}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults$NoOpOverscrollEffect.class", "size": 3616, "crc": -737178323}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "size": 6101, "crc": -1257341468}, {"key": "androidx/compose/foundation/gestures/ScrollableElement.class", "name": "androidx/compose/foundation/gestures/ScrollableElement.class", "size": 7040, "crc": -621340978}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "size": 2889, "crc": 1712375071}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "size": 931, "crc": -200164780}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "size": 1005, "crc": 1478014053}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$1.class", "size": 1730, "crc": 308990824}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$semanticsScrollBy$2.class", "size": 5579, "crc": 1391760397}, {"key": "androidx/compose/foundation/gestures/ScrollableKt.class", "name": "androidx/compose/foundation/gestures/ScrollableKt.class", "size": 10332, "crc": 1377313909}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "size": 1951, "crc": -772846978}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "size": 4388, "crc": 1216834239}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$drag$2$1.class", "size": 5548, "crc": -736879088}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$ensureMouseWheelScrollNodeInitialized$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$ensureMouseWheelScrollNodeInitialized$1.class", "size": 2048, "crc": -2000345707}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onDragStopped$1.class", "size": 3579, "crc": -785194505}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "size": 3458, "crc": 1047235766}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1.class", "size": 3906, "crc": -1339647285}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onWheelScrollStopped$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onWheelScrollStopped$1.class", "size": 3614, "crc": 1443534375}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$1$1.class", "size": 5121, "crc": -343009824}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$2.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$setScrollSemanticsActions$2.class", "size": 3796, "crc": 323191562}, {"key": "androidx/compose/foundation/gestures/ScrollableNode.class", "name": "androidx/compose/foundation/gestures/ScrollableNode.class", "size": 25500, "crc": -232656886}, {"key": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "size": 1592, "crc": 798422405}, {"key": "androidx/compose/foundation/gestures/ScrollableState.class", "name": "androidx/compose/foundation/gestures/ScrollableState.class", "size": 3317, "crc": -1662819360}, {"key": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "name": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "size": 5128, "crc": -2034064243}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "size": 1809, "crc": 375773696}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$reverseScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$reverseScope$1.class", "size": 2797, "crc": 380591105}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "size": 5495, "crc": 686819318}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$nestedScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$nestedScrollScope$1.class", "size": 3983, "crc": 1254848092}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$onScrollStopped$performFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$onScrollStopped$performFling$1.class", "size": 4605, "crc": -1874397422}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$scroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$scroll$2.class", "size": 4310, "crc": 479680398}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic.class", "size": 21926, "crc": -1949365428}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic2D$doFlingAnimation$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic2D$doFlingAnimation$1.class", "size": 1827, "crc": 1378455422}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic2D$doFlingAnimation$2$flingScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic2D$doFlingAnimation$2$flingScope$1.class", "size": 3092, "crc": 1501128111}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic2D$doFlingAnimation$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic2D$doFlingAnimation$2.class", "size": 5362, "crc": -760670014}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic2D$nestedScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic2D$nestedScrollScope$1.class", "size": 3859, "crc": 1567688251}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic2D$onScrollStopped$performFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic2D$onScrollStopped$performFling$1.class", "size": 4568, "crc": -2046676335}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic2D$scroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic2D$scroll$2.class", "size": 4348, "crc": -461147031}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic2D.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic2D.class", "size": 19292, "crc": -1898417445}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "size": 2968, "crc": 1486901417}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "size": 1797, "crc": 576167517}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitPrimaryFirstDown$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitPrimaryFirstDown$1.class", "size": 1832, "crc": 1939341623}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "size": 4457, "crc": -1807688051}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "size": 1694, "crc": 151963349}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "size": 4448, "crc": 1070968415}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "size": 3403, "crc": 1514763837}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "size": 3400, "crc": -1224258857}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$resetJob$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$resetJob$1.class", "size": 3557, "crc": -2002797512}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "size": 8074, "crc": -2068281667}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "size": 5117, "crc": -437934991}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "size": 4448, "crc": -2103229999}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "size": 3400, "crc": -1577388789}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "size": 3403, "crc": -856376879}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "size": 3400, "crc": -1796401115}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "size": 3756, "crc": 1998791967}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "size": 4454, "crc": -53113296}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "size": 3400, "crc": 1580381103}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "size": 3399, "crc": -1979546721}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$resetJob$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$resetJob$1.class", "size": 3557, "crc": 1972863492}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$secondUp$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$secondUp$1.class", "size": 3436, "crc": 1739177429}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "size": 11929, "crc": 403133282}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "size": 5633, "crc": 1674311868}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$launchAwaitingReset$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$launchAwaitingReset$1.class", "size": 4162, "crc": 320037221}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$1.class", "size": 1703, "crc": -1326586501}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForLongPress$2.class", "size": 9042, "crc": 324948538}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "size": 1824, "crc": -617576046}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "size": 26114, "crc": 994500421}, {"key": "androidx/compose/foundation/gestures/TapGestureDetector_androidKt.class", "name": "androidx/compose/foundation/gestures/TapGestureDetector_androidKt.class", "size": 962, "crc": -676163927}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehavior.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehavior.class", "size": 2560, "crc": 1847879885}, {"key": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt.class", "name": "androidx/compose/foundation/gestures/TargetedFlingBehaviorKt.class", "size": 1454, "crc": 2012213420}, {"key": "androidx/compose/foundation/gestures/TouchSlopDetector.class", "name": "androidx/compose/foundation/gestures/TouchSlopDetector.class", "size": 6850, "crc": 753589505}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "size": 1778, "crc": 682892786}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "size": 1063, "crc": -1902702743}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "size": 1063, "crc": -1385145439}, {"key": "androidx/compose/foundation/gestures/TransformEvent.class", "name": "androidx/compose/foundation/gestures/TransformEvent.class", "size": 1333, "crc": -1038291390}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "size": 11826, "crc": 354135971}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "size": 10511, "crc": 99459110}, {"key": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "size": 583, "crc": -1951901445}, {"key": "androidx/compose/foundation/gestures/TransformScope.class", "name": "androidx/compose/foundation/gestures/TransformScope.class", "size": 1465, "crc": 602126327}, {"key": "androidx/compose/foundation/gestures/TransformableElement.class", "name": "androidx/compose/foundation/gestures/TransformableElement.class", "size": 4251, "crc": -1344301746}, {"key": "androidx/compose/foundation/gestures/TransformableKt$awaitCtrlMouseScrollOrNull$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$awaitCtrlMouseScrollOrNull$1.class", "size": 1826, "crc": 184831992}, {"key": "androidx/compose/foundation/gestures/TransformableKt$awaitFirstCtrlMouseScroll$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$awaitFirstCtrlMouseScroll$1.class", "size": 1820, "crc": 49110113}, {"key": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "size": 2301, "crc": 1940040930}, {"key": "androidx/compose/foundation/gestures/TransformableKt$detectZoomByCtrlMouseScroll$2.class", "name": "androidx/compose/foundation/gestures/TransformableKt$detectZoomByCtrlMouseScroll$2.class", "size": 7611, "crc": 691076770}, {"key": "androidx/compose/foundation/gestures/TransformableKt.class", "name": "androidx/compose/foundation/gestures/TransformableKt.class", "size": 21201, "crc": 62866579}, {"key": "androidx/compose/foundation/gestures/TransformableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/gestures/TransformableNode$onPointerEvent$2.class", "size": 2271, "crc": -61980932}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "size": 5241, "crc": -76052972}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "size": 5330, "crc": -320873832}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "size": 5203, "crc": -1372551035}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "size": 4732, "crc": -1066395963}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "size": 2209, "crc": -1358519768}, {"key": "androidx/compose/foundation/gestures/TransformableNode.class", "name": "androidx/compose/foundation/gestures/TransformableNode.class", "size": 9520, "crc": 1389825166}, {"key": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "size": 706, "crc": 800814050}, {"key": "androidx/compose/foundation/gestures/TransformableState.class", "name": "androidx/compose/foundation/gestures/TransformableState.class", "size": 2228, "crc": -283707600}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateBy$3.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateBy$3.class", "size": 6639, "crc": 1230013680}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "size": 6215, "crc": 2059373819}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "size": 5745, "crc": 575959221}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "size": 5780, "crc": 2003004820}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "size": 3281, "crc": 1458989741}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "size": 3504, "crc": -439869648}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "size": 3208, "crc": 2037498328}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "size": 3497, "crc": -921125796}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt.class", "size": 16523, "crc": -1295179809}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "size": 1545, "crc": 1646446513}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "size": 2053, "crc": 260659320}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "size": 10168, "crc": 1046469032}, {"key": "androidx/compose/foundation/gestures/ViewConfigurationApi26Impl.class", "name": "androidx/compose/foundation/gestures/ViewConfigurationApi26Impl.class", "size": 1372, "crc": -1877777746}, {"key": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "name": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "size": 2032, "crc": -755844138}, {"key": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "size": 1685, "crc": 1113439279}, {"key": "androidx/compose/foundation/gestures/snapping/DecayApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/DecayApproachAnimation.class", "size": 4209, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "size": 1505, "crc": 857756316}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "size": 2881, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 6659, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "size": 8151, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 6401, "crc": -129160340}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "size": 7920, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 10032, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt.class", "size": 8181, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "size": 2199, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "size": 10118, "crc": -846382892}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$1.class", "size": 1949, "crc": 67440430}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "size": 2177, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "size": 13847, "crc": 1497839853}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "size": 1927, "crc": -825792604}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "size": 1992, "crc": 279416265}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "size": 20292, "crc": -255126607}, {"key": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "name": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "size": 764, "crc": 577358723}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$Center.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$Center.class", "size": 1536, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$End.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$End.class", "size": 1523, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition$Start.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition$Start.class", "size": 1481, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPosition.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPosition.class", "size": 1021, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPositionKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPositionKt.class", "size": 1436, "crc": 2094832672}, {"key": "androidx/compose/foundation/gestures/snapping/TargetApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/TargetApproachAnimation.class", "size": 4330, "crc": 2008798413}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "size": 1480, "crc": 2100002736}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "size": 916, "crc": 76767651}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "size": 1474, "crc": -502446255}, {"key": "androidx/compose/foundation/interaction/DragInteraction.class", "name": "androidx/compose/foundation/interaction/DragInteraction.class", "size": 831, "crc": 1862257882}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "size": 3118, "crc": -137160834}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "size": 4512, "crc": -1561998193}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt.class", "size": 4873, "crc": -485564192}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "size": 921, "crc": 432111999}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "size": 1492, "crc": -1507059186}, {"key": "androidx/compose/foundation/interaction/FocusInteraction.class", "name": "androidx/compose/foundation/interaction/FocusInteraction.class", "size": 753, "crc": -487609642}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "size": 3013, "crc": 2049185470}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "size": 4522, "crc": -1258030315}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "size": 4887, "crc": 1066166822}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "size": 921, "crc": -328920330}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "size": 1483, "crc": -661601962}, {"key": "androidx/compose/foundation/interaction/HoverInteraction.class", "name": "androidx/compose/foundation/interaction/HoverInteraction.class", "size": 747, "crc": -1225354618}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "size": 3007, "crc": 271927604}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "size": 4522, "crc": -1614052570}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "size": 4887, "crc": 22348135}, {"key": "androidx/compose/foundation/interaction/Interaction.class", "name": "androidx/compose/foundation/interaction/Interaction.class", "size": 421, "crc": 277124302}, {"key": "androidx/compose/foundation/interaction/InteractionSource.class", "name": "androidx/compose/foundation/interaction/InteractionSource.class", "size": 897, "crc": -182752648}, {"key": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "name": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "size": 908, "crc": 2022410403}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "size": 1318, "crc": 1534776844}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "size": 3030, "crc": 1518600875}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "size": 1489, "crc": -1276896134}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "size": 1371, "crc": 1392446811}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "size": 1492, "crc": 1203096578}, {"key": "androidx/compose/foundation/interaction/PressInteraction.class", "name": "androidx/compose/foundation/interaction/PressInteraction.class", "size": 843, "crc": 749924703}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "size": 3136, "crc": 1682181222}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "size": 4522, "crc": -1950438723}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt.class", "size": 4891, "crc": 1865277462}, {"key": "androidx/compose/foundation/internal/ClipboardUtils.class", "name": "androidx/compose/foundation/internal/ClipboardUtils.class", "size": 3142, "crc": -378106784}, {"key": "androidx/compose/foundation/internal/ClipboardUtils_androidKt.class", "name": "androidx/compose/foundation/internal/ClipboardUtils_androidKt.class", "size": 8843, "crc": -1155681676}, {"key": "androidx/compose/foundation/internal/DecodeHelper.class", "name": "androidx/compose/foundation/internal/DecodeHelper.class", "size": 10569, "crc": -188464785}, {"key": "androidx/compose/foundation/internal/EncodeHelper.class", "name": "androidx/compose/foundation/internal/EncodeHelper.class", "size": 10269, "crc": -1364685537}, {"key": "androidx/compose/foundation/internal/InlineClassHelperKt.class", "name": "androidx/compose/foundation/internal/InlineClassHelperKt.class", "size": 3656, "crc": -57678315}, {"key": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 455, "crc": -536673483}, {"key": "androidx/compose/foundation/internal/MutableSpanStyle.class", "name": "androidx/compose/foundation/internal/MutableSpanStyle.class", "size": 10152, "crc": -577397069}, {"key": "androidx/compose/foundation/internal/PlatformOptimizedCancellationException.class", "name": "androidx/compose/foundation/internal/PlatformOptimizedCancellationException.class", "size": 1808, "crc": 912983244}, {"key": "androidx/compose/foundation/internal/PlatformOptimizedCancellationException_jvmKt.class", "name": "androidx/compose/foundation/internal/PlatformOptimizedCancellationException_jvmKt.class", "size": 854, "crc": 1944843209}, {"key": "androidx/compose/foundation/lazy/DefaultLazyListPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/DefaultLazyListPrefetchStrategy.class", "size": 6068, "crc": -1860822610}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "size": 1183, "crc": -683829125}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "size": 1634, "crc": -1313957994}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "size": 1642, "crc": 1063608139}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "size": 3370, "crc": 1169867703}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "size": 1186, "crc": -2053639872}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "size": 1568, "crc": 347087546}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "size": 1576, "crc": 1081189454}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "size": 3281, "crc": 1714868057}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "size": 2158, "crc": -220347865}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "size": 2130, "crc": -633897074}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "size": 1280, "crc": -373276977}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "size": 1861, "crc": 337563805}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "size": 1869, "crc": 2021800605}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "size": 3597, "crc": 368941466}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "size": 1283, "crc": -562688787}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "size": 1795, "crc": -2027107257}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "size": 1803, "crc": 1428933853}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "size": 3507, "crc": 1761932001}, {"key": "androidx/compose/foundation/lazy/LazyDslKt.class", "name": "androidx/compose/foundation/lazy/LazyDslKt.class", "size": 45292, "crc": 500748249}, {"key": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "size": 2446, "crc": -91713707}, {"key": "androidx/compose/foundation/lazy/LazyItemScope.class", "name": "androidx/compose/foundation/lazy/LazyItemScope.class", "size": 4811, "crc": -830126140}, {"key": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "size": 4209, "crc": 1006724381}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 5209, "crc": 211416400}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "size": 1219, "crc": -2116363791}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "size": 3944, "crc": 1551603737}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "size": 3385, "crc": 1497077953}, {"key": "androidx/compose/foundation/lazy/LazyListCacheWindowScope.class", "name": "androidx/compose/foundation/lazy/LazyListCacheWindowScope.class", "size": 7786, "crc": 1364767975}, {"key": "androidx/compose/foundation/lazy/LazyListCacheWindowStrategy.class", "name": "androidx/compose/foundation/lazy/LazyListCacheWindowStrategy.class", "size": 5612, "crc": 807925676}, {"key": "androidx/compose/foundation/lazy/LazyListCacheWindowStrategyKt.class", "name": "androidx/compose/foundation/lazy/LazyListCacheWindowStrategyKt.class", "size": 442, "crc": 1974688890}, {"key": "androidx/compose/foundation/lazy/LazyListInterval.class", "name": "androidx/compose/foundation/lazy/LazyListInterval.class", "size": 3331, "crc": 1471451168}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "size": 3218, "crc": -1122399363}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$stickyHeader$1.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$stickyHeader$1.class", "size": 3265, "crc": -911808633}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "size": 8281, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "name": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "size": 989, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "size": 1226, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "size": 4724, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "size": 7312, "crc": 642945323}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "size": 1234, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "size": 7508, "crc": -647785856}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 4279, "crc": -357983003}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "size": 17580, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListKt.class", "name": "androidx/compose/foundation/lazy/LazyListKt.class", "size": 26184, "crc": -708105799}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "size": 1882, "crc": 145530151}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "size": 3386, "crc": 731490479}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfoKt.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfoKt.class", "size": 2650, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "size": 34232, "crc": 1291132633}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "size": 11241, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "size": 18530, "crc": 925977737}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "size": 412, "crc": 199492745}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "size": 5731, "crc": 644331275}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchResultScope.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchResultScope.class", "size": 780, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchResultScopeImpl.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchResultScopeImpl.class", "size": 1262, "crc": 511771564}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchScope.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchScope.class", "size": 2128, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchStrategy.class", "size": 2081, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListPrefetchStrategyKt.class", "name": "androidx/compose/foundation/lazy/LazyListPrefetchStrategyKt.class", "size": 1274, "crc": 528807685}, {"key": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "size": 4787, "crc": 1346770635}, {"key": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "name": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "size": 1178, "crc": -2101306942}, {"key": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$1.class", "name": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$1.class", "size": 3140, "crc": -1131646246}, {"key": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$2.class", "name": "androidx/compose/foundation/lazy/LazyListScope$stickyHeader$2.class", "size": 3129, "crc": 467432332}, {"key": "androidx/compose/foundation/lazy/LazyListScope.class", "name": "androidx/compose/foundation/lazy/LazyListScope.class", "size": 8553, "crc": -859239211}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "size": 7365, "crc": -1554281845}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "size": 513, "crc": 537962284}, {"key": "androidx/compose/foundation/lazy/LazyListScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/lazy/LazyListScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 5477, "crc": 514893671}, {"key": "androidx/compose/foundation/lazy/LazyListScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/LazyListScrollScopeKt.class", "size": 1339, "crc": -46039526}, {"key": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "name": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "size": 3791, "crc": -434511163}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "size": 5033, "crc": -188750284}, {"key": "androidx/compose/foundation/lazy/LazyListState$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$animateScrollToItem$2.class", "size": 4094, "crc": 168022786}, {"key": "androidx/compose/foundation/lazy/LazyListState$prefetchScope$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$prefetchScope$1.class", "size": 7754, "crc": 994749789}, {"key": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "size": 1427, "crc": -1460733200}, {"key": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1$1.class", "size": 2993, "crc": -1153268735}, {"key": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$requestScrollToItem$1.class", "size": 3639, "crc": -976778367}, {"key": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "size": 1856, "crc": 1547508510}, {"key": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "size": 3393, "crc": -637901354}, {"key": "androidx/compose/foundation/lazy/LazyListState.class", "name": "androidx/compose/foundation/lazy/LazyListState.class", "size": 33025, "crc": 1239484613}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "size": 1795, "crc": -1658453788}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt.class", "size": 12384, "crc": 57796070}, {"key": "androidx/compose/foundation/lazy/LazyList_androidKt.class", "name": "androidx/compose/foundation/lazy/LazyList_androidKt.class", "size": 1375, "crc": 363559397}, {"key": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "name": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "size": 611, "crc": 1481435526}, {"key": "androidx/compose/foundation/lazy/ParentSizeElement.class", "name": "androidx/compose/foundation/lazy/ParentSizeElement.class", "size": 4806, "crc": -1283747375}, {"key": "androidx/compose/foundation/lazy/ParentSizeNode.class", "name": "androidx/compose/foundation/lazy/ParentSizeNode.class", "size": 6970, "crc": 1023312151}, {"key": "androidx/compose/foundation/lazy/grid/DefaultLazyGridPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/grid/DefaultLazyGridPrefetchStrategy.class", "size": 10324, "crc": 145312461}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "size": 3967, "crc": 531211103}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "size": 3165, "crc": -2052369072}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "size": 4436, "crc": 1042910740}, {"key": "androidx/compose/foundation/lazy/grid/GridCells.class", "name": "androidx/compose/foundation/lazy/grid/GridCells.class", "size": 1199, "crc": -937011175}, {"key": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "name": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "size": 2299, "crc": 997917237}, {"key": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "name": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "size": 3530, "crc": 1376036350}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "size": 3827, "crc": 470309557}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "size": 3164, "crc": -1320414412}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridCacheWindowPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridCacheWindowPrefetchStrategy.class", "size": 5868, "crc": 177781056}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridCacheWindowScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridCacheWindowScope.class", "size": 11545, "crc": 249683717}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "size": 1251, "crc": 944787183}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "size": 3418, "crc": -805015882}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "size": 1702, "crc": -630242154}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "size": 2434, "crc": -1352473959}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "size": 1710, "crc": -1447133794}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "size": 3506, "crc": -1022945834}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "size": 1254, "crc": 1625268943}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "size": 1636, "crc": 1631109882}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "size": 2344, "crc": 654761609}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "size": 1644, "crc": -1963085193}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "size": 1348, "crc": 1551246495}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "size": 3644, "crc": -1714448879}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "size": 1929, "crc": -1891639908}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "size": 2679, "crc": -613606665}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "size": 1937, "crc": -1828577464}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "size": 3732, "crc": -711860235}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "size": 1351, "crc": -41587317}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "size": 1863, "crc": 2023833897}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "size": 2589, "crc": 1394471792}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "size": 1871, "crc": 289334835}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "size": 45558, "crc": -938394439}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "size": 4341, "crc": 1568792675}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "size": 1613, "crc": -1682143350}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "size": 3324, "crc": 1230714829}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$stickyHeader$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$stickyHeader$2.class", "size": 3339, "crc": -734562658}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "size": 12529, "crc": -1257206196}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "size": 921, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "size": 1642, "crc": -330007854}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfoKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfoKt.class", "size": 1104, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "size": 1282, "crc": 846847128}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "size": 4844, "crc": -383292962}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "size": 7383, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "size": 1254, "crc": -492084805}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "size": 7300, "crc": -265461727}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "size": 3038, "crc": -460989816}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "size": 2502, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "size": 782, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 3738, "crc": -332742006}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "size": 3356, "crc": -282497669}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "size": 22009, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "size": 25856, "crc": 963766530}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "size": 1873, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfoKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfoKt.class", "size": 4158, "crc": -159428356}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "size": 38171, "crc": -2117311868}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "size": 12887, "crc": -167194104}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "size": 17625, "crc": 718889623}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "size": 417, "crc": -328474213}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "size": 6080, "crc": 355013161}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "size": 5866, "crc": -392174478}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "size": 6569, "crc": 987425926}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchResultScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchResultScope.class", "size": 880, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchResultScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchResultScopeImpl.class", "size": 1891, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchScope.class", "size": 1933, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategy.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategy.class", "size": 2121, "crc": -650158663}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategyKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridPrefetchStrategyKt.class", "size": 1304, "crc": 1627312785}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "size": 1234, "crc": -1401206836}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "size": 4769, "crc": -680779546}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "size": 633, "crc": -678164516}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "size": 7756, "crc": 891993684}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "size": 518, "crc": 1481687160}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 6291, "crc": -49512672}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollScopeKt.class", "size": 1364, "crc": -297007958}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "size": 1229, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "size": 940, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "size": 1946, "crc": -459899140}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "size": 1263, "crc": 240946134}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "size": 1615, "crc": 581155487}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "size": 1807, "crc": 347887626}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "size": 12358, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "size": 5170, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$animateScrollToItem$2.class", "size": 4251, "crc": 879895594}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchScope$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchScope$1.class", "size": 10207, "crc": -616434657}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "size": 1462, "crc": 458254467}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$requestScrollToItem$1.class", "size": 3569, "crc": -887938033}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "size": 1896, "crc": -1693516138}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "size": 3433, "crc": -1839770469}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "size": 32244, "crc": -1449865457}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "size": 1804, "crc": 353212471}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "size": 12814, "crc": -502513913}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "size": 5080, "crc": 1837136399}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "size": 3768, "crc": -434474955}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$Companion.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$Companion.class", "size": 1897, "crc": -637412429}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$PrefetchRequestScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler$PrefetchRequestScopeImpl.class", "size": 1896, "crc": -526388605}, {"key": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler.class", "name": "androidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler.class", "size": 9476, "crc": 1063057894}, {"key": "androidx/compose/foundation/lazy/layout/Averages.class", "name": "androidx/compose/foundation/lazy/layout/Averages.class", "size": 3680, "crc": -1992426631}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "size": 1940, "crc": 72847446}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "size": 6548, "crc": 1599636267}, {"key": "androidx/compose/foundation/lazy/layout/CacheWindowLogic.class", "name": "androidx/compose/foundation/lazy/layout/CacheWindowLogic.class", "size": 21940, "crc": -888287466}, {"key": "androidx/compose/foundation/lazy/layout/CacheWindowLogicKt.class", "name": "androidx/compose/foundation/lazy/layout/CacheWindowLogicKt.class", "size": 2450, "crc": 1522918960}, {"key": "androidx/compose/foundation/lazy/layout/CacheWindowScope.class", "name": "androidx/compose/foundation/lazy/layout/CacheWindowScope.class", "size": 2419, "crc": -527881451}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "size": 1790, "crc": -69004028}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "size": 1110, "crc": 502525424}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "size": 3241, "crc": 2075717881}, {"key": "androidx/compose/foundation/lazy/layout/DpLazyLayoutCacheWindow.class", "name": "androidx/compose/foundation/lazy/layout/DpLazyLayoutCacheWindow.class", "size": 2467, "crc": -1579187938}, {"key": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "name": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "size": 1196, "crc": -22067321}, {"key": "androidx/compose/foundation/lazy/layout/FractionLazyLayoutCacheWindow.class", "name": "androidx/compose/foundation/lazy/layout/FractionLazyLayoutCacheWindow.class", "size": 2178, "crc": 1118926268}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "size": 2936, "crc": 297899638}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList.class", "size": 1991, "crc": 1645049609}, {"key": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "name": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "size": 2633, "crc": 1327265935}, {"key": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "name": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "size": 1900, "crc": 1899439007}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateItemElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateItemElement.class", "size": 6761, "crc": -705878220}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "size": 3762, "crc": -1872416683}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "size": 4340, "crc": -1438540732}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "size": 5469, "crc": 578296222}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierElement.class", "size": 4524, "crc": -82376720}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "size": 2328, "crc": -1365032092}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion$emptyBeyondBoundsScope$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion$emptyBeyondBoundsScope$1.class", "size": 1211, "crc": 819687628}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$Companion.class", "size": 1249, "crc": -1596695168}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$WhenMappings.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$WhenMappings.class", "size": 891, "crc": -1746565711}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$layout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode$layout$2.class", "size": 2553, "crc": 105055885}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode.class", "size": 12151, "crc": -1222782551}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "size": 839, "crc": -558866644}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "size": 4689, "crc": -1231600980}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutCacheWindow.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutCacheWindow.class", "size": 1177, "crc": -85295053}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutCacheWindowKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutCacheWindowKt.class", "size": 2840, "crc": 253186505}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "size": 1229, "crc": 744091151}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "size": 1525, "crc": 240619618}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "size": 4722, "crc": 1314613925}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$Companion.class", "size": 1156, "crc": 1481100284}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$1.class", "size": 3727, "crc": -1582863880}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateAppearance$2.class", "size": 6278, "crc": -2018319338}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animateDisappearance$1.class", "size": 6067, "crc": 2014645638}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$animatePlacementDelta$1.class", "size": 6731, "crc": 386773500}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$cancelPlacementAnimation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$cancelPlacementAnimation$1.class", "size": 4219, "crc": 342434908}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$1.class", "size": 3570, "crc": 896740956}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$2.class", "size": 3566, "crc": -335108870}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation$release$3.class", "size": 3566, "crc": 1282736406}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation.class", "size": 17789, "crc": 1975311625}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimationKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimationKt.class", "size": 1504, "crc": -943220143}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsElement.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsElement.class", "size": 5000, "crc": -1998534703}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$DisplayingDisappearingItemsNode.class", "size": 7996, "crc": 2142445797}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$ItemInfo.class", "size": 9195, "crc": -1146544237}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$1.class", "size": 3055, "crc": 1245611234}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortBy$2.class", "size": 3046, "crc": 793709176}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "size": 3125, "crc": -1177052966}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "size": 3115, "crc": -2059090904}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator.class", "size": 34699, "crc": 1669224736}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimatorKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemAnimatorKt.class", "size": 1514, "crc": 307282212}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$invoke$lambda$2$lambda$1$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$invoke$lambda$2$lambda$1$$inlined$onDispose$1.class", "size": 2681, "crc": 439396750}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "size": 8055, "crc": 490280689}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "size": 4272, "crc": -1112233745}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "size": 4638, "crc": 441746906}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "size": 2679, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "size": 4758, "crc": 651285853}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "size": 1638, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "size": 1340, "crc": -323895017}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "size": 4737, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "size": 1461, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "size": 1083, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2$invoke$lambda$5$lambda$4$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2$invoke$lambda$5$lambda$4$$inlined$onDispose$1.class", "size": 2635, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "size": 13808, "crc": -494231306}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$sam$androidx_compose_foundation_lazy_layout_LazyLayoutMeasurePolicy$0.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$sam$androidx_compose_foundation_lazy_layout_LazyLayoutMeasurePolicy$0.class", "size": 2218, "crc": -660260608}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "size": 8857, "crc": -353126737}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasurePolicy.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasurePolicy.class", "size": 965, "crc": -925120043}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "size": 1712, "crc": -273798827}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "size": 11569, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItem.class", "size": 1706, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemKt.class", "size": 4655, "crc": -952259182}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemProvider.class", "size": 3889, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "size": 1728, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "size": 4077, "crc": 554874725}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "size": 7863, "crc": 41443053}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$lambda$3$lambda$2$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$lambda$3$lambda$2$$inlined$onDispose$1.class", "size": 2388, "crc": -83599085}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "size": 9372, "crc": 1322466571}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "size": 912, "crc": -915989189}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "size": 8571, "crc": 365378315}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$NestedPrefetchScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$NestedPrefetchScopeImpl.class", "size": 3394, "crc": -1381055752}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "size": 853, "crc": -1634814704}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchResultScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchResultScope.class", "size": 1126, "crc": -601365801}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "size": 10633, "crc": -52542811}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchStateKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchStateKt.class", "size": 2647, "crc": -1623290254}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses$updateScrollDeltaForApproach$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses$updateScrollDeltaForApproach$2$1.class", "size": 4431, "crc": 662142165}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses.class", "size": 7960, "crc": 225229652}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPassesKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPassesKt.class", "size": 1757, "crc": 1390499754}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScope.class", "size": 1844, "crc": 23829630}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt$animateScrollToItem$1.class", "size": 2175, "crc": 392999942}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutScrollScopeKt.class", "size": 15548, "crc": 161106389}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "size": 1329, "crc": -419564331}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "size": 3493, "crc": 299693936}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifier.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifier.class", "size": 5098, "crc": 1258029477}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode$updateCachedSemanticsValues$3$2.class", "size": 3833, "crc": 470278225}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode.class", "size": 11099, "crc": 957876824}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutStickyItemsKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutStickyItemsKt.class", "size": 7746, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "size": 4563, "crc": 303183564}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$lambda$4$lambda$3$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$lambda$4$lambda$3$$inlined$onDispose$1.class", "size": 2539, "crc": -424204918}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "size": 12982, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "size": 3090, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "size": 9216, "crc": -274643598}, {"key": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "size": 704, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "name": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "size": 7640, "crc": 624425290}, {"key": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "size": 7096, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/NestedPrefetchScope.class", "name": "androidx/compose/foundation/lazy/layout/NestedPrefetchScope.class", "size": 1720, "crc": -284381540}, {"key": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "name": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "size": 4406, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$NestedPrefetchController.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl$NestedPrefetchController.class", "size": 9398, "crc": 391112170}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider$HandleAndRequestImpl.class", "size": 25483, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchHandleProvider.class", "size": 8235, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchMetrics.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchMetrics.class", "size": 3332, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchRequest.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchRequest.class", "size": 1152, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchRequestScope.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchRequestScope.class", "size": 845, "crc": -659543241}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler.class", "size": 1083, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt$RobolectricImpl$1.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt$RobolectricImpl$1.class", "size": 1155, "crc": 601144989}, {"key": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt.class", "size": 5915, "crc": -1611341315}, {"key": "androidx/compose/foundation/lazy/layout/PriorityPrefetchScheduler.class", "name": "androidx/compose/foundation/lazy/layout/PriorityPrefetchScheduler.class", "size": 1258, "crc": -2061796743}, {"key": "androidx/compose/foundation/lazy/layout/PriorityTask$Companion.class", "name": "androidx/compose/foundation/lazy/layout/PriorityTask$Companion.class", "size": 1179, "crc": 2016325624}, {"key": "androidx/compose/foundation/lazy/layout/PriorityTask.class", "name": "androidx/compose/foundation/lazy/layout/PriorityTask.class", "size": 1944, "crc": -407942030}, {"key": "androidx/compose/foundation/lazy/layout/StableValue.class", "name": "androidx/compose/foundation/lazy/layout/StableValue.class", "size": 2634, "crc": -735151249}, {"key": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion$StickToTopPlacement$1.class", "name": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion$StickToTopPlacement$1.class", "size": 5764, "crc": -2023481817}, {"key": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion.class", "name": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement$Companion.class", "size": 1329, "crc": -88579051}, {"key": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement.class", "name": "androidx/compose/foundation/lazy/layout/StickyItemsPlacement.class", "size": 1684, "crc": -729809986}, {"key": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateModifierElement.class", "name": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateModifierElement.class", "size": 4440, "crc": 179039864}, {"key": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateNode.class", "name": "androidx/compose/foundation/lazy/layout/TraversablePrefetchStateNode.class", "size": 1970, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "size": 1029, "crc": 946214874}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "size": 4150, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "size": 3282, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "size": 1083, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "size": 1332, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "size": 3632, "crc": 1758611533}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "size": 1787, "crc": 1465781248}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "size": 1791, "crc": -172319409}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "size": 2096, "crc": -1501438665}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "size": 3720, "crc": -1205464608}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "size": 1335, "crc": -484150079}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "size": 1721, "crc": 428405225}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "size": 1725, "crc": -1658692985}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "size": 2006, "crc": 316427495}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "size": 1429, "crc": 701659354}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "size": 3858, "crc": 1527010580}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "size": 2014, "crc": -534977395}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "size": 2018, "crc": -525374260}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "size": 2323, "crc": -1905833729}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "size": 3946, "crc": 607719248}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "size": 1432, "crc": 1633871428}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "size": 1948, "crc": 2129206547}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "size": 1952, "crc": -880060881}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "size": 2233, "crc": -2064334131}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "size": 48992, "crc": -1663629949}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "size": 4212, "crc": 484088040}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "size": 3558, "crc": 443418053}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "size": 8687, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "size": 1216, "crc": 985599487}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "size": 1176, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "size": 5222, "crc": 545029047}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "size": 7454, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "size": 1350, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "size": 7893, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "size": 3137, "crc": -76996280}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "size": 2579, "crc": -390691239}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "size": 18819, "crc": 144131340}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "size": 1064, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "size": 1387, "crc": -1678713350}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "size": 3021, "crc": 1778567898}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "size": 3022, "crc": -290520264}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "size": 8716, "crc": 233865451}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "size": 1825, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "size": 4218, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "size": 12815, "crc": 56427140}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "size": 76261, "crc": -349203085}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "size": 932, "crc": 625032401}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "size": 11532, "crc": 290807687}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "size": 9696, "crc": -286866468}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "size": 7353, "crc": -550910908}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "size": 12581, "crc": -765108494}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "size": 1917, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "size": 8691, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "size": 20038, "crc": -388059168}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "size": 1315, "crc": 1761207404}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "size": 4028, "crc": -1686923963}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "size": 670, "crc": 1218643992}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "size": 12209, "crc": 504636811}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "size": 545, "crc": -1912950040}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 6604, "crc": 2019609343}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollScopeKt.class", "size": 1463, "crc": 629808800}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "size": 5514, "crc": -53181404}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "size": 4169, "crc": 1739523344}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "size": 3827, "crc": 115981240}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "size": 1274, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "size": 3382, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "size": 1505, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$animateScrollToItem$2.class", "size": 4755, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "size": 1576, "crc": 551765540}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$requestScrollToItem$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$requestScrollToItem$1.class", "size": 3722, "crc": 373921746}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "size": 2049, "crc": -849164574}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "size": 1603, "crc": -1084774028}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "size": 3581, "crc": -2125275102}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "size": 35963, "crc": 720230548}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "size": 5354, "crc": -876216941}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "size": 4227, "crc": -186834840}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "size": 4054, "crc": 1530905939}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "size": 3265, "crc": -1201355627}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "size": 2522, "crc": -681773195}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "size": 1185, "crc": -1198057804}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "size": 1482, "crc": -1517857550}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "size": 1677, "crc": -773299935}, {"key": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "name": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "size": 7716, "crc": -956258609}, {"key": "androidx/compose/foundation/pager/DefaultPagerState$Companion.class", "name": "androidx/compose/foundation/pager/DefaultPagerState$Companion.class", "size": 1384, "crc": -394132027}, {"key": "androidx/compose/foundation/pager/DefaultPagerState.class", "name": "androidx/compose/foundation/pager/DefaultPagerState.class", "size": 5372, "crc": 152422402}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "size": 7275, "crc": 1975213420}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "size": 3986, "crc": -480679700}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "size": 2075, "crc": -616022738}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "size": 1270, "crc": -15509080}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "size": 32174, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 5164, "crc": 906109481}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "size": 1215, "crc": **********}, {"key": "androidx/compose/foundation/pager/MeasuredPage.class", "name": "androidx/compose/foundation/pager/MeasuredPage.class", "size": 11942, "crc": **********}, {"key": "androidx/compose/foundation/pager/MeasuredPageKt.class", "name": "androidx/compose/foundation/pager/MeasuredPageKt.class", "size": 397, "crc": **********}, {"key": "androidx/compose/foundation/pager/PageInfo.class", "name": "androidx/compose/foundation/pager/PageInfo.class", "size": 755, "crc": -1592833373}, {"key": "androidx/compose/foundation/pager/PageSize$Fill.class", "name": "androidx/compose/foundation/pager/PageSize$Fill.class", "size": 1328, "crc": 1059751578}, {"key": "androidx/compose/foundation/pager/PageSize$Fixed.class", "name": "androidx/compose/foundation/pager/PageSize$Fixed.class", "size": 2268, "crc": -717184347}, {"key": "androidx/compose/foundation/pager/PageSize.class", "name": "androidx/compose/foundation/pager/PageSize.class", "size": 946, "crc": -1700834349}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "size": 3883, "crc": 1591137213}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "size": 3130, "crc": -1435932089}, {"key": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "name": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "size": 2738, "crc": 845350387}, {"key": "androidx/compose/foundation/pager/PagerDebugConfig.class", "name": "androidx/compose/foundation/pager/PagerDebugConfig.class", "size": 1200, "crc": -420958030}, {"key": "androidx/compose/foundation/pager/PagerDefaults.class", "name": "androidx/compose/foundation/pager/PagerDefaults.class", "size": 11707, "crc": -1812353633}, {"key": "androidx/compose/foundation/pager/PagerIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerIntervalContent.class", "size": 2956, "crc": -1477357711}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "size": 3501, "crc": 28853386}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "size": 3492, "crc": -310330687}, {"key": "androidx/compose/foundation/pager/PagerKt.class", "name": "androidx/compose/foundation/pager/PagerKt.class", "size": 36057, "crc": 1116461671}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "size": 2152, "crc": -544360886}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "size": 2365, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "size": 4182, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "size": 4821, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "size": 7491, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt.class", "size": 34785, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "size": 15243, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "size": 8276, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerMeasureResult.class", "name": "androidx/compose/foundation/pager/PagerMeasureResult.class", "size": 12747, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerScope.class", "name": "androidx/compose/foundation/pager/PagerScope.class", "size": 471, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerScopeImpl.class", "name": "androidx/compose/foundation/pager/PagerScopeImpl.class", "size": 922, "crc": -1362653905}, {"key": "androidx/compose/foundation/pager/PagerScrollPosition.class", "name": "androidx/compose/foundation/pager/PagerScrollPosition.class", "size": 7621, "crc": -882401441}, {"key": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "name": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "size": 1586, "crc": 181051178}, {"key": "androidx/compose/foundation/pager/PagerScrollScopeKt$LazyLayoutScrollScope$1.class", "name": "androidx/compose/foundation/pager/PagerScrollScopeKt$LazyLayoutScrollScope$1.class", "size": 3865, "crc": 140157295}, {"key": "androidx/compose/foundation/pager/PagerScrollScopeKt.class", "name": "androidx/compose/foundation/pager/PagerScrollScopeKt.class", "size": 1326, "crc": -212678293}, {"key": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "name": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "size": 3729, "crc": -863249930}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "size": 2676, "crc": -1743567730}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance.class", "size": 1038, "crc": -178226607}, {"key": "androidx/compose/foundation/pager/PagerSnapDistanceKt.class", "name": "androidx/compose/foundation/pager/PagerSnapDistanceKt.class", "size": 722, "crc": 731327666}, {"key": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "name": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "size": 2834, "crc": -944892417}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "size": 1909, "crc": -1188155353}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3.class", "size": 5291, "crc": -317808594}, {"key": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "size": 1410, "crc": 145325774}, {"key": "androidx/compose/foundation/pager/PagerState$requestScrollToPage$1.class", "name": "androidx/compose/foundation/pager/PagerState$requestScrollToPage$1.class", "size": 3510, "crc": -623499537}, {"key": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "name": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "size": 1940, "crc": 610737808}, {"key": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "size": 5455, "crc": 1463055648}, {"key": "androidx/compose/foundation/pager/PagerState.class", "name": "androidx/compose/foundation/pager/PagerState.class", "size": 46845, "crc": -1093054713}, {"key": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "size": 1768, "crc": 1698896284}, {"key": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "size": 1064, "crc": 523866367}, {"key": "androidx/compose/foundation/pager/PagerStateKt.class", "name": "androidx/compose/foundation/pager/PagerStateKt.class", "size": 17517, "crc": 174435247}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "size": 1882, "crc": -1336239701}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "size": 6217, "crc": 728440570}, {"key": "androidx/compose/foundation/platform/Synchronization_androidKt.class", "name": "androidx/compose/foundation/platform/Synchronization_androidKt.class", "size": 1977, "crc": 516784868}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "size": 1567, "crc": 1384106120}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "size": 3279, "crc": -642690016}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "size": 2053, "crc": -1833097971}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "size": 6002, "crc": 1362696532}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "size": 1694, "crc": 814488207}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewRequesterKt.class", "size": 1704, "crc": -49116419}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewResponderKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt__BringIntoViewResponderKt.class", "size": 2449, "crc": -877203667}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "size": 3495, "crc": 2115622238}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "size": 1302, "crc": -1292124466}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "size": 3279, "crc": 1702269911}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1$1.class", "size": 2670, "crc": 2045501978}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$1.class", "size": 4477, "crc": 1866765633}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2$2.class", "size": 3874, "crc": 464716534}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringIntoView$2.class", "size": 4969, "crc": 1783388598}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "size": 7176, "crc": 601985707}, {"key": "androidx/compose/foundation/selection/SelectableElement.class", "name": "androidx/compose/foundation/selection/SelectableElement.class", "size": 5752, "crc": -1887097010}, {"key": "androidx/compose/foundation/selection/SelectableGroupKt.class", "name": "androidx/compose/foundation/selection/SelectableGroupKt.class", "size": 1836, "crc": -1609438778}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "size": 6114, "crc": -858230459}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable$4.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable$4.class", "size": 6374, "crc": -1695354193}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6487, "crc": 18234301}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3281, "crc": -407046674}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "size": 3349, "crc": -481964350}, {"key": "androidx/compose/foundation/selection/SelectableKt.class", "name": "androidx/compose/foundation/selection/SelectableKt.class", "size": 8896, "crc": 266047381}, {"key": "androidx/compose/foundation/selection/SelectableNode.class", "name": "androidx/compose/foundation/selection/SelectableNode.class", "size": 3766, "crc": 1272833245}, {"key": "androidx/compose/foundation/selection/ToggleableElement.class", "name": "androidx/compose/foundation/selection/ToggleableElement.class", "size": 5847, "crc": 527226035}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "size": 6156, "crc": -1256682772}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable$4.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable$4.class", "size": 6416, "crc": 2445460}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6490, "crc": -1940214008}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3279, "crc": 362894210}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "size": 3347, "crc": -1197708282}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "size": 6414, "crc": -202520407}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$4.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$4.class", "size": 6667, "crc": 1266399909}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$clickableWithIndicationIfNeeded$1.class", "size": 6696, "crc": -2121927264}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3452, "crc": 1138898750}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-oSLSa3U$$inlined$debugInspectorInfo$1.class", "size": 3518, "crc": 569751665}, {"key": "androidx/compose/foundation/selection/ToggleableKt.class", "name": "androidx/compose/foundation/selection/ToggleableKt.class", "size": 15099, "crc": -1198000017}, {"key": "androidx/compose/foundation/selection/ToggleableNode.class", "name": "androidx/compose/foundation/selection/ToggleableNode.class", "size": 5795, "crc": -292181270}, {"key": "androidx/compose/foundation/selection/TriStateToggleableElement.class", "name": "androidx/compose/foundation/selection/TriStateToggleableElement.class", "size": 6056, "crc": 812924886}, {"key": "androidx/compose/foundation/selection/TriStateToggleableNode.class", "name": "androidx/compose/foundation/selection/TriStateToggleableNode.class", "size": 4056, "crc": 117645243}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "size": 8079, "crc": 65649481}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "size": 4769, "crc": -137764774}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "size": 7291, "crc": 2054950789}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "size": 4873, "crc": -201466356}, {"key": "androidx/compose/foundation/shape/CornerBasedShape.class", "name": "androidx/compose/foundation/shape/CornerBasedShape.class", "size": 6296, "crc": -1617046706}, {"key": "androidx/compose/foundation/shape/CornerSize.class", "name": "androidx/compose/foundation/shape/CornerSize.class", "size": 827, "crc": -2059670084}, {"key": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "name": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "size": 1572, "crc": 879813544}, {"key": "androidx/compose/foundation/shape/CornerSizeKt.class", "name": "androidx/compose/foundation/shape/CornerSizeKt.class", "size": 2002, "crc": -1926776876}, {"key": "androidx/compose/foundation/shape/CutCornerShape.class", "name": "androidx/compose/foundation/shape/CutCornerShape.class", "size": 8069, "crc": 1472095945}, {"key": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "size": 4557, "crc": 106046397}, {"key": "androidx/compose/foundation/shape/DpCornerSize.class", "name": "androidx/compose/foundation/shape/DpCornerSize.class", "size": 3826, "crc": 1908786624}, {"key": "androidx/compose/foundation/shape/GenericShape.class", "name": "androidx/compose/foundation/shape/GenericShape.class", "size": 3523, "crc": -1885895276}, {"key": "androidx/compose/foundation/shape/PercentCornerSize.class", "name": "androidx/compose/foundation/shape/PercentCornerSize.class", "size": 3307, "crc": -800244907}, {"key": "androidx/compose/foundation/shape/PxCornerSize.class", "name": "androidx/compose/foundation/shape/PxCornerSize.class", "size": 2793, "crc": -1890950802}, {"key": "androidx/compose/foundation/shape/RoundedCornerShape.class", "name": "androidx/compose/foundation/shape/RoundedCornerShape.class", "size": 7173, "crc": -570253467}, {"key": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "size": 4934, "crc": -1667529620}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1.class", "size": 9731, "crc": 329852537}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "size": 11112, "crc": -2129231613}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "size": 12275, "crc": 755689278}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "size": 5410, "crc": 1462683388}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "size": 14861, "crc": -908001077}, {"key": "androidx/compose/foundation/text/AutoSizeStepBased.class", "name": "androidx/compose/foundation/text/AutoSizeStepBased.class", "size": 7694, "crc": -634536887}, {"key": "androidx/compose/foundation/text/AutofillHighlightKt$LocalAutofillHighlightColor$1.class", "name": "androidx/compose/foundation/text/AutofillHighlightKt$LocalAutofillHighlightColor$1.class", "size": 1251, "crc": -174107542}, {"key": "androidx/compose/foundation/text/AutofillHighlightKt.class", "name": "androidx/compose/foundation/text/AutofillHighlightKt.class", "size": 1567, "crc": -740866376}, {"key": "androidx/compose/foundation/text/AutofillHighlight_androidKt.class", "name": "androidx/compose/foundation/text/AutofillHighlight_androidKt.class", "size": 583, "crc": 995473225}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "size": 4034, "crc": -153342990}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$2$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$2$1.class", "size": 4162, "crc": 1408277041}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "size": 7646, "crc": -210449881}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$2$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$2$1.class", "size": 2552, "crc": 1262979641}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "size": 3046, "crc": -258388136}, {"key": "androidx/compose/foundation/text/BasicSecureTextFieldKt.class", "name": "androidx/compose/foundation/text/BasicSecureTextFieldKt.class", "size": 36195, "crc": -1867896895}, {"key": "androidx/compose/foundation/text/BasicSecureTextField_androidKt$DefaultContentResolverForSecureTextField$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextField_androidKt$DefaultContentResolverForSecureTextField$1$1.class", "size": 2350, "crc": -698718448}, {"key": "androidx/compose/foundation/text/BasicSecureTextField_androidKt$platformAllowsRevealLastTyped$lambda$7$lambda$6$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextField_androidKt$platformAllowsRevealLastTyped$lambda$7$lambda$6$$inlined$onDispose$1.class", "size": 2708, "crc": 487899354}, {"key": "androidx/compose/foundation/text/BasicSecureTextField_androidKt$platformAllowsRevealLastTyped$settingsObserver$1$1.class", "name": "androidx/compose/foundation/text/BasicSecureTextField_androidKt$platformAllowsRevealLastTyped$settingsObserver$1$1.class", "size": 1985, "crc": -311529029}, {"key": "androidx/compose/foundation/text/BasicSecureTextField_androidKt.class", "name": "androidx/compose/foundation/text/BasicSecureTextField_androidKt.class", "size": 12584, "crc": -530742615}, {"key": "androidx/compose/foundation/text/BasicTextFieldDefaults.class", "name": "androidx/compose/foundation/text/BasicTextFieldDefaults.class", "size": 1363, "crc": -808244397}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1$1.class", "size": 15495, "crc": -1380594269}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "size": 7795, "crc": 405390165}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$lambda$13$lambda$12$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$lambda$13$lambda$12$$inlined$onDispose$1.class", "size": 2927, "crc": 1671316851}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$1.class", "size": 2141, "crc": 1310881952}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$1$1.class", "size": 3958, "crc": -1956389807}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$2$1.class", "size": 3845, "crc": -493183020}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$2$3$1.class", "size": 3843, "crc": -1498553837}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$1.class", "size": 4109, "crc": 1199689931}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$2.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$2.class", "size": 4109, "crc": 1438707711}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$3.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$3.class", "size": 4109, "crc": -1223980101}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$4.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$4.class", "size": 3354, "crc": 1447966917}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$5.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1$showTextToolbar$lambda$5$$inlined$menuItem$5.class", "size": 3344, "crc": -393402347}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$textToolbarHandler$1$1.class", "size": 9525, "crc": 1934663326}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$DefaultTextFieldDecorator$1.class", "size": 3886, "crc": -1620552839}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$1$1.class", "size": 1485, "crc": -485792882}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldCursorHandle$2$1.class", "size": 2636, "crc": 1161667314}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$1$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$1$1.class", "size": 1510, "crc": 170640779}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$2$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$2$1.class", "size": 2773, "crc": 417824174}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$3$1.class", "size": 1510, "crc": -524914910}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$4$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$TextFieldSelectionHandles$4$1.class", "size": 2773, "crc": -412786841}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt.class", "size": 101842, "crc": -346623549}, {"key": "androidx/compose/foundation/text/BasicTextKt.class", "name": "androidx/compose/foundation/text/BasicTextKt.class", "size": 72361, "crc": -1372195376}, {"key": "androidx/compose/foundation/text/BasicText_androidKt.class", "name": "androidx/compose/foundation/text/BasicText_androidKt.class", "size": 18040, "crc": -1814185783}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "size": 3944, "crc": 1949241602}, {"key": "androidx/compose/foundation/text/ClickableTextKt.class", "name": "androidx/compose/foundation/text/ClickableTextKt.class", "size": 11977, "crc": -1584479609}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$-665310900$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$-665310900$1.class", "size": 2945, "crc": -1467091715}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$444370233$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$444370233$1.class", "size": 2942, "crc": -377732389}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$486633673$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$486633673$1.class", "size": 2942, "crc": 1135663912}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$759698998$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda$759698998$1.class", "size": 2942, "crc": 922606695}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "size": 2976, "crc": -109700569}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda$559628295$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda$559628295$1.class", "size": 2935, "crc": 651916934}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "size": 1776, "crc": 834008568}, {"key": "androidx/compose/foundation/text/ContentResolverForSecureTextField.class", "name": "androidx/compose/foundation/text/ContentResolverForSecureTextField.class", "size": 1114, "crc": -1366465673}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$2$1$1.class", "size": 4103, "crc": -156840937}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$5$1$1.class", "size": 4176, "crc": 544626735}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1$WhenMappings.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1$WhenMappings.class", "size": 1093, "crc": 859848485}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$menuBuilder$1$1$1.class", "size": 4596, "crc": 1065459863}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$modifier$1$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$ContextMenuArea$modifier$1$1.class", "size": 4206, "crc": 1699650480}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$1.class", "size": 2388, "crc": -1017679550}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$2.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$TextItem$2.class", "size": 1812, "crc": 1599061739}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$1.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$1.class", "size": 1761, "crc": 1226306354}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$2.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt$getContextMenuItemsAvailability$2.class", "size": 1748, "crc": 432460662}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "size": 30181, "crc": 1591633935}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1$2.class", "size": 2954, "crc": -2110744719}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "size": 6128, "crc": -840674944}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "size": 10652, "crc": -1661043015}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "size": 10314, "crc": 1076440785}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "size": 10286, "crc": -1365537057}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "size": 7297, "crc": 1317970092}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "size": 2284, "crc": 389803175}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1$1.class", "size": 5344, "crc": -1679109356}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$lambda$33$lambda$32$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$lambda$33$lambda$32$$inlined$onDispose$1.class", "size": 2700, "crc": 2041399304}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$lambda$36$lambda$35$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$lambda$36$lambda$35$$inlined$onDispose$1.class", "size": 2377, "crc": 1780608793}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "size": 950, "crc": -409349413}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$1.class", "size": 3831, "crc": 1600413982}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1$2.class", "size": 4884, "crc": 1826731916}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1$1.class", "size": 4747, "crc": 1223591741}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "size": 2347, "crc": -1156191260}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "size": 2598, "crc": -261930020}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt.class", "size": 96873, "crc": -1887789656}, {"key": "androidx/compose/foundation/text/DeadKeyCombiner.class", "name": "androidx/compose/foundation/text/DeadKeyCombiner.class", "size": 2453, "crc": 594669240}, {"key": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "name": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "size": 3160, "crc": -1532867584}, {"key": "androidx/compose/foundation/text/Handle.class", "name": "androidx/compose/foundation/text/Handle.class", "size": 1943, "crc": -151108210}, {"key": "androidx/compose/foundation/text/HandleState.class", "name": "androidx/compose/foundation/text/HandleState.class", "size": 1965, "crc": -473525613}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "size": 3150, "crc": 1716464649}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "size": 12355, "crc": 316303185}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "size": 4351, "crc": 783116228}, {"key": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "size": 10690, "crc": -1727580421}, {"key": "androidx/compose/foundation/text/InlineTextContent.class", "name": "androidx/compose/foundation/text/InlineTextContent.class", "size": 2001, "crc": 1020745769}, {"key": "androidx/compose/foundation/text/InlineTextContentKt.class", "name": "androidx/compose/foundation/text/InlineTextContentKt.class", "size": 2862, "crc": -1799797234}, {"key": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "name": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "size": 1163, "crc": -1118447962}, {"key": "androidx/compose/foundation/text/KeyCommand.class", "name": "androidx/compose/foundation/text/KeyCommand.class", "size": 5333, "crc": -338213006}, {"key": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "name": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "size": 1365, "crc": -1939169676}, {"key": "androidx/compose/foundation/text/KeyMapping.class", "name": "androidx/compose/foundation/text/KeyMapping.class", "size": 836, "crc": -1212186117}, {"key": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "size": 4686, "crc": -584609324}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "size": 1271, "crc": -146280755}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "size": 3097, "crc": -1898003410}, {"key": "androidx/compose/foundation/text/KeyMappingKt.class", "name": "androidx/compose/foundation/text/KeyMappingKt.class", "size": 2021, "crc": -1121349837}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "size": 2424, "crc": 1605724074}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "size": 958, "crc": -1352331874}, {"key": "androidx/compose/foundation/text/KeyboardActionRunner.class", "name": "androidx/compose/foundation/text/KeyboardActionRunner.class", "size": 4763, "crc": 1190264469}, {"key": "androidx/compose/foundation/text/KeyboardActionScope.class", "name": "androidx/compose/foundation/text/KeyboardActionScope.class", "size": 614, "crc": -2099044778}, {"key": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "size": 1343, "crc": 1491915875}, {"key": "androidx/compose/foundation/text/KeyboardActions.class", "name": "androidx/compose/foundation/text/KeyboardActions.class", "size": 5340, "crc": -32928235}, {"key": "androidx/compose/foundation/text/KeyboardActionsKt.class", "name": "androidx/compose/foundation/text/KeyboardActionsKt.class", "size": 1442, "crc": -1191295368}, {"key": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "size": 1654, "crc": 1028607190}, {"key": "androidx/compose/foundation/text/KeyboardOptions.class", "name": "androidx/compose/foundation/text/KeyboardOptions.class", "size": 19985, "crc": -285449112}, {"key": "androidx/compose/foundation/text/LegacyTextFieldState.class", "name": "androidx/compose/foundation/text/LegacyTextFieldState.class", "size": 22648, "crc": -28782385}, {"key": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver$collectInteractionsForLinks$2.class", "name": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver$collectInteractionsForLinks$2.class", "size": 5804, "crc": 104435319}, {"key": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver.class", "name": "androidx/compose/foundation/text/LinkStateInteractionSourceObserver.class", "size": 4940, "crc": 1048025061}, {"key": "androidx/compose/foundation/text/LinksTextMeasurePolicy.class", "name": "androidx/compose/foundation/text/LinksTextMeasurePolicy.class", "size": 5559, "crc": -1665831594}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "size": 3915, "crc": 2131276980}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "size": 3912, "crc": -2093332384}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "size": 4472, "crc": -1042033934}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "size": 7087, "crc": 670266956}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "size": 6659, "crc": -1441491767}, {"key": "androidx/compose/foundation/text/MappedKeys.class", "name": "androidx/compose/foundation/text/MappedKeys.class", "size": 4808, "crc": -189826145}, {"key": "androidx/compose/foundation/text/MenuItemsAvailability$Companion.class", "name": "androidx/compose/foundation/text/MenuItemsAvailability$Companion.class", "size": 1311, "crc": 2098195345}, {"key": "androidx/compose/foundation/text/MenuItemsAvailability.class", "name": "androidx/compose/foundation/text/MenuItemsAvailability.class", "size": 3930, "crc": 984636058}, {"key": "androidx/compose/foundation/text/PasswordInputTransformation.class", "name": "androidx/compose/foundation/text/PasswordInputTransformation.class", "size": 4518, "crc": -1554028458}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "size": 6681, "crc": -312915934}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "size": 5411, "crc": -1850093586}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "size": 2655, "crc": -301783243}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$observeHideEvents$2.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$observeHideEvents$2.class", "size": 3533, "crc": -1499176947}, {"key": "androidx/compose/foundation/text/SecureTextFieldController$passwordInputTransformation$1.class", "name": "androidx/compose/foundation/text/SecureTextFieldController$passwordInputTransformation$1.class", "size": 1395, "crc": 912349559}, {"key": "androidx/compose/foundation/text/SecureTextFieldController.class", "name": "androidx/compose/foundation/text/SecureTextFieldController.class", "size": 6519, "crc": 355081136}, {"key": "androidx/compose/foundation/text/StringHelpersKt.class", "name": "androidx/compose/foundation/text/StringHelpersKt.class", "size": 1456, "crc": -2118914837}, {"key": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "name": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "size": 3719, "crc": -475511788}, {"key": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "name": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "size": 903, "crc": -997353189}, {"key": "androidx/compose/foundation/text/TextAnnotatorScope.class", "name": "androidx/compose/foundation/text/TextAnnotatorScope.class", "size": 4486, "crc": 622861497}, {"key": "androidx/compose/foundation/text/TextAutoSize$Companion.class", "name": "androidx/compose/foundation/text/TextAutoSize$Companion.class", "size": 1974, "crc": 2031678905}, {"key": "androidx/compose/foundation/text/TextAutoSize.class", "name": "androidx/compose/foundation/text/TextAutoSize.class", "size": 1477, "crc": -1783489851}, {"key": "androidx/compose/foundation/text/TextAutoSizeDefaults.class", "name": "androidx/compose/foundation/text/TextAutoSizeDefaults.class", "size": 1290, "crc": 1853561269}, {"key": "androidx/compose/foundation/text/TextContextMenuItems.class", "name": "androidx/compose/foundation/text/TextContextMenuItems.class", "size": 5061, "crc": 2034726823}, {"key": "androidx/compose/foundation/text/TextDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextDelegate$Companion.class", "size": 1422, "crc": -109365754}, {"key": "androidx/compose/foundation/text/TextDelegate.class", "name": "androidx/compose/foundation/text/TextDelegate.class", "size": 14572, "crc": -1837948282}, {"key": "androidx/compose/foundation/text/TextDelegateKt.class", "name": "androidx/compose/foundation/text/TextDelegateKt.class", "size": 5295, "crc": -2033540482}, {"key": "androidx/compose/foundation/text/TextDragObserver.class", "name": "androidx/compose/foundation/text/TextDragObserver.class", "size": 836, "crc": -1742984413}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "size": 3504, "crc": 1113407099}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "size": 13621, "crc": -2009208066}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt.class", "size": 1992, "crc": 1157268701}, {"key": "androidx/compose/foundation/text/TextFieldCursor_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldCursor_androidKt.class", "size": 1573, "crc": -1064389683}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "size": 2120, "crc": 1891993215}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "size": 21538, "crc": -356541896}, {"key": "androidx/compose/foundation/text/TextFieldDelegate.class", "name": "androidx/compose/foundation/text/TextFieldDelegate.class", "size": 7203, "crc": 501981135}, {"key": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "name": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "size": 8692, "crc": 1792280461}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "size": 3428, "crc": 262932436}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "size": 2228, "crc": -584645233}, {"key": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "name": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "size": 2225, "crc": 1759415931}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$WhenMappings.class", "size": 3304, "crc": -688201453}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput.class", "size": 20025, "crc": -2106235895}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1$1.class", "size": 1784, "crc": 1180154621}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "size": 8196, "crc": 1621368566}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "size": 4272, "crc": -1619399439}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "size": 938, "crc": 1544772792}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$1.class", "size": 5504, "crc": 1260420980}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1$2.class", "size": 5436, "crc": 924941161}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "size": 5708, "crc": 682334257}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "size": 4320, "crc": 348007838}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$invoke$lambda$4$lambda$3$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$invoke$lambda$4$lambda$3$$inlined$onDispose$1.class", "size": 3457, "crc": -1190430996}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "size": 11006, "crc": -529964249}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "size": 2393, "crc": -46599102}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "size": 859, "crc": -133900930}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "size": 3434, "crc": -1879964223}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "size": 5662, "crc": 1849000178}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "size": 8591, "crc": -595660030}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt.class", "size": 8559, "crc": -697996247}, {"key": "androidx/compose/foundation/text/TextFieldScroll_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldScroll_androidKt.class", "size": 1921, "crc": 824167935}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "size": 1443, "crc": 1352857131}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "size": 11258, "crc": -1967323914}, {"key": "androidx/compose/foundation/text/TextFieldSize.class", "name": "androidx/compose/foundation/text/TextFieldSize.class", "size": 4406, "crc": 1632837430}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "size": 13493, "crc": 894515586}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt.class", "size": 1325, "crc": 178050901}, {"key": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "name": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "size": 6492, "crc": -530052158}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "size": 9659, "crc": -1637439995}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "size": 4155, "crc": 1441865443}, {"key": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$3$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$LinksComposables$1$3$1.class", "size": 3467, "crc": 203469095}, {"key": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$lambda$24$lambda$23$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$StyleAnnotation$lambda$24$lambda$23$$inlined$onDispose$1.class", "size": 2379, "crc": -2125422100}, {"key": "androidx/compose/foundation/text/TextLinkScope$shapeForRange$1$1.class", "name": "androidx/compose/foundation/text/TextLinkScope$shapeForRange$1$1.class", "size": 1784, "crc": -479923059}, {"key": "androidx/compose/foundation/text/TextLinkScope.class", "name": "androidx/compose/foundation/text/TextLinkScope.class", "size": 36217, "crc": 2080148094}, {"key": "androidx/compose/foundation/text/TextLinkScopeKt.class", "name": "androidx/compose/foundation/text/TextLinkScopeKt.class", "size": 1109, "crc": -2027096420}, {"key": "androidx/compose/foundation/text/TextMeasurePolicy.class", "name": "androidx/compose/foundation/text/TextMeasurePolicy.class", "size": 10989, "crc": -1162557483}, {"key": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "name": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "size": 914, "crc": -212266456}, {"key": "androidx/compose/foundation/text/TextRangeLayoutMeasureResult.class", "name": "androidx/compose/foundation/text/TextRangeLayoutMeasureResult.class", "size": 1809, "crc": -847775996}, {"key": "androidx/compose/foundation/text/TextRangeLayoutMeasureScope.class", "name": "androidx/compose/foundation/text/TextRangeLayoutMeasureScope.class", "size": 1619, "crc": 1206779757}, {"key": "androidx/compose/foundation/text/TextRangeLayoutModifier.class", "name": "androidx/compose/foundation/text/TextRangeLayoutModifier.class", "size": 1937, "crc": -1678025473}, {"key": "androidx/compose/foundation/text/TextRangeScopeMeasurePolicy.class", "name": "androidx/compose/foundation/text/TextRangeScopeMeasurePolicy.class", "size": 890, "crc": 601533374}, {"key": "androidx/compose/foundation/text/TouchMode_androidKt.class", "name": "androidx/compose/foundation/text/TouchMode_androidKt.class", "size": 779, "crc": 1174389671}, {"key": "androidx/compose/foundation/text/UndoManager$Entry.class", "name": "androidx/compose/foundation/text/UndoManager$Entry.class", "size": 2025, "crc": -1169137824}, {"key": "androidx/compose/foundation/text/UndoManager.class", "name": "androidx/compose/foundation/text/UndoManager.class", "size": 4701, "crc": -646278051}, {"key": "androidx/compose/foundation/text/UndoManagerKt.class", "name": "androidx/compose/foundation/text/UndoManagerKt.class", "size": 574, "crc": 622720676}, {"key": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "name": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "size": 486, "crc": -1468021547}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "size": 1967, "crc": 1781820178}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "size": 6096, "crc": -1593810192}, {"key": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "size": 10157, "crc": 2085098222}, {"key": "androidx/compose/foundation/text/contextmenu/ProcessTextApi23Impl.class", "name": "androidx/compose/foundation/text/contextmenu/ProcessTextApi23Impl.class", "size": 9296, "crc": 669223944}, {"key": "androidx/compose/foundation/text/contextmenu/ProcessText_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/ProcessText_androidKt.class", "size": 6005, "crc": 981502022}, {"key": "androidx/compose/foundation/text/contextmenu/builder/TextContextMenuBuilderScope.class", "name": "androidx/compose/foundation/text/contextmenu/builder/TextContextMenuBuilderScope.class", "size": 7321, "crc": 1667740052}, {"key": "androidx/compose/foundation/text/contextmenu/builder/TextContextMenuBuilderScopeKt.class", "name": "androidx/compose/foundation/text/contextmenu/builder/TextContextMenuBuilderScopeKt.class", "size": 1028, "crc": -988457638}, {"key": "androidx/compose/foundation/text/contextmenu/builder/TextContextMenuBuilderScope_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/builder/TextContextMenuBuilderScope_androidKt.class", "size": 3041, "crc": -908410202}, {"key": "androidx/compose/foundation/text/contextmenu/data/ProcessTextKey.class", "name": "androidx/compose/foundation/text/contextmenu/data/ProcessTextKey.class", "size": 1359, "crc": -1837468183}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuComponent.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuComponent.class", "size": 1135, "crc": 233975362}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuData$Companion.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuData$Companion.class", "size": 1312, "crc": -1763538271}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuData.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuData.class", "size": 2909, "crc": 1680339267}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuItem.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuItem.class", "size": 3261, "crc": -160841631}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuKeys.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuKeys.class", "size": 1794, "crc": 771721369}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuSeparator.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuSeparator.class", "size": 1097, "crc": 2129995223}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuSession.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuSession.class", "size": 516, "crc": -2099133260}, {"key": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuTextClassificationItem.class", "name": "androidx/compose/foundation/text/contextmenu/data/TextContextMenuTextClassificationItem.class", "size": 2280, "crc": -1295398974}, {"key": "androidx/compose/foundation/text/contextmenu/gestures/RightClickGesturesKt$awaitFirstRightClickDown$1.class", "name": "androidx/compose/foundation/text/contextmenu/gestures/RightClickGesturesKt$awaitFirstRightClickDown$1.class", "size": 1839, "crc": 617807017}, {"key": "androidx/compose/foundation/text/contextmenu/gestures/RightClickGesturesKt$onRightClickDown$2.class", "name": "androidx/compose/foundation/text/contextmenu/gestures/RightClickGesturesKt$onRightClickDown$2.class", "size": 4627, "crc": 669794063}, {"key": "androidx/compose/foundation/text/contextmenu/gestures/RightClickGesturesKt.class", "name": "androidx/compose/foundation/text/contextmenu/gestures/RightClickGesturesKt.class", "size": 6073, "crc": -314912925}, {"key": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider$TextActionModeCallbackImpl.class", "name": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider$TextActionModeCallbackImpl.class", "size": 9651, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider$TextContextMenuSessionImpl.class", "name": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider$TextContextMenuSessionImpl.class", "size": 2366, "crc": -280074011}, {"key": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider$showTextContextMenu$2.class", "name": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider$showTextContextMenu$2.class", "size": 9751, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider.class", "name": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider.class", "size": 16065, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider_androidKt$ProvidePlatformTextContextMenuToolbar$2.class", "name": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider_androidKt$ProvidePlatformTextContextMenuToolbar$2.class", "size": 12240, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider_androidKt$platformTextContextMenuToolbarProvider$lambda$10$lambda$9$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider_androidKt$platformTextContextMenuToolbarProvider$lambda$10$lambda$9$$inlined$onDispose$1.class", "size": 2806, "crc": 909209330}, {"key": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/internal/AndroidTextContextMenuToolbarProvider_androidKt.class", "size": 17950, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt$lambda$-**********$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt$lambda$-**********$1.class", "size": 4473, "crc": 781672316}, {"key": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt$lambda$129995601$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt$lambda$129995601$1.class", "size": 4467, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt$lambda$636288403$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt$lambda$636288403$1.class", "size": 4467, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/internal/ComposableSingletons$DefaultTextContextMenuDropdownProvider_androidKt.class", "size": 3309, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$DefaultTextContextMenuDropdown$1$1$1$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$DefaultTextContextMenuDropdown$1$1$1$1.class", "size": 2757, "crc": 752084185}, {"key": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$DefaultTextContextMenuDropdown$1$1$1$2.class", "name": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$DefaultTextContextMenuDropdown$1$1$1$2.class", "size": 3379, "crc": -384692338}, {"key": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$OpenContextMenu$2$data$2$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$OpenContextMenu$2$data$2$1.class", "size": 1703, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$OpenContextMenu$2.class", "name": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt$OpenContextMenu$2.class", "size": 6823, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/internal/DefaultTextContextMenuDropdownProvider_androidKt.class", "size": 29109, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/FloatingTextActionModeCallback.class", "name": "androidx/compose/foundation/text/contextmenu/internal/FloatingTextActionModeCallback.class", "size": 4563, "crc": -377704257}, {"key": "androidx/compose/foundation/text/contextmenu/internal/MaintainWindowPositionPopupPositionProvider.class", "name": "androidx/compose/foundation/text/contextmenu/internal/MaintainWindowPositionPopupPositionProvider.class", "size": 4075, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/PlatformDefaultTextContextMenuProviders_androidKt$ProvideBothDefaultProviders$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/PlatformDefaultTextContextMenuProviders_androidKt$ProvideBothDefaultProviders$1.class", "size": 12933, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/PlatformDefaultTextContextMenuProviders_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/internal/PlatformDefaultTextContextMenuProviders_androidKt.class", "size": 19969, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/PrimaryTextActionModeCallback.class", "name": "androidx/compose/foundation/text/contextmenu/internal/PrimaryTextActionModeCallback.class", "size": 2230, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextActionModeCallback.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextActionModeCallback.class", "size": 1557, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextClassificationHelper34.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextClassificationHelper34.class", "size": 2101, "crc": 526735194}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextClassificationHelperApi28.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextClassificationHelperApi28.class", "size": 2527, "crc": -712627683}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$1.class", "size": 2526, "crc": -1067406655}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$2$1.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$2$1.class", "size": 3159, "crc": 198928416}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$4.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$4.class", "size": 2431, "crc": -820762056}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$5.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28$textClassificationItem$5.class", "size": 3206, "crc": -495316235}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuHelperApi28.class", "size": 16688, "crc": -597536619}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuTextClassificationHelper_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextContextMenuTextClassificationHelper_androidKt.class", "size": 601, "crc": -2047008653}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextToolbarHelper.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextToolbarHelper.class", "size": 2523, "crc": -1452487644}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextToolbarHelperApi23.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextToolbarHelperApi23.class", "size": 1828, "crc": -684269742}, {"key": "androidx/compose/foundation/text/contextmenu/internal/TextToolbarHelperApi28.class", "name": "androidx/compose/foundation/text/contextmenu/internal/TextToolbarHelperApi28.class", "size": 5106, "crc": 1095061107}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsElement.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsElement.class", "size": 3775, "crc": -707686458}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsNode.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsNode.class", "size": 2601, "crc": 1633259686}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsWithContextElement.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsWithContextElement.class", "size": 3971, "crc": 1793033466}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsWithContextNode.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/AddTextContextMenuDataComponentsWithContextNode.class", "size": 4127, "crc": -934858515}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/FilterTextContextMenuDataComponentsElement.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/FilterTextContextMenuDataComponentsElement.class", "size": 3741, "crc": -30736387}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/FilterTextContextMenuDataComponentsNode.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/FilterTextContextMenuDataComponentsNode.class", "size": 2385, "crc": -1246911171}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuDataTraverseKey.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuDataTraverseKey.class", "size": 1345, "crc": 1951932158}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureElement.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureElement.class", "size": 3634, "crc": -1118080175}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$1$1.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$1$1.class", "size": 1829, "crc": -439342925}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$1.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$1.class", "size": 2126, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$ClickTextContextMenuDataProvider.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$ClickTextContextMenuDataProvider.class", "size": 5285, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$Companion.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$Companion.class", "size": 1018, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$tryShowContextMenu$1.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode$tryShowContextMenu$1.class", "size": 4978, "crc": -954939810}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGestureNode.class", "size": 8469, "crc": -373771148}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGesturesModifierKt.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuGesturesModifierKt.class", "size": 1784, "crc": -2143460556}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuModifierKt$collectTextContextMenuData$1$1.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuModifierKt$collectTextContextMenuData$1$1.class", "size": 2041, "crc": -896836752}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuModifierKt.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuModifierKt.class", "size": 6704, "crc": -1270468521}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuModifier_androidKt.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuModifier_androidKt.class", "size": 1699, "crc": -483400048}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerElement.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerElement.class", "size": 5192, "crc": -74791191}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerModifierKt.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerModifierKt.class", "size": 4516, "crc": -747058135}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerNode$show$1.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerNode$show$1.class", "size": 4606, "crc": 1481924795}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerNode.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/TextContextMenuToolbarHandlerNode.class", "size": 11563, "crc": -718256174}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/ToolbarRequester.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/ToolbarRequester.class", "size": 3328, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/modifier/ToolbarRequesterImpl.class", "name": "androidx/compose/foundation/text/contextmenu/modifier/ToolbarRequesterImpl.class", "size": 1476, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProvider$SessionImpl.class", "name": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProvider$SessionImpl.class", "size": 3064, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProvider$showTextContextMenu$2.class", "name": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProvider$showTextContextMenu$2.class", "size": 4113, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProvider.class", "name": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProvider.class", "size": 10828, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProviderKt$ProvideBasicTextContextMenu$2.class", "name": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProviderKt$ProvideBasicTextContextMenu$2.class", "size": 13944, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProviderKt$basicTextContextMenuProvider$lambda$8$lambda$7$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProviderKt$basicTextContextMenuProvider$lambda$8$lambda$7$$inlined$onDispose$1.class", "size": 2582, "crc": -627253574}, {"key": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProviderKt.class", "name": "androidx/compose/foundation/text/contextmenu/provider/BasicTextContextMenuProviderKt.class", "size": 16831, "crc": **********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/TextContextMenuDataProvider.class", "name": "androidx/compose/foundation/text/contextmenu/provider/TextContextMenuDataProvider.class", "size": 1280, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/TextContextMenuProvider.class", "name": "androidx/compose/foundation/text/contextmenu/provider/TextContextMenuProvider.class", "size": 1161, "crc": -**********}, {"key": "androidx/compose/foundation/text/contextmenu/provider/TextContextMenuProviderKt.class", "name": "androidx/compose/foundation/text/contextmenu/provider/TextContextMenuProviderKt.class", "size": 2416, "crc": 973146061}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorElement.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorElement.class", "size": 3301, "crc": -701354767}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetectorNode.class", "size": 5337, "crc": 651351170}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingDetector_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingDetector_androidKt.class", "size": 2066, "crc": 510236761}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerElement.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerElement.class", "size": 2580, "crc": 340833383}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$onFocusEvent$1.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode$onFocusEvent$1.class", "size": 3627, "crc": -1744373310}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandlerNode.class", "size": 4095, "crc": 1385239290}, {"key": "androidx/compose/foundation/text/handwriting/HandwritingHandler_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/HandwritingHandler_androidKt.class", "size": 1090, "crc": -1334814153}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingElement.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingElement.class", "size": 3506, "crc": 488228878}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingKt.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingKt.class", "size": 3932, "crc": -918816256}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1$1.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1$1.class", "size": 12940, "crc": 2007718961}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode$suspendingPointerInputModifierNode$1.class", "size": 2199, "crc": 1806336072}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwritingNode.class", "size": 5032, "crc": 1813772479}, {"key": "androidx/compose/foundation/text/handwriting/StylusHandwriting_androidKt.class", "name": "androidx/compose/foundation/text/handwriting/StylusHandwriting_androidKt.class", "size": 738, "crc": -1574330832}, {"key": "androidx/compose/foundation/text/input/AllCapsTransformation.class", "name": "androidx/compose/foundation/text/input/AllCapsTransformation.class", "size": 5827, "crc": -1114117553}, {"key": "androidx/compose/foundation/text/input/FilterChain.class", "name": "androidx/compose/foundation/text/input/FilterChain.class", "size": 4374, "crc": -1710337062}, {"key": "androidx/compose/foundation/text/input/InputTransformation$Companion.class", "name": "androidx/compose/foundation/text/input/InputTransformation$Companion.class", "size": 1211, "crc": -1191225917}, {"key": "androidx/compose/foundation/text/input/InputTransformation.class", "name": "androidx/compose/foundation/text/input/InputTransformation.class", "size": 1722, "crc": -1608010157}, {"key": "androidx/compose/foundation/text/input/InputTransformationByValue.class", "name": "androidx/compose/foundation/text/input/InputTransformationByValue.class", "size": 4795, "crc": -534725052}, {"key": "androidx/compose/foundation/text/input/InputTransformationKt.class", "name": "androidx/compose/foundation/text/input/InputTransformationKt.class", "size": 3037, "crc": -1486312062}, {"key": "androidx/compose/foundation/text/input/KeyboardActionHandler.class", "name": "androidx/compose/foundation/text/input/KeyboardActionHandler.class", "size": 853, "crc": -1355718967}, {"key": "androidx/compose/foundation/text/input/MaxLengthFilter.class", "name": "androidx/compose/foundation/text/input/MaxLengthFilter.class", "size": 4207, "crc": -2023929623}, {"key": "androidx/compose/foundation/text/input/OutputTransformation.class", "name": "androidx/compose/foundation/text/input/OutputTransformation.class", "size": 799, "crc": 1465412948}, {"key": "androidx/compose/foundation/text/input/TextFieldBuffer$ChangeList.class", "name": "androidx/compose/foundation/text/input/TextFieldBuffer$ChangeList.class", "size": 877, "crc": -802279529}, {"key": "androidx/compose/foundation/text/input/TextFieldBuffer.class", "name": "androidx/compose/foundation/text/input/TextFieldBuffer.class", "size": 25075, "crc": 932125487}, {"key": "androidx/compose/foundation/text/input/TextFieldBufferKt.class", "name": "androidx/compose/foundation/text/input/TextFieldBufferKt.class", "size": 6200, "crc": 545739781}, {"key": "androidx/compose/foundation/text/input/TextFieldCharSequence.class", "name": "androidx/compose/foundation/text/input/TextFieldCharSequence.class", "size": 8090, "crc": 2102176493}, {"key": "androidx/compose/foundation/text/input/TextFieldCharSequenceKt.class", "name": "androidx/compose/foundation/text/input/TextFieldCharSequenceKt.class", "size": 1868, "crc": 398078662}, {"key": "androidx/compose/foundation/text/input/TextFieldDecorator.class", "name": "androidx/compose/foundation/text/input/TextFieldDecorator.class", "size": 1034, "crc": -1423598009}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$Companion.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$Companion.class", "size": 1345, "crc": 985402967}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$MultiLine.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$MultiLine.class", "size": 3856, "crc": 847782813}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits$SingleLine.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits$SingleLine.class", "size": 1212, "crc": 943389852}, {"key": "androidx/compose/foundation/text/input/TextFieldLineLimits.class", "name": "androidx/compose/foundation/text/input/TextFieldLineLimits.class", "size": 1236, "crc": -324099377}, {"key": "androidx/compose/foundation/text/input/TextFieldState$NotifyImeListener.class", "name": "androidx/compose/foundation/text/input/TextFieldState$NotifyImeListener.class", "size": 1003, "crc": 1372686918}, {"key": "androidx/compose/foundation/text/input/TextFieldState$Saver.class", "name": "androidx/compose/foundation/text/input/TextFieldState$Saver.class", "size": 4985, "crc": 1788022069}, {"key": "androidx/compose/foundation/text/input/TextFieldState$WhenMappings.class", "name": "androidx/compose/foundation/text/input/TextFieldState$WhenMappings.class", "size": 1013, "crc": 1903367976}, {"key": "androidx/compose/foundation/text/input/TextFieldState.class", "name": "androidx/compose/foundation/text/input/TextFieldState.class", "size": 23365, "crc": 1337322468}, {"key": "androidx/compose/foundation/text/input/TextFieldStateKt.class", "name": "androidx/compose/foundation/text/input/TextFieldStateKt.class", "size": 11942, "crc": -1978300722}, {"key": "androidx/compose/foundation/text/input/TextHighlightType$Companion.class", "name": "androidx/compose/foundation/text/input/TextHighlightType$Companion.class", "size": 1388, "crc": -1944929517}, {"key": "androidx/compose/foundation/text/input/TextHighlightType.class", "name": "androidx/compose/foundation/text/input/TextHighlightType.class", "size": 2780, "crc": -1160542928}, {"key": "androidx/compose/foundation/text/input/TextObfuscationMode$Companion.class", "name": "androidx/compose/foundation/text/input/TextObfuscationMode$Companion.class", "size": 1465, "crc": 624126090}, {"key": "androidx/compose/foundation/text/input/TextObfuscationMode.class", "name": "androidx/compose/foundation/text/input/TextObfuscationMode.class", "size": 2915, "crc": 1730377205}, {"key": "androidx/compose/foundation/text/input/TextObfuscationMode_androidKt.class", "name": "androidx/compose/foundation/text/input/TextObfuscationMode_androidKt.class", "size": 1089, "crc": 434040260}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "size": 7108, "crc": -1357539964}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion$Saver.class", "size": 6748, "crc": -1342777222}, {"key": "androidx/compose/foundation/text/input/TextUndoManager$Companion.class", "name": "androidx/compose/foundation/text/input/TextUndoManager$Companion.class", "size": 980, "crc": 998237202}, {"key": "androidx/compose/foundation/text/input/TextUndoManager.class", "name": "androidx/compose/foundation/text/input/TextUndoManager.class", "size": 9158, "crc": 1566269652}, {"key": "androidx/compose/foundation/text/input/TextUndoManagerKt.class", "name": "androidx/compose/foundation/text/input/TextUndoManagerKt.class", "size": 5493, "crc": 1380045708}, {"key": "androidx/compose/foundation/text/input/UndoState.class", "name": "androidx/compose/foundation/text/input/UndoState.class", "size": 2007, "crc": 1809154139}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1$2.class", "size": 2100, "crc": 1740659536}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$1.class", "size": 5498, "crc": 1734976064}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$request$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1$request$1.class", "size": 2664, "crc": 1395295296}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2$1.class", "size": 7552, "crc": 488827206}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter$startInput$2.class", "size": 5470, "crc": 2030050739}, {"key": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter.class", "name": "androidx/compose/foundation/text/input/internal/AndroidLegacyPlatformTextInputServiceAdapter.class", "size": 11644, "crc": -14163597}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextFieldKeyEventHandler.class", "size": 5722, "crc": 1486065697}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "size": 2241, "crc": 1811592321}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "size": 2317, "crc": 1439451156}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$1.class", "size": 6386, "crc": 1342916618}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$2.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1$2.class", "size": 2018, "crc": -30886642}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$2$1.class", "size": 5058, "crc": -1173427427}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$textInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3$3$textInputSession$1.class", "size": 8198, "crc": 243039588}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$3.class", "size": 14846, "crc": 731597106}, {"key": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/AndroidTextInputSession_androidKt.class", "size": 9904, "crc": -1588507081}, {"key": "androidx/compose/foundation/text/input/internal/Api25CommitContentImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api25CommitContentImpl.class", "size": 1589, "crc": 1336346693}, {"key": "androidx/compose/foundation/text/input/internal/Api34LegacyPerformHandwritingGestureImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api34LegacyPerformHandwritingGestureImpl.class", "size": 4938, "crc": -1800817436}, {"key": "androidx/compose/foundation/text/input/internal/Api34PerformHandwritingGestureImpl.class", "name": "androidx/compose/foundation/text/input/internal/Api34PerformHandwritingGestureImpl.class", "size": 3189, "crc": -1023477619}, {"key": "androidx/compose/foundation/text/input/internal/Api34StartStylusHandwriting.class", "name": "androidx/compose/foundation/text/input/internal/Api34StartStylusHandwriting.class", "size": 1422, "crc": 1837762447}, {"key": "androidx/compose/foundation/text/input/internal/ChangeTracker$Change.class", "name": "androidx/compose/foundation/text/input/internal/ChangeTracker$Change.class", "size": 3720, "crc": -827333905}, {"key": "androidx/compose/foundation/text/input/internal/ChangeTracker.class", "name": "androidx/compose/foundation/text/input/internal/ChangeTracker.class", "size": 8900, "crc": -1414656048}, {"key": "androidx/compose/foundation/text/input/internal/ClipboardKeyCommandsHandler.class", "name": "androidx/compose/foundation/text/input/internal/ClipboardKeyCommandsHandler.class", "size": 3914, "crc": -212549647}, {"key": "androidx/compose/foundation/text/input/internal/CodepointHelpers_jvmKt.class", "name": "androidx/compose/foundation/text/input/internal/CodepointHelpers_jvmKt.class", "size": 1034, "crc": -818911714}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformation$Companion.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformation$Companion.class", "size": 838, "crc": -1876290484}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformation.class", "size": 1030, "crc": 1848031046}, {"key": "androidx/compose/foundation/text/input/internal/CodepointTransformationKt.class", "name": "androidx/compose/foundation/text/input/internal/CodepointTransformationKt.class", "size": 3657, "crc": 741113624}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager.class", "size": 1545, "crc": -1979974216}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImpl.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImpl.class", "size": 4696, "crc": 623488346}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi21.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi21.class", "size": 2360, "crc": 987113464}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi24.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi24.class", "size": 1540, "crc": 797493947}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi34.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManagerImplApi34.class", "size": 1656, "crc": -1411559632}, {"key": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ComposeInputMethodManager_androidKt.class", "size": 3130, "crc": 605144437}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifier.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifier.class", "size": 9962, "crc": 555397537}, {"key": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/CoreTextFieldSemanticsModifierNode.class", "size": 19836, "crc": 1341520532}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi33Helper.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi33Helper.class", "size": 2254, "crc": -1936736250}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi34Helper.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoApi34Helper.class", "size": 2369, "crc": 1791838329}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoBuilder_androidKt.class", "size": 8341, "crc": -1490991215}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$2.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1$2.class", "size": 2311, "crc": 1515107920}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController$startOrStopMonitoring$1.class", "size": 5000, "crc": -2112385359}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnchorInfoController.class", "size": 10241, "crc": -95548599}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2$1.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2$1.class", "size": 4487, "crc": -1442667849}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState$snapToVisibleAndAnimate$2.class", "size": 4487, "crc": 88610600}, {"key": "androidx/compose/foundation/text/input/internal/CursorAnimationState.class", "name": "androidx/compose/foundation/text/input/internal/CursorAnimationState.class", "size": 5231, "crc": -1772214960}, {"key": "androidx/compose/foundation/text/input/internal/DefaultImeEditCommandScope.class", "name": "androidx/compose/foundation/text/input/internal/DefaultImeEditCommandScope.class", "size": 8008, "crc": -704524259}, {"key": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi21.class", "name": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi21.class", "size": 1379, "crc": 1244352976}, {"key": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi24.class", "name": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi24.class", "size": 1466, "crc": 97129590}, {"key": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi28.class", "name": "androidx/compose/foundation/text/input/internal/DigitDirectionalityApi28.class", "size": 1655, "crc": -8615083}, {"key": "androidx/compose/foundation/text/input/internal/EditorInfoApi34.class", "name": "androidx/compose/foundation/text/input/internal/EditorInfoApi34.class", "size": 2003, "crc": -500276194}, {"key": "androidx/compose/foundation/text/input/internal/EditorInfo_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/EditorInfo_androidKt.class", "size": 6701, "crc": -1859912599}, {"key": "androidx/compose/foundation/text/input/internal/GapBuffer.class", "name": "androidx/compose/foundation/text/input/internal/GapBuffer.class", "size": 4583, "crc": -1688507792}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGestureApi34.class", "size": 40741, "crc": -2072934408}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt$compoundEditCommand$1.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt$compoundEditCommand$1.class", "size": 1508, "crc": -1968803102}, {"key": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/HandwritingGesture_androidKt.class", "size": 18843, "crc": -86358532}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommandScope.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommandScope.class", "size": 1272, "crc": -174200283}, {"key": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ImeEditCommand_androidKt.class", "size": 14207, "crc": 1506026022}, {"key": "androidx/compose/foundation/text/input/internal/IndexTransformationType.class", "name": "androidx/compose/foundation/text/input/internal/IndexTransformationType.class", "size": 2242, "crc": 780446759}, {"key": "androidx/compose/foundation/text/input/internal/InputEventCallback2.class", "name": "androidx/compose/foundation/text/input/internal/InputEventCallback2.class", "size": 1607, "crc": -109635312}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManager.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManager.class", "size": 1335, "crc": -342853996}, {"key": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl.class", "name": "androidx/compose/foundation/text/input/internal/InputMethodManagerImpl.class", "size": 5197, "crc": -601372165}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifier.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifier.class", "size": 6636, "crc": 1505347081}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode$launchTextInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode$launchTextInputSession$1.class", "size": 4452, "crc": 900252755}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNode.class", "size": 9451, "crc": 756582332}, {"key": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNodeKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyAdaptingPlatformTextInputModifierNodeKt.class", "size": 1911, "crc": 22696239}, {"key": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoBuilder_androidKt.class", "size": 9303, "crc": -151418647}, {"key": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoController.class", "name": "androidx/compose/foundation/text/input/internal/LegacyCursorAnchorInfoController.class", "size": 6806, "crc": -2131443294}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter$LegacyPlatformTextInputNode.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter$LegacyPlatformTextInputNode.class", "size": 2647, "crc": 417412689}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter.class", "size": 4584, "crc": 134860518}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt$inputMethodManagerFactory$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt$inputMethodManagerFactory$1.class", "size": 1720, "crc": -200688268}, {"key": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/LegacyPlatformTextInputServiceAdapter_androidKt.class", "size": 3334, "crc": -656748035}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$createInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest$createInputConnection$1.class", "size": 4365, "crc": 39441892}, {"key": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest.class", "name": "androidx/compose/foundation/text/input/internal/LegacyTextInputMethodRequest.class", "size": 15955, "crc": -112102373}, {"key": "androidx/compose/foundation/text/input/internal/LocaleListHelper.class", "name": "androidx/compose/foundation/text/input/internal/LocaleListHelper.class", "size": 4327, "crc": -1469064714}, {"key": "androidx/compose/foundation/text/input/internal/MaskCodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/MaskCodepointTransformation.class", "size": 2536, "crc": -893102258}, {"key": "androidx/compose/foundation/text/input/internal/MathUtilsKt.class", "name": "androidx/compose/foundation/text/input/internal/MathUtilsKt.class", "size": 3937, "crc": 187407385}, {"key": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculator.class", "name": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculator.class", "size": 5924, "crc": -994839486}, {"key": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculatorKt.class", "name": "androidx/compose/foundation/text/input/internal/OffsetMappingCalculatorKt.class", "size": 446, "crc": -1975770157}, {"key": "androidx/compose/foundation/text/input/internal/OpArray.class", "name": "androidx/compose/foundation/text/input/internal/OpArray.class", "size": 4752, "crc": -1048609666}, {"key": "androidx/compose/foundation/text/input/internal/PartialGapBuffer$Companion.class", "name": "androidx/compose/foundation/text/input/internal/PartialGapBuffer$Companion.class", "size": 1023, "crc": -1702298438}, {"key": "androidx/compose/foundation/text/input/internal/PartialGapBuffer.class", "name": "androidx/compose/foundation/text/input/internal/PartialGapBuffer.class", "size": 6817, "crc": -2063665314}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection.class", "size": 24145, "crc": -67463121}, {"key": "androidx/compose/foundation/text/input/internal/RecordingInputConnection_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/RecordingInputConnection_androidKt.class", "size": 2061, "crc": 1285710978}, {"key": "androidx/compose/foundation/text/input/internal/SelectionWedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/SelectionWedgeAffinity.class", "size": 3584, "crc": 1494590022}, {"key": "androidx/compose/foundation/text/input/internal/SingleLineCodepointTransformation.class", "name": "androidx/compose/foundation/text/input/internal/SingleLineCodepointTransformation.class", "size": 1700, "crc": -138476836}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitContentDelegateInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$commitContentDelegateInputConnection$1.class", "size": 3735, "crc": -1668245221}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$terminalInputConnection$1.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection$terminalInputConnection$1.class", "size": 1865, "crc": 2092339444}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection.class", "size": 17401, "crc": -212538654}, {"key": "androidx/compose/foundation/text/input/internal/StatelessInputConnection_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/StatelessInputConnection_androidKt.class", "size": 12414, "crc": 1259061127}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifier.class", "size": 10174, "crc": 2019513245}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierKt.class", "size": 3811, "crc": -1050407150}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$1.class", "size": 4860, "crc": 934559238}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$2.class", "size": 3767, "crc": 634223458}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1$2.class", "size": 3991, "crc": -958217985}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$startCursorJob$1.class", "size": 6523, "crc": 222096635}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "size": 4949, "crc": -516974376}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldCoreModifierNode.class", "size": 30401, "crc": -458300257}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifier.class", "size": 11782, "crc": -408303937}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierKt.class", "size": 1592, "crc": 12984019}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$1$1.class", "size": 3694, "crc": -1258267258}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$10$1.class", "size": 3800, "crc": 1197131853}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$11$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$11$1.class", "size": 3802, "crc": 1398827166}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$applySemantics$9$1.class", "size": 3911, "crc": -1956983014}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1$WhenMappings.class", "size": 1017, "crc": -1392535695}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$clipboardKeyCommandsHandler$1$1.class", "size": 5015, "crc": -1257368329}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "size": 2067, "crc": 1204533347}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$observeUntransformedTextChanges$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$observeUntransformedTextChanges$3.class", "size": 1929, "crc": 518294628}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onIsFocusedUpdated$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$onIsFocusedUpdated$1.class", "size": 3788, "crc": 2072694131}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$1.class", "size": 4069, "crc": -650057915}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$2.class", "size": 6203, "crc": -1920480034}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$3.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1$3.class", "size": 4342, "crc": -1225967614}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1.class", "size": 6946, "crc": 57937610}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "size": 2694, "crc": 1079277929}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "size": 1918, "crc": -1777550514}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "size": 7379, "crc": 509612036}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "size": 4268, "crc": 969089257}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$updateNode$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode$updateNode$1.class", "size": 4141, "crc": -1887567086}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDecoratorModifierNode.class", "size": 51825, "crc": 361006945}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$2.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt$textFieldDragAndDropNode$2.class", "size": 7364, "crc": 660507096}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldDragAndDropNode_androidKt.class", "size": 8032, "crc": 1373482419}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "size": 3355, "crc": -25838811}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler.class", "size": 18217, "crc": -1641655648}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldKeyEventHandler_androidKt.class", "size": 1969, "crc": 344365866}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "size": 8265, "crc": -1475262989}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "size": 2555, "crc": 229010643}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "size": 1730, "crc": 1544477851}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "size": 4748, "crc": -1106300012}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "size": 2355, "crc": -1156479442}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "size": 1748, "crc": -743536510}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "size": 3808, "crc": -1148870435}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache.class", "size": 24241, "crc": 1060649077}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCacheKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCacheKt.class", "size": 1523, "crc": 1318425240}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldLayoutStateCache_androidKt.class", "size": 1942, "crc": -991236305}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifier.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifier.class", "size": 5737, "crc": 1563905589}, {"key": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode.class", "name": "androidx/compose/foundation/text/input/internal/TextFieldTextLayoutModifierNode.class", "size": 12755, "crc": -813526925}, {"key": "androidx/compose/foundation/text/input/internal/TextInputSession.class", "name": "androidx/compose/foundation/text/input/internal/TextInputSession.class", "size": 2089, "crc": -624472756}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutState.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutState.class", "size": 14150, "crc": -517898017}, {"key": "androidx/compose/foundation/text/input/internal/TextLayoutStateKt.class", "name": "androidx/compose/foundation/text/input/internal/TextLayoutStateKt.class", "size": 6810, "crc": 183014017}, {"key": "androidx/compose/foundation/text/input/internal/ToCharArray_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/ToCharArray_androidKt.class", "size": 1148, "crc": 518661467}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion$WhenMappings.class", "size": 1063, "crc": 624021419}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$Companion.class", "size": 12060, "crc": 1328926951}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$TransformedText.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$TransformedText.class", "size": 3823, "crc": -1209052554}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "size": 2287, "crc": -777646683}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "size": 2283, "crc": -31803685}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldState.class", "size": 32543, "crc": -1781764535}, {"key": "androidx/compose/foundation/text/input/internal/TransformedTextFieldStateKt.class", "name": "androidx/compose/foundation/text/input/internal/TransformedTextFieldStateKt.class", "size": 2505, "crc": 1013281961}, {"key": "androidx/compose/foundation/text/input/internal/WedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/WedgeAffinity.class", "size": 2031, "crc": 463416813}, {"key": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "size": 2097, "crc": 1608044918}, {"key": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "size": 2441, "crc": -1649812777}, {"key": "androidx/compose/foundation/text/input/internal/selection/ClipboardPasteState.class", "name": "androidx/compose/foundation/text/input/internal/selection/ClipboardPasteState.class", "size": 2433, "crc": 1718660987}, {"key": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity$WhenMappings.class", "size": 954, "crc": -1717099219}, {"key": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity.class", "name": "androidx/compose/foundation/text/input/internal/selection/CursorAndWedgeAffinity.class", "size": 5115, "crc": -1597416371}, {"key": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "size": 7462, "crc": 1131309956}, {"key": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/PressDownGestureKt.class", "size": 2686, "crc": -2007025148}, {"key": "androidx/compose/foundation/text/input/internal/selection/SelectionMovementDeletionContext$Companion.class", "name": "androidx/compose/foundation/text/input/internal/selection/SelectionMovementDeletionContext$Companion.class", "size": 1047, "crc": -1598707345}, {"key": "androidx/compose/foundation/text/input/internal/selection/SelectionMovementDeletionContext.class", "name": "androidx/compose/foundation/text/input/internal/selection/SelectionMovementDeletionContext.class", "size": 34382, "crc": -1729498587}, {"key": "androidx/compose/foundation/text/input/internal/selection/TapOnPosition.class", "name": "androidx/compose/foundation/text/input/internal/selection/TapOnPosition.class", "size": 606, "crc": 401523981}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState$Companion.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState$Companion.class", "size": 1361, "crc": -149792734}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldHandleState.class", "size": 5736, "crc": -748812713}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "size": 945, "crc": -313257789}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierKt.class", "size": 7720, "crc": -1982824811}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNode.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNode.class", "size": 2759, "crc": -817223133}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "size": 4716, "crc": 2124880289}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "size": 5510, "crc": -1572152902}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "size": 7090, "crc": -1748247411}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "size": 14175, "crc": -699286804}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelectionState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldPreparedSelectionState.class", "size": 1228, "crc": -1230042258}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$InputType.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$InputType.class", "size": 2429, "crc": 1860394192}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldMouseSelectionObserver.class", "size": 8843, "crc": 42132118}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$TextFieldTextDragObserver.class", "size": 12054, "crc": 745364747}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$WhenMappings.class", "size": 1101, "crc": -1537141934}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$copy$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$copy$1.class", "size": 2028, "crc": 2053527655}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "size": 3954, "crc": 295015396}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "size": 4059, "crc": 1324804160}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "size": 5677, "crc": 1696865462}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "size": 4865, "crc": -776821840}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cut$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$cut$1.class", "size": 1977, "crc": 968224681}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "size": 2457, "crc": -1112643756}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "size": 2511, "crc": -1893805915}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1$1.class", "size": 5746, "crc": -1928843857}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1$1.class", "size": 6739, "crc": 611015572}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "size": 5100, "crc": 2005273360}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "size": 4548, "crc": 1856176504}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$maybeSuggestSelectionRange$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$maybeSuggestSelectionRange$1.class", "size": 5294, "crc": 1880491538}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "size": 1964, "crc": 226838938}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "size": 2389, "crc": -740912400}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$4.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$4.class", "size": 2291, "crc": -1530101195}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$paste$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$paste$1.class", "size": 2054, "crc": 1450132408}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$pasteAsPlainText$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$pasteAsPlainText$1.class", "size": 2201, "crc": 1102957538}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "size": 3972, "crc": -488291364}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "size": 2227, "crc": 67261997}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "size": 5260, "crc": 222579343}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "size": 4146, "crc": -1403492226}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "size": 5142, "crc": -1313008798}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$1.class", "size": 2125, "crc": -2073431040}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$1.class", "size": 3862, "crc": 1492936111}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2$2.class", "size": 3872, "crc": 1017640512}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState$startToolbarAndHandlesVisibilityObserver$2.class", "size": 4266, "crc": 1382715541}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState.class", "size": 74682, "crc": 1585706517}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt$menuItem$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt$menuItem$1.class", "size": 2351, "crc": 1383413169}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionStateKt.class", "size": 3051, "crc": 133295873}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$1$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$1$1$1.class", "size": 3634, "crc": 767058688}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$1$1$2.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$1$1$2.class", "size": 3736, "crc": -247561970}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$1$1$3.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$1$1$3.class", "size": 3636, "crc": -1864296142}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$textFieldSuspendItem$1$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$addBasicTextFieldTextContextMenuComponents$1$textFieldSuspendItem$1$1.class", "size": 4169, "crc": 122995140}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$lambda$1$textFieldItem$$inlined$TextItem$1.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt$contextMenuBuilder$lambda$1$textFieldItem$$inlined$TextItem$1.class", "size": 3582, "crc": -130597480}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextFieldSelectionState_androidKt.class", "size": 19299, "crc": -116637443}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt$WhenMappings.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt$WhenMappings.class", "size": 1099, "crc": 599079468}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextPreparedSelectionKt.class", "size": 4206, "crc": -1385808670}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextToolbarHandler.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextToolbarHandler.class", "size": 1344, "crc": 2034897032}, {"key": "androidx/compose/foundation/text/input/internal/selection/TextToolbarState.class", "name": "androidx/compose/foundation/text/input/internal/selection/TextToolbarState.class", "size": 2185, "crc": -816404786}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextDeleteType.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextDeleteType.class", "size": 2187, "crc": -221441090}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextEditType.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextEditType.class", "size": 2116, "crc": -1045723553}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextFieldEditUndoBehavior.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextFieldEditUndoBehavior.class", "size": 2225, "crc": -487417493}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "size": 3652, "crc": 1954253719}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation$Companion.class", "size": 1505, "crc": 1260759281}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperation.class", "size": 5514, "crc": 2016361535}, {"key": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperationKt.class", "name": "androidx/compose/foundation/text/input/internal/undo/TextUndoOperationKt.class", "size": 4501, "crc": 925630506}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion$createSaver$1.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion$createSaver$1.class", "size": 6841, "crc": 606772075}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager$Companion.class", "size": 1859, "crc": -1020477802}, {"key": "androidx/compose/foundation/text/input/internal/undo/UndoManager.class", "name": "androidx/compose/foundation/text/input/internal/undo/UndoManager.class", "size": 6760, "crc": 1909714327}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "size": 1138, "crc": 187844482}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "size": 4953, "crc": -1908199061}, {"key": "androidx/compose/foundation/text/modifiers/LayoutCacheOperation$Companion.class", "name": "androidx/compose/foundation/text/modifiers/LayoutCacheOperation$Companion.class", "size": 1725, "crc": 1124583545}, {"key": "androidx/compose/foundation/text/modifiers/LayoutCacheOperation.class", "name": "androidx/compose/foundation/text/modifiers/LayoutCacheOperation.class", "size": 3144, "crc": -601829011}, {"key": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "size": 2645, "crc": -1929787538}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "size": 4556, "crc": 1432599469}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "size": 6462, "crc": -1749170465}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "size": 1301, "crc": -710840812}, {"key": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "size": 1340, "crc": 1388889765}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache$TextAutoSizeLayoutScopeImpl.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache$TextAutoSizeLayoutScopeImpl.class", "size": 6532, "crc": 564055401}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "size": 22952, "crc": -1114865024}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCacheKt.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCacheKt.class", "size": 3260, "crc": 59044909}, {"key": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "size": 19913, "crc": -242699994}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "size": 10319, "crc": 923408056}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "size": 13744, "crc": 1635919323}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController.class", "size": 13292, "crc": -1771460548}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "size": 5230, "crc": 1260526054}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "size": 5792, "crc": 1666198653}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "size": 2636, "crc": 1679354366}, {"key": "androidx/compose/foundation/text/modifiers/SimpleTextAutoSizeLayoutScope.class", "name": "androidx/compose/foundation/text/modifiers/SimpleTextAutoSizeLayoutScope.class", "size": 929, "crc": -619616926}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "size": 1307, "crc": 1195251004}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "size": 4209, "crc": -1743410797}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "size": 10778, "crc": -1248545783}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "size": 5657, "crc": -1144278292}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "size": 37328, "crc": 1483847477}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNodeKt.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNodeKt.class", "size": 815, "crc": -1966162112}, {"key": "androidx/compose/foundation/text/modifiers/TextAutoSizeLayoutScope.class", "name": "androidx/compose/foundation/text/modifiers/TextAutoSizeLayoutScope.class", "size": 1316, "crc": -717496123}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "size": 6154, "crc": -812350566}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "size": 5246, "crc": 2105720937}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "size": 28269, "crc": -1596615133}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "size": 13588, "crc": 1750007646}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "size": 4026, "crc": -1644141234}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "size": 11679, "crc": 811025285}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "size": 24691, "crc": 185210229}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "size": 981, "crc": 580145474}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "size": 26756, "crc": -1121889775}, {"key": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "name": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "size": 831, "crc": 654250999}, {"key": "androidx/compose/foundation/text/selection/ClicksCounter.class", "name": "androidx/compose/foundation/text/selection/ClicksCounter.class", "size": 3192, "crc": -754326823}, {"key": "androidx/compose/foundation/text/selection/CrossStatus.class", "name": "androidx/compose/foundation/text/selection/CrossStatus.class", "size": 2045, "crc": 451147710}, {"key": "androidx/compose/foundation/text/selection/Direction.class", "name": "androidx/compose/foundation/text/selection/Direction.class", "size": 2017, "crc": 357323466}, {"key": "androidx/compose/foundation/text/selection/DownResolution.class", "name": "androidx/compose/foundation/text/selection/DownResolution.class", "size": 2114, "crc": 963946637}, {"key": "androidx/compose/foundation/text/selection/HandleImageCache.class", "name": "androidx/compose/foundation/text/selection/HandleImageCache.class", "size": 2171, "crc": -15320706}, {"key": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "name": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "size": 4270, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "name": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "size": 1296, "crc": 411993979}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "size": 952, "crc": -611247139}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "size": 15315, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "size": 12582, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "size": 11324, "crc": 454970240}, {"key": "androidx/compose/foundation/text/selection/OffsetProvider.class", "name": "androidx/compose/foundation/text/selection/OffsetProvider.class", "size": 547, "crc": -346867239}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviors.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviors.class", "size": 1352, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$classifyText$1.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$classifyText$1.class", "size": 2507, "crc": -12278633}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$onShowContextMenu$2.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$onShowContextMenu$2.class", "size": 4069, "crc": -71005345}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$requireTextClassificationSession$2$1.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$requireTextClassificationSession$2$1.class", "size": 3910, "crc": -1160337900}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$requireTextClassificationSession$2$textClassificationSession$1$1.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$requireTextClassificationSession$2$textClassificationSession$1$1.class", "size": 4745, "crc": 1970743082}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$requireTextClassificationSession$2.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$requireTextClassificationSession$2.class", "size": 7485, "crc": -1580480535}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$suggestSelectionForLongPressOrDoubleClick$2.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl$suggestSelectionForLongPressOrDoubleClick$2.class", "size": 9011, "crc": -595152593}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviorsImpl.class", "size": 19146, "crc": -746596727}, {"key": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviors_androidKt.class", "name": "androidx/compose/foundation/text/selection/PlatformSelectionBehaviors_androidKt.class", "size": 12957, "crc": -787184045}, {"key": "androidx/compose/foundation/text/selection/Selectable.class", "name": "androidx/compose/foundation/text/selection/Selectable.class", "size": 2418, "crc": -1004812656}, {"key": "androidx/compose/foundation/text/selection/SelectableInfo.class", "name": "androidx/compose/foundation/text/selection/SelectableInfo.class", "size": 5859, "crc": -68323162}, {"key": "androidx/compose/foundation/text/selection/SelectedTextType.class", "name": "androidx/compose/foundation/text/selection/SelectedTextType.class", "size": 2032, "crc": 1331491012}, {"key": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "name": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "size": 3618, "crc": -804825301}, {"key": "androidx/compose/foundation/text/selection/Selection.class", "name": "androidx/compose/foundation/text/selection/Selection.class", "size": 4863, "crc": 1380236707}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "size": 1425, "crc": 1958083269}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "size": 1388, "crc": 226653524}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "size": 6300, "crc": 428542940}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "size": 1213, "crc": -1953305697}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "size": 12511, "crc": 1970856242}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "size": 3984, "crc": -238309244}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1$1$1$1$1.class", "size": 2118, "crc": 1440526580}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1$1.class", "size": 11322, "crc": -1331711085}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$1.class", "size": 4056, "crc": -871113561}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4.class", "size": 3939, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$lambda$14$lambda$13$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$lambda$14$lambda$13$$inlined$onDispose$1.class", "size": 2430, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "size": 1876, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "size": 23170, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "size": 1688, "crc": 721400319}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "size": 1997, "crc": 356380567}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelectionBtf2$1.class", "size": 2021, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "size": 7459, "crc": 1053718254}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "size": 2993, "crc": 1413201414}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGesturePointerInputBtf2$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGesturePointerInputBtf2$2.class", "size": 8078, "crc": -131716497}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "size": 1919, "crc": 1350006552}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionFirstPress$1.class", "size": 1979, "crc": -1229688192}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$1.class", "size": 2082, "crc": -2027379690}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelectionSubsequentPress$downResolution$1.class", "size": 7363, "crc": 1192602958}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "size": 4574, "crc": 631416014}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "size": 2218, "crc": 2012797428}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "size": 28504, "crc": 1634663428}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "size": 2104, "crc": -2044472287}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "size": 4808, "crc": -1164762288}, {"key": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "size": 5129, "crc": -1457476133}, {"key": "androidx/compose/foundation/text/selection/SelectionHelpersKt.class", "name": "androidx/compose/foundation/text/selection/SelectionHelpersKt.class", "size": 1980, "crc": -485473592}, {"key": "androidx/compose/foundation/text/selection/SelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SelectionLayout.class", "size": 2642, "crc": -1814572603}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "size": 936, "crc": -705032835}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "size": 7786, "crc": 2064258379}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "size": 926, "crc": -2098468004}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "size": 6123, "crc": 139182306}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "size": 6744, "crc": -1704032645}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2$1.class", "size": 4488, "crc": -1735600237}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1$2.class", "size": 5255, "crc": 1971991951}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "size": 5463, "crc": -898656587}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "size": 12661, "crc": 1351064728}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$contextMenuAreaModifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$contextMenuAreaModifier$1.class", "size": 3207, "crc": -366904371}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$contextMenuAreaModifier$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$contextMenuAreaModifier$2.class", "size": 3207, "crc": 1306758271}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "size": 2707, "crc": 599661556}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "size": 8934, "crc": 1770196972}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "size": 1749, "crc": 900081330}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "size": 5320, "crc": -1207007197}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "size": 2512, "crc": 1555374790}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$suggestSelectionForLongPressOrDoubleClick$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$suggestSelectionForLongPressOrDoubleClick$2.class", "size": 7390, "crc": 510455201}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionTextToolbar$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionTextToolbar$1.class", "size": 1381, "crc": 1746330733}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionTextToolbar$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionTextToolbar$2.class", "size": 1339, "crc": -444201597}, {"key": "androidx/compose/foundation/text/selection/SelectionManager.class", "name": "androidx/compose/foundation/text/selection/SelectionManager.class", "size": 71708, "crc": -334157924}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "size": 909, "crc": 1072054987}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "size": 14644, "crc": 446984881}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "size": 12443, "crc": -276071318}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "size": 14593, "crc": -535613719}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "size": 3603, "crc": -36158686}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "size": 3589, "crc": -849070524}, {"key": "androidx/compose/foundation/text/selection/SelectionMode.class", "name": "androidx/compose/foundation/text/selection/SelectionMode.class", "size": 5281, "crc": 1715099983}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "size": 902, "crc": 2081640161}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "size": 2896, "crc": -956700091}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "size": 1490, "crc": 510348079}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "size": 21789, "crc": 1754596241}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "size": 2567, "crc": 256522913}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "size": 5267, "crc": -1518253456}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "size": 9270, "crc": -1520618346}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "size": 1015, "crc": 1611067731}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "size": 6775, "crc": 2017391630}, {"key": "androidx/compose/foundation/text/selection/TextClassificationResult.class", "name": "androidx/compose/foundation/text/selection/TextClassificationResult.class", "size": 4138, "crc": 362917064}, {"key": "androidx/compose/foundation/text/selection/TextClassifierHelperMethods$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/TextClassifierHelperMethods$WhenMappings.class", "size": 955, "crc": 88224349}, {"key": "androidx/compose/foundation/text/selection/TextClassifierHelperMethods.class", "name": "androidx/compose/foundation/text/selection/TextClassifierHelperMethods.class", "size": 6683, "crc": -1154959567}, {"key": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "size": 11271, "crc": -244616725}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$contextMenuAreaModifier$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$contextMenuAreaModifier$1.class", "size": 3403, "crc": -1462719767}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$contextMenuAreaModifier$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$contextMenuAreaModifier$2.class", "size": 3484, "crc": 1825672043}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$contextMenuAreaModifier$3.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$contextMenuAreaModifier$3.class", "size": 3099, "crc": -283894128}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$copy$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$copy$1.class", "size": 5534, "crc": -1304665870}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "size": 6447, "crc": -821601230}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cut$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cut$1.class", "size": 6143, "crc": -358960348}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "size": 5302, "crc": -534667210}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$maybeSuggestSelection$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$maybeSuggestSelection$1.class", "size": 6283, "crc": -467107203}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "size": 6760, "crc": 1198104142}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$notifyPlatformSelectionBehaviorsOnShowContextMenu$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$notifyPlatformSelectionBehaviorsOnShowContextMenu$1.class", "size": 2248, "crc": -1061527795}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$paste$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$paste$1.class", "size": 6087, "crc": -1614574823}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1$1$copy$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1$1$copy$1$1.class", "size": 3670, "crc": -335873967}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1$1$cut$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1$1$cut$1$1.class", "size": 3564, "crc": 816985469}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1$1$paste$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1$1$paste$1$1.class", "size": 3574, "crc": 1093808156}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbarViaTextToolbar$1.class", "size": 10238, "crc": 474708200}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "size": 10305, "crc": 159683294}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$updateClipboardEntry$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$updateClipboardEntry$1.class", "size": 1976, "crc": 1484935330}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "size": 53638, "crc": -1942730836}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1$1.class", "size": 1429, "crc": -2103569527}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2$1.class", "size": 2055, "crc": 463256691}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "size": 936, "crc": -1988819163}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "size": 14759, "crc": 1619456490}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$2$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$2$1$1.class", "size": 3481, "crc": 2059547170}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$2$1$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$2$1$2.class", "size": 3551, "crc": 1704855737}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$2$1$3.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$2$1$3.class", "size": 3483, "crc": -1984484063}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$textFieldSuspendItem$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$addBasicTextFieldTextContextMenuComponents$1$textFieldSuspendItem$1$1.class", "size": 4022, "crc": 622362597}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "size": 12704, "crc": 411616601}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "size": 19589, "crc": -1992322195}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "size": 3060, "crc": -1206718200}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "size": 1337, "crc": 1117960250}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "size": 2527, "crc": 1649742015}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "size": 2690, "crc": 1447671314}, {"key": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "size": 4129, "crc": 1410304926}, {"key": "META-INF/androidx.compose.foundation_foundation.version", "name": "META-INF/androidx.compose.foundation_foundation.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/foundation_release.kotlin_module", "name": "META-INF/foundation_release.kotlin_module", "size": 8851, "crc": -1646441514}]