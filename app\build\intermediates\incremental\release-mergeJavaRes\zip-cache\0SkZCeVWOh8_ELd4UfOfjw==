[{"key": "androidx/compose/ui/AbsoluteAlignment.class", "name": "androidx/compose/ui/AbsoluteAlignment.class", "size": 3401, "crc": -171314205}, {"key": "androidx/compose/ui/Actual_androidKt.class", "name": "androidx/compose/ui/Actual_androidKt.class", "size": 2362, "crc": -390150088}, {"key": "androidx/compose/ui/Actual_jvmKt$tryPopulateReflectively$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/Actual_jvmKt$tryPopulateReflectively$$inlined$sortedBy$1.class", "size": 2300, "crc": -1258913533}, {"key": "androidx/compose/ui/Actual_jvmKt.class", "name": "androidx/compose/ui/Actual_jvmKt.class", "size": 3813, "crc": 326654867}, {"key": "androidx/compose/ui/Alignment$Companion.class", "name": "androidx/compose/ui/Alignment$Companion.class", "size": 5358, "crc": 2033437993}, {"key": "androidx/compose/ui/Alignment$Horizontal.class", "name": "androidx/compose/ui/Alignment$Horizontal.class", "size": 1439, "crc": 1580325545}, {"key": "androidx/compose/ui/Alignment$Vertical.class", "name": "androidx/compose/ui/Alignment$Vertical.class", "size": 1298, "crc": -1808357713}, {"key": "androidx/compose/ui/Alignment.class", "name": "androidx/compose/ui/Alignment.class", "size": 1259, "crc": 681986104}, {"key": "androidx/compose/ui/AtomicReference_jvmKt.class", "name": "androidx/compose/ui/AtomicReference_jvmKt.class", "size": 426, "crc": 2048800136}, {"key": "androidx/compose/ui/BiasAbsoluteAlignment$Horizontal.class", "name": "androidx/compose/ui/BiasAbsoluteAlignment$Horizontal.class", "size": 4177, "crc": 682656385}, {"key": "androidx/compose/ui/BiasAbsoluteAlignment.class", "name": "androidx/compose/ui/BiasAbsoluteAlignment.class", "size": 5911, "crc": -1567825637}, {"key": "androidx/compose/ui/BiasAlignment$Horizontal.class", "name": "androidx/compose/ui/BiasAlignment$Horizontal.class", "size": 4184, "crc": -1317999010}, {"key": "androidx/compose/ui/BiasAlignment$Vertical.class", "name": "androidx/compose/ui/BiasAlignment$Vertical.class", "size": 4053, "crc": -1254183783}, {"key": "androidx/compose/ui/BiasAlignment.class", "name": "androidx/compose/ui/BiasAlignment.class", "size": 5416, "crc": -1274241255}, {"key": "androidx/compose/ui/CombinedAlignment.class", "name": "androidx/compose/ui/CombinedAlignment.class", "size": 3385, "crc": 1777684737}, {"key": "androidx/compose/ui/CombinedModifier$toString$1.class", "name": "androidx/compose/ui/CombinedModifier$toString$1.class", "size": 1872, "crc": 1649717213}, {"key": "androidx/compose/ui/CombinedModifier.class", "name": "androidx/compose/ui/CombinedModifier.class", "size": 3917, "crc": 1208735450}, {"key": "androidx/compose/ui/ComposeUiFlags.class", "name": "androidx/compose/ui/ComposeUiFlags.class", "size": 3388, "crc": -344420072}, {"key": "androidx/compose/ui/ComposedModifier.class", "name": "androidx/compose/ui/ComposedModifier.class", "size": 2220, "crc": -1823667436}, {"key": "androidx/compose/ui/ComposedModifierKt$materializeImpl$1.class", "name": "androidx/compose/ui/ComposedModifierKt$materializeImpl$1.class", "size": 1657, "crc": 1382094530}, {"key": "androidx/compose/ui/ComposedModifierKt$materializeImpl$result$1.class", "name": "androidx/compose/ui/ComposedModifierKt$materializeImpl$result$1.class", "size": 2933, "crc": -1387918861}, {"key": "androidx/compose/ui/ComposedModifierKt.class", "name": "androidx/compose/ui/ComposedModifierKt.class", "size": 10157, "crc": 545454926}, {"key": "androidx/compose/ui/CompositionLocalMapInjectionElement.class", "name": "androidx/compose/ui/CompositionLocalMapInjectionElement.class", "size": 3194, "crc": 730277204}, {"key": "androidx/compose/ui/CompositionLocalMapInjectionNode.class", "name": "androidx/compose/ui/CompositionLocalMapInjectionNode.class", "size": 1845, "crc": 1463713627}, {"key": "androidx/compose/ui/FrameRateCategory$Companion.class", "name": "androidx/compose/ui/FrameRateCategory$Companion.class", "size": 1338, "crc": -1561062851}, {"key": "androidx/compose/ui/FrameRateCategory.class", "name": "androidx/compose/ui/FrameRateCategory.class", "size": 2755, "crc": 214203992}, {"key": "androidx/compose/ui/FrameRateElement.class", "name": "androidx/compose/ui/FrameRateElement.class", "size": 4040, "crc": 233959619}, {"key": "androidx/compose/ui/FrameRateKt.class", "name": "androidx/compose/ui/FrameRateKt.class", "size": 1972, "crc": -2133664945}, {"key": "androidx/compose/ui/FrameRateModifierNode.class", "name": "androidx/compose/ui/FrameRateModifierNode.class", "size": 5807, "crc": -362791850}, {"key": "androidx/compose/ui/KeepScreenOnElement.class", "name": "androidx/compose/ui/KeepScreenOnElement.class", "size": 2604, "crc": -917245898}, {"key": "androidx/compose/ui/KeepScreenOnKt.class", "name": "androidx/compose/ui/KeepScreenOnKt.class", "size": 858, "crc": 402560635}, {"key": "androidx/compose/ui/KeepScreenOnNode.class", "name": "androidx/compose/ui/KeepScreenOnNode.class", "size": 1164, "crc": -77483184}, {"key": "androidx/compose/ui/KeyedComposedModifier1.class", "name": "androidx/compose/ui/KeyedComposedModifier1.class", "size": 2731, "crc": -420983322}, {"key": "androidx/compose/ui/KeyedComposedModifier2.class", "name": "androidx/compose/ui/KeyedComposedModifier2.class", "size": 3077, "crc": -1225514821}, {"key": "androidx/compose/ui/KeyedComposedModifier3.class", "name": "androidx/compose/ui/KeyedComposedModifier3.class", "size": 3379, "crc": 1586453804}, {"key": "androidx/compose/ui/KeyedComposedModifierN.class", "name": "androidx/compose/ui/KeyedComposedModifierN.class", "size": 2828, "crc": -392228133}, {"key": "androidx/compose/ui/Modifier$Companion.class", "name": "androidx/compose/ui/Modifier$Companion.class", "size": 2540, "crc": -691928566}, {"key": "androidx/compose/ui/Modifier$DefaultImpls.class", "name": "androidx/compose/ui/Modifier$DefaultImpls.class", "size": 820, "crc": -1630897316}, {"key": "androidx/compose/ui/Modifier$Element$DefaultImpls.class", "name": "androidx/compose/ui/Modifier$Element$DefaultImpls.class", "size": 2345, "crc": -579253675}, {"key": "androidx/compose/ui/Modifier$Element.class", "name": "androidx/compose/ui/Modifier$Element.class", "size": 3157, "crc": 1047479767}, {"key": "androidx/compose/ui/Modifier$Node.class", "name": "androidx/compose/ui/Modifier$Node.class", "size": 12416, "crc": -1784177521}, {"key": "androidx/compose/ui/Modifier.class", "name": "androidx/compose/ui/Modifier.class", "size": 2548, "crc": -498344601}, {"key": "androidx/compose/ui/ModifierNodeDetachedCancellationException.class", "name": "androidx/compose/ui/ModifierNodeDetachedCancellationException.class", "size": 943, "crc": -1279673957}, {"key": "androidx/compose/ui/MotionDurationScale$DefaultImpls.class", "name": "androidx/compose/ui/MotionDurationScale$DefaultImpls.class", "size": 2969, "crc": 239952223}, {"key": "androidx/compose/ui/MotionDurationScale$Key.class", "name": "androidx/compose/ui/MotionDurationScale$Key.class", "size": 1026, "crc": 726925241}, {"key": "androidx/compose/ui/MotionDurationScale.class", "name": "androidx/compose/ui/MotionDurationScale.class", "size": 1479, "crc": 2037276165}, {"key": "androidx/compose/ui/SensitiveContentKt.class", "name": "androidx/compose/ui/SensitiveContentKt.class", "size": 1169, "crc": 1683454614}, {"key": "androidx/compose/ui/SensitiveContentNode.class", "name": "androidx/compose/ui/SensitiveContentNode.class", "size": 4213, "crc": -913632572}, {"key": "androidx/compose/ui/SensitiveNodeElement.class", "name": "androidx/compose/ui/SensitiveNodeElement.class", "size": 3748, "crc": 2124700018}, {"key": "androidx/compose/ui/SessionMutex$Session.class", "name": "androidx/compose/ui/SessionMutex$Session.class", "size": 1364, "crc": -2050117412}, {"key": "androidx/compose/ui/SessionMutex$withSessionCancellingPrevious$2.class", "name": "androidx/compose/ui/SessionMutex$withSessionCancellingPrevious$2.class", "size": 5300, "crc": 361286667}, {"key": "androidx/compose/ui/SessionMutex.class", "name": "androidx/compose/ui/SessionMutex.class", "size": 6610, "crc": -1155906358}, {"key": "androidx/compose/ui/UiComposable.class", "name": "androidx/compose/ui/UiComposable.class", "size": 1018, "crc": -411963593}, {"key": "androidx/compose/ui/ZIndexElement.class", "name": "androidx/compose/ui/ZIndexElement.class", "size": 3792, "crc": -527670214}, {"key": "androidx/compose/ui/ZIndexModifierKt.class", "name": "androidx/compose/ui/ZIndexModifierKt.class", "size": 953, "crc": 262369021}, {"key": "androidx/compose/ui/ZIndexNode$measure$1.class", "name": "androidx/compose/ui/ZIndexNode$measure$1.class", "size": 1862, "crc": 769404634}, {"key": "androidx/compose/ui/ZIndexNode.class", "name": "androidx/compose/ui/ZIndexNode.class", "size": 2989, "crc": 2106617630}, {"key": "androidx/compose/ui/autofill/AndroidAutofill.class", "name": "androidx/compose/ui/autofill/AndroidAutofill.class", "size": 5968, "crc": -1971099677}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager$onFocusChanged$2$1.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager$onFocusChanged$2$1.class", "size": 2257, "crc": 54231612}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager$requestAutofill$1.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager$requestAutofill$1.class", "size": 2535, "crc": -363136130}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager.class", "size": 16801, "crc": -286018007}, {"key": "androidx/compose/ui/autofill/AndroidAutofillManager_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofillManager_androidKt.class", "size": 2126, "crc": -1100915252}, {"key": "androidx/compose/ui/autofill/AndroidAutofillType_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofillType_androidKt.class", "size": 5116, "crc": -669915614}, {"key": "androidx/compose/ui/autofill/AndroidAutofill_androidKt.class", "name": "androidx/compose/ui/autofill/AndroidAutofill_androidKt.class", "size": 8876, "crc": -2090528162}, {"key": "androidx/compose/ui/autofill/AndroidContentDataType.class", "name": "androidx/compose/ui/autofill/AndroidContentDataType.class", "size": 2331, "crc": -2085420588}, {"key": "androidx/compose/ui/autofill/AndroidContentType.class", "name": "androidx/compose/ui/autofill/AndroidContentType.class", "size": 1830, "crc": -1533170465}, {"key": "androidx/compose/ui/autofill/Autofill.class", "name": "androidx/compose/ui/autofill/Autofill.class", "size": 1106, "crc": 1803555178}, {"key": "androidx/compose/ui/autofill/AutofillApi26Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi26Helper.class", "size": 8778, "crc": -262098530}, {"key": "androidx/compose/ui/autofill/AutofillApi27Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi27Helper.class", "size": 1476, "crc": -534596345}, {"key": "androidx/compose/ui/autofill/AutofillApi28Helper.class", "name": "androidx/compose/ui/autofill/AutofillApi28Helper.class", "size": 1259, "crc": -982162458}, {"key": "androidx/compose/ui/autofill/AutofillCallback.class", "name": "androidx/compose/ui/autofill/AutofillCallback.class", "size": 2770, "crc": -1855706516}, {"key": "androidx/compose/ui/autofill/AutofillManager.class", "name": "androidx/compose/ui/autofill/AutofillManager.class", "size": 790, "crc": 793987788}, {"key": "androidx/compose/ui/autofill/AutofillModifierKt$contentType$1.class", "name": "androidx/compose/ui/autofill/AutofillModifierKt$contentType$1.class", "size": 1826, "crc": -606623960}, {"key": "androidx/compose/ui/autofill/AutofillModifierKt.class", "name": "androidx/compose/ui/autofill/AutofillModifierKt.class", "size": 1240, "crc": -20805115}, {"key": "androidx/compose/ui/autofill/AutofillNode$Companion.class", "name": "androidx/compose/ui/autofill/AutofillNode$Companion.class", "size": 2437, "crc": -1156009025}, {"key": "androidx/compose/ui/autofill/AutofillNode.class", "name": "androidx/compose/ui/autofill/AutofillNode.class", "size": 5677, "crc": -1736815132}, {"key": "androidx/compose/ui/autofill/AutofillSemanticCallback.class", "name": "androidx/compose/ui/autofill/AutofillSemanticCallback.class", "size": 3232, "crc": 918925263}, {"key": "androidx/compose/ui/autofill/AutofillTree.class", "name": "androidx/compose/ui/autofill/AutofillTree.class", "size": 2596, "crc": 1458091720}, {"key": "androidx/compose/ui/autofill/AutofillType.class", "name": "androidx/compose/ui/autofill/AutofillType.class", "size": 4451, "crc": 1157871173}, {"key": "androidx/compose/ui/autofill/ContentDataType$Companion.class", "name": "androidx/compose/ui/autofill/ContentDataType$Companion.class", "size": 1868, "crc": -1128195796}, {"key": "androidx/compose/ui/autofill/ContentDataType.class", "name": "androidx/compose/ui/autofill/ContentDataType.class", "size": 830, "crc": -626313119}, {"key": "androidx/compose/ui/autofill/ContentDataType_androidKt.class", "name": "androidx/compose/ui/autofill/ContentDataType_androidKt.class", "size": 1326, "crc": -7366905}, {"key": "androidx/compose/ui/autofill/ContentType$Companion.class", "name": "androidx/compose/ui/autofill/ContentType$Companion.class", "size": 8292, "crc": -1825782808}, {"key": "androidx/compose/ui/autofill/ContentType.class", "name": "androidx/compose/ui/autofill/ContentType.class", "size": 1011, "crc": -80334205}, {"key": "androidx/compose/ui/autofill/ContentType_androidKt.class", "name": "androidx/compose/ui/autofill/ContentType_androidKt.class", "size": 2552, "crc": 1279252390}, {"key": "androidx/compose/ui/autofill/PlatformAutofillManager.class", "name": "androidx/compose/ui/autofill/PlatformAutofillManager.class", "size": 1483, "crc": 868344471}, {"key": "androidx/compose/ui/autofill/PlatformAutofillManagerImpl.class", "name": "androidx/compose/ui/autofill/PlatformAutofillManagerImpl.class", "size": 3312, "crc": -1875387082}, {"key": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt$populate$5.class", "name": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt$populate$5.class", "size": 2124, "crc": -398935534}, {"key": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt.class", "name": "androidx/compose/ui/autofill/PopulateViewStructure_androidKt.class", "size": 16320, "crc": 666173076}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$Companion.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$Companion.class", "size": 1060, "crc": -237700741}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$TranslateStatus.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$TranslateStatus.class", "size": 2298, "crc": -466907615}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$ViewTranslationHelperMethods.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$ViewTranslationHelperMethods.class", "size": 9294, "crc": -1602617358}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$WhenMappings.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$WhenMappings.class", "size": 943, "crc": -1166470550}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$boundsUpdatesEventLoop$1.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$boundsUpdatesEventLoop$1.class", "size": 1955, "crc": -1920877996}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$sendContentCaptureAppearEvents$1.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$sendContentCaptureAppearEvents$1.class", "size": 2599, "crc": 301993158}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$updateBuffersOnAppeared$1.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager$updateBuffersOnAppeared$1.class", "size": 1947, "crc": 1382969593}, {"key": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager.class", "name": "androidx/compose/ui/contentcapture/AndroidContentCaptureManager.class", "size": 45510, "crc": -1334622665}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureEvent.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureEvent.class", "size": 4340, "crc": 2006516788}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureEventType.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureEventType.class", "size": 2030, "crc": 795082127}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureManager$Companion.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureManager$Companion.class", "size": 1236, "crc": 2026445861}, {"key": "androidx/compose/ui/contentcapture/ContentCaptureManager.class", "name": "androidx/compose/ui/contentcapture/ContentCaptureManager.class", "size": 957, "crc": -1018944085}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$modifier$1.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$modifier$1.class", "size": 2793, "crc": 1324813635}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$1$1.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$1$1.class", "size": 1509, "crc": -2049800788}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$dragAndDropSourceScope$1.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager$requestDragAndDropTransfer$dragAndDropSourceScope$1.class", "size": 2776, "crc": -1566261322}, {"key": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager.class", "name": "androidx/compose/ui/draganddrop/AndroidDragAndDropManager.class", "size": 8644, "crc": -2093879095}, {"key": "androidx/compose/ui/draganddrop/ComposeDragShadowBuilder.class", "name": "androidx/compose/ui/draganddrop/ComposeDragShadowBuilder.class", "size": 6950, "crc": -1771972801}, {"key": "androidx/compose/ui/draganddrop/DragAndDropEvent.class", "name": "androidx/compose/ui/draganddrop/DragAndDropEvent.class", "size": 1127, "crc": -1158709301}, {"key": "androidx/compose/ui/draganddrop/DragAndDropManager.class", "name": "androidx/compose/ui/draganddrop/DragAndDropManager.class", "size": 1426, "crc": 1254198468}, {"key": "androidx/compose/ui/draganddrop/DragAndDropModifierNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropModifierNode.class", "size": 2021, "crc": 141675693}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion$DragAndDropTraversableKey.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion$DragAndDropTraversableKey.class", "size": 979, "crc": 994630851}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$Companion.class", "size": 984, "crc": 2116187670}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$acceptDragAndDropTransfer$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$acceptDragAndDropTransfer$1.class", "size": 4742, "crc": 2098347947}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$drag$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$drag$1.class", "size": 2422, "crc": -455086961}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$onEnded$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$onEnded$1.class", "size": 2716, "crc": 746913919}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$onMoved$$inlined$firstDescendantOrNull$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$onMoved$$inlined$firstDescendantOrNull$1.class", "size": 4217, "crc": -1848092735}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode$startDragAndDropTransfer$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode$startDragAndDropTransfer$1.class", "size": 4545, "crc": -1489614556}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNode.class", "size": 15084, "crc": -859633077}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropModifierNode$1.class", "size": 2307, "crc": -1839001188}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropTargetModifierNode$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$DragAndDropTargetModifierNode$1.class", "size": 2331, "crc": 1817757188}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$firstDescendantOrNull$1.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt$firstDescendantOrNull$1.class", "size": 2677, "crc": -952933737}, {"key": "androidx/compose/ui/draganddrop/DragAndDropNodeKt.class", "name": "androidx/compose/ui/draganddrop/DragAndDropNodeKt.class", "size": 10970, "crc": 263522450}, {"key": "androidx/compose/ui/draganddrop/DragAndDropSourceModifierNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropSourceModifierNode.class", "size": 893, "crc": 1561027802}, {"key": "androidx/compose/ui/draganddrop/DragAndDropStartTransferScope.class", "name": "androidx/compose/ui/draganddrop/DragAndDropStartTransferScope.class", "size": 1277, "crc": -982469867}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTarget.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTarget.class", "size": 1470, "crc": -1489935863}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTargetModifierNode.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTargetModifierNode.class", "size": 606, "crc": 1398573565}, {"key": "androidx/compose/ui/draganddrop/DragAndDropTransferData.class", "name": "androidx/compose/ui/draganddrop/DragAndDropTransferData.class", "size": 1835, "crc": 660548557}, {"key": "androidx/compose/ui/draganddrop/DragAndDrop_androidKt.class", "name": "androidx/compose/ui/draganddrop/DragAndDrop_androidKt.class", "size": 3640, "crc": -84112774}, {"key": "androidx/compose/ui/draw/AlphaKt.class", "name": "androidx/compose/ui/draw/AlphaKt.class", "size": 1176, "crc": 71937955}, {"key": "androidx/compose/ui/draw/BlockDropShadowElement.class", "name": "androidx/compose/ui/draw/BlockDropShadowElement.class", "size": 4149, "crc": 1207556847}, {"key": "androidx/compose/ui/draw/BlockDropShadowNode$obtainPainter$1.class", "name": "androidx/compose/ui/draw/BlockDropShadowNode$obtainPainter$1.class", "size": 1468, "crc": 39119646}, {"key": "androidx/compose/ui/draw/BlockDropShadowNode.class", "name": "androidx/compose/ui/draw/BlockDropShadowNode.class", "size": 14235, "crc": 1475528305}, {"key": "androidx/compose/ui/draw/BlockInnerShadowElement.class", "name": "androidx/compose/ui/draw/BlockInnerShadowElement.class", "size": 4161, "crc": -616133575}, {"key": "androidx/compose/ui/draw/BlockInnerShadowNode$obtainPainter$1.class", "name": "androidx/compose/ui/draw/BlockInnerShadowNode$obtainPainter$1.class", "size": 1475, "crc": -1126825732}, {"key": "androidx/compose/ui/draw/BlockInnerShadowNode.class", "name": "androidx/compose/ui/draw/BlockInnerShadowNode.class", "size": 14269, "crc": -1049623191}, {"key": "androidx/compose/ui/draw/BlurKt$blur$1.class", "name": "androidx/compose/ui/draw/BlurKt$blur$1.class", "size": 2511, "crc": 1261033866}, {"key": "androidx/compose/ui/draw/BlurKt.class", "name": "androidx/compose/ui/draw/BlurKt.class", "size": 3845, "crc": -1393939893}, {"key": "androidx/compose/ui/draw/BlurredEdgeTreatment$Companion.class", "name": "androidx/compose/ui/draw/BlurredEdgeTreatment$Companion.class", "size": 1391, "crc": 2060833486}, {"key": "androidx/compose/ui/draw/BlurredEdgeTreatment.class", "name": "androidx/compose/ui/draw/BlurredEdgeTreatment.class", "size": 3551, "crc": -1392402940}, {"key": "androidx/compose/ui/draw/BuildDrawCacheParams.class", "name": "androidx/compose/ui/draw/BuildDrawCacheParams.class", "size": 943, "crc": -1299916020}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNode.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNode.class", "size": 624, "crc": 162393218}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$1.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$1.class", "size": 1367, "crc": 452107790}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$getOrBuildCachedDrawBlock$1$1.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl$getOrBuildCachedDrawBlock$1$1.class", "size": 1660, "crc": -1578498893}, {"key": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl.class", "name": "androidx/compose/ui/draw/CacheDrawModifierNodeImpl.class", "size": 9568, "crc": 1486742147}, {"key": "androidx/compose/ui/draw/CacheDrawScope$onDrawBehind$1.class", "name": "androidx/compose/ui/draw/CacheDrawScope$onDrawBehind$1.class", "size": 1838, "crc": 1293273809}, {"key": "androidx/compose/ui/draw/CacheDrawScope$record$1$1.class", "name": "androidx/compose/ui/draw/CacheDrawScope$record$1$1.class", "size": 3642, "crc": 41797020}, {"key": "androidx/compose/ui/draw/CacheDrawScope.class", "name": "androidx/compose/ui/draw/CacheDrawScope.class", "size": 8710, "crc": -1953161268}, {"key": "androidx/compose/ui/draw/ClipKt.class", "name": "androidx/compose/ui/draw/ClipKt.class", "size": 1408, "crc": -1917245738}, {"key": "androidx/compose/ui/draw/DrawBackgroundModifier.class", "name": "androidx/compose/ui/draw/DrawBackgroundModifier.class", "size": 2448, "crc": 706301198}, {"key": "androidx/compose/ui/draw/DrawBehindElement.class", "name": "androidx/compose/ui/draw/DrawBehindElement.class", "size": 3555, "crc": 1252269446}, {"key": "androidx/compose/ui/draw/DrawCacheModifier$DefaultImpls.class", "name": "androidx/compose/ui/draw/DrawCacheModifier$DefaultImpls.class", "size": 2453, "crc": -1245928860}, {"key": "androidx/compose/ui/draw/DrawCacheModifier.class", "name": "androidx/compose/ui/draw/DrawCacheModifier.class", "size": 2214, "crc": 786188277}, {"key": "androidx/compose/ui/draw/DrawModifier$DefaultImpls.class", "name": "androidx/compose/ui/draw/DrawModifier$DefaultImpls.class", "size": 2408, "crc": 2097563781}, {"key": "androidx/compose/ui/draw/DrawModifier.class", "name": "androidx/compose/ui/draw/DrawModifier.class", "size": 2133, "crc": -1377652251}, {"key": "androidx/compose/ui/draw/DrawModifierKt.class", "name": "androidx/compose/ui/draw/DrawModifierKt.class", "size": 3172, "crc": -2105890908}, {"key": "androidx/compose/ui/draw/DrawResult.class", "name": "androidx/compose/ui/draw/DrawResult.class", "size": 1782, "crc": 513484665}, {"key": "androidx/compose/ui/draw/DrawWithCacheElement.class", "name": "androidx/compose/ui/draw/DrawWithCacheElement.class", "size": 3821, "crc": 1618061367}, {"key": "androidx/compose/ui/draw/DrawWithContentElement.class", "name": "androidx/compose/ui/draw/DrawWithContentElement.class", "size": 3603, "crc": -1022239231}, {"key": "androidx/compose/ui/draw/DrawWithContentModifier.class", "name": "androidx/compose/ui/draw/DrawWithContentModifier.class", "size": 2152, "crc": 128222955}, {"key": "androidx/compose/ui/draw/DropShadowScope$DefaultImpls.class", "name": "androidx/compose/ui/draw/DropShadowScope$DefaultImpls.class", "size": 3314, "crc": 1759645903}, {"key": "androidx/compose/ui/draw/DropShadowScope.class", "name": "androidx/compose/ui/draw/DropShadowScope.class", "size": 2848, "crc": 695774312}, {"key": "androidx/compose/ui/draw/EmptyBuildDrawCacheParams.class", "name": "androidx/compose/ui/draw/EmptyBuildDrawCacheParams.class", "size": 1968, "crc": 1594831287}, {"key": "androidx/compose/ui/draw/InnerShadowScope$DefaultImpls.class", "name": "androidx/compose/ui/draw/InnerShadowScope$DefaultImpls.class", "size": 3326, "crc": 285480243}, {"key": "androidx/compose/ui/draw/InnerShadowScope.class", "name": "androidx/compose/ui/draw/InnerShadowScope.class", "size": 2860, "crc": 97462253}, {"key": "androidx/compose/ui/draw/PainterElement.class", "name": "androidx/compose/ui/draw/PainterElement.class", "size": 8196, "crc": 879898427}, {"key": "androidx/compose/ui/draw/PainterModifierKt.class", "name": "androidx/compose/ui/draw/PainterModifierKt.class", "size": 2712, "crc": 1261189558}, {"key": "androidx/compose/ui/draw/PainterNode$measure$1.class", "name": "androidx/compose/ui/draw/PainterNode$measure$1.class", "size": 1816, "crc": 1454183360}, {"key": "androidx/compose/ui/draw/PainterNode.class", "name": "androidx/compose/ui/draw/PainterNode.class", "size": 21691, "crc": 360529707}, {"key": "androidx/compose/ui/draw/RotateKt.class", "name": "androidx/compose/ui/draw/RotateKt.class", "size": 1195, "crc": -968580022}, {"key": "androidx/compose/ui/draw/ScaleKt.class", "name": "androidx/compose/ui/draw/ScaleKt.class", "size": 1458, "crc": 1338095717}, {"key": "androidx/compose/ui/draw/ScopedGraphicsContext.class", "name": "androidx/compose/ui/draw/ScopedGraphicsContext.class", "size": 4984, "crc": -1151545788}, {"key": "androidx/compose/ui/draw/ShadowGraphicsLayerElement$createBlock$1.class", "name": "androidx/compose/ui/draw/ShadowGraphicsLayerElement$createBlock$1.class", "size": 2089, "crc": 693336512}, {"key": "androidx/compose/ui/draw/ShadowGraphicsLayerElement.class", "name": "androidx/compose/ui/draw/ShadowGraphicsLayerElement.class", "size": 7511, "crc": -1773787288}, {"key": "androidx/compose/ui/draw/ShadowKt.class", "name": "androidx/compose/ui/draw/ShadowKt.class", "size": 6087, "crc": 1536984404}, {"key": "androidx/compose/ui/draw/ShadowScope$DefaultImpls.class", "name": "androidx/compose/ui/draw/ShadowScope$DefaultImpls.class", "size": 3318, "crc": 98018912}, {"key": "androidx/compose/ui/draw/ShadowScope.class", "name": "androidx/compose/ui/draw/ShadowScope.class", "size": 3942, "crc": -65107523}, {"key": "androidx/compose/ui/draw/SimpleDropShadowElement.class", "name": "androidx/compose/ui/draw/SimpleDropShadowElement.class", "size": 4928, "crc": -1671992912}, {"key": "androidx/compose/ui/draw/SimpleDropShadowNode.class", "name": "androidx/compose/ui/draw/SimpleDropShadowNode.class", "size": 5018, "crc": 1402173288}, {"key": "androidx/compose/ui/draw/SimpleInnerShadowElement.class", "name": "androidx/compose/ui/draw/SimpleInnerShadowElement.class", "size": 4940, "crc": 671071937}, {"key": "androidx/compose/ui/draw/SimpleInnerShadowNode.class", "name": "androidx/compose/ui/draw/SimpleInnerShadowNode.class", "size": 5090, "crc": 2122639999}, {"key": "androidx/compose/ui/focus/BeyondBoundsLayoutKt.class", "name": "androidx/compose/ui/focus/BeyondBoundsLayoutKt.class", "size": 9827, "crc": -13662069}, {"key": "androidx/compose/ui/focus/CancelIndicatingFocusBoundaryScope.class", "name": "androidx/compose/ui/focus/CancelIndicatingFocusBoundaryScope.class", "size": 1622, "crc": 1623611216}, {"key": "androidx/compose/ui/focus/CustomDestinationResult.class", "name": "androidx/compose/ui/focus/CustomDestinationResult.class", "size": 2071, "crc": -663602707}, {"key": "androidx/compose/ui/focus/FocusChangedElement.class", "name": "androidx/compose/ui/focus/FocusChangedElement.class", "size": 3452, "crc": -468130929}, {"key": "androidx/compose/ui/focus/FocusChangedModifierKt.class", "name": "androidx/compose/ui/focus/FocusChangedModifierKt.class", "size": 1313, "crc": 1476468409}, {"key": "androidx/compose/ui/focus/FocusChangedNode.class", "name": "androidx/compose/ui/focus/FocusChangedNode.class", "size": 2292, "crc": 50259386}, {"key": "androidx/compose/ui/focus/FocusDirection$Companion.class", "name": "androidx/compose/ui/focus/FocusDirection$Companion.class", "size": 2059, "crc": -1071478797}, {"key": "androidx/compose/ui/focus/FocusDirection.class", "name": "androidx/compose/ui/focus/FocusDirection.class", "size": 3293, "crc": -156473540}, {"key": "androidx/compose/ui/focus/FocusEnterExitScope.class", "name": "androidx/compose/ui/focus/FocusEnterExitScope.class", "size": 1127, "crc": -1189599666}, {"key": "androidx/compose/ui/focus/FocusEventElement.class", "name": "androidx/compose/ui/focus/FocusEventElement.class", "size": 3430, "crc": 18270827}, {"key": "androidx/compose/ui/focus/FocusEventModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusEventModifier$DefaultImpls.class", "size": 2477, "crc": -2060009993}, {"key": "androidx/compose/ui/focus/FocusEventModifier.class", "name": "androidx/compose/ui/focus/FocusEventModifier.class", "size": 2271, "crc": 1402583658}, {"key": "androidx/compose/ui/focus/FocusEventModifierKt.class", "name": "androidx/compose/ui/focus/FocusEventModifierKt.class", "size": 1303, "crc": 500018604}, {"key": "androidx/compose/ui/focus/FocusEventModifierNode.class", "name": "androidx/compose/ui/focus/FocusEventModifierNode.class", "size": 765, "crc": -203037571}, {"key": "androidx/compose/ui/focus/FocusEventModifierNodeKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusEventModifierNodeKt$WhenMappings.class", "size": 938, "crc": -1788159905}, {"key": "androidx/compose/ui/focus/FocusEventModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusEventModifierNodeKt.class", "size": 9634, "crc": -311786889}, {"key": "androidx/compose/ui/focus/FocusEventNode.class", "name": "androidx/compose/ui/focus/FocusEventNode.class", "size": 2024, "crc": -263221383}, {"key": "androidx/compose/ui/focus/FocusInteropUtils$Companion.class", "name": "androidx/compose/ui/focus/FocusInteropUtils$Companion.class", "size": 1119, "crc": 1303747035}, {"key": "androidx/compose/ui/focus/FocusInteropUtils.class", "name": "androidx/compose/ui/focus/FocusInteropUtils.class", "size": 1076, "crc": 2106041394}, {"key": "androidx/compose/ui/focus/FocusInteropUtils_androidKt.class", "name": "androidx/compose/ui/focus/FocusInteropUtils_androidKt.class", "size": 6256, "crc": 558095806}, {"key": "androidx/compose/ui/focus/FocusInvalidationManager$scheduleInvalidation$1.class", "name": "androidx/compose/ui/focus/FocusInvalidationManager$scheduleInvalidation$1.class", "size": 1347, "crc": -820973440}, {"key": "androidx/compose/ui/focus/FocusInvalidationManager.class", "name": "androidx/compose/ui/focus/FocusInvalidationManager.class", "size": 11028, "crc": -1766235905}, {"key": "androidx/compose/ui/focus/FocusListener.class", "name": "androidx/compose/ui/focus/FocusListener.class", "size": 756, "crc": 218947468}, {"key": "androidx/compose/ui/focus/FocusManager$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusManager$DefaultImpls.class", "size": 518, "crc": 130311121}, {"key": "androidx/compose/ui/focus/FocusManager.class", "name": "androidx/compose/ui/focus/FocusManager.class", "size": 1127, "crc": 516493126}, {"key": "androidx/compose/ui/focus/FocusModifierKt.class", "name": "androidx/compose/ui/focus/FocusModifierKt.class", "size": 1387, "crc": -88014778}, {"key": "androidx/compose/ui/focus/FocusOrder.class", "name": "androidx/compose/ui/focus/FocusOrder.class", "size": 3608, "crc": 646806431}, {"key": "androidx/compose/ui/focus/FocusOrderModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusOrderModifier$DefaultImpls.class", "size": 2477, "crc": 705458018}, {"key": "androidx/compose/ui/focus/FocusOrderModifier.class", "name": "androidx/compose/ui/focus/FocusOrderModifier.class", "size": 2281, "crc": 23498204}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$1.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$1.class", "size": 1655, "crc": 1689215508}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$2.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt$focusOrder$2.class", "size": 1697, "crc": -75576673}, {"key": "androidx/compose/ui/focus/FocusOrderModifierKt.class", "name": "androidx/compose/ui/focus/FocusOrderModifierKt.class", "size": 3294, "crc": 880185994}, {"key": "androidx/compose/ui/focus/FocusOrderToProperties.class", "name": "androidx/compose/ui/focus/FocusOrderToProperties.class", "size": 2105, "crc": -1184999267}, {"key": "androidx/compose/ui/focus/FocusOwner$dispatchIndirectTouchEvent$1.class", "name": "androidx/compose/ui/focus/FocusOwner$dispatchIndirectTouchEvent$1.class", "size": 1351, "crc": 1507941571}, {"key": "androidx/compose/ui/focus/FocusOwner$dispatchKeyEvent$1.class", "name": "androidx/compose/ui/focus/FocusOwner$dispatchKeyEvent$1.class", "size": 1297, "crc": -767088710}, {"key": "androidx/compose/ui/focus/FocusOwner$dispatchRotaryEvent$1.class", "name": "androidx/compose/ui/focus/FocusOwner$dispatchRotaryEvent$1.class", "size": 1327, "crc": 102097196}, {"key": "androidx/compose/ui/focus/FocusOwner.class", "name": "androidx/compose/ui/focus/FocusOwner.class", "size": 6367, "crc": -535701605}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$WhenMappings.class", "size": 943, "crc": 2015316714}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$focusSearch$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$focusSearch$1.class", "size": 2602, "crc": 157416946}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$modifier$1.class", "size": 2584, "crc": -2128800808}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$moveFocus$focusSearchSuccess$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$moveFocus$focusSearchSuccess$1.class", "size": 1889, "crc": -1594665972}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl$takeFocus$1.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl$takeFocus$1.class", "size": 1490, "crc": 6755525}, {"key": "androidx/compose/ui/focus/FocusOwnerImpl.class", "name": "androidx/compose/ui/focus/FocusOwnerImpl.class", "size": 85539, "crc": -1945893421}, {"key": "androidx/compose/ui/focus/FocusOwnerImplKt.class", "name": "androidx/compose/ui/focus/FocusOwnerImplKt.class", "size": 981, "crc": -1346139191}, {"key": "androidx/compose/ui/focus/FocusOwnerKt.class", "name": "androidx/compose/ui/focus/FocusOwnerKt.class", "size": 501, "crc": 853114442}, {"key": "androidx/compose/ui/focus/FocusProperties$enter$1.class", "name": "androidx/compose/ui/focus/FocusProperties$enter$1.class", "size": 1750, "crc": -1468574301}, {"key": "androidx/compose/ui/focus/FocusProperties$exit$1.class", "name": "androidx/compose/ui/focus/FocusProperties$exit$1.class", "size": 1747, "crc": 1612197831}, {"key": "androidx/compose/ui/focus/FocusProperties$onEnter$1.class", "name": "androidx/compose/ui/focus/FocusProperties$onEnter$1.class", "size": 1423, "crc": 2113388690}, {"key": "androidx/compose/ui/focus/FocusProperties$onExit$1.class", "name": "androidx/compose/ui/focus/FocusProperties$onExit$1.class", "size": 1420, "crc": -998933020}, {"key": "androidx/compose/ui/focus/FocusProperties.class", "name": "androidx/compose/ui/focus/FocusProperties.class", "size": 6043, "crc": -49642074}, {"key": "androidx/compose/ui/focus/FocusPropertiesElement.class", "name": "androidx/compose/ui/focus/FocusPropertiesElement.class", "size": 4175, "crc": -2079847476}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl$onEnter$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl$onEnter$1.class", "size": 1380, "crc": 1736073687}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl$onExit$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl$onExit$1.class", "size": 1378, "crc": 1166769618}, {"key": "androidx/compose/ui/focus/FocusPropertiesImpl.class", "name": "androidx/compose/ui/focus/FocusPropertiesImpl.class", "size": 5264, "crc": 1866339068}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt$sam$androidx_compose_ui_focus_FocusPropertiesScope$0.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt$sam$androidx_compose_ui_focus_FocusPropertiesScope$0.class", "size": 1837, "crc": 295212172}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt$toUsingEnterExitScope$1.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt$toUsingEnterExitScope$1.class", "size": 2612, "crc": -1079089779}, {"key": "androidx/compose/ui/focus/FocusPropertiesKt.class", "name": "androidx/compose/ui/focus/FocusPropertiesKt.class", "size": 2504, "crc": 1083414667}, {"key": "androidx/compose/ui/focus/FocusPropertiesModifierNode.class", "name": "androidx/compose/ui/focus/FocusPropertiesModifierNode.class", "size": 803, "crc": 164485243}, {"key": "androidx/compose/ui/focus/FocusPropertiesModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusPropertiesModifierNodeKt.class", "size": 7514, "crc": -1303376656}, {"key": "androidx/compose/ui/focus/FocusPropertiesNode.class", "name": "androidx/compose/ui/focus/FocusPropertiesNode.class", "size": 1773, "crc": 189077338}, {"key": "androidx/compose/ui/focus/FocusPropertiesScope.class", "name": "androidx/compose/ui/focus/FocusPropertiesScope.class", "size": 677, "crc": 1516803307}, {"key": "androidx/compose/ui/focus/FocusRequester$Companion$FocusRequesterFactory.class", "name": "androidx/compose/ui/focus/FocusRequester$Companion$FocusRequesterFactory.class", "size": 2825, "crc": 1429937073}, {"key": "androidx/compose/ui/focus/FocusRequester$Companion.class", "name": "androidx/compose/ui/focus/FocusRequester$Companion.class", "size": 1885, "crc": 845605994}, {"key": "androidx/compose/ui/focus/FocusRequester$requestFocus$1.class", "name": "androidx/compose/ui/focus/FocusRequester$requestFocus$1.class", "size": 1429, "crc": 1840190681}, {"key": "androidx/compose/ui/focus/FocusRequester.class", "name": "androidx/compose/ui/focus/FocusRequester.class", "size": 20658, "crc": -1653899579}, {"key": "androidx/compose/ui/focus/FocusRequesterElement.class", "name": "androidx/compose/ui/focus/FocusRequesterElement.class", "size": 5388, "crc": -224281987}, {"key": "androidx/compose/ui/focus/FocusRequesterKt.class", "name": "androidx/compose/ui/focus/FocusRequesterKt.class", "size": 1142, "crc": 789725699}, {"key": "androidx/compose/ui/focus/FocusRequesterModifier$DefaultImpls.class", "name": "androidx/compose/ui/focus/FocusRequesterModifier$DefaultImpls.class", "size": 2517, "crc": 1319619261}, {"key": "androidx/compose/ui/focus/FocusRequesterModifier.class", "name": "androidx/compose/ui/focus/FocusRequesterModifier.class", "size": 2304, "crc": -635847611}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierKt.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierKt.class", "size": 1087, "crc": -1564326590}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNode.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNode.class", "size": 519, "crc": 964943300}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt$requestFocus$1$1.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt$requestFocus$1$1.class", "size": 1740, "crc": -1700015272}, {"key": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusRequesterModifierNodeKt.class", "size": 30531, "crc": 1925584334}, {"key": "androidx/compose/ui/focus/FocusRequesterNode.class", "name": "androidx/compose/ui/focus/FocusRequesterNode.class", "size": 2876, "crc": 544472306}, {"key": "androidx/compose/ui/focus/FocusRestorerElement.class", "name": "androidx/compose/ui/focus/FocusRestorerElement.class", "size": 4140, "crc": -834420788}, {"key": "androidx/compose/ui/focus/FocusRestorerKt$saveFocusedChild$1$1.class", "name": "androidx/compose/ui/focus/FocusRestorerKt$saveFocusedChild$1$1.class", "size": 1308, "crc": 773441615}, {"key": "androidx/compose/ui/focus/FocusRestorerKt.class", "name": "androidx/compose/ui/focus/FocusRestorerKt.class", "size": 15319, "crc": -356254368}, {"key": "androidx/compose/ui/focus/FocusRestorerNode$onEnter$1.class", "name": "androidx/compose/ui/focus/FocusRestorerNode$onEnter$1.class", "size": 2955, "crc": 1873465721}, {"key": "androidx/compose/ui/focus/FocusRestorerNode$onExit$1.class", "name": "androidx/compose/ui/focus/FocusRestorerNode$onExit$1.class", "size": 2495, "crc": -2036512991}, {"key": "androidx/compose/ui/focus/FocusRestorerNode.class", "name": "androidx/compose/ui/focus/FocusRestorerNode.class", "size": 3957, "crc": 387055966}, {"key": "androidx/compose/ui/focus/FocusState.class", "name": "androidx/compose/ui/focus/FocusState.class", "size": 554, "crc": -390172898}, {"key": "androidx/compose/ui/focus/FocusStateImpl$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusStateImpl$WhenMappings.class", "size": 860, "crc": -1794392034}, {"key": "androidx/compose/ui/focus/FocusStateImpl.class", "name": "androidx/compose/ui/focus/FocusStateImpl.class", "size": 2945, "crc": -1940764870}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNode.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNode.class", "size": 2142, "crc": -1696343286}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNodeKt$FocusTargetModifierNode$1.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNodeKt$FocusTargetModifierNode$1.class", "size": 1677, "crc": -591133185}, {"key": "androidx/compose/ui/focus/FocusTargetModifierNodeKt.class", "name": "androidx/compose/ui/focus/FocusTargetModifierNodeKt.class", "size": 2790, "crc": 606539735}, {"key": "androidx/compose/ui/focus/FocusTargetNode$FocusTargetElement.class", "name": "androidx/compose/ui/focus/FocusTargetNode$FocusTargetElement.class", "size": 2837, "crc": -2137802049}, {"key": "androidx/compose/ui/focus/FocusTargetNode$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTargetNode$WhenMappings.class", "size": 1346, "crc": -1256566192}, {"key": "androidx/compose/ui/focus/FocusTargetNode$invalidateFocus$1.class", "name": "androidx/compose/ui/focus/FocusTargetNode$invalidateFocus$1.class", "size": 1790, "crc": -1813733645}, {"key": "androidx/compose/ui/focus/FocusTargetNode.class", "name": "androidx/compose/ui/focus/FocusTargetNode.class", "size": 28118, "crc": -1877593491}, {"key": "androidx/compose/ui/focus/FocusTargetNodeKt.class", "name": "androidx/compose/ui/focus/FocusTargetNodeKt.class", "size": 1083, "crc": -289987589}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt$WhenMappings.class", "size": 923, "crc": 150673239}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt$grantFocus$1.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt$grantFocus$1.class", "size": 1396, "crc": -1311143363}, {"key": "androidx/compose/ui/focus/FocusTransactionsKt.class", "name": "androidx/compose/ui/focus/FocusTransactionsKt.class", "size": 27851, "crc": 789243689}, {"key": "androidx/compose/ui/focus/FocusTraversalKt$WhenMappings.class", "name": "androidx/compose/ui/focus/FocusTraversalKt$WhenMappings.class", "size": 1206, "crc": 531599992}, {"key": "androidx/compose/ui/focus/FocusTraversalKt.class", "name": "androidx/compose/ui/focus/FocusTraversalKt.class", "size": 19164, "crc": -1344883436}, {"key": "androidx/compose/ui/focus/Focusability$Companion.class", "name": "androidx/compose/ui/focus/Focusability$Companion.class", "size": 1358, "crc": -2109612082}, {"key": "androidx/compose/ui/focus/Focusability.class", "name": "androidx/compose/ui/focus/Focusability.class", "size": 4564, "crc": -1036609532}, {"key": "androidx/compose/ui/focus/FocusableChildrenComparator.class", "name": "androidx/compose/ui/focus/FocusableChildrenComparator.class", "size": 5153, "crc": -1477897064}, {"key": "androidx/compose/ui/focus/InvalidateSemantics.class", "name": "androidx/compose/ui/focus/InvalidateSemantics.class", "size": 1502, "crc": -1464241072}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$WhenMappings.class", "size": 947, "crc": 1446920044}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "size": 3427, "crc": -14618276}, {"key": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt.class", "name": "androidx/compose/ui/focus/OneDimensionalFocusSearchKt.class", "size": 29452, "crc": 590525314}, {"key": "androidx/compose/ui/focus/PlatformFocusOwner.class", "name": "androidx/compose/ui/focus/PlatformFocusOwner.class", "size": 1135, "crc": -1835401792}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$WhenMappings.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$WhenMappings.class", "size": 947, "crc": 646473592}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt$generateAndSearchChildren$1.class", "size": 3441, "crc": -1660504406}, {"key": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt.class", "name": "androidx/compose/ui/focus/TwoDimensionalFocusSearchKt.class", "size": 26519, "crc": 1745754316}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerElement.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerElement.class", "size": 3670, "crc": -584266199}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier$measure$1.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier$measure$1.class", "size": 2157, "crc": 997297572}, {"key": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier.class", "name": "androidx/compose/ui/graphics/BlockGraphicsLayerModifier.class", "size": 5926, "crc": -1134943130}, {"key": "androidx/compose/ui/graphics/CompositingStrategy$Companion.class", "name": "androidx/compose/ui/graphics/CompositingStrategy$Companion.class", "size": 1413, "crc": -737645567}, {"key": "androidx/compose/ui/graphics/CompositingStrategy.class", "name": "androidx/compose/ui/graphics/CompositingStrategy.class", "size": 2832, "crc": -886629326}, {"key": "androidx/compose/ui/graphics/Fields.class", "name": "androidx/compose/ui/graphics/Fields.class", "size": 1962, "crc": 1238705989}, {"key": "androidx/compose/ui/graphics/GraphicsContextObserver.class", "name": "androidx/compose/ui/graphics/GraphicsContextObserver.class", "size": 1754, "crc": -1280105212}, {"key": "androidx/compose/ui/graphics/GraphicsLayerElement.class", "name": "androidx/compose/ui/graphics/GraphicsLayerElement.class", "size": 14769, "crc": 919379409}, {"key": "androidx/compose/ui/graphics/GraphicsLayerModifierKt.class", "name": "androidx/compose/ui/graphics/GraphicsLayerModifierKt.class", "size": 12380, "crc": 19936696}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScope$DefaultImpls.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScope$DefaultImpls.class", "size": 6538, "crc": 645195950}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScope.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScope.class", "size": 9722, "crc": 270103472}, {"key": "androidx/compose/ui/graphics/GraphicsLayerScopeKt.class", "name": "androidx/compose/ui/graphics/GraphicsLayerScopeKt.class", "size": 5056, "crc": -1451888277}, {"key": "androidx/compose/ui/graphics/ReusableGraphicsLayerScope.class", "name": "androidx/compose/ui/graphics/ReusableGraphicsLayerScope.class", "size": 12392, "crc": 864373900}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$layerBlock$1.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$layerBlock$1.class", "size": 3419, "crc": -981535896}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$measure$1.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier$measure$1.class", "size": 2229, "crc": 156680434}, {"key": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier.class", "name": "androidx/compose/ui/graphics/SimpleGraphicsLayerModifier.class", "size": 14349, "crc": 685577202}, {"key": "androidx/compose/ui/graphics/TransformOrigin$Companion.class", "name": "androidx/compose/ui/graphics/TransformOrigin$Companion.class", "size": 1069, "crc": 143467793}, {"key": "androidx/compose/ui/graphics/TransformOrigin.class", "name": "androidx/compose/ui/graphics/TransformOrigin.class", "size": 5065, "crc": -1535609104}, {"key": "androidx/compose/ui/graphics/TransformOriginKt.class", "name": "androidx/compose/ui/graphics/TransformOriginKt.class", "size": 1631, "crc": 1233864113}, {"key": "androidx/compose/ui/graphics/vector/DrawCache.class", "name": "androidx/compose/ui/graphics/vector/DrawCache.class", "size": 10019, "crc": -387022739}, {"key": "androidx/compose/ui/graphics/vector/GroupComponent$wrappedListener$1.class", "name": "androidx/compose/ui/graphics/vector/GroupComponent$wrappedListener$1.class", "size": 1786, "crc": -1683815527}, {"key": "androidx/compose/ui/graphics/vector/GroupComponent.class", "name": "androidx/compose/ui/graphics/vector/GroupComponent.class", "size": 15411, "crc": -1138713571}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Builder$GroupParams.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Builder$GroupParams.class", "size": 5296, "crc": 1445232740}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Builder.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Builder.class", "size": 12027, "crc": 556406820}, {"key": "androidx/compose/ui/graphics/vector/ImageVector$Companion.class", "name": "androidx/compose/ui/graphics/vector/ImageVector$Companion.class", "size": 2420, "crc": 1044058257}, {"key": "androidx/compose/ui/graphics/vector/ImageVector.class", "name": "androidx/compose/ui/graphics/vector/ImageVector.class", "size": 6463, "crc": -485772037}, {"key": "androidx/compose/ui/graphics/vector/ImageVectorKt.class", "name": "androidx/compose/ui/graphics/vector/ImageVectorKt.class", "size": 8330, "crc": 1337606543}, {"key": "androidx/compose/ui/graphics/vector/PathComponent$pathMeasure$2.class", "name": "androidx/compose/ui/graphics/vector/PathComponent$pathMeasure$2.class", "size": 1264, "crc": -1025752376}, {"key": "androidx/compose/ui/graphics/vector/PathComponent.class", "name": "androidx/compose/ui/graphics/vector/PathComponent.class", "size": 10529, "crc": -1502533414}, {"key": "androidx/compose/ui/graphics/vector/VNode.class", "name": "androidx/compose/ui/graphics/vector/VNode.class", "size": 2438, "crc": -303257817}, {"key": "androidx/compose/ui/graphics/vector/VectorApplier.class", "name": "androidx/compose/ui/graphics/vector/VectorApplier.class", "size": 3540, "crc": -1427612754}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$1.class", "size": 1552, "crc": 1178488446}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$drawVectorBlock$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$drawVectorBlock$1.class", "size": 4806, "crc": 266782662}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent$invalidateCallback$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent$invalidateCallback$1.class", "size": 1185, "crc": -68487122}, {"key": "androidx/compose/ui/graphics/vector/VectorComponent.class", "name": "androidx/compose/ui/graphics/vector/VectorComponent.class", "size": 13673, "crc": -1918898306}, {"key": "androidx/compose/ui/graphics/vector/VectorComposable.class", "name": "androidx/compose/ui/graphics/vector/VectorComposable.class", "size": 1066, "crc": -479083151}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$1$1.class", "size": 1406, "crc": -679509267}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$1.class", "size": 1749, "crc": 368840284}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$2.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$2.class", "size": 1733, "crc": 987083648}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$3.class", "size": 1731, "crc": 1588538952}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$4.class", "size": 1731, "crc": -425721227}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$5.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$5.class", "size": 1731, "crc": 1243767063}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$6.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$6.class", "size": 1731, "crc": -1035326153}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$7.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$7.class", "size": 1737, "crc": -2143726964}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$8.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$8.class", "size": 1737, "crc": 666816523}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$9.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$2$9.class", "size": 1989, "crc": -1528964714}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Group$4.class", "size": 2648, "crc": 2070940631}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$1$1.class", "size": 1451, "crc": -1554125308}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$1.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$1.class", "size": 1794, "crc": -818848335}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$10.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$10.class", "size": 1897, "crc": 1167310558}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$11.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$11.class", "size": 1787, "crc": -648887092}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$12.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$12.class", "size": 1785, "crc": 846478675}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$13.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$13.class", "size": 1783, "crc": 894561019}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$14.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$14.class", "size": 1786, "crc": -2129556400}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$2.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$2.class", "size": 2029, "crc": -230347835}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$3.class", "size": 1903, "crc": -1079407462}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$4.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$4.class", "size": 1886, "crc": -1570258745}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$5.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$5.class", "size": 1779, "crc": 55534699}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$6.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$6.class", "size": 1888, "crc": 1680556733}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$7.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$7.class", "size": 1781, "crc": -1476090891}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$8.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$8.class", "size": 1785, "crc": -4818571}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$9.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$2$9.class", "size": 1899, "crc": 16701761}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$3.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt$Path$3.class", "size": 2898, "crc": 1234010356}, {"key": "androidx/compose/ui/graphics/vector/VectorComposeKt.class", "name": "androidx/compose/ui/graphics/vector/VectorComposeKt.class", "size": 16831, "crc": 1749902502}, {"key": "androidx/compose/ui/graphics/vector/VectorConfig$DefaultImpls.class", "name": "androidx/compose/ui/graphics/vector/VectorConfig$DefaultImpls.class", "size": 1174, "crc": -1683132969}, {"key": "androidx/compose/ui/graphics/vector/VectorConfig.class", "name": "androidx/compose/ui/graphics/vector/VectorConfig.class", "size": 1410, "crc": 331040581}, {"key": "androidx/compose/ui/graphics/vector/VectorGroup$iterator$1.class", "name": "androidx/compose/ui/graphics/vector/VectorGroup$iterator$1.class", "size": 2206, "crc": 525187131}, {"key": "androidx/compose/ui/graphics/vector/VectorGroup.class", "name": "androidx/compose/ui/graphics/vector/VectorGroup.class", "size": 6091, "crc": -762052465}, {"key": "androidx/compose/ui/graphics/vector/VectorKt.class", "name": "androidx/compose/ui/graphics/vector/VectorKt.class", "size": 6291, "crc": 124649466}, {"key": "androidx/compose/ui/graphics/vector/VectorNode.class", "name": "androidx/compose/ui/graphics/vector/VectorNode.class", "size": 1031, "crc": 1659909211}, {"key": "androidx/compose/ui/graphics/vector/VectorPainter$vector$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainter$vector$1$1.class", "size": 1548, "crc": -178379740}, {"key": "androidx/compose/ui/graphics/vector/VectorPainter.class", "name": "androidx/compose/ui/graphics/vector/VectorPainter.class", "size": 12242, "crc": 1309008136}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$1.class", "size": 2876, "crc": 2145279925}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$2.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$2.class", "size": 2105, "crc": 1321944180}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$1.class", "size": 945, "crc": -1170838305}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$2.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$RenderVectorGroup$config$2.class", "size": 945, "crc": -1834092545}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1$invoke$$inlined$onDispose$1.class", "size": 2208, "crc": -637571466}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$1$1.class", "size": 2895, "crc": 855499024}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$composition$1$1.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt$rememberVectorPainter$2$composition$1$1.class", "size": 4788, "crc": 1259004}, {"key": "androidx/compose/ui/graphics/vector/VectorPainterKt.class", "name": "androidx/compose/ui/graphics/vector/VectorPainterKt.class", "size": 32183, "crc": -847934931}, {"key": "androidx/compose/ui/graphics/vector/VectorPath.class", "name": "androidx/compose/ui/graphics/vector/VectorPath.class", "size": 7032, "crc": 1870156218}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Fill.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Fill.class", "size": 1182, "crc": 1321285130}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$FillAlpha.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$FillAlpha.class", "size": 1140, "crc": 1743147106}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PathData.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PathData.class", "size": 1247, "crc": -1953584434}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PivotX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PivotX.class", "size": 1131, "crc": -1322796637}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$PivotY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$PivotY.class", "size": 1131, "crc": -199318091}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Rotation.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Rotation.class", "size": 1137, "crc": 811864723}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleX.class", "size": 1131, "crc": 1334584420}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$ScaleY.class", "size": 1131, "crc": 179648626}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$Stroke.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$Stroke.class", "size": 1188, "crc": 1122246972}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeAlpha.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeAlpha.class", "size": 1146, "crc": -715916880}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeLineWidth.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$StrokeLineWidth.class", "size": 1158, "crc": -121537342}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateX.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateX.class", "size": 1143, "crc": -70196808}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateY.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TranslateY.class", "size": 1143, "crc": 827744956}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathEnd.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathEnd.class", "size": 1146, "crc": -1558604757}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathOffset.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathOffset.class", "size": 1155, "crc": -777724330}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathStart.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty$TrimPathStart.class", "size": 1152, "crc": -916182032}, {"key": "androidx/compose/ui/graphics/vector/VectorProperty.class", "name": "androidx/compose/ui/graphics/vector/VectorProperty.class", "size": 3632, "crc": 1136427962}, {"key": "androidx/compose/ui/graphics/vector/compat/AndroidVectorParser.class", "name": "androidx/compose/ui/graphics/vector/compat/AndroidVectorParser.class", "size": 9238, "crc": 1455923701}, {"key": "androidx/compose/ui/graphics/vector/compat/AndroidVectorResources.class", "name": "androidx/compose/ui/graphics/vector/compat/AndroidVectorResources.class", "size": 9169, "crc": 296130005}, {"key": "androidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt.class", "name": "androidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt.class", "size": 18936, "crc": 515053702}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedback.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedback.class", "size": 621, "crc": -1604338557}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedbackType$Companion.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedbackType$Companion.class", "size": 3701, "crc": 1066363205}, {"key": "androidx/compose/ui/hapticfeedback/HapticFeedbackType.class", "name": "androidx/compose/ui/hapticfeedback/HapticFeedbackType.class", "size": 3336, "crc": -768132019}, {"key": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedback.class", "name": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedback.class", "size": 2647, "crc": 1784628373}, {"key": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedbackType.class", "name": "androidx/compose/ui/hapticfeedback/PlatformHapticFeedbackType.class", "size": 3099, "crc": 775329209}, {"key": "androidx/compose/ui/input/InputMode$Companion.class", "name": "androidx/compose/ui/input/InputMode$Companion.class", "size": 1186, "crc": 863975520}, {"key": "androidx/compose/ui/input/InputMode.class", "name": "androidx/compose/ui/input/InputMode.class", "size": 2526, "crc": 757819771}, {"key": "androidx/compose/ui/input/InputModeManager.class", "name": "androidx/compose/ui/input/InputModeManager.class", "size": 638, "crc": 489014641}, {"key": "androidx/compose/ui/input/InputModeManagerImpl.class", "name": "androidx/compose/ui/input/InputModeManagerImpl.class", "size": 3972, "crc": -410022396}, {"key": "androidx/compose/ui/input/indirect/AndroidIndirectTouchEvent.class", "name": "androidx/compose/ui/input/indirect/AndroidIndirectTouchEvent.class", "size": 2201, "crc": -699778623}, {"key": "androidx/compose/ui/input/indirect/AndroidIndirectTouchEvent_androidKt.class", "name": "androidx/compose/ui/input/indirect/AndroidIndirectTouchEvent_androidKt.class", "size": 4193, "crc": -709147461}, {"key": "androidx/compose/ui/input/indirect/IndirectTouchEvent.class", "name": "androidx/compose/ui/input/indirect/IndirectTouchEvent.class", "size": 956, "crc": -1158794110}, {"key": "androidx/compose/ui/input/indirect/IndirectTouchEventType$Companion.class", "name": "androidx/compose/ui/input/indirect/IndirectTouchEventType$Companion.class", "size": 1667, "crc": -491077525}, {"key": "androidx/compose/ui/input/indirect/IndirectTouchEventType.class", "name": "androidx/compose/ui/input/indirect/IndirectTouchEventType.class", "size": 2921, "crc": 1196559419}, {"key": "androidx/compose/ui/input/indirect/IndirectTouchInputElement.class", "name": "androidx/compose/ui/input/indirect/IndirectTouchInputElement.class", "size": 4656, "crc": -70912505}, {"key": "androidx/compose/ui/input/indirect/IndirectTouchInputModifierKt.class", "name": "androidx/compose/ui/input/indirect/IndirectTouchInputModifierKt.class", "size": 1769, "crc": -300021109}, {"key": "androidx/compose/ui/input/indirect/IndirectTouchInputModifierNode.class", "name": "androidx/compose/ui/input/indirect/IndirectTouchInputModifierNode.class", "size": 1012, "crc": 2117084372}, {"key": "androidx/compose/ui/input/indirect/IndirectTouchInputNode.class", "name": "androidx/compose/ui/input/indirect/IndirectTouchInputNode.class", "size": 3260, "crc": -257939920}, {"key": "androidx/compose/ui/input/indirect/PlatformIndirectTouchEvent.class", "name": "androidx/compose/ui/input/indirect/PlatformIndirectTouchEvent.class", "size": 555, "crc": -1577767328}, {"key": "androidx/compose/ui/input/key/InterceptedKeyInputNode.class", "name": "androidx/compose/ui/input/key/InterceptedKeyInputNode.class", "size": 3356, "crc": 1094906509}, {"key": "androidx/compose/ui/input/key/Key$Companion.class", "name": "androidx/compose/ui/input/key/Key$Companion.class", "size": 46729, "crc": 1445760180}, {"key": "androidx/compose/ui/input/key/Key.class", "name": "androidx/compose/ui/input/key/Key.class", "size": 33732, "crc": 1119536541}, {"key": "androidx/compose/ui/input/key/KeyEvent.class", "name": "androidx/compose/ui/input/key/KeyEvent.class", "size": 2689, "crc": 640759190}, {"key": "androidx/compose/ui/input/key/KeyEventType$Companion.class", "name": "androidx/compose/ui/input/key/KeyEventType$Companion.class", "size": 1355, "crc": -663884357}, {"key": "androidx/compose/ui/input/key/KeyEventType.class", "name": "androidx/compose/ui/input/key/KeyEventType.class", "size": 2680, "crc": -1309764533}, {"key": "androidx/compose/ui/input/key/KeyEvent_androidKt.class", "name": "androidx/compose/ui/input/key/KeyEvent_androidKt.class", "size": 2521, "crc": -1646933707}, {"key": "androidx/compose/ui/input/key/KeyInputElement.class", "name": "androidx/compose/ui/input/key/KeyInputElement.class", "size": 4357, "crc": 351459620}, {"key": "androidx/compose/ui/input/key/KeyInputModifierKt.class", "name": "androidx/compose/ui/input/key/KeyInputModifierKt.class", "size": 1550, "crc": -270957879}, {"key": "androidx/compose/ui/input/key/KeyInputModifierNode.class", "name": "androidx/compose/ui/input/key/KeyInputModifierNode.class", "size": 876, "crc": -1529315554}, {"key": "androidx/compose/ui/input/key/KeyInputNode.class", "name": "androidx/compose/ui/input/key/KeyInputNode.class", "size": 3194, "crc": -195800656}, {"key": "androidx/compose/ui/input/key/Key_androidKt.class", "name": "androidx/compose/ui/input/key/Key_androidKt.class", "size": 1668, "crc": 331541285}, {"key": "androidx/compose/ui/input/key/SoftKeyboardInterceptionElement.class", "name": "androidx/compose/ui/input/key/SoftKeyboardInterceptionElement.class", "size": 4555, "crc": -695817895}, {"key": "androidx/compose/ui/input/key/SoftKeyboardInterceptionModifierNode.class", "name": "androidx/compose/ui/input/key/SoftKeyboardInterceptionModifierNode.class", "size": 1012, "crc": -427316102}, {"key": "androidx/compose/ui/input/key/SoftwareKeyboardInterceptionModifierKt.class", "name": "androidx/compose/ui/input/key/SoftwareKeyboardInterceptionModifierKt.class", "size": 1734, "crc": -375584788}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection$DefaultImpls.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection$DefaultImpls.class", "size": 2314, "crc": 1471872858}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollConnection.class", "size": 4055, "crc": 427091982}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$calculateNestedScrollScope$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$calculateNestedScrollScope$1.class", "size": 1381, "crc": 834011438}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPostFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPostFling$1.class", "size": 1890, "crc": -1778833932}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPreFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher$dispatchPreFling$1.class", "size": 1876, "crc": 116888315}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollDispatcher.class", "size": 7457, "crc": 682767388}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollElement.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollElement.class", "size": 3981, "crc": 1302693000}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollModifierKt.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollModifierKt.class", "size": 1799, "crc": -633997148}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPostFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPostFling$1.class", "size": 1916, "crc": 1868278486}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPreFling$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$onPreFling$1.class", "size": 1881, "crc": -1849155115}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$updateDispatcherFields$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode$updateDispatcherFields$1.class", "size": 1470, "crc": -1413388454}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNode.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNode.class", "size": 10478, "crc": -456537224}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt$findNearestAttachedAncestor$1.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt$findNearestAttachedAncestor$1.class", "size": 2170, "crc": -1449429955}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollNodeKt.class", "size": 2512, "crc": 1736158793}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollSource$Companion.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollSource$Companion.class", "size": 2823, "crc": -1774437611}, {"key": "androidx/compose/ui/input/nestedscroll/NestedScrollSource.class", "name": "androidx/compose/ui/input/nestedscroll/NestedScrollSource.class", "size": 3115, "crc": -2013778805}, {"key": "androidx/compose/ui/input/pointer/AndroidPointerIcon.class", "name": "androidx/compose/ui/input/pointer/AndroidPointerIcon.class", "size": 2449, "crc": -1896955706}, {"key": "androidx/compose/ui/input/pointer/AndroidPointerIconType.class", "name": "androidx/compose/ui/input/pointer/AndroidPointerIconType.class", "size": 2257, "crc": -861393613}, {"key": "androidx/compose/ui/input/pointer/AwaitPointerEventScope$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/AwaitPointerEventScope$DefaultImpls.class", "size": 5117, "crc": -928914164}, {"key": "androidx/compose/ui/input/pointer/AwaitPointerEventScope.class", "name": "androidx/compose/ui/input/pointer/AwaitPointerEventScope.class", "size": 7481, "crc": -1194892107}, {"key": "androidx/compose/ui/input/pointer/CancelTimeoutCancellationException.class", "name": "androidx/compose/ui/input/pointer/CancelTimeoutCancellationException.class", "size": 1088, "crc": 463489670}, {"key": "androidx/compose/ui/input/pointer/ConsumedData.class", "name": "androidx/compose/ui/input/pointer/ConsumedData.class", "size": 3024, "crc": 1749658352}, {"key": "androidx/compose/ui/input/pointer/HistoricalChange.class", "name": "androidx/compose/ui/input/pointer/HistoricalChange.class", "size": 2642, "crc": -1751722419}, {"key": "androidx/compose/ui/input/pointer/HitPathTracker$addHitPath$1.class", "name": "androidx/compose/ui/input/pointer/HitPathTracker$addHitPath$1.class", "size": 1521, "crc": 990607586}, {"key": "androidx/compose/ui/input/pointer/HitPathTracker.class", "name": "androidx/compose/ui/input/pointer/HitPathTracker.class", "size": 12097, "crc": 1772686404}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconFromAncestorNodeWithCursorInBoundsOrDefaultIcon$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconFromAncestorNodeWithCursorInBoundsOrDefaultIcon$1.class", "size": 2237, "crc": -1291483380}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconIfDescendantsDoNotHavePriority$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$displayIconIfDescendantsDoNotHavePriority$1.class", "size": 2370, "crc": 1837543653}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findDescendantNodeWithCursorInBounds$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findDescendantNodeWithCursorInBounds$1.class", "size": 2695, "crc": 31446109}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findOverridingAncestorNode$1.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode$findOverridingAncestorNode$1.class", "size": 2150, "crc": -1540633020}, {"key": "androidx/compose/ui/input/pointer/HoverIconModifierNode.class", "name": "androidx/compose/ui/input/pointer/HoverIconModifierNode.class", "size": 11327, "crc": 1255098990}, {"key": "androidx/compose/ui/input/pointer/InternalPointerEvent.class", "name": "androidx/compose/ui/input/pointer/InternalPointerEvent.class", "size": 4831, "crc": -247239591}, {"key": "androidx/compose/ui/input/pointer/MatrixPositionCalculator.class", "name": "androidx/compose/ui/input/pointer/MatrixPositionCalculator.class", "size": 820, "crc": 47285797}, {"key": "androidx/compose/ui/input/pointer/MotionEventAdapter.class", "name": "androidx/compose/ui/input/pointer/MotionEventAdapter.class", "size": 11511, "crc": -160653673}, {"key": "androidx/compose/ui/input/pointer/MotionEventClassification.class", "name": "androidx/compose/ui/input/pointer/MotionEventClassification.class", "size": 651, "crc": -1736345555}, {"key": "androidx/compose/ui/input/pointer/MotionEventHelper.class", "name": "androidx/compose/ui/input/pointer/MotionEventHelper.class", "size": 2553, "crc": -433618039}, {"key": "androidx/compose/ui/input/pointer/Node.class", "name": "androidx/compose/ui/input/pointer/Node.class", "size": 25842, "crc": 517109239}, {"key": "androidx/compose/ui/input/pointer/NodeParent.class", "name": "androidx/compose/ui/input/pointer/NodeParent.class", "size": 8585, "crc": -357255278}, {"key": "androidx/compose/ui/input/pointer/PointerButtons.class", "name": "androidx/compose/ui/input/pointer/PointerButtons.class", "size": 2130, "crc": 365242828}, {"key": "androidx/compose/ui/input/pointer/PointerEvent.class", "name": "androidx/compose/ui/input/pointer/PointerEvent.class", "size": 8792, "crc": 145977112}, {"key": "androidx/compose/ui/input/pointer/PointerEventKt.class", "name": "androidx/compose/ui/input/pointer/PointerEventKt.class", "size": 9970, "crc": 1046690774}, {"key": "androidx/compose/ui/input/pointer/PointerEventPass.class", "name": "androidx/compose/ui/input/pointer/PointerEventPass.class", "size": 1995, "crc": 223895247}, {"key": "androidx/compose/ui/input/pointer/PointerEventTimeoutCancellationException.class", "name": "androidx/compose/ui/input/pointer/PointerEventTimeoutCancellationException.class", "size": 1699, "crc": 106592097}, {"key": "androidx/compose/ui/input/pointer/PointerEventType$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerEventType$Companion.class", "size": 1972, "crc": 1325591515}, {"key": "androidx/compose/ui/input/pointer/PointerEventType.class", "name": "androidx/compose/ui/input/pointer/PointerEventType.class", "size": 3181, "crc": 180166826}, {"key": "androidx/compose/ui/input/pointer/PointerEvent_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerEvent_androidKt.class", "size": 4541, "crc": -97635609}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierElement.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierElement.class", "size": 5313, "crc": 713837185}, {"key": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode.class", "name": "androidx/compose/ui/input/pointer/PointerHoverIconModifierNode.class", "size": 2931, "crc": 1593611263}, {"key": "androidx/compose/ui/input/pointer/PointerIcon$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerIcon$Companion.class", "size": 1765, "crc": -1795523929}, {"key": "androidx/compose/ui/input/pointer/PointerIcon.class", "name": "androidx/compose/ui/input/pointer/PointerIcon.class", "size": 800, "crc": -1306003427}, {"key": "androidx/compose/ui/input/pointer/PointerIconKt.class", "name": "androidx/compose/ui/input/pointer/PointerIconKt.class", "size": 2538, "crc": -1773528168}, {"key": "androidx/compose/ui/input/pointer/PointerIconService.class", "name": "androidx/compose/ui/input/pointer/PointerIconService.class", "size": 966, "crc": 1009132968}, {"key": "androidx/compose/ui/input/pointer/PointerIcon_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerIcon_androidKt.class", "size": 2039, "crc": 1055096334}, {"key": "androidx/compose/ui/input/pointer/PointerId.class", "name": "androidx/compose/ui/input/pointer/PointerId.class", "size": 2112, "crc": -1973102847}, {"key": "androidx/compose/ui/input/pointer/PointerInputChange.class", "name": "androidx/compose/ui/input/pointer/PointerInputChange.class", "size": 19126, "crc": -983873617}, {"key": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer$PointerInputData.class", "name": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer$PointerInputData.class", "size": 1577, "crc": 373609561}, {"key": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer.class", "name": "androidx/compose/ui/input/pointer/PointerInputChangeEventProducer.class", "size": 5078, "crc": 1162298891}, {"key": "androidx/compose/ui/input/pointer/PointerInputEvent.class", "name": "androidx/compose/ui/input/pointer/PointerInputEvent.class", "size": 2164, "crc": -1193197981}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventData.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventData.class", "size": 8448, "crc": -1913058574}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventHandler.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventHandler.class", "size": 996, "crc": -1385370791}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventProcessor.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventProcessor.class", "size": 6125, "crc": 404118320}, {"key": "androidx/compose/ui/input/pointer/PointerInputEventProcessorKt.class", "name": "androidx/compose/ui/input/pointer/PointerInputEventProcessorKt.class", "size": 2009, "crc": 1382079211}, {"key": "androidx/compose/ui/input/pointer/PointerInputFilter.class", "name": "androidx/compose/ui/input/pointer/PointerInputFilter.class", "size": 2863, "crc": -1661085497}, {"key": "androidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls.class", "size": 2561, "crc": -1350455513}, {"key": "androidx/compose/ui/input/pointer/PointerInputModifier.class", "name": "androidx/compose/ui/input/pointer/PointerInputModifier.class", "size": 2261, "crc": -918717104}, {"key": "androidx/compose/ui/input/pointer/PointerInputResetException.class", "name": "androidx/compose/ui/input/pointer/PointerInputResetException.class", "size": 954, "crc": 1773738627}, {"key": "androidx/compose/ui/input/pointer/PointerInputScope$DefaultImpls.class", "name": "androidx/compose/ui/input/pointer/PointerInputScope$DefaultImpls.class", "size": 4359, "crc": -905585453}, {"key": "androidx/compose/ui/input/pointer/PointerInputScope.class", "name": "androidx/compose/ui/input/pointer/PointerInputScope.class", "size": 5486, "crc": 1413243396}, {"key": "androidx/compose/ui/input/pointer/PointerInputTestUtilKt.class", "name": "androidx/compose/ui/input/pointer/PointerInputTestUtilKt.class", "size": 13265, "crc": -934947608}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$DispatchToViewState.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$DispatchToViewState.class", "size": 2308, "crc": 1985785609}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$2.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$dispatchToView$2.class", "size": 2596, "crc": -1757169234}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$onCancel$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$onCancel$1.class", "size": 1692, "crc": 85223198}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$stopDispatching$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1$stopDispatching$1.class", "size": 1761, "crc": 239551200}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter$pointerInputFilter$1.class", "size": 12651, "crc": 1072313191}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter.class", "size": 3824, "crc": 534096787}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1$1.class", "size": 4402, "crc": -416257951}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$motionEventSpy$1.class", "size": 2365, "crc": -1795693654}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$$inlined$debugInspectorInfo$1.class", "size": 3361, "crc": -555674639}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$2.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$2.class", "size": 5344, "crc": -1501625560}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$3.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt$pointerInteropFilter$3.class", "size": 1963, "crc": -436385195}, {"key": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerInteropFilter_androidKt.class", "size": 5874, "crc": 1501992432}, {"key": "androidx/compose/ui/input/pointer/PointerInteropUtils_androidKt.class", "name": "androidx/compose/ui/input/pointer/PointerInteropUtils_androidKt.class", "size": 5801, "crc": 960180670}, {"key": "androidx/compose/ui/input/pointer/PointerKeyboardModifiers.class", "name": "androidx/compose/ui/input/pointer/PointerKeyboardModifiers.class", "size": 2180, "crc": 945425896}, {"key": "androidx/compose/ui/input/pointer/PointerType$Companion.class", "name": "androidx/compose/ui/input/pointer/PointerType$Companion.class", "size": 1663, "crc": -101596315}, {"key": "androidx/compose/ui/input/pointer/PointerType.class", "name": "androidx/compose/ui/input/pointer/PointerType.class", "size": 2889, "crc": -324742167}, {"key": "androidx/compose/ui/input/pointer/PositionCalculator.class", "name": "androidx/compose/ui/input/pointer/PositionCalculator.class", "size": 692, "crc": 1535050940}, {"key": "androidx/compose/ui/input/pointer/ProcessResult.class", "name": "androidx/compose/ui/input/pointer/ProcessResult.class", "size": 2793, "crc": -1296078172}, {"key": "androidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent.class", "name": "androidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent.class", "size": 2149, "crc": -534477501}, {"key": "androidx/compose/ui/input/pointer/StylusHoverIconModifierElement.class", "name": "androidx/compose/ui/input/pointer/StylusHoverIconModifierElement.class", "size": 6292, "crc": -1863073085}, {"key": "androidx/compose/ui/input/pointer/StylusHoverIconModifierNode.class", "name": "androidx/compose/ui/input/pointer/StylusHoverIconModifierNode.class", "size": 3017, "crc": -1841334218}, {"key": "androidx/compose/ui/input/pointer/SuspendPointerInputElement.class", "name": "androidx/compose/ui/input/pointer/SuspendPointerInputElement.class", "size": 5133, "crc": 760819797}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt$sam$androidx_compose_ui_input_pointer_PointerInputEventHandler$0.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt$sam$androidx_compose_ui_input_pointer_PointerInputEventHandler$0.class", "size": 2300, "crc": 373884539}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt.class", "size": 7316, "crc": -815520156}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilter_jvmKt.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputFilter_jvmKt.class", "size": 823, "crc": -809429914}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNode.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNode.class", "size": 3176, "crc": -1361069386}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$1.class", "size": 1507, "crc": 1715297511}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$1.class", "size": 2561, "crc": -228556606}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$job$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeout$job$1.class", "size": 4837, "crc": -1598359639}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeoutOrNull$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine$withTimeoutOrNull$1.class", "size": 2556, "crc": 185834100}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$PointerEventHandlerCoroutine.class", "size": 16400, "crc": 666714732}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$WhenMappings.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$WhenMappings.class", "size": 960, "crc": 568801706}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$awaitPointerEventScope$2$2.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$awaitPointerEventScope$2$2.class", "size": 2129, "crc": -1215076298}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$onPointerEvent$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$onPointerEvent$1.class", "size": 5090, "crc": 1453133675}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$pointerInputHandler$1.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl$pointerInputHandler$1.class", "size": 3182, "crc": 1200347722}, {"key": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl.class", "name": "androidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl.class", "size": 27815, "crc": -2070591377}, {"key": "androidx/compose/ui/input/pointer/util/DataPointAtTime.class", "name": "androidx/compose/ui/input/pointer/util/DataPointAtTime.class", "size": 2955, "crc": 195899014}, {"key": "androidx/compose/ui/input/pointer/util/ExperimentalVelocityTrackerApi.class", "name": "androidx/compose/ui/input/pointer/util/ExperimentalVelocityTrackerApi.class", "size": 878, "crc": 1415967001}, {"key": "androidx/compose/ui/input/pointer/util/PointerIdArray.class", "name": "androidx/compose/ui/input/pointer/util/PointerIdArray.class", "size": 3992, "crc": -316208787}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker.class", "size": 6131, "crc": -1016977589}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$Strategy.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$Strategy.class", "size": 2148, "crc": -1524937096}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$WhenMappings.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D$WhenMappings.class", "size": 934, "crc": -999850799}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTracker1D.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTracker1D.class", "size": 6649, "crc": 446336137}, {"key": "androidx/compose/ui/input/pointer/util/VelocityTrackerKt.class", "name": "androidx/compose/ui/input/pointer/util/VelocityTrackerKt.class", "size": 12865, "crc": 246843245}, {"key": "androidx/compose/ui/input/rotary/RotaryInputElement.class", "name": "androidx/compose/ui/input/rotary/RotaryInputElement.class", "size": 4484, "crc": 2099351584}, {"key": "androidx/compose/ui/input/rotary/RotaryInputModifierKt.class", "name": "androidx/compose/ui/input/rotary/RotaryInputModifierKt.class", "size": 1653, "crc": 1237247662}, {"key": "androidx/compose/ui/input/rotary/RotaryInputModifierNode.class", "name": "androidx/compose/ui/input/rotary/RotaryInputModifierNode.class", "size": 880, "crc": 1372683466}, {"key": "androidx/compose/ui/input/rotary/RotaryInputNode.class", "name": "androidx/compose/ui/input/rotary/RotaryInputNode.class", "size": 3105, "crc": 1631087380}, {"key": "androidx/compose/ui/input/rotary/RotaryScrollEvent.class", "name": "androidx/compose/ui/input/rotary/RotaryScrollEvent.class", "size": 3656, "crc": 1887594563}, {"key": "androidx/compose/ui/internal/InlineClassHelperKt.class", "name": "androidx/compose/ui/internal/InlineClassHelperKt.class", "size": 3827, "crc": -591556227}, {"key": "androidx/compose/ui/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/ui/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 439, "crc": 100509217}, {"key": "androidx/compose/ui/internal/PlatformOptimizedCancellationException.class", "name": "androidx/compose/ui/internal/PlatformOptimizedCancellationException.class", "size": 1776, "crc": 822274018}, {"key": "androidx/compose/ui/internal/PlatformOptimizedCancellationException_jvmKt.class", "name": "androidx/compose/ui/internal/PlatformOptimizedCancellationException_jvmKt.class", "size": 838, "crc": -1657816007}, {"key": "androidx/compose/ui/layout/AlignmentLine$Companion.class", "name": "androidx/compose/ui/layout/AlignmentLine$Companion.class", "size": 876, "crc": 2072631176}, {"key": "androidx/compose/ui/layout/AlignmentLine.class", "name": "androidx/compose/ui/layout/AlignmentLine.class", "size": 2094, "crc": 989766841}, {"key": "androidx/compose/ui/layout/AlignmentLineKt$FirstBaseline$1.class", "name": "androidx/compose/ui/layout/AlignmentLineKt$FirstBaseline$1.class", "size": 1471, "crc": -1933057769}, {"key": "androidx/compose/ui/layout/AlignmentLineKt$LastBaseline$1.class", "name": "androidx/compose/ui/layout/AlignmentLineKt$LastBaseline$1.class", "size": 1469, "crc": -840107103}, {"key": "androidx/compose/ui/layout/AlignmentLineKt.class", "name": "androidx/compose/ui/layout/AlignmentLineKt.class", "size": 2066, "crc": 1999574416}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicMeasureScope.class", "size": 954, "crc": 1866643394}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope$layout$1.class", "size": 2623, "crc": 2096045778}, {"key": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachIntrinsicsMeasureScope.class", "size": 8688, "crc": -1733911681}, {"key": "androidx/compose/ui/layout/ApproachLayoutElement.class", "name": "androidx/compose/ui/layout/ApproachLayoutElement.class", "size": 6879, "crc": 617688184}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicHeight$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicHeight$1.class", "size": 1793, "crc": 1205940550}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicWidth$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$maxApproachIntrinsicWidth$1.class", "size": 1790, "crc": -1425057209}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$measure$1$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$measure$1$1.class", "size": 1885, "crc": 129046000}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicHeight$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicHeight$1.class", "size": 1793, "crc": 574716729}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicWidth$1.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode$minApproachIntrinsicWidth$1.class", "size": 1790, "crc": -2035392958}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNode.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNode.class", "size": 6928, "crc": 1270708012}, {"key": "androidx/compose/ui/layout/ApproachLayoutModifierNodeImpl.class", "name": "androidx/compose/ui/layout/ApproachLayoutModifierNodeImpl.class", "size": 6716, "crc": 1017302409}, {"key": "androidx/compose/ui/layout/ApproachMeasureScope.class", "name": "androidx/compose/ui/layout/ApproachMeasureScope.class", "size": 775, "crc": 1565744514}, {"key": "androidx/compose/ui/layout/ApproachMeasureScopeImpl$layout$1.class", "name": "androidx/compose/ui/layout/ApproachMeasureScopeImpl$layout$1.class", "size": 3735, "crc": 239236615}, {"key": "androidx/compose/ui/layout/ApproachMeasureScopeImpl.class", "name": "androidx/compose/ui/layout/ApproachMeasureScopeImpl.class", "size": 13680, "crc": 367239692}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$BeyondBoundsScope.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$BeyondBoundsScope.class", "size": 639, "crc": -1027728605}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection$Companion.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection$Companion.class", "size": 1944, "crc": 941391500}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout$LayoutDirection.class", "size": 3240, "crc": 275174164}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayout.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayout.class", "size": 1378, "crc": 737261673}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayoutKt$ModifierLocalBeyondBoundsLayout$1.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayoutKt$ModifierLocalBeyondBoundsLayout$1.class", "size": 1238, "crc": -1835902639}, {"key": "androidx/compose/ui/layout/BeyondBoundsLayoutKt.class", "name": "androidx/compose/ui/layout/BeyondBoundsLayoutKt.class", "size": 1520, "crc": -673992644}, {"key": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt$lambda$641200809$1.class", "name": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt$lambda$641200809$1.class", "size": 2214, "crc": 1005387469}, {"key": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt.class", "name": "androidx/compose/ui/layout/ComposableSingletons$SubcomposeLayoutKt.class", "size": 1553, "crc": -937927541}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Crop$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Crop$1.class", "size": 2656, "crc": -775318830}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillBounds$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillBounds$1.class", "size": 3798, "crc": -934171238}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillHeight$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillHeight$1.class", "size": 3497, "crc": 1786454025}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$FillWidth$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$FillWidth$1.class", "size": 3486, "crc": -2026033013}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Fit$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Fit$1.class", "size": 2649, "crc": 891579796}, {"key": "androidx/compose/ui/layout/ContentScale$Companion$Inside$1.class", "name": "androidx/compose/ui/layout/ContentScale$Companion$Inside$1.class", "size": 4002, "crc": 1299550446}, {"key": "androidx/compose/ui/layout/ContentScale$Companion.class", "name": "androidx/compose/ui/layout/ContentScale$Companion.class", "size": 3347, "crc": -2114477022}, {"key": "androidx/compose/ui/layout/ContentScale.class", "name": "androidx/compose/ui/layout/ContentScale.class", "size": 1013, "crc": -966498542}, {"key": "androidx/compose/ui/layout/ContentScaleKt.class", "name": "androidx/compose/ui/layout/ContentScaleKt.class", "size": 4924, "crc": 146858204}, {"key": "androidx/compose/ui/layout/DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/DefaultIntrinsicMeasurable.class", "size": 3565, "crc": -1201936899}, {"key": "androidx/compose/ui/layout/FixedCountSubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/FixedCountSubcomposeSlotReusePolicy.class", "size": 1686, "crc": 1991623747}, {"key": "androidx/compose/ui/layout/FixedScale.class", "name": "androidx/compose/ui/layout/FixedScale.class", "size": 3712, "crc": 1970888050}, {"key": "androidx/compose/ui/layout/FixedSizeIntrinsicsPlaceable.class", "name": "androidx/compose/ui/layout/FixedSizeIntrinsicsPlaceable.class", "size": 2811, "crc": -2047168185}, {"key": "androidx/compose/ui/layout/GraphicLayerInfo$DefaultImpls.class", "name": "androidx/compose/ui/layout/GraphicLayerInfo$DefaultImpls.class", "size": 786, "crc": -592432080}, {"key": "androidx/compose/ui/layout/GraphicLayerInfo.class", "name": "androidx/compose/ui/layout/GraphicLayerInfo.class", "size": 926, "crc": -1512307904}, {"key": "androidx/compose/ui/layout/HorizontalAlignmentLine.class", "name": "androidx/compose/ui/layout/HorizontalAlignmentLine.class", "size": 1248, "crc": -931483226}, {"key": "androidx/compose/ui/layout/HorizontalRuler$Companion$maxOf$1.class", "name": "androidx/compose/ui/layout/HorizontalRuler$Companion$maxOf$1.class", "size": 2139, "crc": 907601747}, {"key": "androidx/compose/ui/layout/HorizontalRuler$Companion$minOf$1.class", "name": "androidx/compose/ui/layout/HorizontalRuler$Companion$minOf$1.class", "size": 2139, "crc": 1058506244}, {"key": "androidx/compose/ui/layout/HorizontalRuler$Companion.class", "name": "androidx/compose/ui/layout/HorizontalRuler$Companion.class", "size": 2648, "crc": -64883931}, {"key": "androidx/compose/ui/layout/HorizontalRuler.class", "name": "androidx/compose/ui/layout/HorizontalRuler.class", "size": 4641, "crc": 448338664}, {"key": "androidx/compose/ui/layout/InnerRectRulers.class", "name": "androidx/compose/ui/layout/InnerRectRulers.class", "size": 3405, "crc": -228607762}, {"key": "androidx/compose/ui/layout/InnermostAnimationProperties.class", "name": "androidx/compose/ui/layout/InnermostAnimationProperties.class", "size": 4344, "crc": 384684520}, {"key": "androidx/compose/ui/layout/InnermostInsetsRulers.class", "name": "androidx/compose/ui/layout/InnermostInsetsRulers.class", "size": 5952, "crc": -379785487}, {"key": "androidx/compose/ui/layout/InsetsListener.class", "name": "androidx/compose/ui/layout/InsetsListener.class", "size": 21069, "crc": -718044891}, {"key": "androidx/compose/ui/layout/IntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/IntrinsicMeasurable.class", "size": 825, "crc": 1427222084}, {"key": "androidx/compose/ui/layout/IntrinsicMeasureScope.class", "name": "androidx/compose/ui/layout/IntrinsicMeasureScope.class", "size": 929, "crc": -596707651}, {"key": "androidx/compose/ui/layout/IntrinsicMinMax.class", "name": "androidx/compose/ui/layout/IntrinsicMinMax.class", "size": 1869, "crc": 1598293550}, {"key": "androidx/compose/ui/layout/IntrinsicWidthHeight.class", "name": "androidx/compose/ui/layout/IntrinsicWidthHeight.class", "size": 1909, "crc": 167914582}, {"key": "androidx/compose/ui/layout/IntrinsicsMeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/IntrinsicsMeasureScope$layout$1.class", "size": 2599, "crc": -497499418}, {"key": "androidx/compose/ui/layout/IntrinsicsMeasureScope.class", "name": "androidx/compose/ui/layout/IntrinsicsMeasureScope.class", "size": 8199, "crc": 923831721}, {"key": "androidx/compose/ui/layout/LayoutBoundsElement.class", "name": "androidx/compose/ui/layout/LayoutBoundsElement.class", "size": 4343, "crc": 845160297}, {"key": "androidx/compose/ui/layout/LayoutBoundsHolder.class", "name": "androidx/compose/ui/layout/LayoutBoundsHolder.class", "size": 2961, "crc": -1166118714}, {"key": "androidx/compose/ui/layout/LayoutBoundsHolderKt.class", "name": "androidx/compose/ui/layout/LayoutBoundsHolderKt.class", "size": 1104, "crc": -2100814002}, {"key": "androidx/compose/ui/layout/LayoutBoundsNode$rectChanged$1.class", "name": "androidx/compose/ui/layout/LayoutBoundsNode$rectChanged$1.class", "size": 1747, "crc": 1058087759}, {"key": "androidx/compose/ui/layout/LayoutBoundsNode.class", "name": "androidx/compose/ui/layout/LayoutBoundsNode.class", "size": 3957, "crc": -902049496}, {"key": "androidx/compose/ui/layout/LayoutCoordinates$DefaultImpls.class", "name": "androidx/compose/ui/layout/LayoutCoordinates$DefaultImpls.class", "size": 2753, "crc": 1115044829}, {"key": "androidx/compose/ui/layout/LayoutCoordinates.class", "name": "androidx/compose/ui/layout/LayoutCoordinates.class", "size": 6075, "crc": 2048380570}, {"key": "androidx/compose/ui/layout/LayoutCoordinatesKt.class", "name": "androidx/compose/ui/layout/LayoutCoordinatesKt.class", "size": 10797, "crc": -961110336}, {"key": "androidx/compose/ui/layout/LayoutElement.class", "name": "androidx/compose/ui/layout/LayoutElement.class", "size": 4009, "crc": -1708172158}, {"key": "androidx/compose/ui/layout/LayoutIdElement.class", "name": "androidx/compose/ui/layout/LayoutIdElement.class", "size": 3642, "crc": -1214204336}, {"key": "androidx/compose/ui/layout/LayoutIdKt.class", "name": "androidx/compose/ui/layout/LayoutIdKt.class", "size": 1528, "crc": 638208181}, {"key": "androidx/compose/ui/layout/LayoutIdModifier.class", "name": "androidx/compose/ui/layout/LayoutIdModifier.class", "size": 1952, "crc": 1381103828}, {"key": "androidx/compose/ui/layout/LayoutIdParentData.class", "name": "androidx/compose/ui/layout/LayoutIdParentData.class", "size": 566, "crc": 2000606255}, {"key": "androidx/compose/ui/layout/LayoutInfo.class", "name": "androidx/compose/ui/layout/LayoutInfo.class", "size": 2060, "crc": 10053415}, {"key": "androidx/compose/ui/layout/LayoutInfo_androidKt.class", "name": "androidx/compose/ui/layout/LayoutInfo_androidKt.class", "size": 1207, "crc": 1117863431}, {"key": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$1$1.class", "name": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$1$1.class", "size": 1558, "crc": -369993046}, {"key": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$2.class", "name": "androidx/compose/ui/layout/LayoutKt$MultiMeasureLayout$2.class", "size": 2290, "crc": -552476327}, {"key": "androidx/compose/ui/layout/LayoutKt$combineAsVirtualLayouts$1.class", "name": "androidx/compose/ui/layout/LayoutKt$combineAsVirtualLayouts$1.class", "size": 6541, "crc": 406409077}, {"key": "androidx/compose/ui/layout/LayoutKt$materializerOf$1.class", "name": "androidx/compose/ui/layout/LayoutKt$materializerOf$1.class", "size": 5377, "crc": -603116089}, {"key": "androidx/compose/ui/layout/LayoutKt$materializerOfWithCompositionLocalInjection$1.class", "name": "androidx/compose/ui/layout/LayoutKt$materializerOfWithCompositionLocalInjection$1.class", "size": 5606, "crc": 1640610962}, {"key": "androidx/compose/ui/layout/LayoutKt.class", "name": "androidx/compose/ui/layout/LayoutKt.class", "size": 16299, "crc": -873359900}, {"key": "androidx/compose/ui/layout/LayoutModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/LayoutModifier$DefaultImpls.class", "size": 3560, "crc": -301734281}, {"key": "androidx/compose/ui/layout/LayoutModifier.class", "name": "androidx/compose/ui/layout/LayoutModifier.class", "size": 4459, "crc": -1562193913}, {"key": "androidx/compose/ui/layout/LayoutModifierImpl.class", "name": "androidx/compose/ui/layout/LayoutModifierImpl.class", "size": 3561, "crc": -1606580905}, {"key": "androidx/compose/ui/layout/LayoutModifierKt.class", "name": "androidx/compose/ui/layout/LayoutModifierKt.class", "size": 1608, "crc": -622368475}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$ApproachMeasureScopeImpl.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$ApproachMeasureScopeImpl.class", "size": 8265, "crc": -582678257}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$NodeState.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$NodeState.class", "size": 5713, "crc": -1805293900}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope$layout$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope$layout$1.class", "size": 4477, "crc": 1466690464}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$Scope.class", "size": 7007, "crc": -426438489}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$1.class", "size": 3901, "crc": -1506707584}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$2.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1$measure-3p2s80s$$inlined$createMeasureResult$2.class", "size": 3940, "crc": -1565109234}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasurePolicy$1.class", "size": 6129, "crc": 921387513}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasureResult$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createMeasureResult$1.class", "size": 2501, "crc": -1266784702}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createPrecomposedSlotHandle$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createPrecomposedSlotHandle$1.class", "size": 1193, "crc": -174647466}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createPrecomposedSlotHandle$2.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$createPrecomposedSlotHandle$2.class", "size": 8272, "crc": -1101334649}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$deactivateOutOfFrame$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$deactivateOutOfFrame$1.class", "size": 1786, "crc": -1528460227}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precomposePaused$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precomposePaused$1.class", "size": 2432, "crc": -8271003}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precomposePaused$2.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$precomposePaused$2.class", "size": 7252, "crc": 911188737}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$subcompose$4$1$composable$1.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState$subcompose$4$1$composable$1.class", "size": 4742, "crc": -175490335}, {"key": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState.class", "name": "androidx/compose/ui/layout/LayoutNodeSubcompositionsState.class", "size": 47662, "crc": 1070585554}, {"key": "androidx/compose/ui/layout/LookaheadCapablePlacementScope.class", "name": "androidx/compose/ui/layout/LookaheadCapablePlacementScope.class", "size": 3339, "crc": 1557921322}, {"key": "androidx/compose/ui/layout/LookaheadLayoutCoordinates.class", "name": "androidx/compose/ui/layout/LookaheadLayoutCoordinates.class", "size": 12478, "crc": -548065028}, {"key": "androidx/compose/ui/layout/LookaheadLayoutCoordinatesKt.class", "name": "androidx/compose/ui/layout/LookaheadLayoutCoordinatesKt.class", "size": 1671, "crc": 1959220938}, {"key": "androidx/compose/ui/layout/LookaheadScope.class", "name": "androidx/compose/ui/layout/LookaheadScope.class", "size": 2670, "crc": 880886759}, {"key": "androidx/compose/ui/layout/LookaheadScopeImpl.class", "name": "androidx/compose/ui/layout/LookaheadScopeImpl.class", "size": 3796, "crc": -1597712599}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$1$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$1$1.class", "size": 1372, "crc": -1926746274}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$1.class", "size": 1510, "crc": -1377576874}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2$1.class", "size": 1728, "crc": -140281942}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$2$2.class", "size": 1977, "crc": 827986880}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$4.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$LookaheadScope$4.class", "size": 1946, "crc": -2128886364}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt$defaultPlacementApproachInProgress$1.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt$defaultPlacementApproachInProgress$1.class", "size": 1838, "crc": -267203518}, {"key": "androidx/compose/ui/layout/LookaheadScopeKt.class", "name": "androidx/compose/ui/layout/LookaheadScopeKt.class", "size": 11701, "crc": 1717322351}, {"key": "androidx/compose/ui/layout/Measurable.class", "name": "androidx/compose/ui/layout/Measurable.class", "size": 800, "crc": -1182449559}, {"key": "androidx/compose/ui/layout/MeasurePolicy$DefaultImpls.class", "name": "androidx/compose/ui/layout/MeasurePolicy$DefaultImpls.class", "size": 1829, "crc": 1807053837}, {"key": "androidx/compose/ui/layout/MeasurePolicy.class", "name": "androidx/compose/ui/layout/MeasurePolicy.class", "size": 7976, "crc": -2087309409}, {"key": "androidx/compose/ui/layout/MeasureResult.class", "name": "androidx/compose/ui/layout/MeasureResult.class", "size": 1422, "crc": -1570401070}, {"key": "androidx/compose/ui/layout/MeasureScope$DefaultImpls.class", "name": "androidx/compose/ui/layout/MeasureScope$DefaultImpls.class", "size": 5691, "crc": 2072281730}, {"key": "androidx/compose/ui/layout/MeasureScope$layout$1.class", "name": "androidx/compose/ui/layout/MeasureScope$layout$1.class", "size": 3845, "crc": -2060292475}, {"key": "androidx/compose/ui/layout/MeasureScope.class", "name": "androidx/compose/ui/layout/MeasureScope.class", "size": 8444, "crc": -532258228}, {"key": "androidx/compose/ui/layout/MeasureScopeMarker.class", "name": "androidx/compose/ui/layout/MeasureScopeMarker.class", "size": 594, "crc": 946550021}, {"key": "androidx/compose/ui/layout/Measured.class", "name": "androidx/compose/ui/layout/Measured.class", "size": 1070, "crc": 1978404683}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "size": 4114, "crc": 1563903075}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$EmptyPlaceable.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$EmptyPlaceable.class", "size": 3003, "crc": -281215768}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicMinMax.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicMinMax.class", "size": 2118, "crc": 2056443397}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicWidthHeight.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics$IntrinsicWidthHeight.class", "size": 2163, "crc": 826033703}, {"key": "androidx/compose/ui/layout/MeasuringIntrinsics.class", "name": "androidx/compose/ui/layout/MeasuringIntrinsics.class", "size": 4764, "crc": -1326687392}, {"key": "androidx/compose/ui/layout/ModifierInfo.class", "name": "androidx/compose/ui/layout/ModifierInfo.class", "size": 2417, "crc": 549939682}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicy.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicy.class", "size": 9954, "crc": 1471518739}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicyImpl.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicyImpl.class", "size": 6373, "crc": 1851539012}, {"key": "androidx/compose/ui/layout/MultiContentMeasurePolicyKt.class", "name": "androidx/compose/ui/layout/MultiContentMeasurePolicyKt.class", "size": 1070, "crc": -1207161392}, {"key": "androidx/compose/ui/layout/NoOpSubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/NoOpSubcomposeSlotReusePolicy.class", "size": 1619, "crc": -390615829}, {"key": "androidx/compose/ui/layout/NoWindowInsetsAnimation.class", "name": "androidx/compose/ui/layout/NoWindowInsetsAnimation.class", "size": 1976, "crc": -1593994088}, {"key": "androidx/compose/ui/layout/OnFirstVisibleElement.class", "name": "androidx/compose/ui/layout/OnFirstVisibleElement.class", "size": 5035, "crc": 391705548}, {"key": "androidx/compose/ui/layout/OnFirstVisibleModifierKt.class", "name": "androidx/compose/ui/layout/OnFirstVisibleModifierKt.class", "size": 2158, "crc": 1100889704}, {"key": "androidx/compose/ui/layout/OnFirstVisibleNode$rectChanged$1.class", "name": "androidx/compose/ui/layout/OnFirstVisibleNode$rectChanged$1.class", "size": 1868, "crc": -600319599}, {"key": "androidx/compose/ui/layout/OnFirstVisibleNode$startTimer$1.class", "name": "androidx/compose/ui/layout/OnFirstVisibleNode$startTimer$1.class", "size": 3361, "crc": 776208483}, {"key": "androidx/compose/ui/layout/OnFirstVisibleNode$updateViewport$1.class", "name": "androidx/compose/ui/layout/OnFirstVisibleNode$updateViewport$1.class", "size": 1645, "crc": 1726090912}, {"key": "androidx/compose/ui/layout/OnFirstVisibleNode.class", "name": "androidx/compose/ui/layout/OnFirstVisibleNode.class", "size": 9163, "crc": 2120961602}, {"key": "androidx/compose/ui/layout/OnGlobalLayoutListenerKt.class", "name": "androidx/compose/ui/layout/OnGlobalLayoutListenerKt.class", "size": 2677, "crc": 218223805}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedElement.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedElement.class", "size": 3562, "crc": -1468984735}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifier$DefaultImpls.class", "size": 2586, "crc": -878563419}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifier.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifier.class", "size": 2275, "crc": -2098322084}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedModifierKt.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedModifierKt.class", "size": 1409, "crc": -906858799}, {"key": "androidx/compose/ui/layout/OnGloballyPositionedNode.class", "name": "androidx/compose/ui/layout/OnGloballyPositionedNode.class", "size": 2127, "crc": -1002881257}, {"key": "androidx/compose/ui/layout/OnLayoutRectChangedElement.class", "name": "androidx/compose/ui/layout/OnLayoutRectChangedElement.class", "size": 4349, "crc": -931507923}, {"key": "androidx/compose/ui/layout/OnLayoutRectChangedModifierKt.class", "name": "androidx/compose/ui/layout/OnLayoutRectChangedModifierKt.class", "size": 3638, "crc": -768851980}, {"key": "androidx/compose/ui/layout/OnLayoutRectChangedNode.class", "name": "androidx/compose/ui/layout/OnLayoutRectChangedNode.class", "size": 3754, "crc": 1125693580}, {"key": "androidx/compose/ui/layout/OnPlacedElement.class", "name": "androidx/compose/ui/layout/OnPlacedElement.class", "size": 3441, "crc": -1642179347}, {"key": "androidx/compose/ui/layout/OnPlacedModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnPlacedModifier$DefaultImpls.class", "size": 2466, "crc": -1130596800}, {"key": "androidx/compose/ui/layout/OnPlacedModifier.class", "name": "androidx/compose/ui/layout/OnPlacedModifier.class", "size": 2179, "crc": -1440150241}, {"key": "androidx/compose/ui/layout/OnPlacedModifierKt.class", "name": "androidx/compose/ui/layout/OnPlacedModifierKt.class", "size": 1349, "crc": -922257349}, {"key": "androidx/compose/ui/layout/OnPlacedNode.class", "name": "androidx/compose/ui/layout/OnPlacedNode.class", "size": 2063, "crc": 1619351692}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifier$DefaultImpls.class", "size": 2506, "crc": -1534406134}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifier.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifier.class", "size": 2095, "crc": 217431197}, {"key": "androidx/compose/ui/layout/OnRemeasuredModifierKt.class", "name": "androidx/compose/ui/layout/OnRemeasuredModifierKt.class", "size": 1349, "crc": 55048333}, {"key": "androidx/compose/ui/layout/OnSizeChangedModifier.class", "name": "androidx/compose/ui/layout/OnSizeChangedModifier.class", "size": 3199, "crc": 1669998579}, {"key": "androidx/compose/ui/layout/OnSizeChangedNode.class", "name": "androidx/compose/ui/layout/OnSizeChangedNode.class", "size": 3477, "crc": 1648283644}, {"key": "androidx/compose/ui/layout/OnVisibilityChangedElement.class", "name": "androidx/compose/ui/layout/OnVisibilityChangedElement.class", "size": 5170, "crc": 778544922}, {"key": "androidx/compose/ui/layout/OnVisibilityChangedModifierKt.class", "name": "androidx/compose/ui/layout/OnVisibilityChangedModifierKt.class", "size": 2233, "crc": -148678600}, {"key": "androidx/compose/ui/layout/OnVisibilityChangedNode$rectChanged$1.class", "name": "androidx/compose/ui/layout/OnVisibilityChangedNode$rectChanged$1.class", "size": 1898, "crc": -326526010}, {"key": "androidx/compose/ui/layout/OnVisibilityChangedNode$startTimer$1.class", "name": "androidx/compose/ui/layout/OnVisibilityChangedNode$startTimer$1.class", "size": 3406, "crc": 611976261}, {"key": "androidx/compose/ui/layout/OnVisibilityChangedNode$updateViewport$1.class", "name": "androidx/compose/ui/layout/OnVisibilityChangedNode$updateViewport$1.class", "size": 1675, "crc": -1671129318}, {"key": "androidx/compose/ui/layout/OnVisibilityChangedNode.class", "name": "androidx/compose/ui/layout/OnVisibilityChangedNode.class", "size": 9512, "crc": -2006544142}, {"key": "androidx/compose/ui/layout/OuterPlacementScope.class", "name": "androidx/compose/ui/layout/OuterPlacementScope.class", "size": 2517, "crc": 1713538587}, {"key": "androidx/compose/ui/layout/OuterRectRulers.class", "name": "androidx/compose/ui/layout/OuterRectRulers.class", "size": 3405, "crc": 935574347}, {"key": "androidx/compose/ui/layout/ParentDataModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/ParentDataModifier$DefaultImpls.class", "size": 2486, "crc": 984757720}, {"key": "androidx/compose/ui/layout/ParentDataModifier.class", "name": "androidx/compose/ui/layout/ParentDataModifier.class", "size": 2305, "crc": -474160270}, {"key": "androidx/compose/ui/layout/PausedPrecompositionImpl.class", "name": "androidx/compose/ui/layout/PausedPrecompositionImpl.class", "size": 673, "crc": 1576883023}, {"key": "androidx/compose/ui/layout/PinnableContainer$PinnedHandle.class", "name": "androidx/compose/ui/layout/PinnableContainer$PinnedHandle.class", "size": 578, "crc": 502130647}, {"key": "androidx/compose/ui/layout/PinnableContainer.class", "name": "androidx/compose/ui/layout/PinnableContainer.class", "size": 812, "crc": 1898437149}, {"key": "androidx/compose/ui/layout/PinnableContainerKt$LocalPinnableContainer$1.class", "name": "androidx/compose/ui/layout/PinnableContainerKt$LocalPinnableContainer$1.class", "size": 1213, "crc": 1870725375}, {"key": "androidx/compose/ui/layout/PinnableContainerKt.class", "name": "androidx/compose/ui/layout/PinnableContainerKt.class", "size": 1553, "crc": -714973644}, {"key": "androidx/compose/ui/layout/Placeable$PlacementScope.class", "name": "androidx/compose/ui/layout/Placeable$PlacementScope.class", "size": 20218, "crc": -1817340661}, {"key": "androidx/compose/ui/layout/Placeable.class", "name": "androidx/compose/ui/layout/Placeable.class", "size": 7126, "crc": 429907105}, {"key": "androidx/compose/ui/layout/PlaceableKt$DefaultLayerBlock$1.class", "name": "androidx/compose/ui/layout/PlaceableKt$DefaultLayerBlock$1.class", "size": 1381, "crc": 1780655043}, {"key": "androidx/compose/ui/layout/PlaceableKt.class", "name": "androidx/compose/ui/layout/PlaceableKt.class", "size": 2553, "crc": -1527447319}, {"key": "androidx/compose/ui/layout/PlacementScopeMarker.class", "name": "androidx/compose/ui/layout/PlacementScopeMarker.class", "size": 606, "crc": 1767631982}, {"key": "androidx/compose/ui/layout/PlatformWindowInsetsAnimation.class", "name": "androidx/compose/ui/layout/PlatformWindowInsetsAnimation.class", "size": 535, "crc": 362297181}, {"key": "androidx/compose/ui/layout/RectRulers$Companion.class", "name": "androidx/compose/ui/layout/RectRulers$Companion.class", "size": 715, "crc": -1867774008}, {"key": "androidx/compose/ui/layout/RectRulers.class", "name": "androidx/compose/ui/layout/RectRulers.class", "size": 1198, "crc": 46608663}, {"key": "androidx/compose/ui/layout/RectRulersImpl.class", "name": "androidx/compose/ui/layout/RectRulersImpl.class", "size": 2821, "crc": -986863010}, {"key": "androidx/compose/ui/layout/RectRulersKt.class", "name": "androidx/compose/ui/layout/RectRulersKt.class", "size": 1866, "crc": 1685709833}, {"key": "androidx/compose/ui/layout/Remeasurement.class", "name": "androidx/compose/ui/layout/Remeasurement.class", "size": 455, "crc": 2001704857}, {"key": "androidx/compose/ui/layout/RemeasurementModifier$DefaultImpls.class", "name": "androidx/compose/ui/layout/RemeasurementModifier$DefaultImpls.class", "size": 2516, "crc": -137216278}, {"key": "androidx/compose/ui/layout/RemeasurementModifier.class", "name": "androidx/compose/ui/layout/RemeasurementModifier.class", "size": 2224, "crc": -1233136335}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$1.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$1.class", "size": 1604, "crc": 763210711}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$2.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$2.class", "size": 1866, "crc": -61238641}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy$measure$3.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy$measure$3.class", "size": 3193, "crc": **********}, {"key": "androidx/compose/ui/layout/RootMeasurePolicy.class", "name": "androidx/compose/ui/layout/RootMeasurePolicy.class", "size": 5286, "crc": -**********}, {"key": "androidx/compose/ui/layout/Ruler.class", "name": "androidx/compose/ui/layout/Ruler.class", "size": 2564, "crc": -**********}, {"key": "androidx/compose/ui/layout/RulerKt.class", "name": "androidx/compose/ui/layout/RulerKt.class", "size": 2513, "crc": -26797403}, {"key": "androidx/compose/ui/layout/RulerProviderModifierElement.class", "name": "androidx/compose/ui/layout/RulerProviderModifierElement.class", "size": 2751, "crc": -**********}, {"key": "androidx/compose/ui/layout/RulerProviderModifierNode$measure$1.class", "name": "androidx/compose/ui/layout/RulerProviderModifierNode$measure$1.class", "size": 1867, "crc": **********}, {"key": "androidx/compose/ui/layout/RulerProviderModifierNode$rulerLambda$1.class", "name": "androidx/compose/ui/layout/RulerProviderModifierNode$rulerLambda$1.class", "size": 7216, "crc": -34066003}, {"key": "androidx/compose/ui/layout/RulerProviderModifierNode.class", "name": "androidx/compose/ui/layout/RulerProviderModifierNode.class", "size": 6046, "crc": -**********}, {"key": "androidx/compose/ui/layout/RulerScope.class", "name": "androidx/compose/ui/layout/RulerScope.class", "size": 1166, "crc": -**********}, {"key": "androidx/compose/ui/layout/ScaleFactor$Companion.class", "name": "androidx/compose/ui/layout/ScaleFactor$Companion.class", "size": 1232, "crc": 152595276}, {"key": "androidx/compose/ui/layout/ScaleFactor.class", "name": "androidx/compose/ui/layout/ScaleFactor.class", "size": 8451, "crc": 770215887}, {"key": "androidx/compose/ui/layout/ScaleFactorKt.class", "name": "androidx/compose/ui/layout/ScaleFactorKt.class", "size": 7639, "crc": **********}, {"key": "androidx/compose/ui/layout/SimplePlacementScope.class", "name": "androidx/compose/ui/layout/SimplePlacementScope.class", "size": 1760, "crc": 955738146}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$ReusedSlotId$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$ReusedSlotId$1.class", "size": 803, "crc": 1395395068}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$2.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$2.class", "size": 2233, "crc": -511709308}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$4$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$4$1.class", "size": 1475, "crc": 402957344}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$5.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt$SubcomposeLayout$5.class", "size": 2483, "crc": 353721437}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutKt.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutKt.class", "size": 12040, "crc": 256558994}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$PausedPrecomposition.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$PausedPrecomposition.class", "size": 1339, "crc": 2092620716}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$PrecomposedSlotHandle.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$PrecomposedSlotHandle.class", "size": 2458, "crc": -1678681584}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setCompositionContext$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setCompositionContext$1.class", "size": 2089, "crc": 667362707}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setMeasurePolicy$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setMeasurePolicy$1.class", "size": 2699, "crc": 2073101625}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState$setRoot$1.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState$setRoot$1.class", "size": 2866, "crc": -1299022925}, {"key": "androidx/compose/ui/layout/SubcomposeLayoutState.class", "name": "androidx/compose/ui/layout/SubcomposeLayoutState.class", "size": 8147, "crc": -912850472}, {"key": "androidx/compose/ui/layout/SubcomposeMeasureScope.class", "name": "androidx/compose/ui/layout/SubcomposeMeasureScope.class", "size": 1215, "crc": -1581027799}, {"key": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy$SlotIdsSet.class", "name": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy$SlotIdsSet.class", "size": 11673, "crc": 1749399352}, {"key": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy.class", "name": "androidx/compose/ui/layout/SubcomposeSlotReusePolicy.class", "size": 1049, "crc": 2126775078}, {"key": "androidx/compose/ui/layout/TestModifierUpdater.class", "name": "androidx/compose/ui/layout/TestModifierUpdater.class", "size": 1698, "crc": -1089040587}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$1$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$1$1.class", "size": 1848, "crc": -1362248027}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$2.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$2.class", "size": 1902, "crc": 1297717155}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1$1.class", "size": 1742, "crc": -1355195018}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt$TestModifierUpdaterLayout$measurePolicy$1$1.class", "size": 2220, "crc": 752713901}, {"key": "androidx/compose/ui/layout/TestModifierUpdaterKt.class", "name": "androidx/compose/ui/layout/TestModifierUpdaterKt.class", "size": 7832, "crc": 1068430525}, {"key": "androidx/compose/ui/layout/ValueInsets.class", "name": "androidx/compose/ui/layout/ValueInsets.class", "size": 3607, "crc": 532455571}, {"key": "androidx/compose/ui/layout/ValueInsets_androidKt.class", "name": "androidx/compose/ui/layout/ValueInsets_androidKt.class", "size": 1599, "crc": 882174816}, {"key": "androidx/compose/ui/layout/VerticalAlignmentLine.class", "name": "androidx/compose/ui/layout/VerticalAlignmentLine.class", "size": 1244, "crc": -1232076368}, {"key": "androidx/compose/ui/layout/VerticalRuler$Companion$maxOf$1.class", "name": "androidx/compose/ui/layout/VerticalRuler$Companion$maxOf$1.class", "size": 2115, "crc": 101326808}, {"key": "androidx/compose/ui/layout/VerticalRuler$Companion$minOf$1.class", "name": "androidx/compose/ui/layout/VerticalRuler$Companion$minOf$1.class", "size": 2115, "crc": -2106593452}, {"key": "androidx/compose/ui/layout/VerticalRuler$Companion.class", "name": "androidx/compose/ui/layout/VerticalRuler$Companion.class", "size": 2626, "crc": 1510756544}, {"key": "androidx/compose/ui/layout/VerticalRuler.class", "name": "androidx/compose/ui/layout/VerticalRuler.class", "size": 4612, "crc": 233181109}, {"key": "androidx/compose/ui/layout/WindowInsetsAnimation.class", "name": "androidx/compose/ui/layout/WindowInsetsAnimation.class", "size": 1418, "crc": -1054075493}, {"key": "androidx/compose/ui/layout/WindowInsetsRulers$Companion.class", "name": "androidx/compose/ui/layout/WindowInsetsRulers$Companion.class", "size": 4404, "crc": -1745204732}, {"key": "androidx/compose/ui/layout/WindowInsetsRulers.class", "name": "androidx/compose/ui/layout/WindowInsetsRulers.class", "size": 1610, "crc": 1142368782}, {"key": "androidx/compose/ui/layout/WindowInsetsRulersImpl.class", "name": "androidx/compose/ui/layout/WindowInsetsRulersImpl.class", "size": 2634, "crc": 828175842}, {"key": "androidx/compose/ui/layout/WindowInsetsRulersKt.class", "name": "androidx/compose/ui/layout/WindowInsetsRulersKt.class", "size": 1547, "crc": 209215796}, {"key": "androidx/compose/ui/layout/WindowInsetsRulers_androidKt.class", "name": "androidx/compose/ui/layout/WindowInsetsRulers_androidKt.class", "size": 15754, "crc": -1889530136}, {"key": "androidx/compose/ui/layout/WindowWindowInsetsAnimationValues.class", "name": "androidx/compose/ui/layout/WindowWindowInsetsAnimationValues.class", "size": 8263, "crc": -1310992153}, {"key": "androidx/compose/ui/modifier/BackwardsCompatLocalMap.class", "name": "androidx/compose/ui/modifier/BackwardsCompatLocalMap.class", "size": 4244, "crc": 1278522794}, {"key": "androidx/compose/ui/modifier/EmptyMap.class", "name": "androidx/compose/ui/modifier/EmptyMap.class", "size": 2390, "crc": 522314011}, {"key": "androidx/compose/ui/modifier/ModifierLocal.class", "name": "androidx/compose/ui/modifier/ModifierLocal.class", "size": 1612, "crc": 1912000916}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumer$DefaultImpls.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumer$DefaultImpls.class", "size": 2534, "crc": -1960814160}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumer.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumer.class", "size": 2327, "crc": -2133264654}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerImpl.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerImpl.class", "size": 2774, "crc": 2101504580}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerKt$modifierLocalConsumer$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerKt$modifierLocalConsumer$$inlined$debugInspectorInfo$1.class", "size": 2930, "crc": -706959255}, {"key": "androidx/compose/ui/modifier/ModifierLocalConsumerKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalConsumerKt.class", "size": 2819, "crc": 734889807}, {"key": "androidx/compose/ui/modifier/ModifierLocalKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalKt.class", "size": 1112, "crc": -1875024181}, {"key": "androidx/compose/ui/modifier/ModifierLocalManager$invalidate$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalManager$invalidate$1.class", "size": 1255, "crc": 1078403129}, {"key": "androidx/compose/ui/modifier/ModifierLocalManager.class", "name": "androidx/compose/ui/modifier/ModifierLocalManager.class", "size": 14980, "crc": -1493038480}, {"key": "androidx/compose/ui/modifier/ModifierLocalMap.class", "name": "androidx/compose/ui/modifier/ModifierLocalMap.class", "size": 2107, "crc": **********}, {"key": "androidx/compose/ui/modifier/ModifierLocalModifierNode.class", "name": "androidx/compose/ui/modifier/ModifierLocalModifierNode.class", "size": 9705, "crc": -**********}, {"key": "androidx/compose/ui/modifier/ModifierLocalModifierNodeKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalModifierNodeKt.class", "size": 8085, "crc": -522668507}, {"key": "androidx/compose/ui/modifier/ModifierLocalProvider$DefaultImpls.class", "name": "androidx/compose/ui/modifier/ModifierLocalProvider$DefaultImpls.class", "size": 2763, "crc": -23097788}, {"key": "androidx/compose/ui/modifier/ModifierLocalProvider.class", "name": "androidx/compose/ui/modifier/ModifierLocalProvider.class", "size": 2559, "crc": -375333009}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$$inlined$debugInspectorInfo$1.class", "size": 3145, "crc": -515030862}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$1.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt$modifierLocalProvider$1.class", "size": 3637, "crc": -512695598}, {"key": "androidx/compose/ui/modifier/ModifierLocalProviderKt.class", "name": "androidx/compose/ui/modifier/ModifierLocalProviderKt.class", "size": 3080, "crc": -79347615}, {"key": "androidx/compose/ui/modifier/ModifierLocalReadScope.class", "name": "androidx/compose/ui/modifier/ModifierLocalReadScope.class", "size": 816, "crc": 905358586}, {"key": "androidx/compose/ui/modifier/MultiLocalMap.class", "name": "androidx/compose/ui/modifier/MultiLocalMap.class", "size": 3453, "crc": -**********}, {"key": "androidx/compose/ui/modifier/ProvidableModifierLocal.class", "name": "androidx/compose/ui/modifier/ProvidableModifierLocal.class", "size": 1255, "crc": **********}, {"key": "androidx/compose/ui/modifier/SingleLocalMap.class", "name": "androidx/compose/ui/modifier/SingleLocalMap.class", "size": 4896, "crc": -162561169}, {"key": "androidx/compose/ui/node/AlignmentLines$recalculate$1.class", "name": "androidx/compose/ui/node/AlignmentLines$recalculate$1.class", "size": 4926, "crc": -**********}, {"key": "androidx/compose/ui/node/AlignmentLines.class", "name": "androidx/compose/ui/node/AlignmentLines.class", "size": 11379, "crc": -48387536}, {"key": "androidx/compose/ui/node/AlignmentLinesOwner.class", "name": "androidx/compose/ui/node/AlignmentLinesOwner.class", "size": 1927, "crc": -391561581}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$2.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$2.class", "size": 1273, "crc": -1091099924}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$3.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$initializeModifier$3.class", "size": 2664, "crc": -1150357309}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$updateDrawCache$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$updateDrawCache$1.class", "size": 1627, "crc": -818786334}, {"key": "androidx/compose/ui/node/BackwardsCompatNode$updateModifierLocalConsumer$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNode$updateModifierLocalConsumer$1.class", "size": 1842, "crc": -1319651262}, {"key": "androidx/compose/ui/node/BackwardsCompatNode.class", "name": "androidx/compose/ui/node/BackwardsCompatNode.class", "size": 31320, "crc": 253462739}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$DetachedModifierLocalReadScope$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$DetachedModifierLocalReadScope$1.class", "size": 1423, "crc": 269247852}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$onDrawCacheReadsChanged$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$onDrawCacheReadsChanged$1.class", "size": 1472, "crc": -1978739092}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt$updateModifierLocalConsumer$1.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt$updateModifierLocalConsumer$1.class", "size": 1477, "crc": -906728309}, {"key": "androidx/compose/ui/node/BackwardsCompatNodeKt.class", "name": "androidx/compose/ui/node/BackwardsCompatNodeKt.class", "size": 3221, "crc": 16879863}, {"key": "androidx/compose/ui/node/CanFocusChecker.class", "name": "androidx/compose/ui/node/CanFocusChecker.class", "size": 2623, "crc": 1182500023}, {"key": "androidx/compose/ui/node/CenteredArray.class", "name": "androidx/compose/ui/node/CenteredArray.class", "size": 2693, "crc": 434227508}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetCompositeKeyHash$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetCompositeKeyHash$1.class", "size": 1591, "crc": 1493432661}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetDensity$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetDensity$1.class", "size": 1671, "crc": 1293684702}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetLayoutDirection$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetLayoutDirection$1.class", "size": 1739, "crc": 1902624267}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetMeasurePolicy$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetMeasurePolicy$1.class", "size": 1729, "crc": -1666166772}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetModifier$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetModifier$1.class", "size": 1654, "crc": 58129167}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetResolvedCompositionLocals$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetResolvedCompositionLocals$1.class", "size": 1783, "crc": -20429029}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$SetViewConfiguration$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$SetViewConfiguration$1.class", "size": 1775, "crc": 690199251}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion$VirtualConstructor$1.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion$VirtualConstructor$1.class", "size": 1358, "crc": -1137003210}, {"key": "androidx/compose/ui/node/ComposeUiNode$Companion.class", "name": "androidx/compose/ui/node/ComposeUiNode$Companion.class", "size": 6999, "crc": -1668679186}, {"key": "androidx/compose/ui/node/ComposeUiNode.class", "name": "androidx/compose/ui/node/ComposeUiNode.class", "size": 2653, "crc": 135354492}, {"key": "androidx/compose/ui/node/CompositionLocalConsumerModifierNode.class", "name": "androidx/compose/ui/node/CompositionLocalConsumerModifierNode.class", "size": 547, "crc": 211476991}, {"key": "androidx/compose/ui/node/CompositionLocalConsumerModifierNodeKt.class", "name": "androidx/compose/ui/node/CompositionLocalConsumerModifierNodeKt.class", "size": 3225, "crc": 1174806520}, {"key": "androidx/compose/ui/node/DelegatableNode$RegistrationHandle.class", "name": "androidx/compose/ui/node/DelegatableNode$RegistrationHandle.class", "size": 585, "crc": 1760091190}, {"key": "androidx/compose/ui/node/DelegatableNode.class", "name": "androidx/compose/ui/node/DelegatableNode.class", "size": 1085, "crc": 530424916}, {"key": "androidx/compose/ui/node/DelegatableNodeKt.class", "name": "androidx/compose/ui/node/DelegatableNodeKt.class", "size": 68953, "crc": 2116374542}, {"key": "androidx/compose/ui/node/DelegatableNode_androidKt.class", "name": "androidx/compose/ui/node/DelegatableNode_androidKt.class", "size": 2709, "crc": 413587418}, {"key": "androidx/compose/ui/node/DelegatingNode.class", "name": "androidx/compose/ui/node/DelegatingNode.class", "size": 12564, "crc": -1331194409}, {"key": "androidx/compose/ui/node/DepthSortedSet.class", "name": "androidx/compose/ui/node/DepthSortedSet.class", "size": 6650, "crc": -1662156594}, {"key": "androidx/compose/ui/node/DepthSortedSetKt$DepthComparator$1.class", "name": "androidx/compose/ui/node/DepthSortedSetKt$DepthComparator$1.class", "size": 1549, "crc": -938132484}, {"key": "androidx/compose/ui/node/DepthSortedSetKt.class", "name": "androidx/compose/ui/node/DepthSortedSetKt.class", "size": 983, "crc": -1511688089}, {"key": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses$WhenMappings.class", "name": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses$WhenMappings.class", "size": 961, "crc": 1068288143}, {"key": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses.class", "name": "androidx/compose/ui/node/DepthSortedSetsForDifferentPasses.class", "size": 6426, "crc": -676242128}, {"key": "androidx/compose/ui/node/DiffCallback.class", "name": "androidx/compose/ui/node/DiffCallback.class", "size": 662, "crc": 85474602}, {"key": "androidx/compose/ui/node/DistanceAndFlags.class", "name": "androidx/compose/ui/node/DistanceAndFlags.class", "size": 4181, "crc": -230917939}, {"key": "androidx/compose/ui/node/DpTouchBoundsExpansion$Companion.class", "name": "androidx/compose/ui/node/DpTouchBoundsExpansion$Companion.class", "size": 2848, "crc": -1954191027}, {"key": "androidx/compose/ui/node/DpTouchBoundsExpansion.class", "name": "androidx/compose/ui/node/DpTouchBoundsExpansion.class", "size": 7292, "crc": -747140750}, {"key": "androidx/compose/ui/node/DrawModifierNode.class", "name": "androidx/compose/ui/node/DrawModifierNode.class", "size": 907, "crc": 842892114}, {"key": "androidx/compose/ui/node/DrawModifierNodeKt.class", "name": "androidx/compose/ui/node/DrawModifierNodeKt.class", "size": 2092, "crc": -3391505}, {"key": "androidx/compose/ui/node/GlobalPositionAwareModifierNode.class", "name": "androidx/compose/ui/node/GlobalPositionAwareModifierNode.class", "size": 815, "crc": 843711633}, {"key": "androidx/compose/ui/node/HitTestResult$HitTestResultIterator.class", "name": "androidx/compose/ui/node/HitTestResult$HitTestResultIterator.class", "size": 3946, "crc": 2006380732}, {"key": "androidx/compose/ui/node/HitTestResult$SubList.class", "name": "androidx/compose/ui/node/HitTestResult$SubList.class", "size": 9249, "crc": 1987539758}, {"key": "androidx/compose/ui/node/HitTestResult.class", "name": "androidx/compose/ui/node/HitTestResult.class", "size": 17004, "crc": -1280722959}, {"key": "androidx/compose/ui/node/HitTestResultKt.class", "name": "androidx/compose/ui/node/HitTestResultKt.class", "size": 1345, "crc": -1273524492}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator$Companion.class", "size": 1196, "crc": -1636542882}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator$LookaheadDelegateImpl.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator$LookaheadDelegateImpl.class", "size": 7054, "crc": -1234015318}, {"key": "androidx/compose/ui/node/InnerNodeCoordinator.class", "name": "androidx/compose/ui/node/InnerNodeCoordinator.class", "size": 14944, "crc": 30279946}, {"key": "androidx/compose/ui/node/IntStack.class", "name": "androidx/compose/ui/node/IntStack.class", "size": 4478, "crc": 1601134394}, {"key": "androidx/compose/ui/node/InternalCoreApi.class", "name": "androidx/compose/ui/node/InternalCoreApi.class", "size": 1014, "crc": 1654059132}, {"key": "androidx/compose/ui/node/InteroperableComposeUiNode.class", "name": "androidx/compose/ui/node/InteroperableComposeUiNode.class", "size": 787, "crc": 845605561}, {"key": "androidx/compose/ui/node/IntrinsicsPolicy.class", "name": "androidx/compose/ui/node/IntrinsicsPolicy.class", "size": 6111, "crc": 1234438987}, {"key": "androidx/compose/ui/node/Invalidation.class", "name": "androidx/compose/ui/node/Invalidation.class", "size": 2012, "crc": 1725800947}, {"key": "androidx/compose/ui/node/LayerPositionalProperties.class", "name": "androidx/compose/ui/node/LayerPositionalProperties.class", "size": 2937, "crc": 482296643}, {"key": "androidx/compose/ui/node/LayoutAwareModifierNode.class", "name": "androidx/compose/ui/node/LayoutAwareModifierNode.class", "size": 1101, "crc": 631642430}, {"key": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicHeight$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicHeight$1.class", "size": 1634, "crc": 1045237745}, {"key": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicWidth$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$maxIntrinsicWidth$1.class", "size": 1630, "crc": -2013497077}, {"key": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicHeight$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicHeight$1.class", "size": 1634, "crc": 1861637080}, {"key": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicWidth$1.class", "name": "androidx/compose/ui/node/LayoutModifierNode$minIntrinsicWidth$1.class", "size": 1630, "crc": -631121737}, {"key": "androidx/compose/ui/node/LayoutModifierNode.class", "name": "androidx/compose/ui/node/LayoutModifierNode.class", "size": 3246, "crc": 1631806055}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$Companion.class", "size": 1241, "crc": -1574917124}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$LookaheadDelegateForLayoutModifierNode.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$LookaheadDelegateForLayoutModifierNode.class", "size": 6765, "crc": 558609602}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$measure$1$1$1$1.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator$measure$1$1$1$1.class", "size": 2504, "crc": 1417958093}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinator.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinator.class", "size": 19561, "crc": 1174734030}, {"key": "androidx/compose/ui/node/LayoutModifierNodeCoordinatorKt.class", "name": "androidx/compose/ui/node/LayoutModifierNodeCoordinatorKt.class", "size": 3637, "crc": -1242160164}, {"key": "androidx/compose/ui/node/LayoutModifierNodeKt.class", "name": "androidx/compose/ui/node/LayoutModifierNodeKt.class", "size": 2853, "crc": -1228316829}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$Constructor$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$Constructor$1.class", "size": 1217, "crc": 1268668960}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$DummyViewConfiguration$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$DummyViewConfiguration$1.class", "size": 1783, "crc": 1500156407}, {"key": "androidx/compose/ui/node/LayoutNode$Companion$ErrorMeasurePolicy$1.class", "name": "androidx/compose/ui/node/LayoutNode$Companion$ErrorMeasurePolicy$1.class", "size": 1956, "crc": 487107785}, {"key": "androidx/compose/ui/node/LayoutNode$Companion.class", "name": "androidx/compose/ui/node/LayoutNode$Companion.class", "size": 2260, "crc": -298088310}, {"key": "androidx/compose/ui/node/LayoutNode$LayoutState.class", "name": "androidx/compose/ui/node/LayoutNode$LayoutState.class", "size": 2208, "crc": -972565439}, {"key": "androidx/compose/ui/node/LayoutNode$NoIntrinsicsMeasurePolicy.class", "name": "androidx/compose/ui/node/LayoutNode$NoIntrinsicsMeasurePolicy.class", "size": 3177, "crc": -1238439624}, {"key": "androidx/compose/ui/node/LayoutNode$UsageByParent.class", "name": "androidx/compose/ui/node/LayoutNode$UsageByParent.class", "size": 2092, "crc": 53474177}, {"key": "androidx/compose/ui/node/LayoutNode$WhenMappings.class", "name": "androidx/compose/ui/node/LayoutNode$WhenMappings.class", "size": 789, "crc": -354124379}, {"key": "androidx/compose/ui/node/LayoutNode$_foldedChildren$1.class", "name": "androidx/compose/ui/node/LayoutNode$_foldedChildren$1.class", "size": 1342, "crc": -237036096}, {"key": "androidx/compose/ui/node/LayoutNode$calculateSemanticsConfiguration$1.class", "name": "androidx/compose/ui/node/LayoutNode$calculateSemanticsConfiguration$1.class", "size": 7806, "crc": -1312817254}, {"key": "androidx/compose/ui/node/LayoutNode.class", "name": "androidx/compose/ui/node/LayoutNode.class", "size": 87845, "crc": 568204409}, {"key": "androidx/compose/ui/node/LayoutNodeAlignmentLines.class", "name": "androidx/compose/ui/node/LayoutNodeAlignmentLines.class", "size": 2760, "crc": -2048106897}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScope$record$1.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScope$record$1.class", "size": 5919, "crc": -1341570722}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScope.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScope.class", "size": 29774, "crc": -1183300660}, {"key": "androidx/compose/ui/node/LayoutNodeDrawScopeKt.class", "name": "androidx/compose/ui/node/LayoutNodeDrawScopeKt.class", "size": 2296, "crc": -1557311080}, {"key": "androidx/compose/ui/node/LayoutNodeKt.class", "name": "androidx/compose/ui/node/LayoutNodeKt.class", "size": 3814, "crc": -41389011}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegate.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegate.class", "size": 13506, "crc": -738362182}, {"key": "androidx/compose/ui/node/LayoutNodeLayoutDelegateKt.class", "name": "androidx/compose/ui/node/LayoutNodeLayoutDelegateKt.class", "size": 4502, "crc": 1667549545}, {"key": "androidx/compose/ui/node/LayoutTreeConsistencyChecker.class", "name": "androidx/compose/ui/node/LayoutTreeConsistencyChecker.class", "size": 10269, "crc": -1826651432}, {"key": "androidx/compose/ui/node/LookaheadAlignmentLines.class", "name": "androidx/compose/ui/node/LookaheadAlignmentLines.class", "size": 4584, "crc": -1773227903}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion$onCommitAffectingRuler$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion$onCommitAffectingRuler$1.class", "size": 1744, "crc": 1582754498}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$Companion.class", "size": 1013, "crc": -28771587}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$ResettableRulerScope.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$ResettableRulerScope.class", "size": 4321, "crc": -2019839165}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$captureRulers$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$captureRulers$1.class", "size": 2345, "crc": -1696306752}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable$layout$1.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable$layout$1.class", "size": 3461, "crc": 834438067}, {"key": "androidx/compose/ui/node/LookaheadCapablePlaceable.class", "name": "androidx/compose/ui/node/LookaheadCapablePlaceable.class", "size": 30116, "crc": -1242435017}, {"key": "androidx/compose/ui/node/LookaheadDelegate.class", "name": "androidx/compose/ui/node/LookaheadDelegate.class", "size": 14400, "crc": -1382244467}, {"key": "androidx/compose/ui/node/LookaheadDelegateKt.class", "name": "androidx/compose/ui/node/LookaheadDelegateKt.class", "size": 2404, "crc": 1900851393}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$PlacedState.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$PlacedState.class", "size": 2189, "crc": 704177086}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$WhenMappings.class", "size": 1397, "crc": 1964032254}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$1.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$1.class", "size": 1633, "crc": 1999292221}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$4.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1$4.class", "size": 1712, "crc": -1585255389}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$layoutChildren$1.class", "size": 4866, "crc": -371619701}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$performMeasure$1.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$performMeasure$1.class", "size": 1791, "crc": -481941558}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$placeSelf$1$2.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$placeSelf$1$2.class", "size": 3939, "crc": -638213510}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate$remeasure$1$2.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate$remeasure$1$2.class", "size": 1627, "crc": 1104954940}, {"key": "androidx/compose/ui/node/LookaheadPassDelegate.class", "name": "androidx/compose/ui/node/LookaheadPassDelegate.class", "size": 39219, "crc": 6645161}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate$PostponedRequest.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate$PostponedRequest.class", "size": 1593, "crc": 328195911}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate$WhenMappings.class", "size": 1086, "crc": -840715922}, {"key": "androidx/compose/ui/node/MeasureAndLayoutDelegate.class", "name": "androidx/compose/ui/node/MeasureAndLayoutDelegate.class", "size": 33000, "crc": 1658091500}, {"key": "androidx/compose/ui/node/MeasureBlocks.class", "name": "androidx/compose/ui/node/MeasureBlocks.class", "size": 2064, "crc": 233980924}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$WhenMappings.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$WhenMappings.class", "size": 1275, "crc": -20287108}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$1.class", "size": 1633, "crc": 1881684725}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$2.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1$2.class", "size": 1716, "crc": 1229954059}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$layoutChildrenBlock$1.class", "size": 2152, "crc": 2076138747}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$performMeasureBlock$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$performMeasureBlock$1.class", "size": 1578, "crc": -1456587636}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$placeOuterCoordinatorBlock$1.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$placeOuterCoordinatorBlock$1.class", "size": 3399, "crc": 91687080}, {"key": "androidx/compose/ui/node/MeasurePassDelegate$remeasure$1$2.class", "name": "androidx/compose/ui/node/MeasurePassDelegate$remeasure$1$2.class", "size": 1619, "crc": -1508585339}, {"key": "androidx/compose/ui/node/MeasurePassDelegate.class", "name": "androidx/compose/ui/node/MeasurePassDelegate.class", "size": 42210, "crc": 1933422677}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNode.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNode.class", "size": 756, "crc": 2017353369}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt$WhenMappings.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt$WhenMappings.class", "size": 1096, "crc": -995778193}, {"key": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt.class", "name": "androidx/compose/ui/node/MeasureScopeWithLayoutNodeKt.class", "size": 4566, "crc": 1000698956}, {"key": "androidx/compose/ui/node/MergedViewAdapter.class", "name": "androidx/compose/ui/node/MergedViewAdapter.class", "size": 5254, "crc": 2047161374}, {"key": "androidx/compose/ui/node/ModifierNodeElement.class", "name": "androidx/compose/ui/node/ModifierNodeElement.class", "size": 4469, "crc": -821570843}, {"key": "androidx/compose/ui/node/MotionReferencePlacementDelegate.class", "name": "androidx/compose/ui/node/MotionReferencePlacementDelegate.class", "size": 629, "crc": -202538566}, {"key": "androidx/compose/ui/node/MutableVectorWithMutationTracking.class", "name": "androidx/compose/ui/node/MutableVectorWithMutationTracking.class", "size": 5095, "crc": 564556170}, {"key": "androidx/compose/ui/node/MyersDiffKt.class", "name": "androidx/compose/ui/node/MyersDiffKt.class", "size": 8092, "crc": -355014202}, {"key": "androidx/compose/ui/node/NodeChain$Differ.class", "name": "androidx/compose/ui/node/NodeChain$Differ.class", "size": 10062, "crc": 267583394}, {"key": "androidx/compose/ui/node/NodeChain$Logger.class", "name": "androidx/compose/ui/node/NodeChain$Logger.class", "size": 1862, "crc": 1745525034}, {"key": "androidx/compose/ui/node/NodeChain$sentinelHead$1.class", "name": "androidx/compose/ui/node/NodeChain$sentinelHead$1.class", "size": 908, "crc": 723562743}, {"key": "androidx/compose/ui/node/NodeChain.class", "name": "androidx/compose/ui/node/NodeChain.class", "size": 42609, "crc": 1001679518}, {"key": "androidx/compose/ui/node/NodeChainKt$fillVector$1.class", "name": "androidx/compose/ui/node/NodeChainKt$fillVector$1.class", "size": 2018, "crc": -564657954}, {"key": "androidx/compose/ui/node/NodeChainKt.class", "name": "androidx/compose/ui/node/NodeChainKt.class", "size": 5047, "crc": 1377276719}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$PointerInputSource$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$PointerInputSource$1.class", "size": 6391, "crc": 720779955}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$SemanticsSource$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$SemanticsSource$1.class", "size": 3547, "crc": -2127271294}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayer$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayer$1.class", "size": 1541, "crc": -1634721436}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayerParams$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion$onCommitAffectingLayerParams$1.class", "size": 3244, "crc": 571819594}, {"key": "androidx/compose/ui/node/NodeCoordinator$Companion.class", "name": "androidx/compose/ui/node/NodeCoordinator$Companion.class", "size": 2132, "crc": 1466024299}, {"key": "androidx/compose/ui/node/NodeCoordinator$HitTestSource.class", "name": "androidx/compose/ui/node/NodeCoordinator$HitTestSource.class", "size": 1663, "crc": -903834992}, {"key": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$drawBlock$1.class", "size": 3144, "crc": 781981608}, {"key": "androidx/compose/ui/node/NodeCoordinator$drawBlock$drawBlockCallToDrawModifiers$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$drawBlock$drawBlockCallToDrawModifiers$1.class", "size": 1827, "crc": -922827416}, {"key": "androidx/compose/ui/node/NodeCoordinator$invalidateParentLayer$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$invalidateParentLayer$1.class", "size": 1381, "crc": -2048083488}, {"key": "androidx/compose/ui/node/NodeCoordinator$outOfBoundsHit$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$outOfBoundsHit$1.class", "size": 3501, "crc": -2083984157}, {"key": "androidx/compose/ui/node/NodeCoordinator$speculativeHit$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$speculativeHit$1.class", "size": 3611, "crc": 966800258}, {"key": "androidx/compose/ui/node/NodeCoordinator$updateLayerParameters$1.class", "name": "androidx/compose/ui/node/NodeCoordinator$updateLayerParameters$1.class", "size": 1720, "crc": 980525507}, {"key": "androidx/compose/ui/node/NodeCoordinator.class", "name": "androidx/compose/ui/node/NodeCoordinator.class", "size": 95567, "crc": 512376452}, {"key": "androidx/compose/ui/node/NodeCoordinatorKt.class", "name": "androidx/compose/ui/node/NodeCoordinatorKt.class", "size": 4900, "crc": 170527753}, {"key": "androidx/compose/ui/node/NodeKind.class", "name": "androidx/compose/ui/node/NodeKind.class", "size": 2475, "crc": 1222227845}, {"key": "androidx/compose/ui/node/NodeKindKt.class", "name": "androidx/compose/ui/node/NodeKindKt.class", "size": 19479, "crc": -2102108198}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$ApproachMeasureBlock.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$ApproachMeasureBlock.class", "size": 1178, "crc": 5916519}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$DefaultIntrinsicMeasurable.class", "size": 4142, "crc": -283079180}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$EmptyPlaceable.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$EmptyPlaceable.class", "size": 3045, "crc": -410499539}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicMinMax.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicMinMax.class", "size": 2138, "crc": 1870009707}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicWidthHeight.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$IntrinsicWidthHeight.class", "size": 2183, "crc": 740647702}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics$MeasureBlock.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics$MeasureBlock.class", "size": 1138, "crc": -312281803}, {"key": "androidx/compose/ui/node/NodeMeasuringIntrinsics.class", "name": "androidx/compose/ui/node/NodeMeasuringIntrinsics.class", "size": 7859, "crc": -1446321550}, {"key": "androidx/compose/ui/node/Nodes.class", "name": "androidx/compose/ui/node/Nodes.class", "size": 7926, "crc": 1312807678}, {"key": "androidx/compose/ui/node/ObserverModifierNode.class", "name": "androidx/compose/ui/node/ObserverModifierNode.class", "size": 564, "crc": 1695587584}, {"key": "androidx/compose/ui/node/ObserverModifierNodeKt.class", "name": "androidx/compose/ui/node/ObserverModifierNodeKt.class", "size": 3112, "crc": 1899247374}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion$OnObserveReadsChanged$1.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion$OnObserveReadsChanged$1.class", "size": 1658, "crc": -1510765163}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope$Companion.class", "size": 1409, "crc": -1695740769}, {"key": "androidx/compose/ui/node/ObserverNodeOwnerScope.class", "name": "androidx/compose/ui/node/ObserverNodeOwnerScope.class", "size": 2503, "crc": 1406605335}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher$Companion$DepthComparator.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher$Companion$DepthComparator.class", "size": 1902, "crc": 177538262}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher$Companion.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher$Companion.class", "size": 1019, "crc": -1426990840}, {"key": "androidx/compose/ui/node/OnPositionedDispatcher.class", "name": "androidx/compose/ui/node/OnPositionedDispatcher.class", "size": 5958, "crc": -1190908551}, {"key": "androidx/compose/ui/node/OnUnplacedModifierNode.class", "name": "androidx/compose/ui/node/OnUnplacedModifierNode.class", "size": 559, "crc": -140236388}, {"key": "androidx/compose/ui/node/OutOfFrameExecutor.class", "name": "androidx/compose/ui/node/OutOfFrameExecutor.class", "size": 703, "crc": -1128902063}, {"key": "androidx/compose/ui/node/OwnedLayer.class", "name": "androidx/compose/ui/node/OwnedLayer.class", "size": 3023, "crc": 834909995}, {"key": "androidx/compose/ui/node/Owner$Companion.class", "name": "androidx/compose/ui/node/Owner$Companion.class", "size": 1000, "crc": -260458664}, {"key": "androidx/compose/ui/node/Owner$OnLayoutCompletedListener.class", "name": "androidx/compose/ui/node/Owner$OnLayoutCompletedListener.class", "size": 568, "crc": -353079053}, {"key": "androidx/compose/ui/node/Owner.class", "name": "androidx/compose/ui/node/Owner.class", "size": 15859, "crc": -280788649}, {"key": "androidx/compose/ui/node/OwnerScope.class", "name": "androidx/compose/ui/node/OwnerScope.class", "size": 453, "crc": -1920044812}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$clearInvalidObservations$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$clearInvalidObservations$1.class", "size": 1674, "crc": 1360026020}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayout$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayout$1.class", "size": 1631, "crc": 1650991034}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifier$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifier$1.class", "size": 1647, "crc": 989439917}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifierInLookahead$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLayoutModifierInLookahead$1.class", "size": 1678, "crc": -2097986667}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookahead$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookahead$1.class", "size": 1646, "crc": -562510197}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookaheadMeasure$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingLookaheadMeasure$1.class", "size": 1666, "crc": -42907198}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingMeasure$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingMeasure$1.class", "size": 1639, "crc": 1676572403}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingSemantics$1.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver$onCommitAffectingSemantics$1.class", "size": 1567, "crc": 164906195}, {"key": "androidx/compose/ui/node/OwnerSnapshotObserver.class", "name": "androidx/compose/ui/node/OwnerSnapshotObserver.class", "size": 7678, "crc": 2001820554}, {"key": "androidx/compose/ui/node/ParentDataModifierNode.class", "name": "androidx/compose/ui/node/ParentDataModifierNode.class", "size": 888, "crc": 699056287}, {"key": "androidx/compose/ui/node/ParentDataModifierNodeKt.class", "name": "androidx/compose/ui/node/ParentDataModifierNodeKt.class", "size": 1012, "crc": 2038008154}, {"key": "androidx/compose/ui/node/PlaceableResult.class", "name": "androidx/compose/ui/node/PlaceableResult.class", "size": 1940, "crc": 2000631105}, {"key": "androidx/compose/ui/node/PointerInputModifierNode.class", "name": "androidx/compose/ui/node/PointerInputModifierNode.class", "size": 2061, "crc": -252418547}, {"key": "androidx/compose/ui/node/PointerInputModifierNodeKt.class", "name": "androidx/compose/ui/node/PointerInputModifierNodeKt.class", "size": 2514, "crc": 1571841800}, {"key": "androidx/compose/ui/node/Ref.class", "name": "androidx/compose/ui/node/Ref.class", "size": 1241, "crc": 370625499}, {"key": "androidx/compose/ui/node/RootForTest$UncaughtExceptionHandler.class", "name": "androidx/compose/ui/node/RootForTest$UncaughtExceptionHandler.class", "size": 728, "crc": -1324929414}, {"key": "androidx/compose/ui/node/RootForTest.class", "name": "androidx/compose/ui/node/RootForTest.class", "size": 2746, "crc": 945628307}, {"key": "androidx/compose/ui/node/RulerTrackingMap.class", "name": "androidx/compose/ui/node/RulerTrackingMap.class", "size": 9014, "crc": 854608543}, {"key": "androidx/compose/ui/node/SemanticsModifierNode.class", "name": "androidx/compose/ui/node/SemanticsModifierNode.class", "size": 1272, "crc": -2111401476}, {"key": "androidx/compose/ui/node/SemanticsModifierNodeKt.class", "name": "androidx/compose/ui/node/SemanticsModifierNodeKt.class", "size": 3970, "crc": 364827264}, {"key": "androidx/compose/ui/node/Snake.class", "name": "androidx/compose/ui/node/Snake.class", "size": 6868, "crc": 1636015096}, {"key": "androidx/compose/ui/node/SortedSet.class", "name": "androidx/compose/ui/node/SortedSet.class", "size": 1273, "crc": -1537471586}, {"key": "androidx/compose/ui/node/TailModifierNode.class", "name": "androidx/compose/ui/node/TailModifierNode.class", "size": 1601, "crc": 1469668090}, {"key": "androidx/compose/ui/node/TouchBoundsExpansion$Companion.class", "name": "androidx/compose/ui/node/TouchBoundsExpansion$Companion.class", "size": 4701, "crc": -791686613}, {"key": "androidx/compose/ui/node/TouchBoundsExpansion.class", "name": "androidx/compose/ui/node/TouchBoundsExpansion.class", "size": 4254, "crc": -113850389}, {"key": "androidx/compose/ui/node/TouchBoundsExpansionKt.class", "name": "androidx/compose/ui/node/TouchBoundsExpansionKt.class", "size": 4473, "crc": -890414243}, {"key": "androidx/compose/ui/node/TraversableNode$Companion$TraverseDescendantsAction.class", "name": "androidx/compose/ui/node/TraversableNode$Companion$TraverseDescendantsAction.class", "size": 2408, "crc": 2126670158}, {"key": "androidx/compose/ui/node/TraversableNode$Companion.class", "name": "androidx/compose/ui/node/TraversableNode$Companion.class", "size": 853, "crc": 664785442}, {"key": "androidx/compose/ui/node/TraversableNode.class", "name": "androidx/compose/ui/node/TraversableNode.class", "size": 944, "crc": -193715433}, {"key": "androidx/compose/ui/node/TraversableNodeKt.class", "name": "androidx/compose/ui/node/TraversableNodeKt.class", "size": 30538, "crc": 693316285}, {"key": "androidx/compose/ui/node/UiApplier.class", "name": "androidx/compose/ui/node/UiApplier.class", "size": 2806, "crc": -634048245}, {"key": "androidx/compose/ui/node/ViewAdapter.class", "name": "androidx/compose/ui/node/ViewAdapter.class", "size": 1162, "crc": -370280794}, {"key": "androidx/compose/ui/node/ViewInterop_androidKt.class", "name": "androidx/compose/ui/node/ViewInterop_androidKt.class", "size": 4739, "crc": 2124177053}, {"key": "androidx/compose/ui/node/WeakReference.class", "name": "androidx/compose/ui/node/WeakReference.class", "size": 1067, "crc": -3422600}, {"key": "androidx/compose/ui/platform/AbstractComposeView$ensureCompositionCreated$1.class", "name": "androidx/compose/ui/platform/AbstractComposeView$ensureCompositionCreated$1.class", "size": 2434, "crc": 645185544}, {"key": "androidx/compose/ui/platform/AbstractComposeView.class", "name": "androidx/compose/ui/platform/AbstractComposeView.class", "size": 14392, "crc": -1234018640}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$AbstractTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$AbstractTextSegmentIterator.class", "size": 2193, "crc": 235053247}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator$Companion.class", "size": 2183, "crc": 2106839570}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$CharacterTextSegmentIterator.class", "size": 3912, "crc": 456710163}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator$Companion.class", "size": 2009, "crc": -187170697}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$LineTextSegmentIterator.class", "size": 4730, "crc": 1882765945}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator$Companion.class", "size": 2009, "crc": -17769008}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$PageTextSegmentIterator.class", "size": 7453, "crc": -1896839510}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator$Companion.class", "size": 1894, "crc": -64142349}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$ParagraphTextSegmentIterator.class", "size": 3302, "crc": -1403918867}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$TextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$TextSegmentIterator.class", "size": 803, "crc": 1455559470}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator$Companion.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator$Companion.class", "size": 2138, "crc": -293399405}, {"key": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator.class", "name": "androidx/compose/ui/platform/AccessibilityIterators$WordTextSegmentIterator.class", "size": 4458, "crc": 446000033}, {"key": "androidx/compose/ui/platform/AccessibilityIterators.class", "name": "androidx/compose/ui/platform/AccessibilityIterators.class", "size": 1623, "crc": -1514798492}, {"key": "androidx/compose/ui/platform/AccessibilityManager$DefaultImpls.class", "name": "androidx/compose/ui/platform/AccessibilityManager$DefaultImpls.class", "size": 596, "crc": -167370214}, {"key": "androidx/compose/ui/platform/AccessibilityManager.class", "name": "androidx/compose/ui/platform/AccessibilityManager.class", "size": 1254, "crc": 1856939077}, {"key": "androidx/compose/ui/platform/AndroidAccessibilityManager$Companion.class", "name": "androidx/compose/ui/platform/AndroidAccessibilityManager$Companion.class", "size": 1036, "crc": -925453554}, {"key": "androidx/compose/ui/platform/AndroidAccessibilityManager.class", "name": "androidx/compose/ui/platform/AndroidAccessibilityManager.class", "size": 3076, "crc": 660327982}, {"key": "androidx/compose/ui/platform/AndroidClipboard.class", "name": "androidx/compose/ui/platform/AndroidClipboard.class", "size": 2784, "crc": -819504066}, {"key": "androidx/compose/ui/platform/AndroidClipboardManager.class", "name": "androidx/compose/ui/platform/AndroidClipboardManager.class", "size": 5156, "crc": 421035304}, {"key": "androidx/compose/ui/platform/AndroidClipboardManager_androidKt.class", "name": "androidx/compose/ui/platform/AndroidClipboardManager_androidKt.class", "size": 8300, "crc": -1439862074}, {"key": "androidx/compose/ui/platform/AndroidComposeView$Companion.class", "name": "androidx/compose/ui/platform/AndroidComposeView$Companion.class", "size": 9472, "crc": 363289041}, {"key": "androidx/compose/ui/platform/AndroidComposeView$ViewTreeOwners.class", "name": "androidx/compose/ui/platform/AndroidComposeView$ViewTreeOwners.class", "size": 1673, "crc": -1222436734}, {"key": "androidx/compose/ui/platform/AndroidComposeView$_inputModeManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$_inputModeManager$1.class", "size": 1997, "crc": 1155687566}, {"key": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$addAndroidView$1.class", "size": 6167, "crc": 928399385}, {"key": "androidx/compose/ui/platform/AndroidComposeView$bringIntoViewNode$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$bringIntoViewNode$1.class", "size": 2815, "crc": -162259824}, {"key": "androidx/compose/ui/platform/AndroidComposeView$configurationChangeObserver$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$configurationChangeObserver$1.class", "size": 1467, "crc": -1765145504}, {"key": "androidx/compose/ui/platform/AndroidComposeView$contentCaptureManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$contentCaptureManager$1.class", "size": 1702, "crc": 895127449}, {"key": "androidx/compose/ui/platform/AndroidComposeView$dispatchGenericMotionEvent$handled$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$dispatchGenericMotionEvent$handled$1.class", "size": 1609, "crc": -1615861743}, {"key": "androidx/compose/ui/platform/AndroidComposeView$dispatchKeyEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$dispatchKeyEvent$1.class", "size": 1535, "crc": 1938522977}, {"key": "androidx/compose/ui/platform/AndroidComposeView$dragAndDropManager$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$dragAndDropManager$1.class", "size": 2496, "crc": 696426488}, {"key": "androidx/compose/ui/platform/AndroidComposeView$focusSearch$searchResult$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$focusSearch$searchResult$1.class", "size": 1879, "crc": -2003435246}, {"key": "androidx/compose/ui/platform/AndroidComposeView$getFocusedRect$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$getFocusedRect$1.class", "size": 1508, "crc": -967216466}, {"key": "androidx/compose/ui/platform/AndroidComposeView$handleRotaryEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$handleRotaryEvent$1.class", "size": 1560, "crc": 1375027513}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$1.class", "size": 1737, "crc": 32551109}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$focusWasMovedOrCancelled$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1$focusWasMovedOrCancelled$1.class", "size": 1787, "crc": 458138289}, {"key": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$keyInputModifier$1.class", "size": 6205, "crc": 1641134272}, {"key": "androidx/compose/ui/platform/AndroidComposeView$onAttachedToWindow$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$onAttachedToWindow$1.class", "size": 1526, "crc": -1634206140}, {"key": "androidx/compose/ui/platform/AndroidComposeView$pointerIconService$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$pointerIconService$1.class", "size": 2513, "crc": -33409538}, {"key": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$1.class", "size": 1512, "crc": -885360004}, {"key": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$altFocus$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$altFocus$1.class", "size": 1530, "crc": -793539617}, {"key": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$focusSearchResult$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$requestFocus$focusSearchResult$1.class", "size": 1807, "crc": -360411969}, {"key": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventOnLayout$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventOnLayout$1.class", "size": 2152, "crc": -837308339}, {"key": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventRunnable$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$resendMotionEventRunnable$1.class", "size": 1993, "crc": 1827092646}, {"key": "androidx/compose/ui/platform/AndroidComposeView$rotaryInputModifier$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$rotaryInputModifier$1.class", "size": 1575, "crc": -1568688555}, {"key": "androidx/compose/ui/platform/AndroidComposeView$snapshotObserver$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$snapshotObserver$1.class", "size": 2467, "crc": -299517867}, {"key": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$1.class", "size": 1793, "crc": 428895419}, {"key": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$textInputSession$2.class", "size": 2014, "crc": -621295735}, {"key": "androidx/compose/ui/platform/AndroidComposeView$viewTreeOwners$2.class", "name": "androidx/compose/ui/platform/AndroidComposeView$viewTreeOwners$2.class", "size": 1658, "crc": 1492594626}, {"key": "androidx/compose/ui/platform/AndroidComposeView.class", "name": "androidx/compose/ui/platform/AndroidComposeView.class", "size": 141510, "crc": -1079001977}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$1.class", "size": 3907, "crc": 541772955}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api24Impl.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api24Impl.class", "size": 3023, "crc": 334243243}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api29Impl.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Api29Impl.class", "size": 4221, "crc": -**********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Companion.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$Companion.class", "size": 1925, "crc": **********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$ComposeAccessibilityNodeProvider.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$ComposeAccessibilityNodeProvider.class", "size": 4344, "crc": -**********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$PendingTextTraversedEvent.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$PendingTextTraversedEvent.class", "size": 2154, "crc": **********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$boundsUpdatesEventLoop$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$boundsUpdatesEventLoop$1.class", "size": 2131, "crc": -621958076}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$onSendAccessibilityEvent$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$onSendAccessibilityEvent$1.class", "size": 2153, "crc": -**********}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeeded$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeeded$1.class", "size": 6093, "crc": 1891640236}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeededLambda$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$scheduleScrollEventIfNeededLambda$1.class", "size": 1974, "crc": -424940517}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$1.class", "size": 1967, "crc": -729817343}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$semanticsNode$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat$sendSubtreeChangeAccessibilityEvents$semanticsNode$1.class", "size": 3297, "crc": 1650325689}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat.class", "size": 135037, "crc": -1622976739}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$WhenMappings.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$WhenMappings.class", "size": 984, "crc": -228127157}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$excludeLineAndPageGranularities$ancestor$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$excludeLineAndPageGranularities$ancestor$1.class", "size": 2395, "crc": -1201400786}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$setTraversalValues$semanticsOrderList$1.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$setTraversalValues$semanticsOrderList$1.class", "size": 2190, "crc": -1278491055}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$setTraversalValues$semanticsOrderList$2.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt$setTraversalValues$semanticsOrderList$2.class", "size": 1978, "crc": 749368856}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt.class", "size": 18099, "crc": 607781102}, {"key": "androidx/compose/ui/platform/AndroidComposeViewAssistHelperMethodsO.class", "name": "androidx/compose/ui/platform/AndroidComposeViewAssistHelperMethodsO.class", "size": 1441, "crc": -131809427}, {"key": "androidx/compose/ui/platform/AndroidComposeViewForceDarkModeQ.class", "name": "androidx/compose/ui/platform/AndroidComposeViewForceDarkModeQ.class", "size": 1182, "crc": 425774026}, {"key": "androidx/compose/ui/platform/AndroidComposeViewSensitiveContent35.class", "name": "androidx/compose/ui/platform/AndroidComposeViewSensitiveContent35.class", "size": 1277, "crc": -39452869}, {"key": "androidx/compose/ui/platform/AndroidComposeViewStartDragAndDropN.class", "name": "androidx/compose/ui/platform/AndroidComposeViewStartDragAndDropN.class", "size": 1910, "crc": -1096916371}, {"key": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallback.class", "name": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallback.class", "size": 2157, "crc": 1995701936}, {"key": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallbackS.class", "name": "androidx/compose/ui/platform/AndroidComposeViewTranslationCallbackS.class", "size": 1714, "crc": -1628992966}, {"key": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsN.class", "name": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsN.class", "size": 2458, "crc": -1185106620}, {"key": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsO.class", "name": "androidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsO.class", "size": 1352, "crc": -79341399}, {"key": "androidx/compose/ui/platform/AndroidComposeView_androidKt$platformTextInputServiceInterceptor$1.class", "name": "androidx/compose/ui/platform/AndroidComposeView_androidKt$platformTextInputServiceInterceptor$1.class", "size": 1572, "crc": 1100977403}, {"key": "androidx/compose/ui/platform/AndroidComposeView_androidKt.class", "name": "androidx/compose/ui/platform/AndroidComposeView_androidKt.class", "size": 8158, "crc": 1520236665}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalConfiguration$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalConfiguration$1.class", "size": 1394, "crc": -464185325}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalContext$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalContext$1.class", "size": 1346, "crc": -1662987665}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalImageVectorCache$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalImageVectorCache$1.class", "size": 1424, "crc": -1432300294}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResourceIdCache$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResourceIdCache$1.class", "size": 1418, "crc": -1856263332}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResources$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalResources$1.class", "size": 1957, "crc": 1535161028}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalView$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$LocalView$1.class", "size": 1319, "crc": 2044794654}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$1$1.class", "size": 2029, "crc": 1385599755}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1$invoke$$inlined$onDispose$1.class", "size": 2453, "crc": 1951540568}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$2$1.class", "size": 3213, "crc": 466663840}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$3.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$3.class", "size": 3445, "crc": -877499747}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$4.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$ProvideAndroidCompositionLocals$4.class", "size": 2233, "crc": 161695055}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1$invoke$$inlined$onDispose$1.class", "size": 2712, "crc": 312177367}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$1$1.class", "size": 3683, "crc": -1748502118}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$callbacks$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainImageVectorCache$callbacks$1$1.class", "size": 2128, "crc": 1107479049}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1$invoke$$inlined$onDispose$1.class", "size": 2704, "crc": 1132822981}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$1$1.class", "size": 3636, "crc": -2066055476}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$callbacks$1$1.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt$obtainResourceIdCache$callbacks$1$1.class", "size": 1799, "crc": -1008797486}, {"key": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt.class", "name": "androidx/compose/ui/platform/AndroidCompositionLocals_androidKt.class", "size": 26208, "crc": 585524717}, {"key": "androidx/compose/ui/platform/AndroidFontResourceLoader.class", "name": "androidx/compose/ui/platform/AndroidFontResourceLoader.class", "size": 3061, "crc": -1619839230}, {"key": "androidx/compose/ui/platform/AndroidFontResourceLoaderHelper.class", "name": "androidx/compose/ui/platform/AndroidFontResourceLoaderHelper.class", "size": 1377, "crc": 590727411}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$1.class", "size": 1939, "crc": 1453935249}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2$1.class", "size": 1806, "crc": 469103072}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$2.class", "size": 2327, "crc": -799065415}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3$1$1.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3$1$1.class", "size": 2085, "crc": 573211448}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession$startInputMethod$3.class", "size": 6397, "crc": 691037529}, {"key": "androidx/compose/ui/platform/AndroidPlatformTextInputSession.class", "name": "androidx/compose/ui/platform/AndroidPlatformTextInputSession.class", "size": 5913, "crc": -1086202082}, {"key": "androidx/compose/ui/platform/AndroidTextToolbar$textActionModeCallback$1.class", "name": "androidx/compose/ui/platform/AndroidTextToolbar$textActionModeCallback$1.class", "size": 1376, "crc": 289150180}, {"key": "androidx/compose/ui/platform/AndroidTextToolbar.class", "name": "androidx/compose/ui/platform/AndroidTextToolbar.class", "size": 5825, "crc": 206127502}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2$dispatcher$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2$dispatcher$1.class", "size": 3194, "crc": -768428050}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$Main$2.class", "size": 2520, "crc": 1301226452}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$currentThread$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion$currentThread$1.class", "size": 2724, "crc": 261069934}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$Companion.class", "size": 2036, "crc": 335261636}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher$dispatchCallback$1.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher$dispatchCallback$1.class", "size": 3602, "crc": 1713083714}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher.class", "size": 10068, "crc": -1336687151}, {"key": "androidx/compose/ui/platform/AndroidUiDispatcher_androidKt.class", "name": "androidx/compose/ui/platform/AndroidUiDispatcher_androidKt.class", "size": 650, "crc": -347247756}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$1.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$1.class", "size": 1898, "crc": 1615589534}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$2.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$2.class", "size": 1903, "crc": -427866450}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$callback$1.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock$withFrameNanos$2$callback$1.class", "size": 3108, "crc": 1270640281}, {"key": "androidx/compose/ui/platform/AndroidUiFrameClock.class", "name": "androidx/compose/ui/platform/AndroidUiFrameClock.class", "size": 7863, "crc": -869007810}, {"key": "androidx/compose/ui/platform/AndroidUriHandler.class", "name": "androidx/compose/ui/platform/AndroidUriHandler.class", "size": 2083, "crc": -1400707055}, {"key": "androidx/compose/ui/platform/AndroidViewConfiguration.class", "name": "androidx/compose/ui/platform/AndroidViewConfiguration.class", "size": 3045, "crc": -1905068013}, {"key": "androidx/compose/ui/platform/AndroidViewConfigurationApi34.class", "name": "androidx/compose/ui/platform/AndroidViewConfigurationApi34.class", "size": 1310, "crc": -2132626116}, {"key": "androidx/compose/ui/platform/AndroidViewsHandler.class", "name": "androidx/compose/ui/platform/AndroidViewsHandler.class", "size": 7704, "crc": -1814048048}, {"key": "androidx/compose/ui/platform/AndroidWindowInfo_androidKt.class", "name": "androidx/compose/ui/platform/AndroidWindowInfo_androidKt.class", "size": 7600, "crc": 1103203908}, {"key": "androidx/compose/ui/platform/Api28ClipboardManagerClipClear.class", "name": "androidx/compose/ui/platform/Api28ClipboardManagerClipClear.class", "size": 1179, "crc": -1049589001}, {"key": "androidx/compose/ui/platform/Api29Impl.class", "name": "androidx/compose/ui/platform/Api29Impl.class", "size": 1362, "crc": -1189652057}, {"key": "androidx/compose/ui/platform/Api30Impl.class", "name": "androidx/compose/ui/platform/Api30Impl.class", "size": 1103, "crc": 61009249}, {"key": "androidx/compose/ui/platform/Api35Impl.class", "name": "androidx/compose/ui/platform/Api35Impl.class", "size": 1186, "crc": -1432141265}, {"key": "androidx/compose/ui/platform/BoundsHelper$Companion.class", "name": "androidx/compose/ui/platform/BoundsHelper$Companion.class", "size": 1865, "crc": -70773495}, {"key": "androidx/compose/ui/platform/BoundsHelper.class", "name": "androidx/compose/ui/platform/BoundsHelper.class", "size": 1000, "crc": -696014026}, {"key": "androidx/compose/ui/platform/BoundsHelperApi16Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi16Impl.class", "size": 1843, "crc": -1200312876}, {"key": "androidx/compose/ui/platform/BoundsHelperApi24Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi24Impl.class", "size": 2202, "crc": -2063785343}, {"key": "androidx/compose/ui/platform/BoundsHelperApi28Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi28Impl.class", "size": 4731, "crc": -1272994084}, {"key": "androidx/compose/ui/platform/BoundsHelperApi29Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi29Impl.class", "size": 3141, "crc": 536013501}, {"key": "androidx/compose/ui/platform/BoundsHelperApi30Impl.class", "name": "androidx/compose/ui/platform/BoundsHelperApi30Impl.class", "size": 1541, "crc": -1404104564}, {"key": "androidx/compose/ui/platform/BringIntoViewOnScreenResponderNode.class", "name": "androidx/compose/ui/platform/BringIntoViewOnScreenResponderNode.class", "size": 3073, "crc": -167346264}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindow.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindow.class", "size": 779, "crc": -1762661981}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindowApi21.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindowApi21.class", "size": 3282, "crc": -1827383786}, {"key": "androidx/compose/ui/platform/CalculateMatrixToWindowApi29.class", "name": "androidx/compose/ui/platform/CalculateMatrixToWindowApi29.class", "size": 2309, "crc": 123299239}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$1.class", "size": 1965, "crc": -1654311385}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$1.class", "size": 2111, "crc": -917935856}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$2.class", "size": 1753, "crc": 1019186700}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$1.class", "size": 2022, "crc": -897923940}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3$2.class", "size": 4442, "crc": 1811878741}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1$startInputMethod$3.class", "size": 5004, "crc": -1884912907}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2$scope$1.class", "size": 4807, "crc": 1767578044}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor$textInputSession$2.class", "size": 4816, "crc": 1594795580}, {"key": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor.class", "name": "androidx/compose/ui/platform/ChainedPlatformTextInputInterceptor.class", "size": 6115, "crc": 385581677}, {"key": "androidx/compose/ui/platform/ClipEntry.class", "name": "androidx/compose/ui/platform/ClipEntry.class", "size": 1625, "crc": 1197725494}, {"key": "androidx/compose/ui/platform/ClipMetadata.class", "name": "androidx/compose/ui/platform/ClipMetadata.class", "size": 1149, "crc": -1517299316}, {"key": "androidx/compose/ui/platform/Clipboard.class", "name": "androidx/compose/ui/platform/Clipboard.class", "size": 1397, "crc": -1791573787}, {"key": "androidx/compose/ui/platform/ClipboardExtensions_androidKt.class", "name": "androidx/compose/ui/platform/ClipboardExtensions_androidKt.class", "size": 1353, "crc": 1643648287}, {"key": "androidx/compose/ui/platform/ClipboardManager.class", "name": "androidx/compose/ui/platform/ClipboardManager.class", "size": 2225, "crc": -1137575718}, {"key": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt$lambda$-1759434350$1.class", "name": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt$lambda$-1759434350$1.class", "size": 2221, "crc": -437224464}, {"key": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt.class", "name": "androidx/compose/ui/platform/ComposableSingletons$Wrapper_androidKt.class", "size": 1564, "crc": 273586623}, {"key": "androidx/compose/ui/platform/ComposeView$Content$1.class", "name": "androidx/compose/ui/platform/ComposeView$Content$1.class", "size": 1552, "crc": 7130579}, {"key": "androidx/compose/ui/platform/ComposeView.class", "name": "androidx/compose/ui/platform/ComposeView.class", "size": 5444, "crc": 1700906356}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAccessibilityManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAccessibilityManager$1.class", "size": 1240, "crc": 1012222418}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofill$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofill$1.class", "size": 1180, "crc": 418481816}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillManager$1.class", "size": 1375, "crc": 1236647847}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillTree$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalAutofillTree$1.class", "size": 1355, "crc": 887319078}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboard$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboard$1.class", "size": 1337, "crc": 1116784857}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboardManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalClipboardManager$1.class", "size": 1379, "crc": -1132041225}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalCursorBlinkEnabled$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalCursorBlinkEnabled$1.class", "size": 1196, "crc": 1680213161}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalDensity$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalDensity$1.class", "size": 1313, "crc": -842557179}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFocusManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFocusManager$1.class", "size": 1346, "crc": -302572200}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontFamilyResolver$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontFamilyResolver$1.class", "size": 1517, "crc": -499363853}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontLoader$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalFontLoader$1.class", "size": 1493, "crc": 1353213823}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalGraphicsContext$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalGraphicsContext$1.class", "size": 1373, "crc": -1695700633}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalHapticFeedback$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalHapticFeedback$1.class", "size": 1385, "crc": -1502451656}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalInputModeManager$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalInputModeManager$1.class", "size": 1366, "crc": -2086130025}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalLayoutDirection$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalLayoutDirection$1.class", "size": 1361, "crc": -735503385}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalPointerIconService$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalPointerIconService$1.class", "size": 1245, "crc": -1480111035}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalProvidableScrollCaptureInProgress$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalProvidableScrollCaptureInProgress$1.class", "size": 1226, "crc": 749232475}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalSoftwareKeyboardController$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalSoftwareKeyboardController$1.class", "size": 1270, "crc": 1831716576}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextInputService$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextInputService$1.class", "size": 1226, "crc": -859760880}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextToolbar$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalTextToolbar$1.class", "size": 1349, "crc": -567447284}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalUriHandler$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalUriHandler$1.class", "size": 1343, "crc": -620556908}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalViewConfiguration$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalViewConfiguration$1.class", "size": 1385, "crc": 1810262480}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$LocalWindowInfo$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$LocalWindowInfo$1.class", "size": 1343, "crc": 2131007791}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt$ProvideCommonCompositionLocals$1.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt$ProvideCommonCompositionLocals$1.class", "size": 2321, "crc": -2000250334}, {"key": "androidx/compose/ui/platform/CompositionLocalsKt.class", "name": "androidx/compose/ui/platform/CompositionLocalsKt.class", "size": 23226, "crc": 1373808397}, {"key": "androidx/compose/ui/platform/DebugUtilsKt.class", "name": "androidx/compose/ui/platform/DebugUtilsKt.class", "size": 849, "crc": 530601269}, {"key": "androidx/compose/ui/platform/DecodeHelper.class", "name": "androidx/compose/ui/platform/DecodeHelper.class", "size": 10560, "crc": -1780769393}, {"key": "androidx/compose/ui/platform/DefaultHapticFeedback.class", "name": "androidx/compose/ui/platform/DefaultHapticFeedback.class", "size": 2625, "crc": 476075628}, {"key": "androidx/compose/ui/platform/DelegatingSoftwareKeyboardController.class", "name": "androidx/compose/ui/platform/DelegatingSoftwareKeyboardController.class", "size": 1655, "crc": 1772361709}, {"key": "androidx/compose/ui/platform/DeviceRenderNode.class", "name": "androidx/compose/ui/platform/DeviceRenderNode.class", "size": 5269, "crc": -974798065}, {"key": "androidx/compose/ui/platform/DeviceRenderNodeData.class", "name": "androidx/compose/ui/platform/DeviceRenderNodeData.class", "size": 15802, "crc": 1480393320}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry.class", "size": 3079, "crc": -1482406028}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$1.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$1.class", "size": 1764, "crc": -1482710068}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$saveableStateRegistry$1.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt$DisposableSaveableStateRegistry$saveableStateRegistry$1.class", "size": 1706, "crc": -732199349}, {"key": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt.class", "name": "androidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt.class", "size": 9888, "crc": 329927141}, {"key": "androidx/compose/ui/platform/DrawChildContainer.class", "name": "androidx/compose/ui/platform/DrawChildContainer.class", "size": 3524, "crc": 569120629}, {"key": "androidx/compose/ui/platform/EncodeHelper.class", "name": "androidx/compose/ui/platform/EncodeHelper.class", "size": 10422, "crc": -515758911}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$Companion$FocusFinderThreadLocal$1.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$Companion$FocusFinderThreadLocal$1.class", "size": 1153, "crc": -606269157}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$Companion.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$Companion.class", "size": 1681, "crc": -659684851}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator$NextFocusGetter.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator$NextFocusGetter.class", "size": 1069, "crc": -931034083}, {"key": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator.class", "name": "androidx/compose/ui/platform/FocusFinderCompat$UserSpecifiedFocusComparator.class", "size": 7192, "crc": -147312266}, {"key": "androidx/compose/ui/platform/FocusFinderCompat.class", "name": "androidx/compose/ui/platform/FocusFinderCompat.class", "size": 14029, "crc": 1049107165}, {"key": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findUserSetNextFocus$1.class", "name": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findUserSetNextFocus$1.class", "size": 1665, "crc": -193813871}, {"key": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findViewInsideOutShouldExist$1.class", "name": "androidx/compose/ui/platform/FocusFinderCompat_androidKt$findViewInsideOutShouldExist$1.class", "size": 1530, "crc": -644521612}, {"key": "androidx/compose/ui/platform/FocusFinderCompat_androidKt.class", "name": "androidx/compose/ui/platform/FocusFinderCompat_androidKt.class", "size": 6991, "crc": -1526868472}, {"key": "androidx/compose/ui/platform/FocusSorter.class", "name": "androidx/compose/ui/platform/FocusSorter.class", "size": 7522, "crc": -2109357610}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$1.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$1.class", "size": 6288, "crc": -1953574743}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$2.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager$ensureStarted$2.class", "size": 1751, "crc": -1590065286}, {"key": "androidx/compose/ui/platform/GlobalSnapshotManager.class", "name": "androidx/compose/ui/platform/GlobalSnapshotManager.class", "size": 3121, "crc": -616998634}, {"key": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer$recordLambda$1.class", "name": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer$recordLambda$1.class", "size": 3809, "crc": 582524781}, {"key": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer.class", "name": "androidx/compose/ui/platform/GraphicsLayerOwnerLayer.class", "size": 24969, "crc": 1152656825}, {"key": "androidx/compose/ui/platform/HapticDefaults.class", "name": "androidx/compose/ui/platform/HapticDefaults.class", "size": 1518, "crc": 26893513}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy$DefaultImpls.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy$DefaultImpls.class", "size": 3492, "crc": 1619045642}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy$Key.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy$Key.class", "size": 1095, "crc": -2050270529}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicy.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicy.class", "size": 2181, "crc": 17524231}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt$withInfiniteAnimationFrameNanos$2.class", "size": 3143, "crc": 1545808560}, {"key": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt.class", "name": "androidx/compose/ui/platform/InfiniteAnimationPolicyKt.class", "size": 2206, "crc": 2055938517}, {"key": "androidx/compose/ui/platform/InputMethodSession$createInputConnection$1$1.class", "name": "androidx/compose/ui/platform/InputMethodSession$createInputConnection$1$1.class", "size": 4123, "crc": -206943227}, {"key": "androidx/compose/ui/platform/InputMethodSession.class", "name": "androidx/compose/ui/platform/InputMethodSession.class", "size": 6768, "crc": 468050079}, {"key": "androidx/compose/ui/platform/InspectableModifier$End.class", "name": "androidx/compose/ui/platform/InspectableModifier$End.class", "size": 954, "crc": -1760996945}, {"key": "androidx/compose/ui/platform/InspectableModifier.class", "name": "androidx/compose/ui/platform/InspectableModifier.class", "size": 2266, "crc": 1735916339}, {"key": "androidx/compose/ui/platform/InspectableValue$DefaultImpls.class", "name": "androidx/compose/ui/platform/InspectableValue$DefaultImpls.class", "size": 1515, "crc": -430961340}, {"key": "androidx/compose/ui/platform/InspectableValue.class", "name": "androidx/compose/ui/platform/InspectableValue.class", "size": 1984, "crc": -1805821156}, {"key": "androidx/compose/ui/platform/InspectableValueKt$NoInspectorInfo$1.class", "name": "androidx/compose/ui/platform/InspectableValueKt$NoInspectorInfo$1.class", "size": 1391, "crc": -1309282661}, {"key": "androidx/compose/ui/platform/InspectableValueKt$debugInspectorInfo$1.class", "name": "androidx/compose/ui/platform/InspectableValueKt$debugInspectorInfo$1.class", "size": 1760, "crc": -415254257}, {"key": "androidx/compose/ui/platform/InspectableValueKt.class", "name": "androidx/compose/ui/platform/InspectableValueKt.class", "size": 4635, "crc": -1230165303}, {"key": "androidx/compose/ui/platform/InspectionModeKt$LocalInspectionMode$1.class", "name": "androidx/compose/ui/platform/InspectionModeKt$LocalInspectionMode$1.class", "size": 1176, "crc": -568659957}, {"key": "androidx/compose/ui/platform/InspectionModeKt.class", "name": "androidx/compose/ui/platform/InspectionModeKt.class", "size": 1360, "crc": -2115734023}, {"key": "androidx/compose/ui/platform/InspectorInfo.class", "name": "androidx/compose/ui/platform/InspectorInfo.class", "size": 1892, "crc": 474215151}, {"key": "androidx/compose/ui/platform/InspectorValueInfo.class", "name": "androidx/compose/ui/platform/InspectorValueInfo.class", "size": 2882, "crc": 1438853201}, {"key": "androidx/compose/ui/platform/InvertMatrixKt.class", "name": "androidx/compose/ui/platform/InvertMatrixKt.class", "size": 6002, "crc": -1084915553}, {"key": "androidx/compose/ui/platform/JvmActuals_jvmKt.class", "name": "androidx/compose/ui/platform/JvmActuals_jvmKt.class", "size": 3291, "crc": -110322605}, {"key": "androidx/compose/ui/platform/LayerMatrixCache.class", "name": "androidx/compose/ui/platform/LayerMatrixCache.class", "size": 5801, "crc": 1648226749}, {"key": "androidx/compose/ui/platform/LazyWindowInfo.class", "name": "androidx/compose/ui/platform/LazyWindowInfo.class", "size": 5898, "crc": 515674367}, {"key": "androidx/compose/ui/platform/LegacyNestedScrollInteropConnection.class", "name": "androidx/compose/ui/platform/LegacyNestedScrollInteropConnection.class", "size": 7735, "crc": -1273105328}, {"key": "androidx/compose/ui/platform/MotionDurationScaleImpl.class", "name": "androidx/compose/ui/platform/MotionDurationScaleImpl.class", "size": 4709, "crc": -996207174}, {"key": "androidx/compose/ui/platform/MotionEventVerifierApi29.class", "name": "androidx/compose/ui/platform/MotionEventVerifierApi29.class", "size": 2294, "crc": -1084444059}, {"key": "androidx/compose/ui/platform/MutableSpanStyle.class", "name": "androidx/compose/ui/platform/MutableSpanStyle.class", "size": 10137, "crc": -945735297}, {"key": "androidx/compose/ui/platform/NestedScrollInteropConnection.class", "name": "androidx/compose/ui/platform/NestedScrollInteropConnection.class", "size": 7762, "crc": -1994766092}, {"key": "androidx/compose/ui/platform/NestedScrollInteropConnectionKt.class", "name": "androidx/compose/ui/platform/NestedScrollInteropConnectionKt.class", "size": 10837, "crc": -2125082314}, {"key": "androidx/compose/ui/platform/NoHapticFeedback.class", "name": "androidx/compose/ui/platform/NoHapticFeedback.class", "size": 1103, "crc": 1780555895}, {"key": "androidx/compose/ui/platform/OutlineResolver.class", "name": "androidx/compose/ui/platform/OutlineResolver.class", "size": 18905, "crc": -799605336}, {"key": "androidx/compose/ui/platform/OutlineVerificationHelper.class", "name": "androidx/compose/ui/platform/OutlineVerificationHelper.class", "size": 2682, "crc": -2063392111}, {"key": "androidx/compose/ui/platform/PlatformTextInputInterceptor.class", "name": "androidx/compose/ui/platform/PlatformTextInputInterceptor.class", "size": 1315, "crc": -399606297}, {"key": "androidx/compose/ui/platform/PlatformTextInputMethodRequest.class", "name": "androidx/compose/ui/platform/PlatformTextInputMethodRequest.class", "size": 853, "crc": 692173387}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNode.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNode.class", "size": 534, "crc": 950737047}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$InterceptPlatformTextInput$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$InterceptPlatformTextInput$1.class", "size": 2252, "crc": -1568813014}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$LocalChainedPlatformTextInputInterceptor$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$LocalChainedPlatformTextInputInterceptor$1.class", "size": 1363, "crc": -691326864}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$establishTextInputSession$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$establishTextInputSession$1.class", "size": 1724, "crc": 572459822}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$interceptedTextInputSession$1.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt$interceptedTextInputSession$1.class", "size": 1821, "crc": 1259469247}, {"key": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt.class", "name": "androidx/compose/ui/platform/PlatformTextInputModifierNodeKt.class", "size": 11784, "crc": -1843125632}, {"key": "androidx/compose/ui/platform/PlatformTextInputSession.class", "name": "androidx/compose/ui/platform/PlatformTextInputSession.class", "size": 1152, "crc": -725456433}, {"key": "androidx/compose/ui/platform/PlatformTextInputSessionScope.class", "name": "androidx/compose/ui/platform/PlatformTextInputSessionScope.class", "size": 650, "crc": 624983528}, {"key": "androidx/compose/ui/platform/RenderNodeApi23$Companion.class", "name": "androidx/compose/ui/platform/RenderNodeApi23$Companion.class", "size": 1321, "crc": -1704003181}, {"key": "androidx/compose/ui/platform/RenderNodeApi23.class", "name": "androidx/compose/ui/platform/RenderNodeApi23.class", "size": 19641, "crc": -1589137727}, {"key": "androidx/compose/ui/platform/RenderNodeApi29.class", "name": "androidx/compose/ui/platform/RenderNodeApi29.class", "size": 16916, "crc": -1882343021}, {"key": "androidx/compose/ui/platform/RenderNodeApi29VerificationHelper.class", "name": "androidx/compose/ui/platform/RenderNodeApi29VerificationHelper.class", "size": 1573, "crc": -265191404}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$Companion$getMatrix$1.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$Companion$getMatrix$1.class", "size": 1663, "crc": -2136278704}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$Companion.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$Companion.class", "size": 1034, "crc": -1475043577}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$UniqueDrawingIdApi29.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$UniqueDrawingIdApi29.class", "size": 1234, "crc": 2112006228}, {"key": "androidx/compose/ui/platform/RenderNodeLayer$updateDisplayList$1$1.class", "name": "androidx/compose/ui/platform/RenderNodeLayer$updateDisplayList$1$1.class", "size": 1855, "crc": -303980719}, {"key": "androidx/compose/ui/platform/RenderNodeLayer.class", "name": "androidx/compose/ui/platform/RenderNodeLayer.class", "size": 20119, "crc": 692240271}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper23.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper23.class", "size": 1115, "crc": -2138453888}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper24.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper24.class", "size": 1111, "crc": -1660443837}, {"key": "androidx/compose/ui/platform/RenderNodeVerificationHelper28.class", "name": "androidx/compose/ui/platform/RenderNodeVerificationHelper28.class", "size": 1639, "crc": 2015994794}, {"key": "androidx/compose/ui/platform/ScrollObservationScope.class", "name": "androidx/compose/ui/platform/ScrollObservationScope.class", "size": 3742, "crc": -590440733}, {"key": "androidx/compose/ui/platform/SemanticsNodeCopy.class", "name": "androidx/compose/ui/platform/SemanticsNodeCopy.class", "size": 3814, "crc": -1976415520}, {"key": "androidx/compose/ui/platform/SemanticsUtils_androidKt.class", "name": "androidx/compose/ui/platform/SemanticsUtils_androidKt.class", "size": 6634, "crc": 1451383885}, {"key": "androidx/compose/ui/platform/ShapeContainingUtilKt.class", "name": "androidx/compose/ui/platform/ShapeContainingUtilKt.class", "size": 10828, "crc": -1711263630}, {"key": "androidx/compose/ui/platform/SoftwareKeyboardController.class", "name": "androidx/compose/ui/platform/SoftwareKeyboardController.class", "size": 587, "crc": -1886632376}, {"key": "androidx/compose/ui/platform/SubcompositionKt.class", "name": "androidx/compose/ui/platform/SubcompositionKt.class", "size": 1928, "crc": -658766556}, {"key": "androidx/compose/ui/platform/Synchronization_androidKt.class", "name": "androidx/compose/ui/platform/Synchronization_androidKt.class", "size": 1953, "crc": 1040201212}, {"key": "androidx/compose/ui/platform/TestTagElement.class", "name": "androidx/compose/ui/platform/TestTagElement.class", "size": 2930, "crc": -1308765642}, {"key": "androidx/compose/ui/platform/TestTagKt.class", "name": "androidx/compose/ui/platform/TestTagKt.class", "size": 1026, "crc": -1382992370}, {"key": "androidx/compose/ui/platform/TestTagNode.class", "name": "androidx/compose/ui/platform/TestTagNode.class", "size": 1702, "crc": 1012209968}, {"key": "androidx/compose/ui/platform/TextToolbar$DefaultImpls.class", "name": "androidx/compose/ui/platform/TextToolbar$DefaultImpls.class", "size": 2258, "crc": -1586472412}, {"key": "androidx/compose/ui/platform/TextToolbar.class", "name": "androidx/compose/ui/platform/TextToolbar.class", "size": 3978, "crc": 954698026}, {"key": "androidx/compose/ui/platform/TextToolbarHelperMethods.class", "name": "androidx/compose/ui/platform/TextToolbarHelperMethods.class", "size": 1851, "crc": -2013728412}, {"key": "androidx/compose/ui/platform/TextToolbarStatus.class", "name": "androidx/compose/ui/platform/TextToolbarStatus.class", "size": 1913, "crc": -618276190}, {"key": "androidx/compose/ui/platform/UriHandler.class", "name": "androidx/compose/ui/platform/UriHandler.class", "size": 570, "crc": -1838808387}, {"key": "androidx/compose/ui/platform/ValueElement.class", "name": "androidx/compose/ui/platform/ValueElement.class", "size": 2929, "crc": -190170282}, {"key": "androidx/compose/ui/platform/ValueElementSequence.class", "name": "androidx/compose/ui/platform/ValueElementSequence.class", "size": 1962, "crc": -1353313439}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$Companion.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$Companion.class", "size": 1387, "crc": -1271276124}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$1.class", "size": 2144, "crc": 1888594272}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow$installFor$listener$1.class", "size": 1716, "crc": 2057924726}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindow.class", "size": 2496, "crc": -1524808778}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$1.class", "size": 2672, "crc": 1764591991}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool$installFor$listener$1.class", "size": 1951, "crc": -26958370}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnDetachedFromWindowOrReleasedFromPool.class", "size": 3590, "crc": 108760043}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnLifecycleDestroyed.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnLifecycleDestroyed.class", "size": 2268, "crc": 126418302}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$1.class", "size": 2200, "crc": -367216977}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$2.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$2.class", "size": 1847, "crc": 1230515643}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$listener$1.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed$installFor$listener$1.class", "size": 4780, "crc": 1406789616}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy$DisposeOnViewTreeLifecycleDestroyed.class", "size": 5288, "crc": 1234375652}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy.class", "size": 1796, "crc": 1297392070}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt$installForLifecycle$2.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt$installForLifecycle$2.class", "size": 1660, "crc": -444147278}, {"key": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt.class", "name": "androidx/compose/ui/platform/ViewCompositionStrategy_androidKt.class", "size": 3416, "crc": -888238900}, {"key": "androidx/compose/ui/platform/ViewConfiguration$DefaultImpls.class", "name": "androidx/compose/ui/platform/ViewConfiguration$DefaultImpls.class", "size": 1545, "crc": 661708775}, {"key": "androidx/compose/ui/platform/ViewConfiguration.class", "name": "androidx/compose/ui/platform/ViewConfiguration.class", "size": 3318, "crc": -80913164}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion$OutlineProvider$1.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion$OutlineProvider$1.class", "size": 1576, "crc": -**********}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion$getMatrix$1.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion$getMatrix$1.class", "size": 1604, "crc": -**********}, {"key": "androidx/compose/ui/platform/ViewLayer$Companion.class", "name": "androidx/compose/ui/platform/ViewLayer$Companion.class", "size": 3965, "crc": -**********}, {"key": "androidx/compose/ui/platform/ViewLayer$UniqueDrawingIdApi29.class", "name": "androidx/compose/ui/platform/ViewLayer$UniqueDrawingIdApi29.class", "size": 1210, "crc": -731775372}, {"key": "androidx/compose/ui/platform/ViewLayer.class", "name": "androidx/compose/ui/platform/ViewLayer.class", "size": 23893, "crc": **********}, {"key": "androidx/compose/ui/platform/ViewLayerContainer.class", "name": "androidx/compose/ui/platform/ViewLayerContainer.class", "size": 1324, "crc": -36582301}, {"key": "androidx/compose/ui/platform/ViewLayerVerificationHelper28.class", "name": "androidx/compose/ui/platform/ViewLayerVerificationHelper28.class", "size": 1311, "crc": 1720632906}, {"key": "androidx/compose/ui/platform/ViewLayerVerificationHelper31.class", "name": "androidx/compose/ui/platform/ViewLayerVerificationHelper31.class", "size": 1522, "crc": -1275842799}, {"key": "androidx/compose/ui/platform/ViewRootForInspector$DefaultImpls.class", "name": "androidx/compose/ui/platform/ViewRootForInspector$DefaultImpls.class", "size": 1185, "crc": 669180828}, {"key": "androidx/compose/ui/platform/ViewRootForInspector.class", "name": "androidx/compose/ui/platform/ViewRootForInspector.class", "size": 1507, "crc": 591817029}, {"key": "androidx/compose/ui/platform/ViewRootForTest$Companion.class", "name": "androidx/compose/ui/platform/ViewRootForTest$Companion.class", "size": 1879, "crc": -2057384386}, {"key": "androidx/compose/ui/platform/ViewRootForTest.class", "name": "androidx/compose/ui/platform/ViewRootForTest.class", "size": 1254, "crc": -2123413402}, {"key": "androidx/compose/ui/platform/WeakCache.class", "name": "androidx/compose/ui/platform/WeakCache.class", "size": 3866, "crc": -2145021801}, {"key": "androidx/compose/ui/platform/WindowInfo.class", "name": "androidx/compose/ui/platform/WindowInfo.class", "size": 2513, "crc": -458998407}, {"key": "androidx/compose/ui/platform/WindowInfoImpl$Companion.class", "name": "androidx/compose/ui/platform/WindowInfoImpl$Companion.class", "size": 1424, "crc": -302498752}, {"key": "androidx/compose/ui/platform/WindowInfoImpl.class", "name": "androidx/compose/ui/platform/WindowInfoImpl.class", "size": 4788, "crc": -170966888}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$1.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$1.class", "size": 1394, "crc": 2073017814}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$2.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1$2.class", "size": 2110, "crc": -829705743}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$1$1.class", "size": 4209, "crc": -642908055}, {"key": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$2.class", "name": "androidx/compose/ui/platform/WindowInfoKt$WindowFocusObserver$2.class", "size": 1806, "crc": -777814465}, {"key": "androidx/compose/ui/platform/WindowInfoKt.class", "name": "androidx/compose/ui/platform/WindowInfoKt.class", "size": 5523, "crc": -796868265}, {"key": "androidx/compose/ui/platform/WindowRecomposerFactory$Companion.class", "name": "androidx/compose/ui/platform/WindowRecomposerFactory$Companion.class", "size": 1965, "crc": 1765747429}, {"key": "androidx/compose/ui/platform/WindowRecomposerFactory.class", "name": "androidx/compose/ui/platform/WindowRecomposerFactory.class", "size": 1129, "crc": -1927076018}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$1.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$1.class", "size": 1752, "crc": -324553428}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$unsetJob$1.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy$createAndInstallWindowRecomposer$unsetJob$1.class", "size": 4233, "crc": 799556762}, {"key": "androidx/compose/ui/platform/WindowRecomposerPolicy.class", "name": "androidx/compose/ui/platform/WindowRecomposerPolicy.class", "size": 6321, "crc": 1307080062}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$1.class", "size": 1816, "crc": -1013042700}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$WhenMappings.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$WhenMappings.class", "size": 1213, "crc": 1361125734}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1$1.class", "size": 2180, "crc": -1380086731}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1$1$1.class", "size": 4277, "crc": 1905204552}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2$onStateChanged$1.class", "size": 7146, "crc": 1582810942}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$createLifecycleAwareWindowRecomposer$2.class", "size": 4078, "crc": 967253597}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$1.class", "size": 6029, "crc": -1331424916}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$contentObserver$1.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt$getAnimationScaleFlowFor$1$1$contentObserver$1.class", "size": 1707, "crc": 578471614}, {"key": "androidx/compose/ui/platform/WindowRecomposer_androidKt.class", "name": "androidx/compose/ui/platform/WindowRecomposer_androidKt.class", "size": 15058, "crc": -493240803}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$1$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$1$1.class", "size": 3595, "crc": 1913557259}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$2$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$2$1.class", "size": 3596, "crc": -1488181183}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$3.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1$3.class", "size": 3119, "crc": -1394530808}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1$1.class", "size": 7360, "crc": -639681877}, {"key": "androidx/compose/ui/platform/WrappedComposition$setContent$1.class", "name": "androidx/compose/ui/platform/WrappedComposition$setContent$1.class", "size": 3649, "crc": -499070716}, {"key": "androidx/compose/ui/platform/WrappedComposition.class", "name": "androidx/compose/ui/platform/WrappedComposition.class", "size": 5620, "crc": -1901064881}, {"key": "androidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods.class", "name": "androidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods.class", "size": 1495, "crc": -1016958746}, {"key": "androidx/compose/ui/platform/Wrapper_androidKt.class", "name": "androidx/compose/ui/platform/Wrapper_androidKt.class", "size": 5842, "crc": 1257619551}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$setCollectionItemInfo$itemInfo$1.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$setCollectionItemInfo$itemInfo$1.class", "size": 1414, "crc": 1257165236}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$toAccessibilityCollectionItemInfo$1.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt$toAccessibilityCollectionItemInfo$1.class", "size": 1698, "crc": -566758144}, {"key": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt.class", "name": "androidx/compose/ui/platform/accessibility/CollectionInfo_androidKt.class", "size": 12778, "crc": 1999443254}, {"key": "androidx/compose/ui/platform/actionmodecallback/FloatingTextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/FloatingTextActionModeCallback.class", "size": 3172, "crc": 751561450}, {"key": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption$WhenMappings.class", "name": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption$WhenMappings.class", "size": 1003, "crc": -82248524}, {"key": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption.class", "name": "androidx/compose/ui/platform/actionmodecallback/MenuItemOption.class", "size": 3291, "crc": -897770694}, {"key": "androidx/compose/ui/platform/actionmodecallback/PrimaryTextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/PrimaryTextActionModeCallback.class", "size": 2368, "crc": 1231348375}, {"key": "androidx/compose/ui/platform/actionmodecallback/TextActionModeCallback.class", "name": "androidx/compose/ui/platform/actionmodecallback/TextActionModeCallback.class", "size": 9818, "crc": 972361662}, {"key": "androidx/compose/ui/relocation/BringIntoViewModifierNode.class", "name": "androidx/compose/ui/relocation/BringIntoViewModifierNode.class", "size": 1311, "crc": -830985691}, {"key": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt$bringIntoView$2.class", "name": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt$bringIntoView$2.class", "size": 2913, "crc": 791350422}, {"key": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt.class", "name": "androidx/compose/ui/relocation/BringIntoViewModifierNodeKt.class", "size": 9203, "crc": 1700010914}, {"key": "androidx/compose/ui/res/ColorResources_androidKt.class", "name": "androidx/compose/ui/res/ColorResources_androidKt.class", "size": 3463, "crc": -542764588}, {"key": "androidx/compose/ui/res/FontResources_androidKt.class", "name": "androidx/compose/ui/res/FontResources_androidKt.class", "size": 5401, "crc": 33929911}, {"key": "androidx/compose/ui/res/ImageResources_androidKt.class", "name": "androidx/compose/ui/res/ImageResources_androidKt.class", "size": 5824, "crc": -1683216505}, {"key": "androidx/compose/ui/res/ImageVectorCache$ImageVectorEntry.class", "name": "androidx/compose/ui/res/ImageVectorCache$ImageVectorEntry.class", "size": 3261, "crc": -718594393}, {"key": "androidx/compose/ui/res/ImageVectorCache$Key.class", "name": "androidx/compose/ui/res/ImageVectorCache$Key.class", "size": 3176, "crc": -1890270074}, {"key": "androidx/compose/ui/res/ImageVectorCache.class", "name": "androidx/compose/ui/res/ImageVectorCache.class", "size": 3255, "crc": 1900259316}, {"key": "androidx/compose/ui/res/PainterResources_androidKt.class", "name": "androidx/compose/ui/res/PainterResources_androidKt.class", "size": 10464, "crc": -238998111}, {"key": "androidx/compose/ui/res/PrimitiveResources_androidKt.class", "name": "androidx/compose/ui/res/PrimitiveResources_androidKt.class", "size": 5363, "crc": 2078514615}, {"key": "androidx/compose/ui/res/ResourceIdCache.class", "name": "androidx/compose/ui/res/ResourceIdCache.class", "size": 2741, "crc": 530678608}, {"key": "androidx/compose/ui/res/ResourceResolutionException.class", "name": "androidx/compose/ui/res/ResourceResolutionException.class", "size": 1105, "crc": 23052800}, {"key": "androidx/compose/ui/res/StringResources_androidKt.class", "name": "androidx/compose/ui/res/StringResources_androidKt.class", "size": 5931, "crc": -1491662369}, {"key": "androidx/compose/ui/res/VectorResources_androidKt.class", "name": "androidx/compose/ui/res/VectorResources_androidKt.class", "size": 9147, "crc": 107695662}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$ScrollCaptureSessionListener.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$ScrollCaptureSessionListener.class", "size": 743, "crc": 276714192}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureEnd$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureEnd$1.class", "size": 4205, "crc": -348281221}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$1.class", "size": 4668, "crc": -855136569}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$2.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$2.class", "size": 2424, "crc": -1287692816}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$3.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$onScrollCaptureImageRequest$3.class", "size": 1536, "crc": -1108133109}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$scrollTracker$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback$scrollTracker$1.class", "size": 8026, "crc": -468605389}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback.class", "size": 12824, "crc": 876420066}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt$launchWithCancellationSignal$1.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt$launchWithCancellationSignal$1.class", "size": 1730, "crc": -724739315}, {"key": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt.class", "name": "androidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback_androidKt.class", "size": 3347, "crc": -912319402}, {"key": "androidx/compose/ui/scrollcapture/DisableAnimationMotionDurationScale.class", "name": "androidx/compose/ui/scrollcapture/DisableAnimationMotionDurationScale.class", "size": 3134, "crc": 522515362}, {"key": "androidx/compose/ui/scrollcapture/RelativeScroller$scrollBy$1.class", "name": "androidx/compose/ui/scrollcapture/RelativeScroller$scrollBy$1.class", "size": 1895, "crc": 197462474}, {"key": "androidx/compose/ui/scrollcapture/RelativeScroller.class", "name": "androidx/compose/ui/scrollcapture/RelativeScroller.class", "size": 5519, "crc": -1198587546}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$1.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$1.class", "size": 1764, "crc": -1310781321}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$2.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$2.class", "size": 1855, "crc": 433364933}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$3.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture$onScrollCaptureSearch$3.class", "size": 1974, "crc": -1085004434}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture.class", "size": 9235, "crc": -901896948}, {"key": "androidx/compose/ui/scrollcapture/ScrollCaptureCandidate.class", "name": "androidx/compose/ui/scrollcapture/ScrollCaptureCandidate.class", "size": 2484, "crc": -1551819915}, {"key": "androidx/compose/ui/scrollcapture/ScrollCapture_androidKt.class", "name": "androidx/compose/ui/scrollcapture/ScrollCapture_androidKt.class", "size": 10186, "crc": -869301610}, {"key": "androidx/compose/ui/semantics/AccessibilityAction.class", "name": "androidx/compose/ui/semantics/AccessibilityAction.class", "size": 2585, "crc": 676512252}, {"key": "androidx/compose/ui/semantics/AppendedSemanticsElement.class", "name": "androidx/compose/ui/semantics/AppendedSemanticsElement.class", "size": 5359, "crc": 212103625}, {"key": "androidx/compose/ui/semantics/ClearAndSetSemanticsElement.class", "name": "androidx/compose/ui/semantics/ClearAndSetSemanticsElement.class", "size": 4787, "crc": 1516498000}, {"key": "androidx/compose/ui/semantics/CollectionInfo.class", "name": "androidx/compose/ui/semantics/CollectionInfo.class", "size": 1061, "crc": -498784955}, {"key": "androidx/compose/ui/semantics/CollectionItemInfo.class", "name": "androidx/compose/ui/semantics/CollectionItemInfo.class", "size": 1402, "crc": 982884345}, {"key": "androidx/compose/ui/semantics/CoreSemanticsModifierNode.class", "name": "androidx/compose/ui/semantics/CoreSemanticsModifierNode.class", "size": 3433, "crc": -1869641126}, {"key": "androidx/compose/ui/semantics/CustomAccessibilityAction.class", "name": "androidx/compose/ui/semantics/CustomAccessibilityAction.class", "size": 2632, "crc": 2014898210}, {"key": "androidx/compose/ui/semantics/EmptySemanticsElement.class", "name": "androidx/compose/ui/semantics/EmptySemanticsElement.class", "size": 2665, "crc": -536681429}, {"key": "androidx/compose/ui/semantics/EmptySemanticsModifier.class", "name": "androidx/compose/ui/semantics/EmptySemanticsModifier.class", "size": 1345, "crc": 482732687}, {"key": "androidx/compose/ui/semantics/LiveRegionMode$Companion.class", "name": "androidx/compose/ui/semantics/LiveRegionMode$Companion.class", "size": 1231, "crc": -2130717604}, {"key": "androidx/compose/ui/semantics/LiveRegionMode.class", "name": "androidx/compose/ui/semantics/LiveRegionMode.class", "size": 2630, "crc": -532775860}, {"key": "androidx/compose/ui/semantics/LtrBoundsComparator.class", "name": "androidx/compose/ui/semantics/LtrBoundsComparator.class", "size": 1971, "crc": -579542333}, {"key": "androidx/compose/ui/semantics/ProgressBarRangeInfo$Companion.class", "name": "androidx/compose/ui/semantics/ProgressBarRangeInfo$Companion.class", "size": 1233, "crc": 1414839959}, {"key": "androidx/compose/ui/semantics/ProgressBarRangeInfo.class", "name": "androidx/compose/ui/semantics/ProgressBarRangeInfo.class", "size": 4496, "crc": -2033006574}, {"key": "androidx/compose/ui/semantics/Role$Companion.class", "name": "androidx/compose/ui/semantics/Role$Companion.class", "size": 2273, "crc": -1169588571}, {"key": "androidx/compose/ui/semantics/Role.class", "name": "androidx/compose/ui/semantics/Role.class", "size": 3493, "crc": -2116007384}, {"key": "androidx/compose/ui/semantics/RtlBoundsComparator.class", "name": "androidx/compose/ui/semantics/RtlBoundsComparator.class", "size": 1971, "crc": -738803783}, {"key": "androidx/compose/ui/semantics/ScrollAxisRange.class", "name": "androidx/compose/ui/semantics/ScrollAxisRange.class", "size": 2725, "crc": -201463217}, {"key": "androidx/compose/ui/semantics/SemanticRegionImpl.class", "name": "androidx/compose/ui/semantics/SemanticRegionImpl.class", "size": 2746, "crc": -2108321626}, {"key": "androidx/compose/ui/semantics/SemanticsActions.class", "name": "androidx/compose/ui/semantics/SemanticsActions.class", "size": 19676, "crc": 1579347972}, {"key": "androidx/compose/ui/semantics/SemanticsConfiguration.class", "name": "androidx/compose/ui/semantics/SemanticsConfiguration.class", "size": 16880, "crc": 1108081006}, {"key": "androidx/compose/ui/semantics/SemanticsConfigurationKt$getOrNull$1.class", "name": "androidx/compose/ui/semantics/SemanticsConfigurationKt$getOrNull$1.class", "size": 1174, "crc": -414925426}, {"key": "androidx/compose/ui/semantics/SemanticsConfigurationKt.class", "name": "androidx/compose/ui/semantics/SemanticsConfigurationKt.class", "size": 1565, "crc": 1025538325}, {"key": "androidx/compose/ui/semantics/SemanticsInfo.class", "name": "androidx/compose/ui/semantics/SemanticsInfo.class", "size": 1223, "crc": -16296895}, {"key": "androidx/compose/ui/semantics/SemanticsInfoKt.class", "name": "androidx/compose/ui/semantics/SemanticsInfoKt.class", "size": 3826, "crc": 277520773}, {"key": "androidx/compose/ui/semantics/SemanticsListener.class", "name": "androidx/compose/ui/semantics/SemanticsListener.class", "size": 901, "crc": -328147279}, {"key": "androidx/compose/ui/semantics/SemanticsModifier$DefaultImpls.class", "name": "androidx/compose/ui/semantics/SemanticsModifier$DefaultImpls.class", "size": 2987, "crc": -1593543863}, {"key": "androidx/compose/ui/semantics/SemanticsModifier.class", "name": "androidx/compose/ui/semantics/SemanticsModifier.class", "size": 2564, "crc": 837079538}, {"key": "androidx/compose/ui/semantics/SemanticsModifierKt.class", "name": "androidx/compose/ui/semantics/SemanticsModifierKt.class", "size": 5965, "crc": -465439454}, {"key": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$1.class", "size": 1775, "crc": 1517758587}, {"key": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$2.class", "name": "androidx/compose/ui/semantics/SemanticsNode$emitFakeNodes$fakeNode$2.class", "size": 1700, "crc": 841208601}, {"key": "androidx/compose/ui/semantics/SemanticsNode$fakeSemanticsNode$fakeNode$1.class", "name": "androidx/compose/ui/semantics/SemanticsNode$fakeSemanticsNode$fakeNode$1.class", "size": 1799, "crc": -1539961415}, {"key": "androidx/compose/ui/semantics/SemanticsNode.class", "name": "androidx/compose/ui/semantics/SemanticsNode.class", "size": 32762, "crc": 1911451533}, {"key": "androidx/compose/ui/semantics/SemanticsNodeKt.class", "name": "androidx/compose/ui/semantics/SemanticsNodeKt.class", "size": 10386, "crc": 563436357}, {"key": "androidx/compose/ui/semantics/SemanticsNodeWithAdjustedBounds.class", "name": "androidx/compose/ui/semantics/SemanticsNodeWithAdjustedBounds.class", "size": 1535, "crc": -403237066}, {"key": "androidx/compose/ui/semantics/SemanticsOwner.class", "name": "androidx/compose/ui/semantics/SemanticsOwner.class", "size": 5599, "crc": 692271143}, {"key": "androidx/compose/ui/semantics/SemanticsOwnerKt.class", "name": "androidx/compose/ui/semantics/SemanticsOwnerKt.class", "size": 11176, "crc": -621890722}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentDataType$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentDataType$1.class", "size": 1603, "crc": -1766947638}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentDescription$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentDescription$1.class", "size": 2498, "crc": -888713400}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$ContentType$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$ContentType$1.class", "size": 1563, "crc": 1057578245}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$HideFromAccessibility$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$HideFromAccessibility$1.class", "size": 1359, "crc": -1702362757}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$InvisibleToUser$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$InvisibleToUser$1.class", "size": 1347, "crc": 1550421441}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$IsDialog$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$IsDialog$1.class", "size": 1500, "crc": -1619030358}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$IsPopup$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$IsPopup$1.class", "size": 1496, "crc": 900164556}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$LinkTestMarker$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$LinkTestMarker$1.class", "size": 1345, "crc": -884168848}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$PaneTitle$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$PaneTitle$1.class", "size": 1471, "crc": 64557211}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$Role$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$Role$1.class", "size": 1520, "crc": -1373386008}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$Shape$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$Shape$1.class", "size": 1503, "crc": -1030107166}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$TestTag$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$TestTag$1.class", "size": 1366, "crc": 1776425731}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$Text$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$Text$1.class", "size": 2617, "crc": 1425560142}, {"key": "androidx/compose/ui/semantics/SemanticsProperties$TraversalIndex$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties$TraversalIndex$1.class", "size": 1415, "crc": 107433440}, {"key": "androidx/compose/ui/semantics/SemanticsProperties.class", "name": "androidx/compose/ui/semantics/SemanticsProperties.class", "size": 20561, "crc": -1815773687}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$AccessibilityClassName$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$AccessibilityClassName$1.class", "size": 1425, "crc": 189658557}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$TestTagsAsResourceId$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid$TestTagsAsResourceId$1.class", "size": 1450, "crc": 1744878469}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesAndroid.class", "size": 3440, "crc": 80389710}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt$ActionPropertyKey$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt$ActionPropertyKey$1.class", "size": 2429, "crc": 2093378133}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt$getScrollViewportLength$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt$getScrollViewportLength$1.class", "size": 2017, "crc": 1245089139}, {"key": "androidx/compose/ui/semantics/SemanticsPropertiesKt.class", "name": "androidx/compose/ui/semantics/SemanticsPropertiesKt.class", "size": 45193, "crc": 802642801}, {"key": "androidx/compose/ui/semantics/SemanticsProperties_androidKt$SemanticsPropertyKey$1.class", "name": "androidx/compose/ui/semantics/SemanticsProperties_androidKt$SemanticsPropertyKey$1.class", "size": 1382, "crc": -1841401317}, {"key": "androidx/compose/ui/semantics/SemanticsProperties_androidKt.class", "name": "androidx/compose/ui/semantics/SemanticsProperties_androidKt.class", "size": 4795, "crc": 424742444}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyKey$1.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyKey$1.class", "size": 1297, "crc": 979311693}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyKey.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyKey.class", "size": 5355, "crc": -1968942110}, {"key": "androidx/compose/ui/semantics/SemanticsPropertyReceiver.class", "name": "androidx/compose/ui/semantics/SemanticsPropertyReceiver.class", "size": 876, "crc": 1139467679}, {"key": "androidx/compose/ui/semantics/SemanticsRegion.class", "name": "androidx/compose/ui/semantics/SemanticsRegion.class", "size": 1056, "crc": 1908934431}, {"key": "androidx/compose/ui/semantics/SemanticsRegion_androidKt.class", "name": "androidx/compose/ui/semantics/SemanticsRegion_androidKt.class", "size": 751, "crc": -1044459461}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt$UnmergedConfigComparator$1$1.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt$UnmergedConfigComparator$1$1.class", "size": 1335, "crc": -515727225}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt$UnmergedConfigComparator$1$2.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt$UnmergedConfigComparator$1$2.class", "size": 1335, "crc": -420811960}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt$UnmergedConfigComparator$1.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt$UnmergedConfigComparator$1.class", "size": 2664, "crc": -2034467291}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt$geometryDepthFirstSearch$isTraversalGroup$1.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt$geometryDepthFirstSearch$isTraversalGroup$1.class", "size": 1428, "crc": -764277339}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt$sortByGeometryGroupings$1.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt$sortByGeometryGroupings$1.class", "size": 1670, "crc": -1464986834}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt$special$$inlined$thenBy$1.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt$special$$inlined$thenBy$1.class", "size": 2496, "crc": 1629002248}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt$special$$inlined$thenBy$2.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt$special$$inlined$thenBy$2.class", "size": 2539, "crc": 142517321}, {"key": "androidx/compose/ui/semantics/SemanticsSortKt.class", "name": "androidx/compose/ui/semantics/SemanticsSortKt.class", "size": 13396, "crc": -269283643}, {"key": "androidx/compose/ui/semantics/TopBottomBoundsComparator.class", "name": "androidx/compose/ui/semantics/TopBottomBoundsComparator.class", "size": 2143, "crc": -213877899}, {"key": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy$measure$1.class", "name": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy$measure$1.class", "size": 1629, "crc": 651800879}, {"key": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy.class", "name": "androidx/compose/ui/spatial/EmptyFillMeasurePolicy.class", "size": 2197, "crc": -713827790}, {"key": "androidx/compose/ui/spatial/RectList.class", "name": "androidx/compose/ui/spatial/RectList.class", "size": 26961, "crc": -561534855}, {"key": "androidx/compose/ui/spatial/RectListDebuggerModifierElement.class", "name": "androidx/compose/ui/spatial/RectListDebuggerModifierElement.class", "size": 2345, "crc": -673677989}, {"key": "androidx/compose/ui/spatial/RectListDebuggerModifierNode$onAttach$1.class", "name": "androidx/compose/ui/spatial/RectListDebuggerModifierNode$onAttach$1.class", "size": 1436, "crc": 35006353}, {"key": "androidx/compose/ui/spatial/RectListDebuggerModifierNode.class", "name": "androidx/compose/ui/spatial/RectListDebuggerModifierNode.class", "size": 6382, "crc": -123592447}, {"key": "androidx/compose/ui/spatial/RectListDebugger_androidKt$RectListDebugger$1.class", "name": "androidx/compose/ui/spatial/RectListDebugger_androidKt$RectListDebugger$1.class", "size": 1724, "crc": 1355987153}, {"key": "androidx/compose/ui/spatial/RectListDebugger_androidKt.class", "name": "androidx/compose/ui/spatial/RectListDebugger_androidKt.class", "size": 7398, "crc": 1378703713}, {"key": "androidx/compose/ui/spatial/RectListKt.class", "name": "androidx/compose/ui/spatial/RectListKt.class", "size": 7012, "crc": 1573525402}, {"key": "androidx/compose/ui/spatial/RectManager$dispatchCallbacks$3$1.class", "name": "androidx/compose/ui/spatial/RectManager$dispatchCallbacks$3$1.class", "size": 2054, "crc": 1479153634}, {"key": "androidx/compose/ui/spatial/RectManager$dispatchLambda$1.class", "name": "androidx/compose/ui/spatial/RectManager$dispatchLambda$1.class", "size": 2572, "crc": -1025005283}, {"key": "androidx/compose/ui/spatial/RectManager.class", "name": "androidx/compose/ui/spatial/RectManager.class", "size": 25691, "crc": -25630672}, {"key": "androidx/compose/ui/spatial/RectManagerKt.class", "name": "androidx/compose/ui/spatial/RectManagerKt.class", "size": 3286, "crc": -453298270}, {"key": "androidx/compose/ui/spatial/RelativeLayoutBounds.class", "name": "androidx/compose/ui/spatial/RelativeLayoutBounds.class", "size": 14177, "crc": 1110953721}, {"key": "androidx/compose/ui/spatial/ThrottledCallbacks$Entry.class", "name": "androidx/compose/ui/spatial/ThrottledCallbacks$Entry.class", "size": 5755, "crc": 1887642469}, {"key": "androidx/compose/ui/spatial/ThrottledCallbacks.class", "name": "androidx/compose/ui/spatial/ThrottledCallbacks.class", "size": 24890, "crc": 1583867272}, {"key": "androidx/compose/ui/spatial/ThrottledCallbacksKt.class", "name": "androidx/compose/ui/spatial/ThrottledCallbacksKt.class", "size": 5290, "crc": 1610553446}, {"key": "androidx/compose/ui/state/ToggleableState.class", "name": "androidx/compose/ui/state/ToggleableState.class", "size": 1937, "crc": 1333814406}, {"key": "androidx/compose/ui/state/ToggleableStateKt.class", "name": "androidx/compose/ui/state/ToggleableStateKt.class", "size": 776, "crc": 973976967}, {"key": "androidx/compose/ui/text/TextMeasurerHelperKt.class", "name": "androidx/compose/ui/text/TextMeasurerHelperKt.class", "size": 5034, "crc": 198939828}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoApi33Helper.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoApi33Helper.class", "size": 2106, "crc": 499384491}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoApi34Helper.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoApi34Helper.class", "size": 2225, "crc": -188056904}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoBuilder_androidKt.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoBuilder_androidKt.class", "size": 9330, "crc": 776258938}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController$invalidate$1$1.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController$invalidate$1$1.class", "size": 1455, "crc": -1379185049}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController$textFieldToRootTransform$1.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController$textFieldToRootTransform$1.class", "size": 1583, "crc": 1084511665}, {"key": "androidx/compose/ui/text/input/CursorAnchorInfoController.class", "name": "androidx/compose/ui/text/input/CursorAnchorInfoController.class", "size": 7575, "crc": 1007594826}, {"key": "androidx/compose/ui/text/input/InputEventCallback2.class", "name": "androidx/compose/ui/text/input/InputEventCallback2.class", "size": 1759, "crc": 1388826550}, {"key": "androidx/compose/ui/text/input/InputMethodManager.class", "name": "androidx/compose/ui/text/input/InputMethodManager.class", "size": 1474, "crc": 1561033709}, {"key": "androidx/compose/ui/text/input/InputMethodManagerImpl$imm$2.class", "name": "androidx/compose/ui/text/input/InputMethodManagerImpl$imm$2.class", "size": 1876, "crc": 859919946}, {"key": "androidx/compose/ui/text/input/InputMethodManagerImpl.class", "name": "androidx/compose/ui/text/input/InputMethodManagerImpl.class", "size": 4119, "crc": -1345758189}, {"key": "androidx/compose/ui/text/input/InputState_androidKt.class", "name": "androidx/compose/ui/text/input/InputState_androidKt.class", "size": 1687, "crc": -978681354}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapper.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapper.class", "size": 690, "crc": 1822832132}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi21.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi21.class", "size": 7701, "crc": 1619550931}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi24.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi24.class", "size": 2301, "crc": -1836251619}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi25.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi25.class", "size": 2048, "crc": 1753957874}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi34.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapperApi34.class", "size": 2599, "crc": 876858264}, {"key": "androidx/compose/ui/text/input/NullableInputConnectionWrapper_androidKt.class", "name": "androidx/compose/ui/text/input/NullableInputConnectionWrapper_androidKt.class", "size": 2039, "crc": 886015185}, {"key": "androidx/compose/ui/text/input/RecordingInputConnection.class", "name": "androidx/compose/ui/text/input/RecordingInputConnection.class", "size": 19660, "crc": 1718561508}, {"key": "androidx/compose/ui/text/input/RecordingInputConnection_androidKt.class", "name": "androidx/compose/ui/text/input/RecordingInputConnection_androidKt.class", "size": 688, "crc": 1557043501}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$TextInputCommand.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$TextInputCommand.class", "size": 2352, "crc": -782462816}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$WhenMappings.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$WhenMappings.class", "size": 1084, "crc": 1343165347}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$baseInputConnection$2.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$baseInputConnection$2.class", "size": 1641, "crc": 633761045}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$createInputConnection$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$createInputConnection$1.class", "size": 3999, "crc": 1798420621}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$onEditCommand$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$onEditCommand$1.class", "size": 1673, "crc": -225465293}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$onImeActionPerformed$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$onImeActionPerformed$1.class", "size": 1618, "crc": 310842173}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$1.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$1.class", "size": 1510, "crc": -1814300908}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$2.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid$stopInput$2.class", "size": 1441, "crc": -765368167}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid.class", "size": 20562, "crc": 1043300711}, {"key": "androidx/compose/ui/text/input/TextInputServiceAndroid_androidKt.class", "name": "androidx/compose/ui/text/input/TextInputServiceAndroid_androidKt.class", "size": 7197, "crc": 1686663674}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$2.class", "size": 2648, "crc": -567788519}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion$OnCommitAffectingUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion$OnCommitAffectingUpdate$1.class", "size": 2313, "crc": -2108256156}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$Companion.class", "size": 1028, "crc": -680588568}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$1.class", "size": 1837, "crc": 1542333901}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$2.class", "size": 1682, "crc": 1830349712}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$3.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$3.class", "size": 2119, "crc": -2033505103}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$4.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$4.class", "size": 2129, "crc": -90255658}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$1.class", "size": 1680, "crc": 311119779}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5$measure$2.class", "size": 2091, "crc": -546104621}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$5.class", "size": 5159, "crc": 476812948}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$1.class", "size": 1683, "crc": 1687752729}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$2.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$2.class", "size": 4221, "crc": -1486220220}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$3.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$layoutNode$1$coreModifier$3.class", "size": 3851, "crc": 104771494}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedFling$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedFling$1.class", "size": 3997, "crc": 98095131}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedPreFling$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$onNestedPreFling$1.class", "size": 3622, "crc": -1826642147}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$release$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$release$1.class", "size": 1309, "crc": 2061279967}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$reset$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$reset$1.class", "size": 1305, "crc": 1326285592}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$runInvalidate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$runInvalidate$1.class", "size": 1568, "crc": -2053969819}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$runUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$runUpdate$1.class", "size": 2277, "crc": -643675954}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder$update$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder$update$1.class", "size": 1307, "crc": -1576293438}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder.class", "size": 42817, "crc": -14957159}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt$NoOpScrollConnection$1.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt$NoOpScrollConnection$1.class", "size": 867, "crc": -672441910}, {"key": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt.class", "name": "androidx/compose/ui/viewinterop/AndroidViewHolder_androidKt.class", "size": 4966, "crc": -1328019644}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$1.class", "size": 2256, "crc": -320505096}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$1.class", "size": 2280, "crc": -1520575374}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$2.class", "size": 2281, "crc": 75707946}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$3.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$2$3.class", "size": 2282, "crc": -944435617}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$1.class", "size": 2281, "crc": 119027810}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$3$2.class", "size": 2282, "crc": 1276013170}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$4.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$AndroidView$4.class", "size": 2602, "crc": -456011145}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$NoOpUpdate$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$NoOpUpdate$1.class", "size": 1302, "crc": 1554144042}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$createAndroidViewNodeFactory$1$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$createAndroidViewNodeFactory$1$1.class", "size": 2818, "crc": -1265700148}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$1.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$1.class", "size": 2173, "crc": -469200560}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$2.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$2.class", "size": 2192, "crc": 1498194079}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$3.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$3.class", "size": 2204, "crc": 475095347}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$4.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$4.class", "size": 2263, "crc": -1941013059}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5$WhenMappings.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5$WhenMappings.class", "size": 891, "crc": 438367122}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt$updateViewHolderParams$5.class", "size": 2549, "crc": -272960712}, {"key": "androidx/compose/ui/viewinterop/AndroidView_androidKt.class", "name": "androidx/compose/ui/viewinterop/AndroidView_androidKt.class", "size": 20264, "crc": 1851256166}, {"key": "androidx/compose/ui/viewinterop/FocusGroupNode_androidKt.class", "name": "androidx/compose/ui/viewinterop/FocusGroupNode_androidKt.class", "size": 4743, "crc": -854181358}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesElement.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesElement.class", "size": 2660, "crc": 841889505}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onEnter$1.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onEnter$1.class", "size": 3076, "crc": -607094121}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onExit$1.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode$onExit$1.class", "size": 5366, "crc": -973467908}, {"key": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode.class", "name": "androidx/compose/ui/viewinterop/FocusGroupPropertiesNode.class", "size": 12697, "crc": 800021795}, {"key": "androidx/compose/ui/viewinterop/FocusTargetPropertiesElement.class", "name": "androidx/compose/ui/viewinterop/FocusTargetPropertiesElement.class", "size": 2668, "crc": 1928824648}, {"key": "androidx/compose/ui/viewinterop/FocusTargetPropertiesNode.class", "name": "androidx/compose/ui/viewinterop/FocusTargetPropertiesNode.class", "size": 1620, "crc": 38880866}, {"key": "androidx/compose/ui/viewinterop/InteropViewFactoryHolder_androidKt.class", "name": "androidx/compose/ui/viewinterop/InteropViewFactoryHolder_androidKt.class", "size": 452, "crc": 837347146}, {"key": "androidx/compose/ui/viewinterop/InteropView_androidKt.class", "name": "androidx/compose/ui/viewinterop/InteropView_androidKt.class", "size": 378, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$registerSaveStateProvider$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$registerSaveStateProvider$1.class", "size": 2272, "crc": 462475147}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$releaseBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$releaseBlock$1.class", "size": 1749, "crc": -755011082}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$resetBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$resetBlock$1.class", "size": 1683, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder$updateBlock$1.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder$updateBlock$1.class", "size": 1687, "crc": -**********}, {"key": "androidx/compose/ui/viewinterop/ViewFactoryHolder.class", "name": "androidx/compose/ui/viewinterop/ViewFactoryHolder.class", "size": 9472, "crc": -**********}, {"key": "androidx/compose/ui/window/AlignmentOffsetPositionProvider.class", "name": "androidx/compose/ui/window/AlignmentOffsetPositionProvider.class", "size": 4185, "crc": -401071680}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$1$1.class", "size": 3282, "crc": **********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1$invoke$$inlined$onDispose$1.class", "size": 2191, "crc": -**********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$2$1.class", "size": 2806, "crc": 259552173}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$3$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$3$1.class", "size": 2227, "crc": **********}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$4.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$4.class", "size": 2412, "crc": -1787432177}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1$1$1.class", "size": 1633, "crc": -1570533904}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialog$1$1$1.class", "size": 5405, "crc": -1937915900}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialogId$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$Dialog$dialogId$1$1.class", "size": 1376, "crc": -451827360}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1$1.class", "size": 3315, "crc": -1672029750}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$1$1.class", "size": 4664, "crc": 984973969}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$2.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt$DialogLayout$2.class", "size": 2133, "crc": 1090307172}, {"key": "androidx/compose/ui/window/AndroidDialog_androidKt.class", "name": "androidx/compose/ui/window/AndroidDialog_androidKt.class", "size": 18086, "crc": 1214354432}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$LocalPopupTestTag$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$LocalPopupTestTag$1.class", "size": 1134, "crc": -1021443406}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$1.class", "size": 2643, "crc": 757730505}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1$invoke$$inlined$onDispose$1.class", "size": 2176, "crc": -1661058329}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$2$1.class", "size": 3920, "crc": 1985052023}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$3$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$3$1.class", "size": 2390, "crc": 1187645091}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1$invoke$$inlined$onDispose$1.class", "size": 1920, "crc": 1421778097}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$4$1.class", "size": 3178, "crc": -668474474}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1$1.class", "size": 1325, "crc": -1115358581}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$5$1.class", "size": 4099, "crc": 1599963747}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$7$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$7$1.class", "size": 2000, "crc": -361485993}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1$1.class", "size": 1644, "crc": -1805999782}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$8$1.class", "size": 2409, "crc": -7542286}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$9.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$9.class", "size": 2667, "crc": 71994531}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupId$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupId$1$1.class", "size": 1416, "crc": 1904714493}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$1$1.class", "size": 1640, "crc": 582382555}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$2$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1$2$1.class", "size": 1802, "crc": -997710627}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$Popup$popupLayout$1$1$1.class", "size": 11472, "crc": 117760066}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$PopupTestTag$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$PopupTestTag$1.class", "size": 1996, "crc": 1391546655}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1$1.class", "size": 1662, "crc": -495614518}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1$2.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1$2.class", "size": 1869, "crc": 105762145}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1$3.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1$3.class", "size": 2275, "crc": 1795394348}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt$SimpleStack$1$1.class", "size": 4925, "crc": 1829807743}, {"key": "androidx/compose/ui/window/AndroidPopup_androidKt.class", "name": "androidx/compose/ui/window/AndroidPopup_androidKt.class", "size": 30652, "crc": -1972783359}, {"key": "androidx/compose/ui/window/Api28Impl.class", "name": "androidx/compose/ui/window/Api28Impl.class", "size": 1273, "crc": 560624546}, {"key": "androidx/compose/ui/window/Api30Impl.class", "name": "androidx/compose/ui/window/Api30Impl.class", "size": 1471, "crc": 641570249}, {"key": "androidx/compose/ui/window/Api33Impl.class", "name": "androidx/compose/ui/window/Api33Impl.class", "size": 2913, "crc": -474690109}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt$lambda$210148896$1.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt$lambda$210148896$1.class", "size": 2249, "crc": 192960942}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidDialog_androidKt.class", "size": 1578, "crc": 1156886049}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt$lambda$-1131826196$1.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt$lambda$-1131826196$1.class", "size": 2248, "crc": 962977034}, {"key": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt.class", "name": "androidx/compose/ui/window/ComposableSingletons$AndroidPopup_androidKt.class", "size": 1581, "crc": -423069630}, {"key": "androidx/compose/ui/window/DialogLayout$1.class", "name": "androidx/compose/ui/window/DialogLayout$1.class", "size": 4584, "crc": -976289609}, {"key": "androidx/compose/ui/window/DialogLayout$Content$4.class", "name": "androidx/compose/ui/window/DialogLayout$Content$4.class", "size": 1549, "crc": 92269244}, {"key": "androidx/compose/ui/window/DialogLayout.class", "name": "androidx/compose/ui/window/DialogLayout.class", "size": 13917, "crc": -242685367}, {"key": "androidx/compose/ui/window/DialogProperties.class", "name": "androidx/compose/ui/window/DialogProperties.class", "size": 4806, "crc": **********}, {"key": "androidx/compose/ui/window/DialogWindowProvider.class", "name": "androidx/compose/ui/window/DialogWindowProvider.class", "size": 616, "crc": -**********}, {"key": "androidx/compose/ui/window/DialogWrapper$1$2.class", "name": "androidx/compose/ui/window/DialogWrapper$1$2.class", "size": 1348, "crc": -10389618}, {"key": "androidx/compose/ui/window/DialogWrapper$2.class", "name": "androidx/compose/ui/window/DialogWrapper$2.class", "size": 2070, "crc": 954236887}, {"key": "androidx/compose/ui/window/DialogWrapper$WhenMappings.class", "name": "androidx/compose/ui/window/DialogWrapper$WhenMappings.class", "size": 809, "crc": -832120630}, {"key": "androidx/compose/ui/window/DialogWrapper.class", "name": "androidx/compose/ui/window/DialogWrapper.class", "size": 13400, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupLayout$2.class", "name": "androidx/compose/ui/window/PopupLayout$2.class", "size": 1408, "crc": 513754394}, {"key": "androidx/compose/ui/window/PopupLayout$Companion$onCommitAffectingPopupPosition$1.class", "name": "androidx/compose/ui/window/PopupLayout$Companion$onCommitAffectingPopupPosition$1.class", "size": 1481, "crc": -79064893}, {"key": "androidx/compose/ui/window/PopupLayout$Companion.class", "name": "androidx/compose/ui/window/PopupLayout$Companion.class", "size": 986, "crc": 421131279}, {"key": "androidx/compose/ui/window/PopupLayout$Content$4.class", "name": "androidx/compose/ui/window/PopupLayout$Content$4.class", "size": 1543, "crc": 448196055}, {"key": "androidx/compose/ui/window/PopupLayout$WhenMappings.class", "name": "androidx/compose/ui/window/PopupLayout$WhenMappings.class", "size": 804, "crc": -571355648}, {"key": "androidx/compose/ui/window/PopupLayout$canCalculatePosition$2.class", "name": "androidx/compose/ui/window/PopupLayout$canCalculatePosition$2.class", "size": 2569, "crc": 606879272}, {"key": "androidx/compose/ui/window/PopupLayout$snapshotStateObserver$1.class", "name": "androidx/compose/ui/window/PopupLayout$snapshotStateObserver$1.class", "size": 2624, "crc": 1733604240}, {"key": "androidx/compose/ui/window/PopupLayout$updatePosition$1.class", "name": "androidx/compose/ui/window/PopupLayout$updatePosition$1.class", "size": 2046, "crc": -1330479673}, {"key": "androidx/compose/ui/window/PopupLayout.class", "name": "androidx/compose/ui/window/PopupLayout.class", "size": 28910, "crc": -2016587865}, {"key": "androidx/compose/ui/window/PopupLayoutHelper.class", "name": "androidx/compose/ui/window/PopupLayoutHelper.class", "size": 1395, "crc": -**********}, {"key": "androidx/compose/ui/window/PopupLayoutHelperImpl.class", "name": "androidx/compose/ui/window/PopupLayoutHelperImpl.class", "size": 1974, "crc": **********}, {"key": "androidx/compose/ui/window/PopupLayoutHelperImpl29.class", "name": "androidx/compose/ui/window/PopupLayoutHelperImpl29.class", "size": 1383, "crc": -480778554}, {"key": "androidx/compose/ui/window/PopupPositionProvider.class", "name": "androidx/compose/ui/window/PopupPositionProvider.class", "size": 1057, "crc": 788632912}, {"key": "androidx/compose/ui/window/PopupProperties.class", "name": "androidx/compose/ui/window/PopupProperties.class", "size": 5368, "crc": -2705189}, {"key": "androidx/compose/ui/window/SecureFlagPolicy.class", "name": "androidx/compose/ui/window/SecureFlagPolicy.class", "size": 1966, "crc": -**********}, {"key": "androidx/compose/ui/window/SecureFlagPolicy_androidKt$WhenMappings.class", "name": "androidx/compose/ui/window/SecureFlagPolicy_androidKt$WhenMappings.class", "size": 905, "crc": **********}, {"key": "androidx/compose/ui/window/SecureFlagPolicy_androidKt.class", "name": "androidx/compose/ui/window/SecureFlagPolicy_androidKt.class", "size": 1160, "crc": **********}, {"key": "androidx/inspection/compose/ui/ProguardDetection.class", "name": "androidx/inspection/compose/ui/ProguardDetection.class", "size": 550, "crc": **********}, {"key": "androidx/compose/ui/platform/coreshims/AutofillIdCompat.class", "name": "androidx/compose/ui/platform/coreshims/AutofillIdCompat.class", "size": 1345, "crc": 1112067839}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api23Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api23Impl.class", "size": 857, "crc": 401769262}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api29Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api29Impl.class", "size": 2868, "crc": -342138471}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api34Impl.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat$Api34Impl.class", "size": 1205, "crc": 976977248}, {"key": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat.class", "name": "androidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat.class", "size": 6362, "crc": -1228692622}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api26Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api26Impl.class", "size": 807, "crc": -1944113727}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api29Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api29Impl.class", "size": 852, "crc": 665748574}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api30Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims$Api30Impl.class", "size": 785, "crc": -1640713814}, {"key": "androidx/compose/ui/platform/coreshims/ViewCompatShims.class", "name": "androidx/compose/ui/platform/coreshims/ViewCompatShims.class", "size": 2851, "crc": -1116252136}, {"key": "androidx/compose/ui/platform/coreshims/ViewStructureCompat$Api23Impl.class", "name": "androidx/compose/ui/platform/coreshims/ViewStructureCompat$Api23Impl.class", "size": 2172, "crc": 668368597}, {"key": "androidx/compose/ui/platform/coreshims/ViewStructureCompat.class", "name": "androidx/compose/ui/platform/coreshims/ViewStructureCompat.class", "size": 3364, "crc": 232904801}, {"key": "META-INF/androidx.compose.ui_ui.version", "name": "META-INF/androidx.compose.ui_ui.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/ui_release.kotlin_module", "name": "META-INF/ui_release.kotlin_module", "size": 5761, "crc": 384638452}]