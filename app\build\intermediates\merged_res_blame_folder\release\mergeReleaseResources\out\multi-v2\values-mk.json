{"logs": [{"outputFile": "com.aj.aj_tv_player.app-mergeReleaseResources-40:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\71219f197f28274676b3874ecf964996\\transformed\\core-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,79", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,500,605,708,8258", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "198,300,397,495,600,703,819,8354"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19b642e7465edfc9a64434f74e28ca0a\\transformed\\material3-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,292,408,526,623,718,830,963,1084,1232,1317,1416,1510,1606,1721,1845,1949,2094,2238,2380,2554,2685,2806,2933,3058,3153,3251,3377,3512,3612,3714,3827,3968,4117,4233,4335,4412,4506,4601,4693,4779,4893,4976,5059,5159,5261,5358,5455,5543,5650,5750,5852,5985,6068,6179", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "169,287,403,521,618,713,825,958,1079,1227,1312,1411,1505,1601,1716,1840,1944,2089,2233,2375,2549,2680,2801,2928,3053,3148,3246,3372,3507,3607,3709,3822,3963,4112,4228,4330,4407,4501,4596,4688,4774,4888,4971,5054,5154,5256,5353,5450,5538,5645,5745,5847,5980,6063,6174,6277"}, "to": {"startLines": "16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1469,1588,1706,1822,1940,2037,2132,2244,2377,2498,2646,2731,2830,2924,3020,3135,3259,3363,3508,3652,3794,3968,4099,4220,4347,4472,4567,4665,4791,4926,5026,5128,5241,5382,5531,5647,5749,5826,5920,6015,6107,6193,6307,6390,6473,6573,6675,6772,6869,6957,7064,7164,7266,7399,7482,7593", "endColumns": "118,117,115,117,96,94,111,132,120,147,84,98,93,95,114,123,103,144,143,141,173,130,120,126,124,94,97,125,134,99,101,112,140,148,115,101,76,93,94,91,85,113,82,82,99,101,96,96,87,106,99,101,132,82,110,102", "endOffsets": "1583,1701,1817,1935,2032,2127,2239,2372,2493,2641,2726,2825,2919,3015,3130,3254,3358,3503,3647,3789,3963,4094,4215,4342,4467,4562,4660,4786,4921,5021,5123,5236,5377,5526,5642,5744,5821,5915,6010,6102,6188,6302,6385,6468,6568,6670,6767,6864,6952,7059,7159,7261,7394,7477,7588,7691"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f608d021a4f1e83e8e85d7b0e5006a22\\transformed\\foundation-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,149", "endColumns": "93,95", "endOffsets": "144,240"}, "to": {"startLines": "83,84", "startColumns": "4,4", "startOffsets": "8632,8726", "endColumns": "93,95", "endOffsets": "8721,8817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\62f35d7bdb9f40920fe06c2287c5bbc3\\transformed\\ui-release\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,299,395,498,583,660,750,842,926,997,1067,1151,1240,1312,1393,1464", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "204,294,390,493,578,655,745,837,921,992,1062,1146,1235,1307,1388,1459,1580"}, "to": {"startLines": "9,10,11,12,13,14,15,72,73,74,75,76,77,78,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "824,928,1018,1114,1217,1302,1379,7696,7788,7872,7943,8013,8097,8186,8359,8440,8511", "endColumns": "103,89,95,102,84,76,89,91,83,70,69,83,88,71,80,70,120", "endOffsets": "923,1013,1109,1212,1297,1374,1464,7783,7867,7938,8008,8092,8181,8253,8435,8506,8627"}}]}]}