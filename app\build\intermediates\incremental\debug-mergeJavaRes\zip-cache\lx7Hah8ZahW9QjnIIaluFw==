[{"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$1.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$1.class", "size": 1448, "crc": 989675001}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$2.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$2.class", "size": 1494, "crc": -1995593822}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$3.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$3.class", "size": 1480, "crc": 1985277746}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$4.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$4.class", "size": 1999, "crc": 775249268}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$1.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$1.class", "size": 1638, "crc": 1186446439}, {"key": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$2.class", "name": "androidx/activity/ActivityViewModelLazyKt$viewModels$factoryPromise$2.class", "size": 1670, "crc": 772492225}, {"key": "androidx/activity/ActivityViewModelLazyKt.class", "name": "androidx/activity/ActivityViewModelLazyKt.class", "size": 4396, "crc": -1101569520}, {"key": "androidx/activity/Api26Impl.class", "name": "androidx/activity/Api26Impl.class", "size": 1646, "crc": 1461313774}, {"key": "androidx/activity/Api34Impl.class", "name": "androidx/activity/Api34Impl.class", "size": 1892, "crc": 598715201}, {"key": "androidx/activity/BackEventCompat$Companion.class", "name": "androidx/activity/BackEventCompat$Companion.class", "size": 892, "crc": -1792355148}, {"key": "androidx/activity/BackEventCompat$SwipeEdge.class", "name": "androidx/activity/BackEventCompat$SwipeEdge.class", "size": 1134, "crc": -662310490}, {"key": "androidx/activity/BackEventCompat.class", "name": "androidx/activity/BackEventCompat.class", "size": 3536, "crc": 19970486}, {"key": "androidx/activity/Cancellable.class", "name": "androidx/activity/Cancellable.class", "size": 421, "crc": -708940838}, {"key": "androidx/activity/ComponentActivity$4.class", "name": "androidx/activity/ComponentActivity$4.class", "size": 1643, "crc": -616331416}, {"key": "androidx/activity/ComponentActivity$Api33Impl.class", "name": "androidx/activity/ComponentActivity$Api33Impl.class", "size": 1504, "crc": -1700798868}, {"key": "androidx/activity/ComponentActivity$Companion.class", "name": "androidx/activity/ComponentActivity$Companion.class", "size": 874, "crc": -499720060}, {"key": "androidx/activity/ComponentActivity$NonConfigurationInstances.class", "name": "androidx/activity/ComponentActivity$NonConfigurationInstances.class", "size": 1556, "crc": 1505560637}, {"key": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutor.class", "name": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutor.class", "size": 870, "crc": **********}, {"key": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorImpl.class", "name": "androidx/activity/ComponentActivity$ReportFullyDrawnExecutorImpl.class", "size": 4911, "crc": -**********}, {"key": "androidx/activity/ComponentActivity$activityResultRegistry$1.class", "name": "androidx/activity/ComponentActivity$activityResultRegistry$1.class", "size": 6861, "crc": **********}, {"key": "androidx/activity/ComponentActivity$defaultViewModelProviderFactory$2.class", "name": "androidx/activity/ComponentActivity$defaultViewModelProviderFactory$2.class", "size": 1813, "crc": 917482130}, {"key": "androidx/activity/ComponentActivity$fullyDrawnReporter$2$1.class", "name": "androidx/activity/ComponentActivity$fullyDrawnReporter$2$1.class", "size": 1309, "crc": -534630695}, {"key": "androidx/activity/ComponentActivity$fullyDrawnReporter$2.class", "name": "androidx/activity/ComponentActivity$fullyDrawnReporter$2.class", "size": 1728, "crc": -182376394}, {"key": "androidx/activity/ComponentActivity$onBackPressedDispatcher$2.class", "name": "androidx/activity/ComponentActivity$onBackPressedDispatcher$2.class", "size": 3396, "crc": -**********}, {"key": "androidx/activity/ComponentActivity.class", "name": "androidx/activity/ComponentActivity.class", "size": 39292, "crc": **********}, {"key": "androidx/activity/ComponentDialog.class", "name": "androidx/activity/ComponentDialog.class", "size": 7997, "crc": -**********}, {"key": "androidx/activity/EdgeToEdge.class", "name": "androidx/activity/EdgeToEdge.class", "size": 5281, "crc": -2116559023}, {"key": "androidx/activity/EdgeToEdgeApi21.class", "name": "androidx/activity/EdgeToEdgeApi21.class", "size": 1719, "crc": 77282730}, {"key": "androidx/activity/EdgeToEdgeApi23.class", "name": "androidx/activity/EdgeToEdgeApi23.class", "size": 2154, "crc": 1399653766}, {"key": "androidx/activity/EdgeToEdgeApi26.class", "name": "androidx/activity/EdgeToEdgeApi26.class", "size": 2399, "crc": -681731985}, {"key": "androidx/activity/EdgeToEdgeApi28.class", "name": "androidx/activity/EdgeToEdgeApi28.class", "size": 1410, "crc": 1281856439}, {"key": "androidx/activity/EdgeToEdgeApi29.class", "name": "androidx/activity/EdgeToEdgeApi29.class", "size": 2634, "crc": 1434390521}, {"key": "androidx/activity/EdgeToEdgeApi30.class", "name": "androidx/activity/EdgeToEdgeApi30.class", "size": 1410, "crc": 1918656727}, {"key": "androidx/activity/EdgeToEdgeBase.class", "name": "androidx/activity/EdgeToEdgeBase.class", "size": 1571, "crc": -805513590}, {"key": "androidx/activity/EdgeToEdgeImpl.class", "name": "androidx/activity/EdgeToEdgeImpl.class", "size": 1036, "crc": 1336037494}, {"key": "androidx/activity/FullyDrawnReporter.class", "name": "androidx/activity/FullyDrawnReporter.class", "size": 6870, "crc": 2043814313}, {"key": "androidx/activity/FullyDrawnReporterKt$reportWhenComplete$1.class", "name": "androidx/activity/FullyDrawnReporterKt$reportWhenComplete$1.class", "size": 1608, "crc": -13762393}, {"key": "androidx/activity/FullyDrawnReporterKt.class", "name": "androidx/activity/FullyDrawnReporterKt.class", "size": 3113, "crc": 1411330510}, {"key": "androidx/activity/FullyDrawnReporterOwner.class", "name": "androidx/activity/FullyDrawnReporterOwner.class", "size": 670, "crc": 339222718}, {"key": "androidx/activity/ImmLeaksCleaner$Cleaner.class", "name": "androidx/activity/ImmLeaksCleaner$Cleaner.class", "size": 1616, "crc": -722502040}, {"key": "androidx/activity/ImmLeaksCleaner$Companion$cleaner$2.class", "name": "androidx/activity/ImmLeaksCleaner$Companion$cleaner$2.class", "size": 3294, "crc": 165635802}, {"key": "androidx/activity/ImmLeaksCleaner$Companion.class", "name": "androidx/activity/ImmLeaksCleaner$Companion.class", "size": 1450, "crc": 383754974}, {"key": "androidx/activity/ImmLeaksCleaner$FailedInitialization.class", "name": "androidx/activity/ImmLeaksCleaner$FailedInitialization.class", "size": 1974, "crc": 587642798}, {"key": "androidx/activity/ImmLeaksCleaner$ValidCleaner.class", "name": "androidx/activity/ImmLeaksCleaner$ValidCleaner.class", "size": 2898, "crc": 877143856}, {"key": "androidx/activity/ImmLeaksCleaner.class", "name": "androidx/activity/ImmLeaksCleaner.class", "size": 4142, "crc": -248418173}, {"key": "androidx/activity/OnBackPressedCallback.class", "name": "androidx/activity/OnBackPressedCallback.class", "size": 4631, "crc": 1780670134}, {"key": "androidx/activity/OnBackPressedDispatcher$1.class", "name": "androidx/activity/OnBackPressedDispatcher$1.class", "size": 1703, "crc": 2085668872}, {"key": "androidx/activity/OnBackPressedDispatcher$2.class", "name": "androidx/activity/OnBackPressedDispatcher$2.class", "size": 1706, "crc": 657247495}, {"key": "androidx/activity/OnBackPressedDispatcher$3.class", "name": "androidx/activity/OnBackPressedDispatcher$3.class", "size": 1242, "crc": 1979290530}, {"key": "androidx/activity/OnBackPressedDispatcher$4.class", "name": "androidx/activity/OnBackPressedDispatcher$4.class", "size": 1251, "crc": -2100397039}, {"key": "androidx/activity/OnBackPressedDispatcher$5.class", "name": "androidx/activity/OnBackPressedDispatcher$5.class", "size": 1242, "crc": 990484437}, {"key": "androidx/activity/OnBackPressedDispatcher$Api33Impl.class", "name": "androidx/activity/OnBackPressedDispatcher$Api33Impl.class", "size": 3022, "crc": 42144475}, {"key": "androidx/activity/OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$Api34Impl$createOnBackAnimationCallback$1.class", "size": 2856, "crc": 673607824}, {"key": "androidx/activity/OnBackPressedDispatcher$Api34Impl.class", "name": "androidx/activity/OnBackPressedDispatcher$Api34Impl.class", "size": 2527, "crc": -1093479073}, {"key": "androidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable.class", "name": "androidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable.class", "size": 3083, "crc": 875110405}, {"key": "androidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable.class", "name": "androidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable.class", "size": 2502, "crc": 2017509439}, {"key": "androidx/activity/OnBackPressedDispatcher$addCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$addCallback$1.class", "size": 1391, "crc": 2122871594}, {"key": "androidx/activity/OnBackPressedDispatcher$addCancellableCallback$1.class", "name": "androidx/activity/OnBackPressedDispatcher$addCancellableCallback$1.class", "size": 1436, "crc": 581549235}, {"key": "androidx/activity/OnBackPressedDispatcher.class", "name": "androidx/activity/OnBackPressedDispatcher.class", "size": 13156, "crc": -1412055980}, {"key": "androidx/activity/OnBackPressedDispatcherKt$addCallback$callback$1.class", "name": "androidx/activity/OnBackPressedDispatcherKt$addCallback$callback$1.class", "size": 1519, "crc": 18186992}, {"key": "androidx/activity/OnBackPressedDispatcherKt.class", "name": "androidx/activity/OnBackPressedDispatcherKt.class", "size": 2485, "crc": 753517581}, {"key": "androidx/activity/OnBackPressedDispatcherOwner.class", "name": "androidx/activity/OnBackPressedDispatcherOwner.class", "size": 780, "crc": -1780134358}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$2.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$2.class", "size": 1892, "crc": 1460774511}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$1.class", "size": 2639, "crc": -2036781025}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1$attachStateChangeListener$1.class", "size": 3092, "crc": -2125807985}, {"key": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1.class", "name": "androidx/activity/PipHintTrackerKt$trackPipAnimationHintView$flow$1.class", "size": 6924, "crc": 446419595}, {"key": "androidx/activity/PipHintTrackerKt.class", "name": "androidx/activity/PipHintTrackerKt.class", "size": 2696, "crc": 501837313}, {"key": "androidx/activity/SystemBarStyle$Companion$auto$1.class", "name": "androidx/activity/SystemBarStyle$Companion$auto$1.class", "size": 1894, "crc": 1443393372}, {"key": "androidx/activity/SystemBarStyle$Companion$dark$1.class", "name": "androidx/activity/SystemBarStyle$Companion$dark$1.class", "size": 1605, "crc": 2094129585}, {"key": "androidx/activity/SystemBarStyle$Companion$light$1.class", "name": "androidx/activity/SystemBarStyle$Companion$light$1.class", "size": 1609, "crc": -1341439616}, {"key": "androidx/activity/SystemBarStyle$Companion.class", "name": "androidx/activity/SystemBarStyle$Companion.class", "size": 3184, "crc": 196767732}, {"key": "androidx/activity/SystemBarStyle.class", "name": "androidx/activity/SystemBarStyle.class", "size": 3747, "crc": 508018530}, {"key": "androidx/activity/ViewTreeFullyDrawnReporterOwner.class", "name": "androidx/activity/ViewTreeFullyDrawnReporterOwner.class", "size": 2123, "crc": -1295596576}, {"key": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner.class", "name": "androidx/activity/ViewTreeOnBackPressedDispatcherOwner.class", "size": 2216, "crc": 1956485870}, {"key": "androidx/activity/contextaware/ContextAware.class", "name": "androidx/activity/contextaware/ContextAware.class", "size": 989, "crc": -731652454}, {"key": "androidx/activity/contextaware/ContextAwareHelper.class", "name": "androidx/activity/contextaware/ContextAwareHelper.class", "size": 3114, "crc": 328417146}, {"key": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$1.class", "name": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$1.class", "size": 2123, "crc": -2026235126}, {"key": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$listener$1.class", "name": "androidx/activity/contextaware/ContextAwareKt$withContextAvailable$2$listener$1.class", "size": 3147, "crc": -749833013}, {"key": "androidx/activity/contextaware/ContextAwareKt.class", "name": "androidx/activity/contextaware/ContextAwareKt.class", "size": 5104, "crc": -237395255}, {"key": "androidx/activity/contextaware/OnContextAvailableListener.class", "name": "androidx/activity/contextaware/OnContextAvailableListener.class", "size": 682, "crc": 1640788270}, {"key": "androidx/activity/result/ActivityResult$Companion$CREATOR$1.class", "name": "androidx/activity/result/ActivityResult$Companion$CREATOR$1.class", "size": 1772, "crc": 947102669}, {"key": "androidx/activity/result/ActivityResult$Companion.class", "name": "androidx/activity/result/ActivityResult$Companion.class", "size": 1498, "crc": -2100103383}, {"key": "androidx/activity/result/ActivityResult.class", "name": "androidx/activity/result/ActivityResult.class", "size": 3516, "crc": -1189187616}, {"key": "androidx/activity/result/ActivityResultCallback.class", "name": "androidx/activity/result/ActivityResultCallback.class", "size": 628, "crc": 1970847939}, {"key": "androidx/activity/result/ActivityResultCaller.class", "name": "androidx/activity/result/ActivityResultCaller.class", "size": 1976, "crc": -1122410830}, {"key": "androidx/activity/result/ActivityResultCallerKt.class", "name": "androidx/activity/result/ActivityResultCallerKt.class", "size": 4450, "crc": 2110302259}, {"key": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1.class", "name": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2$1.class", "size": 2537, "crc": 228477623}, {"key": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2.class", "name": "androidx/activity/result/ActivityResultCallerLauncher$resultContract$2.class", "size": 1752, "crc": -1496703662}, {"key": "androidx/activity/result/ActivityResultCallerLauncher.class", "name": "androidx/activity/result/ActivityResultCallerLauncher.class", "size": 4061, "crc": 1338703306}, {"key": "androidx/activity/result/ActivityResultKt.class", "name": "androidx/activity/result/ActivityResultKt.class", "size": 1252, "crc": -1287981994}, {"key": "androidx/activity/result/ActivityResultLauncher.class", "name": "androidx/activity/result/ActivityResultLauncher.class", "size": 1623, "crc": -432064838}, {"key": "androidx/activity/result/ActivityResultLauncherKt.class", "name": "androidx/activity/result/ActivityResultLauncherKt.class", "size": 2023, "crc": -1442317203}, {"key": "androidx/activity/result/ActivityResultRegistry$CallbackAndContract.class", "name": "androidx/activity/result/ActivityResultRegistry$CallbackAndContract.class", "size": 2225, "crc": -1294436231}, {"key": "androidx/activity/result/ActivityResultRegistry$Companion.class", "name": "androidx/activity/result/ActivityResultRegistry$Companion.class", "size": 1214, "crc": 335734368}, {"key": "androidx/activity/result/ActivityResultRegistry$LifecycleContainer.class", "name": "androidx/activity/result/ActivityResultRegistry$LifecycleContainer.class", "size": 3486, "crc": -409374737}, {"key": "androidx/activity/result/ActivityResultRegistry$generateRandomNumber$1.class", "name": "androidx/activity/result/ActivityResultRegistry$generateRandomNumber$1.class", "size": 1401, "crc": 1052370854}, {"key": "androidx/activity/result/ActivityResultRegistry$register$2.class", "name": "androidx/activity/result/ActivityResultRegistry$register$2.class", "size": 4120, "crc": -888443048}, {"key": "androidx/activity/result/ActivityResultRegistry$register$3.class", "name": "androidx/activity/result/ActivityResultRegistry$register$3.class", "size": 4085, "crc": -257121091}, {"key": "androidx/activity/result/ActivityResultRegistry.class", "name": "androidx/activity/result/ActivityResultRegistry.class", "size": 17262, "crc": -609764154}, {"key": "androidx/activity/result/ActivityResultRegistryOwner.class", "name": "androidx/activity/result/ActivityResultRegistryOwner.class", "size": 726, "crc": 1586312733}, {"key": "androidx/activity/result/IntentSenderRequest$Builder$Flag.class", "name": "androidx/activity/result/IntentSenderRequest$Builder$Flag.class", "size": 816, "crc": 812248084}, {"key": "androidx/activity/result/IntentSenderRequest$Builder.class", "name": "androidx/activity/result/IntentSenderRequest$Builder.class", "size": 2552, "crc": -1699660735}, {"key": "androidx/activity/result/IntentSenderRequest$Companion$CREATOR$1.class", "name": "androidx/activity/result/IntentSenderRequest$Companion$CREATOR$1.class", "size": 1814, "crc": -1381244110}, {"key": "androidx/activity/result/IntentSenderRequest$Companion.class", "name": "androidx/activity/result/IntentSenderRequest$Companion.class", "size": 1070, "crc": 2008159032}, {"key": "androidx/activity/result/IntentSenderRequest.class", "name": "androidx/activity/result/IntentSenderRequest.class", "size": 3926, "crc": 198896510}, {"key": "androidx/activity/result/PickVisualMediaRequest$Builder.class", "name": "androidx/activity/result/PickVisualMediaRequest$Builder.class", "size": 4924, "crc": -2093773998}, {"key": "androidx/activity/result/PickVisualMediaRequest.class", "name": "androidx/activity/result/PickVisualMediaRequest.class", "size": 4547, "crc": 1263648143}, {"key": "androidx/activity/result/PickVisualMediaRequestKt.class", "name": "androidx/activity/result/PickVisualMediaRequestKt.class", "size": 6405, "crc": -971441169}, {"key": "androidx/activity/result/contract/ActivityResultContract$SynchronousResult.class", "name": "androidx/activity/result/contract/ActivityResultContract$SynchronousResult.class", "size": 1068, "crc": -1511044393}, {"key": "androidx/activity/result/contract/ActivityResultContract.class", "name": "androidx/activity/result/contract/ActivityResultContract.class", "size": 2153, "crc": -1439815574}, {"key": "androidx/activity/result/contract/ActivityResultContracts$CaptureVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$CaptureVideo.class", "size": 3545, "crc": -39628678}, {"key": "androidx/activity/result/contract/ActivityResultContracts$CreateDocument.class", "name": "androidx/activity/result/contract/ActivityResultContracts$CreateDocument.class", "size": 4964, "crc": 1949743975}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetContent.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetContent.class", "size": 4248, "crc": -458150323}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents$Companion.class", "size": 3368, "crc": -605384151}, {"key": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents.class", "name": "androidx/activity/result/contract/ActivityResultContracts$GetMultipleContents.class", "size": 5121, "crc": -125922224}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenDocument.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenDocument.class", "size": 4364, "crc": 97910297}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenDocumentTree.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenDocumentTree.class", "size": 4333, "crc": -616894928}, {"key": "androidx/activity/result/contract/ActivityResultContracts$OpenMultipleDocuments.class", "name": "androidx/activity/result/contract/ActivityResultContracts$OpenMultipleDocuments.class", "size": 5200, "crc": -1373826974}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickContact.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickContact.class", "size": 3280, "crc": -1285201310}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia$Companion.class", "size": 1822, "crc": -1209537039}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickMultipleVisualMedia.class", "size": 10516, "crc": -342821984}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$Companion.class", "size": 5619, "crc": -1169786491}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$DefaultTab$AlbumsTab.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$DefaultTab$AlbumsTab.class", "size": 1413, "crc": 1566564108}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$DefaultTab$PhotosTab.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$DefaultTab$PhotosTab.class", "size": 1429, "crc": 895718302}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$DefaultTab.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$DefaultTab.class", "size": 1355, "crc": -776056361}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageAndVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageAndVideo.class", "size": 1229, "crc": 683443034}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageOnly.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$ImageOnly.class", "size": 1217, "crc": -187312014}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$SingleMimeType.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$SingleMimeType.class", "size": 1567, "crc": 1859541849}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VideoOnly.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VideoOnly.class", "size": 1217, "crc": 1322548329}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia$VisualMediaType.class", "size": 1121, "crc": -810064056}, {"key": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia.class", "name": "androidx/activity/result/contract/ActivityResultContracts$PickVisualMedia.class", "size": 10566, "crc": 1194392483}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion.class", "size": 2161, "crc": 2031546162}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions.class", "size": 8434, "crc": -2012586453}, {"key": "androidx/activity/result/contract/ActivityResultContracts$RequestPermission.class", "name": "androidx/activity/result/contract/ActivityResultContracts$RequestPermission.class", "size": 5294, "crc": 120526603}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion.class", "size": 1121, "crc": -914616939}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartActivityForResult.class", "size": 2895, "crc": 339609444}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult$Companion.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult$Companion.class", "size": 1241, "crc": -241308905}, {"key": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult.class", "name": "androidx/activity/result/contract/ActivityResultContracts$StartIntentSenderForResult.class", "size": 3593, "crc": -54491084}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakePicture.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakePicture.class", "size": 3542, "crc": -517211970}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakePicturePreview.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakePicturePreview.class", "size": 4112, "crc": 505615914}, {"key": "androidx/activity/result/contract/ActivityResultContracts$TakeVideo.class", "name": "androidx/activity/result/contract/ActivityResultContracts$TakeVideo.class", "size": 4550, "crc": -243105965}, {"key": "androidx/activity/result/contract/ActivityResultContracts.class", "name": "androidx/activity/result/contract/ActivityResultContracts.class", "size": 2507, "crc": -1296753956}, {"key": "META-INF/activity_release.kotlin_module", "name": "META-INF/activity_release.kotlin_module", "size": 393, "crc": 921419579}, {"key": "META-INF/androidx.activity_activity.version", "name": "META-INF/androidx.activity_activity.version", "size": 7, "crc": -688916209}]