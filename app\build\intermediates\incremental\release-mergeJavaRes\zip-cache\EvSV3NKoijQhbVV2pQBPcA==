[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 25, "crc": -301826126}, {"key": "androidx/lifecycle/ClassesInfoCache$CallbackInfo.class", "name": "androidx/lifecycle/ClassesInfoCache$CallbackInfo.class", "size": 3345, "crc": -313150915}, {"key": "androidx/lifecycle/ClassesInfoCache$MethodReference.class", "name": "androidx/lifecycle/ClassesInfoCache$MethodReference.class", "size": 2284, "crc": 508395143}, {"key": "androidx/lifecycle/ClassesInfoCache.class", "name": "androidx/lifecycle/ClassesInfoCache.class", "size": 7417, "crc": 1890982937}, {"key": "androidx/lifecycle/GenericLifecycleObserver.class", "name": "androidx/lifecycle/GenericLifecycleObserver.class", "size": 557, "crc": 1876517087}, {"key": "androidx/lifecycle/OnLifecycleEvent.class", "name": "androidx/lifecycle/OnLifecycleEvent.class", "size": 614, "crc": -662081732}, {"key": "androidx/lifecycle/ReflectiveGenericLifecycleObserver.class", "name": "androidx/lifecycle/ReflectiveGenericLifecycleObserver.class", "size": 1628, "crc": -661784695}, {"key": "META-INF/lifecycle-common.kotlin_module", "name": "META-INF/lifecycle-common.kotlin_module", "size": 98, "crc": -344145580}, {"key": "androidx/lifecycle/CompositeGeneratedAdaptersObserver.class", "name": "androidx/lifecycle/CompositeGeneratedAdaptersObserver.class", "size": 2192, "crc": -588589195}, {"key": "androidx/lifecycle/DefaultLifecycleObserver.class", "name": "androidx/lifecycle/DefaultLifecycleObserver.class", "size": 1561, "crc": 1138428020}, {"key": "androidx/lifecycle/DefaultLifecycleObserverAdapter$WhenMappings.class", "name": "androidx/lifecycle/DefaultLifecycleObserverAdapter$WhenMappings.class", "size": 1129, "crc": -1393967104}, {"key": "androidx/lifecycle/DefaultLifecycleObserverAdapter.class", "name": "androidx/lifecycle/DefaultLifecycleObserverAdapter.class", "size": 2529, "crc": 417874049}, {"key": "androidx/lifecycle/DispatchQueue.class", "name": "androidx/lifecycle/DispatchQueue.class", "size": 4226, "crc": -2053961371}, {"key": "androidx/lifecycle/GeneratedAdapter.class", "name": "androidx/lifecycle/GeneratedAdapter.class", "size": 1300, "crc": 24417411}, {"key": "androidx/lifecycle/Lifecycle$Event$Companion$WhenMappings.class", "name": "androidx/lifecycle/Lifecycle$Event$Companion$WhenMappings.class", "size": 1073, "crc": -1453898351}, {"key": "androidx/lifecycle/Lifecycle$Event$Companion.class", "name": "androidx/lifecycle/Lifecycle$Event$Companion.class", "size": 2619, "crc": 1493326216}, {"key": "androidx/lifecycle/Lifecycle$Event$WhenMappings.class", "name": "androidx/lifecycle/Lifecycle$Event$WhenMappings.class", "size": 1035, "crc": -1337925050}, {"key": "androidx/lifecycle/Lifecycle$Event.class", "name": "androidx/lifecycle/Lifecycle$Event.class", "size": 3618, "crc": -957783764}, {"key": "androidx/lifecycle/Lifecycle$State.class", "name": "androidx/lifecycle/Lifecycle$State.class", "size": 2085, "crc": 1312149321}, {"key": "androidx/lifecycle/Lifecycle.class", "name": "androidx/lifecycle/Lifecycle.class", "size": 2354, "crc": -1628172174}, {"key": "androidx/lifecycle/LifecycleController.class", "name": "androidx/lifecycle/LifecycleController.class", "size": 4630, "crc": 1874764903}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenCreated$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenCreated$1.class", "size": 4114, "crc": -636169170}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenResumed$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenResumed$1.class", "size": 4114, "crc": -72888868}, {"key": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenStarted$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScope$launchWhenStarted$1.class", "size": 4114, "crc": -832102748}, {"key": "androidx/lifecycle/LifecycleCoroutineScope.class", "name": "androidx/lifecycle/LifecycleCoroutineScope.class", "size": 3541, "crc": 164913769}, {"key": "androidx/lifecycle/LifecycleCoroutineScopeImpl$register$1.class", "name": "androidx/lifecycle/LifecycleCoroutineScopeImpl$register$1.class", "size": 4156, "crc": 2049593091}, {"key": "androidx/lifecycle/LifecycleCoroutineScopeImpl.class", "name": "androidx/lifecycle/LifecycleCoroutineScopeImpl.class", "size": 3622, "crc": 1147053390}, {"key": "androidx/lifecycle/LifecycleEventObserver.class", "name": "androidx/lifecycle/LifecycleEventObserver.class", "size": 968, "crc": -1471782186}, {"key": "androidx/lifecycle/LifecycleKt.class", "name": "androidx/lifecycle/LifecycleKt.class", "size": 2164, "crc": 2103077277}, {"key": "androidx/lifecycle/LifecycleObserver.class", "name": "androidx/lifecycle/LifecycleObserver.class", "size": 395, "crc": -329931382}, {"key": "androidx/lifecycle/LifecycleOwner.class", "name": "androidx/lifecycle/LifecycleOwner.class", "size": 611, "crc": -1363301969}, {"key": "androidx/lifecycle/LifecycleOwnerKt.class", "name": "androidx/lifecycle/LifecycleOwnerKt.class", "size": 1208, "crc": -317348439}, {"key": "androidx/lifecycle/Lifecycling.class", "name": "androidx/lifecycle/Lifecycling.class", "size": 9193, "crc": 1781662277}, {"key": "androidx/lifecycle/MethodCallsLogger.class", "name": "androidx/lifecycle/MethodCallsLogger.class", "size": 1991, "crc": 1355047278}, {"key": "androidx/lifecycle/PausingDispatcher.class", "name": "androidx/lifecycle/PausingDispatcher.class", "size": 2016, "crc": 462084964}, {"key": "androidx/lifecycle/PausingDispatcherKt$whenStateAtLeast$2.class", "name": "androidx/lifecycle/PausingDispatcherKt$whenStateAtLeast$2.class", "size": 5702, "crc": -1188716541}, {"key": "androidx/lifecycle/PausingDispatcherKt.class", "name": "androidx/lifecycle/PausingDispatcherKt.class", "size": 6028, "crc": -1350334020}, {"key": "androidx/lifecycle/SingleGeneratedAdapterObserver.class", "name": "androidx/lifecycle/SingleGeneratedAdapterObserver.class", "size": 1792, "crc": 1222686409}]