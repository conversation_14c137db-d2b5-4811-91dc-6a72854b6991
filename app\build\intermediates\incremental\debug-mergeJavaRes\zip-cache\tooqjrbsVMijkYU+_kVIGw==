[{"key": "META-INF/MANIFEST.MF", "name": "META-INF/MANIFEST.MF", "size": 128, "crc": 219314733}, {"key": "org/intellij/lang/annotations/Flow.class", "name": "org/intellij/lang/annotations/Flow.class", "size": 1217, "crc": -2102043213}, {"key": "org/intellij/lang/annotations/Identifier.class", "name": "org/intellij/lang/annotations/Identifier.class", "size": 324, "crc": 1003706824}, {"key": "org/intellij/lang/annotations/JdkConstants$AdjustableOrientation.class", "name": "org/intellij/lang/annotations/JdkConstants$AdjustableOrientation.class", "size": 299, "crc": *********}, {"key": "org/intellij/lang/annotations/JdkConstants$BoxLayoutAxis.class", "name": "org/intellij/lang/annotations/JdkConstants$BoxLayoutAxis.class", "size": 283, "crc": 2112307339}, {"key": "org/intellij/lang/annotations/JdkConstants$CalendarMonth.class", "name": "org/intellij/lang/annotations/JdkConstants$CalendarMonth.class", "size": 283, "crc": -1461794964}, {"key": "org/intellij/lang/annotations/JdkConstants$CursorType.class", "name": "org/intellij/lang/annotations/JdkConstants$CursorType.class", "size": 277, "crc": 1261991804}, {"key": "org/intellij/lang/annotations/JdkConstants$FlowLayoutAlignment.class", "name": "org/intellij/lang/annotations/JdkConstants$FlowLayoutAlignment.class", "size": 295, "crc": -1309531958}, {"key": "org/intellij/lang/annotations/JdkConstants$FontStyle.class", "name": "org/intellij/lang/annotations/JdkConstants$FontStyle.class", "size": 275, "crc": *********}, {"key": "org/intellij/lang/annotations/JdkConstants$HorizontalAlignment.class", "name": "org/intellij/lang/annotations/JdkConstants$HorizontalAlignment.class", "size": 295, "crc": -1845894850}, {"key": "org/intellij/lang/annotations/JdkConstants$InputEventMask.class", "name": "org/intellij/lang/annotations/JdkConstants$InputEventMask.class", "size": 285, "crc": 115820859}, {"key": "org/intellij/lang/annotations/JdkConstants$ListSelectionMode.class", "name": "org/intellij/lang/annotations/JdkConstants$ListSelectionMode.class", "size": 291, "crc": 1972304950}, {"key": "org/intellij/lang/annotations/JdkConstants$PatternFlags.class", "name": "org/intellij/lang/annotations/JdkConstants$PatternFlags.class", "size": 281, "crc": -1389407250}, {"key": "org/intellij/lang/annotations/JdkConstants$TabLayoutPolicy.class", "name": "org/intellij/lang/annotations/JdkConstants$TabLayoutPolicy.class", "size": 287, "crc": -636391766}, {"key": "org/intellij/lang/annotations/JdkConstants$TabPlacement.class", "name": "org/intellij/lang/annotations/JdkConstants$TabPlacement.class", "size": 281, "crc": -214765930}, {"key": "org/intellij/lang/annotations/JdkConstants$TitledBorderJustification.class", "name": "org/intellij/lang/annotations/JdkConstants$TitledBorderJustification.class", "size": 307, "crc": 859456766}, {"key": "org/intellij/lang/annotations/JdkConstants$TitledBorderTitlePosition.class", "name": "org/intellij/lang/annotations/JdkConstants$TitledBorderTitlePosition.class", "size": 307, "crc": 1103964778}, {"key": "org/intellij/lang/annotations/JdkConstants$TreeSelectionMode.class", "name": "org/intellij/lang/annotations/JdkConstants$TreeSelectionMode.class", "size": 291, "crc": -474777859}, {"key": "org/intellij/lang/annotations/JdkConstants.class", "name": "org/intellij/lang/annotations/JdkConstants.class", "size": 1730, "crc": 1434561639}, {"key": "org/intellij/lang/annotations/Language.class", "name": "org/intellij/lang/annotations/Language.class", "size": 681, "crc": -1817758794}, {"key": "org/intellij/lang/annotations/MagicConstant.class", "name": "org/intellij/lang/annotations/MagicConstant.class", "size": 715, "crc": -1592799795}, {"key": "org/intellij/lang/annotations/Pattern.class", "name": "org/intellij/lang/annotations/Pattern.class", "size": 600, "crc": -530891111}, {"key": "org/intellij/lang/annotations/PrintFormat.class", "name": "org/intellij/lang/annotations/PrintFormat.class", "size": 356, "crc": 1445148429}, {"key": "org/intellij/lang/annotations/PrintFormatPattern.class", "name": "org/intellij/lang/annotations/PrintFormatPattern.class", "size": 964, "crc": -1507760601}, {"key": "org/intellij/lang/annotations/RegExp.class", "name": "org/intellij/lang/annotations/RegExp.class", "size": 665, "crc": -762257026}, {"key": "org/intellij/lang/annotations/Subst.class", "name": "org/intellij/lang/annotations/Subst.class", "size": 199, "crc": -1769088951}, {"key": "org/jetbrains/annotations/Contract.class", "name": "org/jetbrains/annotations/Contract.class", "size": 531, "crc": 562269709}, {"key": "org/jetbrains/annotations/Nls.class", "name": "org/jetbrains/annotations/Nls.class", "size": 502, "crc": 776170298}, {"key": "org/jetbrains/annotations/NonNls.class", "name": "org/jetbrains/annotations/NonNls.class", "size": 508, "crc": 1172811652}, {"key": "org/jetbrains/annotations/NotNull.class", "name": "org/jetbrains/annotations/NotNull.class", "size": 546, "crc": -316212578}, {"key": "org/jetbrains/annotations/Nullable.class", "name": "org/jetbrains/annotations/Nullable.class", "size": 548, "crc": -689673279}, {"key": "org/jetbrains/annotations/PropertyKey.class", "name": "org/jetbrains/annotations/PropertyKey.class", "size": 525, "crc": 862906181}, {"key": "org/jetbrains/annotations/TestOnly.class", "name": "org/jetbrains/annotations/TestOnly.class", "size": 453, "crc": 1033452998}, {"key": "META-INF/maven/org.jetbrains/annotations/pom.xml", "name": "META-INF/maven/org.jetbrains/annotations/pom.xml", "size": 4930, "crc": -127628560}, {"key": "META-INF/maven/org.jetbrains/annotations/pom.properties", "name": "META-INF/maven/org.jetbrains/annotations/pom.properties", "size": 108, "crc": 97218998}]