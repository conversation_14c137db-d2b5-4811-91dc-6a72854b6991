[{"key": "androidx/compose/material3/ActualAndroid_androidKt.class", "name": "androidx/compose/material3/ActualAndroid_androidKt.class", "size": 3810, "crc": 1630203551}, {"key": "androidx/compose/material3/ActualJvm_jvmKt.class", "name": "androidx/compose/material3/ActualJvm_jvmKt.class", "size": 3931, "crc": 349316572}, {"key": "androidx/compose/material3/AlertDialogDefaults.class", "name": "androidx/compose/material3/AlertDialogDefaults.class", "size": 4792, "crc": -1267661811}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$1$1.class", "size": 10018, "crc": 1667837810}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$2$1.class", "size": 10296, "crc": 1768568842}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$3$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1$1$3$1.class", "size": 10111, "crc": 12774402}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$1.class", "size": 16729, "crc": 976156804}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$2.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogContent$2.class", "size": 3478, "crc": -635731110}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1$2.class", "size": 5721, "crc": 503134445}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$1$1.class", "size": 6904, "crc": 820549191}, {"key": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$2.class", "name": "androidx/compose/material3/AlertDialogKt$AlertDialogFlowRow$2.class", "size": 2089, "crc": 1653969182}, {"key": "androidx/compose/material3/AlertDialogKt.class", "name": "androidx/compose/material3/AlertDialogKt.class", "size": 15118, "crc": -398385919}, {"key": "androidx/compose/material3/AnchorAlignmentOffsetPosition$Horizontal.class", "name": "androidx/compose/material3/AnchorAlignmentOffsetPosition$Horizontal.class", "size": 4640, "crc": 977496444}, {"key": "androidx/compose/material3/AnchorAlignmentOffsetPosition$Vertical.class", "name": "androidx/compose/material3/AnchorAlignmentOffsetPosition$Vertical.class", "size": 4280, "crc": -350117031}, {"key": "androidx/compose/material3/AnchorAlignmentOffsetPosition.class", "name": "androidx/compose/material3/AnchorAlignmentOffsetPosition.class", "size": 1019, "crc": 1116652283}, {"key": "androidx/compose/material3/AnchoredDragFinishedSignal.class", "name": "androidx/compose/material3/AnchoredDragFinishedSignal.class", "size": 1794, "crc": 1184741781}, {"key": "androidx/compose/material3/AnchoredDragScope.class", "name": "androidx/compose/material3/AnchoredDragScope.class", "size": 1029, "crc": -909555911}, {"key": "androidx/compose/material3/AnchoredDraggableDefaults.class", "name": "androidx/compose/material3/AnchoredDraggableDefaults.class", "size": 1572, "crc": -1364898104}, {"key": "androidx/compose/material3/AnchoredDraggableKt$anchoredDraggable$1$1.class", "name": "androidx/compose/material3/AnchoredDraggableKt$anchoredDraggable$1$1.class", "size": 3713, "crc": 2035205018}, {"key": "androidx/compose/material3/AnchoredDraggableKt$anchoredDraggable$1.class", "name": "androidx/compose/material3/AnchoredDraggableKt$anchoredDraggable$1.class", "size": 4095, "crc": 930793185}, {"key": "androidx/compose/material3/AnchoredDraggableKt$animateTo$2$1.class", "name": "androidx/compose/material3/AnchoredDraggableKt$animateTo$2$1.class", "size": 1864, "crc": 1533868594}, {"key": "androidx/compose/material3/AnchoredDraggableKt$animateTo$2.class", "name": "androidx/compose/material3/AnchoredDraggableKt$animateTo$2.class", "size": 5137, "crc": -1610393055}, {"key": "androidx/compose/material3/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/material3/AnchoredDraggableKt$restartable$1.class", "size": 1671, "crc": -1635569170}, {"key": "androidx/compose/material3/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/material3/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4240, "crc": -1669303971}, {"key": "androidx/compose/material3/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/material3/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 2090, "crc": 697859690}, {"key": "androidx/compose/material3/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/material3/AnchoredDraggableKt$restartable$2$1.class", "size": 4529, "crc": -1259360804}, {"key": "androidx/compose/material3/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/material3/AnchoredDraggableKt$restartable$2.class", "size": 4662, "crc": 833384175}, {"key": "androidx/compose/material3/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/material3/AnchoredDraggableKt$snapTo$2.class", "size": 3885, "crc": -1144522141}, {"key": "androidx/compose/material3/AnchoredDraggableKt.class", "name": "androidx/compose/material3/AnchoredDraggableKt.class", "size": 8751, "crc": 2110647027}, {"key": "androidx/compose/material3/AnchoredDraggableState$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$1.class", "size": 1606, "crc": 825400860}, {"key": "androidx/compose/material3/AnchoredDraggableState$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$2.class", "size": 1651, "crc": 844652592}, {"key": "androidx/compose/material3/AnchoredDraggableState$Companion$Saver$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$Companion$Saver$1.class", "size": 2288, "crc": 1612086804}, {"key": "androidx/compose/material3/AnchoredDraggableState$Companion$Saver$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$Companion$Saver$2.class", "size": 3073, "crc": -199739457}, {"key": "androidx/compose/material3/AnchoredDraggableState$Companion.class", "name": "androidx/compose/material3/AnchoredDraggableState$Companion.class", "size": 3045, "crc": -835111002}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$1.class", "size": 1929, "crc": -1498967656}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$2$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$2$1.class", "size": 1753, "crc": -1523991485}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4515, "crc": 892262011}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$2.class", "size": 4479, "crc": -1327083241}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$3.class", "size": 1948, "crc": -480143523}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$4$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$4$1.class", "size": 1971, "crc": -1869175970}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4744, "crc": -1848027105}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDrag$4.class", "size": 4717, "crc": -792141620}, {"key": "androidx/compose/material3/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$anchoredDragScope$1.class", "size": 1617, "crc": 1342078}, {"key": "androidx/compose/material3/AnchoredDraggableState$closestValue$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$closestValue$2.class", "size": 2037, "crc": -1088493155}, {"key": "androidx/compose/material3/AnchoredDraggableState$draggableState$1$drag$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$draggableState$1$drag$2.class", "size": 5445, "crc": -778353578}, {"key": "androidx/compose/material3/AnchoredDraggableState$draggableState$1$dragScope$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$draggableState$1$dragScope$1.class", "size": 1936, "crc": 1821413835}, {"key": "androidx/compose/material3/AnchoredDraggableState$draggableState$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$draggableState$1.class", "size": 3826, "crc": 1239640777}, {"key": "androidx/compose/material3/AnchoredDraggableState$progress$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$progress$2.class", "size": 2253, "crc": -275666732}, {"key": "androidx/compose/material3/AnchoredDraggableState$targetValue$2.class", "name": "androidx/compose/material3/AnchoredDraggableState$targetValue$2.class", "size": 2019, "crc": 719070286}, {"key": "androidx/compose/material3/AnchoredDraggableState$trySnapTo$1.class", "name": "androidx/compose/material3/AnchoredDraggableState$trySnapTo$1.class", "size": 2431, "crc": -551280532}, {"key": "androidx/compose/material3/AnchoredDraggableState.class", "name": "androidx/compose/material3/AnchoredDraggableState.class", "size": 24731, "crc": 360944626}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1$1$1.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1$1$1.class", "size": 3199, "crc": 940781288}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1$1.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1$1.class", "size": 3518, "crc": -184850567}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$1.class", "size": 5391, "crc": 1989066616}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$2.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$2.class", "size": 4187, "crc": -1255288940}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$3.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$AlertDialog$3.class", "size": 2718, "crc": -69028938}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$BasicAlertDialog$1$1$1.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$BasicAlertDialog$1$1$1.class", "size": 1857, "crc": 404340801}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$BasicAlertDialog$1.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$BasicAlertDialog$1.class", "size": 12040, "crc": -1709415615}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt$BasicAlertDialog$2.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt$BasicAlertDialog$2.class", "size": 2733, "crc": 1057148650}, {"key": "androidx/compose/material3/AndroidAlertDialog_androidKt.class", "name": "androidx/compose/material3/AndroidAlertDialog_androidKt.class", "size": 13345, "crc": 557725722}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$1.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$1.class", "size": 4329, "crc": -**********}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$2.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$2.class", "size": 3126, "crc": -**********}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$3.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$3.class", "size": 2906, "crc": **********}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenu$popupPositionProvider$1$1.class", "size": 2551, "crc": 18078317}, {"key": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenuItem$2.class", "name": "androidx/compose/material3/AndroidMenu_androidKt$DropdownMenuItem$2.class", "size": 3779, "crc": -**********}, {"key": "androidx/compose/material3/AndroidMenu_androidKt.class", "name": "androidx/compose/material3/AndroidMenu_androidKt.class", "size": 19154, "crc": **********}, {"key": "androidx/compose/material3/AnimatedPaddingValues.class", "name": "androidx/compose/material3/AnimatedPaddingValues.class", "size": 4138, "crc": **********}, {"key": "androidx/compose/material3/AnimationResult.class", "name": "androidx/compose/material3/AnimationResult.class", "size": 1988, "crc": -**********}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$1.class", "size": 3314, "crc": -**********}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$2.class", "size": 13425, "crc": 454271248}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$3.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$3.class", "size": 3593, "crc": 927217295}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$4.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$4.class", "size": 2939, "crc": 2035281708}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1$1.class", "size": 1937, "crc": -2064191886}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$5$1.class", "size": 3771, "crc": 1347835980}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$6.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$6.class", "size": 10829, "crc": 782183221}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$7.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$7.class", "size": 3218, "crc": 197710859}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$1$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$1$1.class", "size": 1931, "crc": 1447961080}, {"key": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$2$1.class", "name": "androidx/compose/material3/AppBarKt$BottomAppBar$appBarDragModifier$2$1.class", "size": 4167, "crc": 784114385}, {"key": "androidx/compose/material3/AppBarKt$CenterAlignedTopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$CenterAlignedTopAppBar$1.class", "size": 3604, "crc": -673849543}, {"key": "androidx/compose/material3/AppBarKt$LargeTopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$LargeTopAppBar$1.class", "size": 3580, "crc": 1386975975}, {"key": "androidx/compose/material3/AppBarKt$MediumTopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$MediumTopAppBar$1.class", "size": 3583, "crc": -1670779722}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$1$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$1$1.class", "size": 2149, "crc": 1900236968}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$2.class", "size": 8145, "crc": -1535779174}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$3.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$3.class", "size": 3890, "crc": 2055151178}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$actionsRow$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$actionsRow$1.class", "size": 9721, "crc": 179845554}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$1$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$1$1.class", "size": 2017, "crc": 1185603768}, {"key": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$2$1.class", "name": "androidx/compose/material3/AppBarKt$SingleRowTopAppBar$appBarDragModifier$2$1.class", "size": 4254, "crc": 1274540180}, {"key": "androidx/compose/material3/AppBarKt$SmallTopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$SmallTopAppBar$1.class", "size": 3580, "crc": 876844429}, {"key": "androidx/compose/material3/AppBarKt$TopAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$TopAppBar$1.class", "size": 3565, "crc": 296664928}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$1$2.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$1$2.class", "size": 2091, "crc": -920424486}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1$1.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1$1.class", "size": 4668, "crc": 1779387622}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$2$1.class", "size": 7793, "crc": -310600993}, {"key": "androidx/compose/material3/AppBarKt$TopAppBarLayout$3.class", "name": "androidx/compose/material3/AppBarKt$TopAppBarLayout$3.class", "size": 4125, "crc": 749395655}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$2$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$2$1.class", "size": 2499, "crc": -329117780}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$3.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$3.class", "size": 15231, "crc": 1878905893}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$4.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$4.class", "size": 4467, "crc": -392223129}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$actionsRow$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$actionsRow$1.class", "size": 9784, "crc": 1158873571}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$1$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$1$1.class", "size": 2090, "crc": -1591119208}, {"key": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$2$1.class", "name": "androidx/compose/material3/AppBarKt$TwoRowsTopAppBar$appBarDragModifier$2$1.class", "size": 4323, "crc": -869292666}, {"key": "androidx/compose/material3/AppBarKt$rememberBottomAppBarState$1$1.class", "name": "androidx/compose/material3/AppBarKt$rememberBottomAppBarState$1$1.class", "size": 1590, "crc": 2054126616}, {"key": "androidx/compose/material3/AppBarKt$rememberTopAppBarState$1$1.class", "name": "androidx/compose/material3/AppBarKt$rememberTopAppBarState$1$1.class", "size": 1550, "crc": 1544685260}, {"key": "androidx/compose/material3/AppBarKt$settleAppBar$1.class", "name": "androidx/compose/material3/AppBarKt$settleAppBar$1.class", "size": 1743, "crc": -579562811}, {"key": "androidx/compose/material3/AppBarKt$settleAppBar$2.class", "name": "androidx/compose/material3/AppBarKt$settleAppBar$2.class", "size": 2882, "crc": -1731117982}, {"key": "androidx/compose/material3/AppBarKt$settleAppBar$3.class", "name": "androidx/compose/material3/AppBarKt$settleAppBar$3.class", "size": 2208, "crc": 323100178}, {"key": "androidx/compose/material3/AppBarKt$settleAppBarBottom$1.class", "name": "androidx/compose/material3/AppBarKt$settleAppBarBottom$1.class", "size": 1776, "crc": -223255360}, {"key": "androidx/compose/material3/AppBarKt$settleAppBarBottom$2.class", "name": "androidx/compose/material3/AppBarKt$settleAppBarBottom$2.class", "size": 2918, "crc": -1284870015}, {"key": "androidx/compose/material3/AppBarKt$settleAppBarBottom$3.class", "name": "androidx/compose/material3/AppBarKt$settleAppBarBottom$3.class", "size": 2240, "crc": 22485708}, {"key": "androidx/compose/material3/AppBarKt.class", "name": "androidx/compose/material3/AppBarKt.class", "size": 83631, "crc": -1175712782}, {"key": "androidx/compose/material3/AssistChipDefaults.class", "name": "androidx/compose/material3/AssistChipDefaults.class", "size": 13892, "crc": -107931857}, {"key": "androidx/compose/material3/BadgeDefaults.class", "name": "androidx/compose/material3/BadgeDefaults.class", "size": 2227, "crc": -218527097}, {"key": "androidx/compose/material3/BadgeKt$Badge$1$1.class", "name": "androidx/compose/material3/BadgeKt$Badge$1$1.class", "size": 3091, "crc": 1064634697}, {"key": "androidx/compose/material3/BadgeKt$Badge$2.class", "name": "androidx/compose/material3/BadgeKt$Badge$2.class", "size": 2339, "crc": 1231716292}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$2$1.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$2$1.class", "size": 2931, "crc": 1216506272}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$3$1$1.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$3$1$1.class", "size": 3966, "crc": 1966374397}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$3$1.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$3$1.class", "size": 6429, "crc": 2064320221}, {"key": "androidx/compose/material3/BadgeKt$BadgedBox$4.class", "name": "androidx/compose/material3/BadgeKt$BadgedBox$4.class", "size": 2490, "crc": 199586632}, {"key": "androidx/compose/material3/BadgeKt.class", "name": "androidx/compose/material3/BadgeKt.class", "size": 28557, "crc": -904278740}, {"key": "androidx/compose/material3/BaseDatePickerStateImpl.class", "name": "androidx/compose/material3/BaseDatePickerStateImpl.class", "size": 4662, "crc": -1092371425}, {"key": "androidx/compose/material3/BasicTooltipDefaults.class", "name": "androidx/compose/material3/BasicTooltipDefaults.class", "size": 1267, "crc": -1822665578}, {"key": "androidx/compose/material3/BasicTooltipKt.class", "name": "androidx/compose/material3/BasicTooltipKt.class", "size": 4444, "crc": -1591704714}, {"key": "androidx/compose/material3/BasicTooltipState.class", "name": "androidx/compose/material3/BasicTooltipState.class", "size": 1800, "crc": 1327068906}, {"key": "androidx/compose/material3/BasicTooltipStateImpl$show$2$1.class", "name": "androidx/compose/material3/BasicTooltipStateImpl$show$2$1.class", "size": 3554, "crc": 1768929312}, {"key": "androidx/compose/material3/BasicTooltipStateImpl$show$2.class", "name": "androidx/compose/material3/BasicTooltipStateImpl$show$2.class", "size": 4067, "crc": -1715963959}, {"key": "androidx/compose/material3/BasicTooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/material3/BasicTooltipStateImpl$show$cancellableShow$1.class", "size": 5239, "crc": -317369451}, {"key": "androidx/compose/material3/BasicTooltipStateImpl.class", "name": "androidx/compose/material3/BasicTooltipStateImpl.class", "size": 5582, "crc": 1744282811}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "size": 2201, "crc": 715453635}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "size": 3060, "crc": -434099508}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$BasicTooltipBox$3.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$BasicTooltipBox$3.class", "size": 3046, "crc": -590824088}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$1$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$1$1$1.class", "size": 3432, "crc": -1328147909}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$1$1.class", "size": 2145, "crc": -1804065518}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "size": 2172, "crc": 1483653061}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$2.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$2.class", "size": 10773, "crc": 1018176218}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$3.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$TooltipPopup$3.class", "size": 2744, "crc": -744354091}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$WrappedAnchor$2.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$WrappedAnchor$2.class", "size": 2529, "crc": 653270683}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "size": 3790, "crc": -1284410948}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$anchorSemantics$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$anchorSemantics$1$1.class", "size": 2028, "crc": 941493178}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$anchorSemantics$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$anchorSemantics$1.class", "size": 2450, "crc": 1482764206}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "size": 4226, "crc": -1012102847}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "size": 3893, "crc": 1954382992}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1$1.class", "size": 6902, "crc": 968506421}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1$1.class", "size": 4174, "crc": -1770070830}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$1.class", "size": 4081, "crc": -799788850}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "size": 3893, "crc": -418740820}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2$1$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2$1$1.class", "size": 5949, "crc": -1153330139}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2$1.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2$1.class", "size": 4127, "crc": 1905179309}, {"key": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2.class", "name": "androidx/compose/material3/BasicTooltip_androidKt$handleGestures$2.class", "size": 4081, "crc": 56687519}, {"key": "androidx/compose/material3/BasicTooltip_androidKt.class", "name": "androidx/compose/material3/BasicTooltip_androidKt.class", "size": 24451, "crc": -675904975}, {"key": "androidx/compose/material3/BottomAppBarDefaults$exitAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/BottomAppBarDefaults$exitAlwaysScrollBehavior$1.class", "size": 1489, "crc": 1543868265}, {"key": "androidx/compose/material3/BottomAppBarDefaults.class", "name": "androidx/compose/material3/BottomAppBarDefaults.class", "size": 8070, "crc": -15773892}, {"key": "androidx/compose/material3/BottomAppBarScrollBehavior.class", "name": "androidx/compose/material3/BottomAppBarScrollBehavior.class", "size": 1752, "crc": 19888659}, {"key": "androidx/compose/material3/BottomAppBarState$Companion$Saver$1.class", "name": "androidx/compose/material3/BottomAppBarState$Companion$Saver$1.class", "size": 2273, "crc": 523903306}, {"key": "androidx/compose/material3/BottomAppBarState$Companion$Saver$2.class", "name": "androidx/compose/material3/BottomAppBarState$Companion$Saver$2.class", "size": 2002, "crc": 1215576812}, {"key": "androidx/compose/material3/BottomAppBarState$Companion.class", "name": "androidx/compose/material3/BottomAppBarState$Companion.class", "size": 1938, "crc": -1051668184}, {"key": "androidx/compose/material3/BottomAppBarState.class", "name": "androidx/compose/material3/BottomAppBarState.class", "size": 1256, "crc": 1034424064}, {"key": "androidx/compose/material3/BottomAppBarStateImpl.class", "name": "androidx/compose/material3/BottomAppBarStateImpl.class", "size": 3901, "crc": 1185660812}, {"key": "androidx/compose/material3/BottomSheetDefaults$DragHandle$1$1.class", "name": "androidx/compose/material3/BottomSheetDefaults$DragHandle$1$1.class", "size": 1874, "crc": 126089034}, {"key": "androidx/compose/material3/BottomSheetDefaults$DragHandle$2.class", "name": "androidx/compose/material3/BottomSheetDefaults$DragHandle$2.class", "size": 2912, "crc": 2103174654}, {"key": "androidx/compose/material3/BottomSheetDefaults$DragHandle$3.class", "name": "androidx/compose/material3/BottomSheetDefaults$DragHandle$3.class", "size": 2241, "crc": 992370044}, {"key": "androidx/compose/material3/BottomSheetDefaults.class", "name": "androidx/compose/material3/BottomSheetDefaults.class", "size": 13197, "crc": 2101969494}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1$1$1$1.class", "size": 2811, "crc": 2096300548}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1$1$1.class", "size": 2440, "crc": -844714105}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$1.class", "size": 7018, "crc": -141941650}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$2.class", "size": 3613, "crc": -2026787731}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$3$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$3$1.class", "size": 1927, "crc": -1600242346}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$4.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffold$4.class", "size": 4865, "crc": 829954796}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$1$1.class", "size": 1776, "crc": 1855893306}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1$WhenMappings.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1$WhenMappings.class", "size": 1048, "crc": 2093289069}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$1.class", "size": 4057, "crc": -1529394978}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$bodyPlaceable$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$bodyPlaceable$1$1.class", "size": 3364, "crc": 917047580}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$bodyPlaceable$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$bodyPlaceable$1.class", "size": 3924, "crc": -879339406}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$sheetPlaceable$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$sheetPlaceable$1.class", "size": 3029, "crc": -1506683651}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$topBarPlaceable$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1$topBarPlaceable$1$1.class", "size": 2927, "crc": 2072577680}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$2$1.class", "size": 6998, "crc": -375416660}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$3.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$BottomSheetScaffoldLayout$3.class", "size": 3958, "crc": -565074929}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1$1.class", "size": 3632, "crc": 1921949082}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$1$1.class", "size": 2219, "crc": 2038078317}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$WhenMappings.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1$WhenMappings.class", "size": 940, "crc": -179989368}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$2$1.class", "size": 3377, "crc": 746854445}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$1$1.class", "size": 3807, "crc": 882572595}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$1.class", "size": 2144, "crc": -542228929}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$2$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$2$1.class", "size": 3814, "crc": 1886103789}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$2.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$2.class", "size": 2144, "crc": -260370684}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$3$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$3$1.class", "size": 3805, "crc": 2004926301}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$3.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1$1$3.class", "size": 2148, "crc": 1766922893}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3$1$1$1.class", "size": 4030, "crc": 1812128357}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$3.class", "size": 16860, "crc": 445390575}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$4.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$StandardBottomSheet$4.class", "size": 3861, "crc": -874227292}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt$rememberStandardBottomSheetState$1.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt$rememberStandardBottomSheetState$1.class", "size": 1696, "crc": 140531773}, {"key": "androidx/compose/material3/BottomSheetScaffoldKt.class", "name": "androidx/compose/material3/BottomSheetScaffoldKt.class", "size": 34337, "crc": 862188759}, {"key": "androidx/compose/material3/BottomSheetScaffoldLayoutSlot.class", "name": "androidx/compose/material3/BottomSheetScaffoldLayoutSlot.class", "size": 1610, "crc": 12548465}, {"key": "androidx/compose/material3/BottomSheetScaffoldState.class", "name": "androidx/compose/material3/BottomSheetScaffoldState.class", "size": 1559, "crc": -1319695882}, {"key": "androidx/compose/material3/ButtonColors.class", "name": "androidx/compose/material3/ButtonColors.class", "size": 5676, "crc": -209889274}, {"key": "androidx/compose/material3/ButtonDefaults.class", "name": "androidx/compose/material3/ButtonDefaults.class", "size": 22034, "crc": -1674126929}, {"key": "androidx/compose/material3/ButtonElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/ButtonElevation$animateElevation$1$1$1.class", "size": 3691, "crc": 806119748}, {"key": "androidx/compose/material3/ButtonElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/ButtonElevation$animateElevation$1$1.class", "size": 4445, "crc": -1744786750}, {"key": "androidx/compose/material3/ButtonElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/ButtonElevation$animateElevation$2$1.class", "size": 6150, "crc": -554835139}, {"key": "androidx/compose/material3/ButtonElevation.class", "name": "androidx/compose/material3/ButtonElevation.class", "size": 10359, "crc": 1547015157}, {"key": "androidx/compose/material3/ButtonKt$Button$2.class", "name": "androidx/compose/material3/ButtonKt$Button$2.class", "size": 2278, "crc": 773467374}, {"key": "androidx/compose/material3/ButtonKt$Button$3$1.class", "name": "androidx/compose/material3/ButtonKt$Button$3$1.class", "size": 10136, "crc": 846227489}, {"key": "androidx/compose/material3/ButtonKt$Button$3.class", "name": "androidx/compose/material3/ButtonKt$Button$3.class", "size": 4226, "crc": 1609725370}, {"key": "androidx/compose/material3/ButtonKt$Button$4.class", "name": "androidx/compose/material3/ButtonKt$Button$4.class", "size": 3969, "crc": 46337473}, {"key": "androidx/compose/material3/ButtonKt$ElevatedButton$2.class", "name": "androidx/compose/material3/ButtonKt$ElevatedButton$2.class", "size": 3993, "crc": 1568897652}, {"key": "androidx/compose/material3/ButtonKt$FilledTonalButton$2.class", "name": "androidx/compose/material3/ButtonKt$FilledTonalButton$2.class", "size": 4002, "crc": -1990498750}, {"key": "androidx/compose/material3/ButtonKt$OutlinedButton$2.class", "name": "androidx/compose/material3/ButtonKt$OutlinedButton$2.class", "size": 3993, "crc": 21408678}, {"key": "androidx/compose/material3/ButtonKt$TextButton$2.class", "name": "androidx/compose/material3/ButtonKt$TextButton$2.class", "size": 3981, "crc": -1997465150}, {"key": "androidx/compose/material3/ButtonKt.class", "name": "androidx/compose/material3/ButtonKt.class", "size": 22154, "crc": -1030512486}, {"key": "androidx/compose/material3/CalendarDate.class", "name": "androidx/compose/material3/CalendarDate.class", "size": 4396, "crc": 1563925946}, {"key": "androidx/compose/material3/CalendarModel.class", "name": "androidx/compose/material3/CalendarModel.class", "size": 5745, "crc": 1496187088}, {"key": "androidx/compose/material3/CalendarModelImpl$Companion.class", "name": "androidx/compose/material3/CalendarModelImpl$Companion.class", "size": 4815, "crc": 1073491957}, {"key": "androidx/compose/material3/CalendarModelImpl.class", "name": "androidx/compose/material3/CalendarModelImpl.class", "size": 10865, "crc": 489250664}, {"key": "androidx/compose/material3/CalendarModelKt.class", "name": "androidx/compose/material3/CalendarModelKt.class", "size": 2828, "crc": -87480919}, {"key": "androidx/compose/material3/CalendarModel_androidKt.class", "name": "androidx/compose/material3/CalendarModel_androidKt.class", "size": 3909, "crc": 2099426642}, {"key": "androidx/compose/material3/CalendarMonth.class", "name": "androidx/compose/material3/CalendarMonth.class", "size": 4775, "crc": -1558432120}, {"key": "androidx/compose/material3/CardColors.class", "name": "androidx/compose/material3/CardColors.class", "size": 5586, "crc": 1581235219}, {"key": "androidx/compose/material3/CardDefaults.class", "name": "androidx/compose/material3/CardDefaults.class", "size": 15899, "crc": -662713014}, {"key": "androidx/compose/material3/CardElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/CardElevation$animateElevation$1$1$1.class", "size": 4174, "crc": -368889895}, {"key": "androidx/compose/material3/CardElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/CardElevation$animateElevation$1$1.class", "size": 4431, "crc": 1556133216}, {"key": "androidx/compose/material3/CardElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/CardElevation$animateElevation$2$1.class", "size": 6352, "crc": -961265975}, {"key": "androidx/compose/material3/CardElevation.class", "name": "androidx/compose/material3/CardElevation.class", "size": 11272, "crc": -1833990041}, {"key": "androidx/compose/material3/CardKt$Card$1.class", "name": "androidx/compose/material3/CardKt$Card$1.class", "size": 9362, "crc": -1874740765}, {"key": "androidx/compose/material3/CardKt$Card$2.class", "name": "androidx/compose/material3/CardKt$Card$2.class", "size": 3051, "crc": 257619147}, {"key": "androidx/compose/material3/CardKt$Card$4.class", "name": "androidx/compose/material3/CardKt$Card$4.class", "size": 9481, "crc": 364237736}, {"key": "androidx/compose/material3/CardKt$Card$5.class", "name": "androidx/compose/material3/CardKt$Card$5.class", "size": 3686, "crc": 158707847}, {"key": "androidx/compose/material3/CardKt$ElevatedCard$1.class", "name": "androidx/compose/material3/CardKt$ElevatedCard$1.class", "size": 2856, "crc": -1797534677}, {"key": "androidx/compose/material3/CardKt$ElevatedCard$3.class", "name": "androidx/compose/material3/CardKt$ElevatedCard$3.class", "size": 3491, "crc": 333635976}, {"key": "androidx/compose/material3/CardKt$OutlinedCard$1.class", "name": "androidx/compose/material3/CardKt$OutlinedCard$1.class", "size": 3075, "crc": 603259995}, {"key": "androidx/compose/material3/CardKt$OutlinedCard$3.class", "name": "androidx/compose/material3/CardKt$OutlinedCard$3.class", "size": 3710, "crc": -1453946943}, {"key": "androidx/compose/material3/CardKt.class", "name": "androidx/compose/material3/CardKt.class", "size": 21793, "crc": 1495447246}, {"key": "androidx/compose/material3/CaretProperties.class", "name": "androidx/compose/material3/CaretProperties.class", "size": 3140, "crc": -983197813}, {"key": "androidx/compose/material3/CaretScope.class", "name": "androidx/compose/material3/CaretScope.class", "size": 1285, "crc": -39185361}, {"key": "androidx/compose/material3/CheckDrawingCache.class", "name": "androidx/compose/material3/CheckDrawingCache.class", "size": 2134, "crc": -784435974}, {"key": "androidx/compose/material3/CheckboxColors$WhenMappings.class", "name": "androidx/compose/material3/CheckboxColors$WhenMappings.class", "size": 858, "crc": -1359544710}, {"key": "androidx/compose/material3/CheckboxColors.class", "name": "androidx/compose/material3/CheckboxColors.class", "size": 15668, "crc": -114370079}, {"key": "androidx/compose/material3/CheckboxDefaults.class", "name": "androidx/compose/material3/CheckboxDefaults.class", "size": 5293, "crc": -935212021}, {"key": "androidx/compose/material3/CheckboxKt$Checkbox$2$1.class", "name": "androidx/compose/material3/CheckboxKt$Checkbox$2$1.class", "size": 1782, "crc": 155259987}, {"key": "androidx/compose/material3/CheckboxKt$Checkbox$3.class", "name": "androidx/compose/material3/CheckboxKt$Checkbox$3.class", "size": 2734, "crc": 2107427053}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$1$1.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$1$1.class", "size": 3513, "crc": 900127084}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$2.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$2.class", "size": 2132, "crc": 1668283404}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$1.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkCenterGravitationShiftFraction$1.class", "size": 3840, "crc": -81504954}, {"key": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkDrawFraction$1.class", "name": "androidx/compose/material3/CheckboxKt$CheckboxImpl$checkDrawFraction$1.class", "size": 3830, "crc": -1761700946}, {"key": "androidx/compose/material3/CheckboxKt$TriStateCheckbox$2.class", "name": "androidx/compose/material3/CheckboxKt$TriStateCheckbox$2.class", "size": 2881, "crc": 1837957417}, {"key": "androidx/compose/material3/CheckboxKt$WhenMappings.class", "name": "androidx/compose/material3/CheckboxKt$WhenMappings.class", "size": 850, "crc": -1502526014}, {"key": "androidx/compose/material3/CheckboxKt.class", "name": "androidx/compose/material3/CheckboxKt.class", "size": 26710, "crc": -1849055179}, {"key": "androidx/compose/material3/ChipBorder.class", "name": "androidx/compose/material3/ChipBorder.class", "size": 3835, "crc": -213161707}, {"key": "androidx/compose/material3/ChipColors.class", "name": "androidx/compose/material3/ChipColors.class", "size": 8935, "crc": 1896065354}, {"key": "androidx/compose/material3/ChipElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/ChipElevation$animateElevation$1$1$1.class", "size": 4174, "crc": -56171932}, {"key": "androidx/compose/material3/ChipElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/ChipElevation$animateElevation$1$1.class", "size": 4431, "crc": 1764601570}, {"key": "androidx/compose/material3/ChipElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/ChipElevation$animateElevation$2$1.class", "size": 5433, "crc": 154318144}, {"key": "androidx/compose/material3/ChipElevation.class", "name": "androidx/compose/material3/ChipElevation.class", "size": 12816, "crc": -201702441}, {"key": "androidx/compose/material3/ChipKt$AssistChip$2.class", "name": "androidx/compose/material3/ChipKt$AssistChip$2.class", "size": 4119, "crc": -899260724}, {"key": "androidx/compose/material3/ChipKt$AssistChip$4.class", "name": "androidx/compose/material3/ChipKt$AssistChip$4.class", "size": 4107, "crc": 957492454}, {"key": "androidx/compose/material3/ChipKt$Chip$1.class", "name": "androidx/compose/material3/ChipKt$Chip$1.class", "size": 2370, "crc": 190806864}, {"key": "androidx/compose/material3/ChipKt$Chip$2.class", "name": "androidx/compose/material3/ChipKt$Chip$2.class", "size": 4638, "crc": 1674082496}, {"key": "androidx/compose/material3/ChipKt$Chip$3.class", "name": "androidx/compose/material3/ChipKt$Chip$3.class", "size": 4661, "crc": 697547913}, {"key": "androidx/compose/material3/ChipKt$ChipContent$1$1$1.class", "name": "androidx/compose/material3/ChipKt$ChipContent$1$1$1.class", "size": 2895, "crc": 1951119223}, {"key": "androidx/compose/material3/ChipKt$ChipContent$1$1.class", "name": "androidx/compose/material3/ChipKt$ChipContent$1$1.class", "size": 6563, "crc": 1455083320}, {"key": "androidx/compose/material3/ChipKt$ChipContent$1.class", "name": "androidx/compose/material3/ChipKt$ChipContent$1.class", "size": 18743, "crc": 142863256}, {"key": "androidx/compose/material3/ChipKt$ChipContent$2.class", "name": "androidx/compose/material3/ChipKt$ChipContent$2.class", "size": 3328, "crc": 2065876200}, {"key": "androidx/compose/material3/ChipKt$ElevatedAssistChip$2.class", "name": "androidx/compose/material3/ChipKt$ElevatedAssistChip$2.class", "size": 4143, "crc": -193951038}, {"key": "androidx/compose/material3/ChipKt$ElevatedAssistChip$4.class", "name": "androidx/compose/material3/ChipKt$ElevatedAssistChip$4.class", "size": 4131, "crc": 1693288155}, {"key": "androidx/compose/material3/ChipKt$ElevatedFilterChip$2.class", "name": "androidx/compose/material3/ChipKt$ElevatedFilterChip$2.class", "size": 4276, "crc": -1342892993}, {"key": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$2.class", "name": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$2.class", "size": 3862, "crc": 1948241695}, {"key": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$4.class", "name": "androidx/compose/material3/ChipKt$ElevatedSuggestionChip$4.class", "size": 3850, "crc": -876758890}, {"key": "androidx/compose/material3/ChipKt$FilterChip$2.class", "name": "androidx/compose/material3/ChipKt$FilterChip$2.class", "size": 4252, "crc": 294796548}, {"key": "androidx/compose/material3/ChipKt$InputChip$2$1$1.class", "name": "androidx/compose/material3/ChipKt$InputChip$2$1$1.class", "size": 1787, "crc": 581065805}, {"key": "androidx/compose/material3/ChipKt$InputChip$2.class", "name": "androidx/compose/material3/ChipKt$InputChip$2.class", "size": 10853, "crc": 2106471075}, {"key": "androidx/compose/material3/ChipKt$InputChip$3.class", "name": "androidx/compose/material3/ChipKt$InputChip$3.class", "size": 4472, "crc": -13374193}, {"key": "androidx/compose/material3/ChipKt$SelectableChip$1.class", "name": "androidx/compose/material3/ChipKt$SelectableChip$1.class", "size": 2454, "crc": 1564518362}, {"key": "androidx/compose/material3/ChipKt$SelectableChip$2.class", "name": "androidx/compose/material3/ChipKt$SelectableChip$2.class", "size": 5043, "crc": -1843235609}, {"key": "androidx/compose/material3/ChipKt$SelectableChip$3.class", "name": "androidx/compose/material3/ChipKt$SelectableChip$3.class", "size": 4998, "crc": -211274663}, {"key": "androidx/compose/material3/ChipKt$SuggestionChip$2.class", "name": "androidx/compose/material3/ChipKt$SuggestionChip$2.class", "size": 3838, "crc": -1941206310}, {"key": "androidx/compose/material3/ChipKt$SuggestionChip$4.class", "name": "androidx/compose/material3/ChipKt$SuggestionChip$4.class", "size": 3826, "crc": 1479557314}, {"key": "androidx/compose/material3/ChipKt.class", "name": "androidx/compose/material3/ChipKt.class", "size": 73689, "crc": 904238338}, {"key": "androidx/compose/material3/ClockDialModifier.class", "name": "androidx/compose/material3/ClockDialModifier.class", "size": 4242, "crc": -98834426}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1$1.class", "size": 4457, "crc": 313534941}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$1.class", "size": 1875, "crc": -952583549}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2$1.class", "size": 4627, "crc": -887903136}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1$2.class", "size": 2918, "crc": 1057003971}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputDragNode$1.class", "size": 4242, "crc": -1674136351}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$1.class", "size": 3620, "crc": -477830887}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2$1.class", "size": 4160, "crc": 117824600}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1$2.class", "size": 2104, "crc": 213602570}, {"key": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1.class", "name": "androidx/compose/material3/ClockDialNode$pointerInputTapNode$1.class", "size": 4273, "crc": 1417473320}, {"key": "androidx/compose/material3/ClockDialNode.class", "name": "androidx/compose/material3/ClockDialNode.class", "size": 5858, "crc": -1487994199}, {"key": "androidx/compose/material3/ColorResourceHelper.class", "name": "androidx/compose/material3/ColorResourceHelper.class", "size": 1665, "crc": 1758668019}, {"key": "androidx/compose/material3/ColorScheme.class", "name": "androidx/compose/material3/ColorScheme.class", "size": 33216, "crc": 1472170229}, {"key": "androidx/compose/material3/ColorSchemeKt$LocalColorScheme$1.class", "name": "androidx/compose/material3/ColorSchemeKt$LocalColorScheme$1.class", "size": 1430, "crc": 1702330990}, {"key": "androidx/compose/material3/ColorSchemeKt$LocalTonalElevationEnabled$1.class", "name": "androidx/compose/material3/ColorSchemeKt$LocalTonalElevationEnabled$1.class", "size": 1257, "crc": -2126073122}, {"key": "androidx/compose/material3/ColorSchemeKt$WhenMappings.class", "name": "androidx/compose/material3/ColorSchemeKt$WhenMappings.class", "size": 2707, "crc": -1519621025}, {"key": "androidx/compose/material3/ColorSchemeKt.class", "name": "androidx/compose/material3/ColorSchemeKt.class", "size": 20853, "crc": -1936714319}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-1$1.class", "size": 2179, "crc": -416314326}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-10$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-10$1.class", "size": 2486, "crc": -379658856}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-11$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-11$1.class", "size": 2283, "crc": -582759982}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-12$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-12$1.class", "size": 2283, "crc": -1280769327}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-2$1.class", "size": 2483, "crc": -855158857}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-3$1.class", "size": 2179, "crc": 1818957549}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-4$1.class", "size": 2483, "crc": 1681615016}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-5$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-5$1.class", "size": 2179, "crc": -1904899395}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-6$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-6$1.class", "size": 2483, "crc": 1556053898}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-7$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-7$1.class", "size": 2179, "crc": -900582656}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-8$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-8$1.class", "size": 2483, "crc": -470649596}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-9$1.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt$lambda-9$1.class", "size": 2179, "crc": -1999855523}, {"key": "androidx/compose/material3/ComposableSingletons$AppBarKt.class", "name": "androidx/compose/material3/ComposableSingletons$AppBarKt.class", "size": 5599, "crc": -1652434557}, {"key": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-1$1.class", "size": 2658, "crc": -1873859251}, {"key": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt$lambda-2$1.class", "size": 3063, "crc": 1246333372}, {"key": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt.class", "name": "androidx/compose/material3/ComposableSingletons$BottomSheetScaffoldKt.class", "size": 2320, "crc": 1869367379}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-1$1.class", "size": 4255, "crc": -59734713}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-2$1.class", "size": 4271, "crc": 1165260143}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-3$1.class", "size": 4418, "crc": -1623332005}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt$lambda-4$1.class", "size": 4412, "crc": -2027647740}, {"key": "androidx/compose/material3/ComposableSingletons$DatePickerKt.class", "name": "androidx/compose/material3/ComposableSingletons$DatePickerKt.class", "size": 2535, "crc": 1053588756}, {"key": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt$lambda-1$1.class", "size": 2839, "crc": 1809492327}, {"key": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt.class", "name": "androidx/compose/material3/ComposableSingletons$DateRangePickerKt.class", "size": 1528, "crc": 1377872080}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-1$1.class", "size": 2193, "crc": -885158696}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-2$1.class", "size": 2193, "crc": 301522688}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-3$1.class", "size": 2193, "crc": 1723170916}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt$lambda-4$1.class", "size": 2193, "crc": 1500136878}, {"key": "androidx/compose/material3/ComposableSingletons$ListItemKt.class", "name": "androidx/compose/material3/ComposableSingletons$ListItemKt.class", "size": 2513, "crc": -253472010}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-1$1.class", "size": 2693, "crc": -1899839198}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt$lambda-2$1.class", "size": 2305, "crc": -376123003}, {"key": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt.class", "name": "androidx/compose/material3/ComposableSingletons$ModalBottomSheet_androidKt.class", "size": 1945, "crc": 1707153957}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-1$1.class", "size": 2192, "crc": -1600917166}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-2$1.class", "size": 2192, "crc": 458602166}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-3$1.class", "size": 2192, "crc": -435099057}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt$lambda-4$1.class", "size": 2192, "crc": -470871808}, {"key": "androidx/compose/material3/ComposableSingletons$ScaffoldKt.class", "name": "androidx/compose/material3/ComposableSingletons$ScaffoldKt.class", "size": 2513, "crc": -1725444869}, {"key": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt$lambda-1$1.class", "size": 2356, "crc": 1691898775}, {"key": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt.class", "name": "androidx/compose/material3/ComposableSingletons$SearchBar_androidKt.class", "size": 1538, "crc": -1803123137}, {"key": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt$lambda-1$1.class", "size": 2507, "crc": 1408222257}, {"key": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt.class", "name": "androidx/compose/material3/ComposableSingletons$SegmentedButtonKt.class", "size": 1528, "crc": 570449482}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt$lambda-1$1.class", "size": 3006, "crc": 41860376}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarHostKt.class", "size": 1595, "crc": -439670390}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarKt$lambda-1$1.class", "size": 4182, "crc": 938963150}, {"key": "androidx/compose/material3/ComposableSingletons$SnackbarKt.class", "name": "androidx/compose/material3/ComposableSingletons$SnackbarKt.class", "size": 1493, "crc": -1278180028}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-1$1.class", "size": 2459, "crc": -2033125112}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-2$1.class", "size": 2460, "crc": 318631135}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-3$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-3$1.class", "size": 2460, "crc": -416206170}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-4$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-4$1.class", "size": 2460, "crc": -1348650545}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-5$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-5$1.class", "size": 2460, "crc": -1205623685}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-6$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-6$1.class", "size": 2460, "crc": 1394027705}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-7$1.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt$lambda-7$1.class", "size": 2460, "crc": -873600772}, {"key": "androidx/compose/material3/ComposableSingletons$TabRowKt.class", "name": "androidx/compose/material3/ComposableSingletons$TabRowKt.class", "size": 3499, "crc": 1292952218}, {"key": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-1$1.class", "name": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-1$1.class", "size": 4427, "crc": -1487896720}, {"key": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-2$1.class", "name": "androidx/compose/material3/ComposableSingletons$TimePickerKt$lambda-2$1.class", "size": 4427, "crc": -1500287823}, {"key": "androidx/compose/material3/ComposableSingletons$TimePickerKt.class", "name": "androidx/compose/material3/ComposableSingletons$TimePickerKt.class", "size": 1937, "crc": -2049221111}, {"key": "androidx/compose/material3/ContentColorKt$LocalContentColor$1.class", "name": "androidx/compose/material3/ContentColorKt$LocalContentColor$1.class", "size": 1413, "crc": 969833585}, {"key": "androidx/compose/material3/ContentColorKt.class", "name": "androidx/compose/material3/ContentColorKt.class", "size": 1498, "crc": 797278195}, {"key": "androidx/compose/material3/DateInputFormat.class", "name": "androidx/compose/material3/DateInputFormat.class", "size": 3260, "crc": -1809910099}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$2$1$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$2$1$1.class", "size": 2037, "crc": -1766775680}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$2.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$2.class", "size": 5308, "crc": 1602085828}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$3$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$3$1.class", "size": 1604, "crc": -2094302093}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$3.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$3.class", "size": 3579, "crc": -518519955}, {"key": "androidx/compose/material3/DateInputKt$DateInputContent$4.class", "name": "androidx/compose/material3/DateInputKt$DateInputContent$4.class", "size": 3099, "crc": -798597849}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$1$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$1$1.class", "size": 6131, "crc": -2024417275}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$2$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$2$1.class", "size": 2581, "crc": -2021099883}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$3.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$3.class", "size": 3596, "crc": 732989426}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$4.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$4.class", "size": 3965, "crc": -335623897}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$errorText$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$errorText$1.class", "size": 2000, "crc": -728265462}, {"key": "androidx/compose/material3/DateInputKt$DateInputTextField$text$2$1.class", "name": "androidx/compose/material3/DateInputKt$DateInputTextField$text$2$1.class", "size": 3306, "crc": 1491934549}, {"key": "androidx/compose/material3/DateInputKt.class", "name": "androidx/compose/material3/DateInputKt.class", "size": 24979, "crc": -366890008}, {"key": "androidx/compose/material3/DateInputValidator.class", "name": "androidx/compose/material3/DateInputValidator.class", "size": 6380, "crc": 1322862804}, {"key": "androidx/compose/material3/DatePickerColors$copy$25.class", "name": "androidx/compose/material3/DatePickerColors$copy$25.class", "size": 1479, "crc": -754606157}, {"key": "androidx/compose/material3/DatePickerColors.class", "name": "androidx/compose/material3/DatePickerColors.class", "size": 29034, "crc": -1359892975}, {"key": "androidx/compose/material3/DatePickerDefaults$AllDates$1.class", "name": "androidx/compose/material3/DatePickerDefaults$AllDates$1.class", "size": 755, "crc": -53094809}, {"key": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$1$1.class", "name": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$1$1.class", "size": 2267, "crc": 1059305526}, {"key": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$2.class", "name": "androidx/compose/material3/DatePickerDefaults$DatePickerHeadline$2.class", "size": 2311, "crc": 1251414709}, {"key": "androidx/compose/material3/DatePickerDefaults$DatePickerTitle$1.class", "name": "androidx/compose/material3/DatePickerDefaults$DatePickerTitle$1.class", "size": 1989, "crc": -51973137}, {"key": "androidx/compose/material3/DatePickerDefaults.class", "name": "androidx/compose/material3/DatePickerDefaults.class", "size": 25708, "crc": 2135514365}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$1$1$1.class", "size": 3352, "crc": -277998796}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1$1$1$1.class", "size": 3683, "crc": 1342475643}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1$1.class", "size": 15152, "crc": 469557275}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$1.class", "size": 5493, "crc": -1943113006}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$2.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt$DatePickerDialog$2.class", "size": 3917, "crc": -1666808552}, {"key": "androidx/compose/material3/DatePickerDialog_androidKt.class", "name": "androidx/compose/material3/DatePickerDialog_androidKt.class", "size": 9681, "crc": -1686349344}, {"key": "androidx/compose/material3/DatePickerFormatter.class", "name": "androidx/compose/material3/DatePickerFormatter.class", "size": 1604, "crc": -1211932436}, {"key": "androidx/compose/material3/DatePickerFormatterImpl.class", "name": "androidx/compose/material3/DatePickerFormatterImpl.class", "size": 3517, "crc": 742682043}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$1.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$1.class", "size": 1994, "crc": 1232898108}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1$1$1$1.class", "size": 9625, "crc": 193840434}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$2$1.class", "size": 15376, "crc": -1922218924}, {"key": "androidx/compose/material3/DatePickerKt$DateEntryContainer$3.class", "name": "androidx/compose/material3/DatePickerKt$DateEntryContainer$3.class", "size": 3314, "crc": 1798631413}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$2.class", "size": 3292, "crc": 71392161}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$3.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$3.class", "size": 3587, "crc": 490967428}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$4$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$4$1$1.class", "size": 1594, "crc": -366162014}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$4.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$4.class", "size": 5037, "crc": 95417255}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$5$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$5$1$1.class", "size": 1584, "crc": 531572209}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$5$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$5$2$1.class", "size": 1495, "crc": -695914138}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$5.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$5.class", "size": 6194, "crc": -1335597068}, {"key": "androidx/compose/material3/DatePickerKt$DatePicker$6.class", "name": "androidx/compose/material3/DatePickerKt$DatePicker$6.class", "size": 3147, "crc": -969681793}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1$1.class", "size": 3784, "crc": -909079524}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$1$1.class", "size": 2147, "crc": 456794825}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1$1.class", "size": 3784, "crc": 1412444598}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$2$1.class", "size": 2147, "crc": -1676495062}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$3$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$3$1.class", "size": 1961, "crc": 1060746298}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$1$1.class", "size": 1853, "crc": -448274032}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1$1.class", "size": 4258, "crc": -604310492}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2$2$1$1.class", "size": 3201, "crc": 1863909600}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$1$4$2.class", "size": 16674, "crc": -1563631903}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$2.class", "size": 3408, "crc": -253670263}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerContent$yearPickerVisible$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerContent$yearPickerVisible$2.class", "size": 2022, "crc": -1810743801}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerHeader$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerHeader$1$1.class", "size": 9213, "crc": -847752003}, {"key": "androidx/compose/material3/DatePickerKt$DatePickerHeader$2.class", "name": "androidx/compose/material3/DatePickerKt$DatePickerHeader$2.class", "size": 2538, "crc": -61452976}, {"key": "androidx/compose/material3/DatePickerKt$Day$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Day$1$1.class", "size": 2399, "crc": -2055978128}, {"key": "androidx/compose/material3/DatePickerKt$Day$2.class", "name": "androidx/compose/material3/DatePickerKt$Day$2.class", "size": 9583, "crc": 49431452}, {"key": "androidx/compose/material3/DatePickerKt$Day$3.class", "name": "androidx/compose/material3/DatePickerKt$Day$3.class", "size": 2980, "crc": -318193817}, {"key": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$1$1.class", "name": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$1$1.class", "size": 1895, "crc": -1855161770}, {"key": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$2$1.class", "name": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$2$1.class", "size": 1896, "crc": 204599319}, {"key": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$3.class", "name": "androidx/compose/material3/DatePickerKt$DisplayModeToggleButton$3.class", "size": 2187, "crc": 2399924}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$1.class", "size": 1418, "crc": -957389726}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$2.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1$2.class", "size": 1418, "crc": -1722629543}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$1.class", "size": 2384, "crc": -2074869882}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1$1.class", "size": 12423, "crc": 984461143}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1$2$1.class", "size": 4091, "crc": 120635416}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$1.class", "size": 8900, "crc": -1932167224}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$2$1.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$2$1.class", "size": 4617, "crc": 554827617}, {"key": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$3.class", "name": "androidx/compose/material3/DatePickerKt$HorizontalMonthsList$3.class", "size": 3600, "crc": -1636124753}, {"key": "androidx/compose/material3/DatePickerKt$Month$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Month$1$1$1$1.class", "size": 1854, "crc": -784744073}, {"key": "androidx/compose/material3/DatePickerKt$Month$1$1$3$1.class", "name": "androidx/compose/material3/DatePickerKt$Month$1$1$3$1.class", "size": 1587, "crc": 1884377816}, {"key": "androidx/compose/material3/DatePickerKt$Month$1$1$3.class", "name": "androidx/compose/material3/DatePickerKt$Month$1$1$3.class", "size": 4010, "crc": -56307809}, {"key": "androidx/compose/material3/DatePickerKt$Month$2.class", "name": "androidx/compose/material3/DatePickerKt$Month$2.class", "size": 3319, "crc": -76682774}, {"key": "androidx/compose/material3/DatePickerKt$Month$rangeSelectionDrawModifier$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Month$rangeSelectionDrawModifier$1$1.class", "size": 2487, "crc": -1392804760}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1$1$1.class", "size": 2232, "crc": -1709054420}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1$1.class", "size": 5025, "crc": 1851671273}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$1$1.class", "size": 11443, "crc": -1866063381}, {"key": "androidx/compose/material3/DatePickerKt$MonthsNavigation$2.class", "name": "androidx/compose/material3/DatePickerKt$MonthsNavigation$2.class", "size": 2958, "crc": -1941721654}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$1.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$1.class", "size": 2063, "crc": -344181358}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$1.class", "size": 1553, "crc": 938838192}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$2.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$2.class", "size": 1521, "crc": 1424579964}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$3.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$3.class", "size": 1521, "crc": -2049253175}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$4.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$4.class", "size": 1557, "crc": 975881383}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$5.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1$5.class", "size": 2526, "crc": -1830255541}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$2$1.class", "size": 5670, "crc": 920968633}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$3.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$3.class", "size": 5914, "crc": 291119674}, {"key": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$4.class", "name": "androidx/compose/material3/DatePickerKt$SwitchableDateEntryContent$4.class", "size": 3516, "crc": -2128835521}, {"key": "androidx/compose/material3/DatePickerKt$WeekDays$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$WeekDays$1$1$1$1.class", "size": 2059, "crc": -277293776}, {"key": "androidx/compose/material3/DatePickerKt$WeekDays$2.class", "name": "androidx/compose/material3/DatePickerKt$WeekDays$2.class", "size": 1914, "crc": -164320493}, {"key": "androidx/compose/material3/DatePickerKt$Year$1$1.class", "name": "androidx/compose/material3/DatePickerKt$Year$1$1.class", "size": 2400, "crc": 1115153068}, {"key": "androidx/compose/material3/DatePickerKt$Year$2.class", "name": "androidx/compose/material3/DatePickerKt$Year$2.class", "size": 9351, "crc": -1950767842}, {"key": "androidx/compose/material3/DatePickerKt$Year$3.class", "name": "androidx/compose/material3/DatePickerKt$Year$3.class", "size": 2879, "crc": -1927081020}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$1.class", "size": 1378, "crc": 320906311}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$2.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$1$2.class", "size": 1378, "crc": -1810292960}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$1.class", "size": 2312, "crc": -1145580621}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$1$1.class", "size": 3365, "crc": 294700080}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$2$1.class", "size": 1800, "crc": -1988654148}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3$1.class", "size": 1822, "crc": -190558828}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1$3.class", "size": 3906, "crc": 2100027166}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1$1.class", "size": 10480, "crc": -557834089}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1$2$1.class", "size": 3676, "crc": 1047601259}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$1.class", "size": 13805, "crc": -270176032}, {"key": "androidx/compose/material3/DatePickerKt$YearPicker$2.class", "name": "androidx/compose/material3/DatePickerKt$YearPicker$2.class", "size": 2975, "crc": -424323209}, {"key": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$1.class", "name": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$1.class", "size": 6419, "crc": -1497060726}, {"key": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$2.class", "name": "androidx/compose/material3/DatePickerKt$YearPickerMenuButton$2.class", "size": 2529, "crc": 1333256860}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1$1.class", "size": 3797, "crc": 1762764412}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollDownAction$1.class", "size": 2291, "crc": 1071932701}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1$1.class", "size": 3787, "crc": 369282931}, {"key": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1.class", "name": "androidx/compose/material3/DatePickerKt$customScrollActions$scrollUpAction$1.class", "size": 2286, "crc": 65491132}, {"key": "androidx/compose/material3/DatePickerKt$rememberDatePickerState$1$1.class", "name": "androidx/compose/material3/DatePickerKt$rememberDatePickerState$1$1.class", "size": 2273, "crc": -683079597}, {"key": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$2.class", "name": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$2.class", "size": 1633, "crc": -635147243}, {"key": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$3.class", "name": "androidx/compose/material3/DatePickerKt$updateDisplayedMonth$3.class", "size": 3254, "crc": -922471140}, {"key": "androidx/compose/material3/DatePickerKt.class", "name": "androidx/compose/material3/DatePickerKt.class", "size": 116730, "crc": 1019062769}, {"key": "androidx/compose/material3/DatePickerState.class", "name": "androidx/compose/material3/DatePickerState.class", "size": 1565, "crc": -1034370959}, {"key": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$1.class", "name": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$1.class", "size": 2715, "crc": -1530846231}, {"key": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$2.class", "name": "androidx/compose/material3/DatePickerStateImpl$Companion$Saver$2.class", "size": 2700, "crc": -1110055511}, {"key": "androidx/compose/material3/DatePickerStateImpl$Companion.class", "name": "androidx/compose/material3/DatePickerStateImpl$Companion.class", "size": 2307, "crc": 423109742}, {"key": "androidx/compose/material3/DatePickerStateImpl.class", "name": "androidx/compose/material3/DatePickerStateImpl.class", "size": 6064, "crc": 23385278}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$1$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$1$1.class", "size": 2195, "crc": -1821762945}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2$1$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2$1$1.class", "size": 2087, "crc": -686928974}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$2.class", "size": 5498, "crc": -321993494}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3$1.class", "size": 1645, "crc": 682404496}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$3.class", "size": 3685, "crc": -294889418}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$4$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$4$1.class", "size": 2195, "crc": 1343301956}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5$1$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5$1$1.class", "size": 2085, "crc": 563014605}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$5.class", "size": 5499, "crc": 1134985877}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6$1.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6$1.class", "size": 1645, "crc": -550267265}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$2$6.class", "size": 3687, "crc": -1237790283}, {"key": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$3.class", "name": "androidx/compose/material3/DateRangeInputKt$DateRangeInputContent$3.class", "size": 3284, "crc": -236345067}, {"key": "androidx/compose/material3/DateRangeInputKt.class", "name": "androidx/compose/material3/DateRangeInputKt.class", "size": 22560, "crc": -351469350}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$1.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$1.class", "size": 3030, "crc": -472653549}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$2.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$2.class", "size": 3028, "crc": 1443425203}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$3.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$3.class", "size": 2456, "crc": 2038726479}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$4$1.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$4$1.class", "size": 2732, "crc": -1625045466}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$6.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerHeadline$6.class", "size": 4102, "crc": -1214183575}, {"key": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerTitle$1.class", "name": "androidx/compose/material3/DateRangePickerDefaults$DateRangePickerTitle$1.class", "size": 2034, "crc": 2141481671}, {"key": "androidx/compose/material3/DateRangePickerDefaults.class", "name": "androidx/compose/material3/DateRangePickerDefaults.class", "size": 23557, "crc": 741760318}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$2.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$2.class", "size": 3380, "crc": 1494919724}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$3.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$3.class", "size": 3752, "crc": -2117311279}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4$1$1.class", "size": 1644, "crc": 575105167}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$4.class", "size": 5247, "crc": -1542261340}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$1$1.class", "size": 1849, "crc": -469238894}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$2$1.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5$2$1.class", "size": 1545, "crc": -719183167}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$5.class", "size": 6485, "crc": 389753939}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$6.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePicker$6.class", "size": 3202, "crc": 671502042}, {"key": "androidx/compose/material3/DateRangePickerKt$DateRangePickerContent$2.class", "name": "androidx/compose/material3/DateRangePickerKt$DateRangePickerContent$2.class", "size": 3698, "crc": -263380490}, {"key": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$1.class", "name": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$1.class", "size": 2100, "crc": 1729088971}, {"key": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$2.class", "name": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$2.class", "size": 6116, "crc": -1049166598}, {"key": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$3.class", "name": "androidx/compose/material3/DateRangePickerKt$SwitchableDateEntryContent$3.class", "size": 3843, "crc": 1316974982}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$1.class", "size": 1435, "crc": 942337509}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$2.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1$2.class", "size": 1435, "crc": 1149601252}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$1.class", "size": 2408, "crc": 1460763625}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1$1.class", "size": 1472, "crc": 2086536257}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1$2$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1$2$1.class", "size": 2277, "crc": -827974278}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1$1$1.class", "size": 7701, "crc": 2019301066}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1$1.class", "size": 16532, "crc": -417229327}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$2$1.class", "size": 4506, "crc": -2068211018}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$onDateSelectionChange$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1$onDateSelectionChange$1$1.class", "size": 2059, "crc": 148896010}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$1.class", "size": 13199, "crc": 2045185630}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$2$1.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$2$1.class", "size": 4698, "crc": -1600572956}, {"key": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$3.class", "name": "androidx/compose/material3/DateRangePickerKt$VerticalMonthsList$3.class", "size": 3863, "crc": 866270902}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1$1.class", "size": 3802, "crc": -1002106590}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollDownAction$1.class", "size": 2291, "crc": 86989580}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1$1.class", "size": 3792, "crc": 1231177228}, {"key": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1.class", "name": "androidx/compose/material3/DateRangePickerKt$customScrollActions$scrollUpAction$1.class", "size": 2286, "crc": 1716497283}, {"key": "androidx/compose/material3/DateRangePickerKt$rememberDateRangePickerState$1$1.class", "name": "androidx/compose/material3/DateRangePickerKt$rememberDateRangePickerState$1$1.class", "size": 2460, "crc": -1832988248}, {"key": "androidx/compose/material3/DateRangePickerKt.class", "name": "androidx/compose/material3/DateRangePickerKt.class", "size": 45278, "crc": -1148343454}, {"key": "androidx/compose/material3/DateRangePickerState.class", "name": "androidx/compose/material3/DateRangePickerState.class", "size": 1787, "crc": -521535889}, {"key": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$1.class", "name": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$1.class", "size": 2818, "crc": -803687434}, {"key": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$2.class", "name": "androidx/compose/material3/DateRangePickerStateImpl$Companion$Saver$2.class", "size": 2775, "crc": -825862094}, {"key": "androidx/compose/material3/DateRangePickerStateImpl$Companion.class", "name": "androidx/compose/material3/DateRangePickerStateImpl$Companion.class", "size": 2352, "crc": 1138928593}, {"key": "androidx/compose/material3/DateRangePickerStateImpl.class", "name": "androidx/compose/material3/DateRangePickerStateImpl.class", "size": 7205, "crc": -1330042266}, {"key": "androidx/compose/material3/DateVisualTransformation$dateOffsetTranslator$1.class", "name": "androidx/compose/material3/DateVisualTransformation$dateOffsetTranslator$1.class", "size": 1680, "crc": -532890468}, {"key": "androidx/compose/material3/DateVisualTransformation.class", "name": "androidx/compose/material3/DateVisualTransformation.class", "size": 4945, "crc": -855653951}, {"key": "androidx/compose/material3/DefaultDrawerItemsColor.class", "name": "androidx/compose/material3/DefaultDrawerItemsColor.class", "size": 6294, "crc": 342480458}, {"key": "androidx/compose/material3/DefaultPlatformTextStyle_androidKt.class", "name": "androidx/compose/material3/DefaultPlatformTextStyle_androidKt.class", "size": 1118, "crc": -1754651935}, {"key": "androidx/compose/material3/DismissDirection.class", "name": "androidx/compose/material3/DismissDirection.class", "size": 1754, "crc": -301312339}, {"key": "androidx/compose/material3/DismissValue.class", "name": "androidx/compose/material3/DismissValue.class", "size": 1796, "crc": 1259437867}, {"key": "androidx/compose/material3/DisplayMode$Companion.class", "name": "androidx/compose/material3/DisplayMode$Companion.class", "size": 1222, "crc": 896220538}, {"key": "androidx/compose/material3/DisplayMode.class", "name": "androidx/compose/material3/DisplayMode.class", "size": 2676, "crc": -1056564555}, {"key": "androidx/compose/material3/DividerDefaults.class", "name": "androidx/compose/material3/DividerDefaults.class", "size": 2445, "crc": 1436694398}, {"key": "androidx/compose/material3/DividerKt$Divider$1.class", "name": "androidx/compose/material3/DividerKt$Divider$1.class", "size": 1842, "crc": 1820876008}, {"key": "androidx/compose/material3/DividerKt$HorizontalDivider$1$1.class", "name": "androidx/compose/material3/DividerKt$HorizontalDivider$1$1.class", "size": 2135, "crc": -1233700716}, {"key": "androidx/compose/material3/DividerKt$HorizontalDivider$2.class", "name": "androidx/compose/material3/DividerKt$HorizontalDivider$2.class", "size": 1872, "crc": -1832410263}, {"key": "androidx/compose/material3/DividerKt$VerticalDivider$1$1.class", "name": "androidx/compose/material3/DividerKt$VerticalDivider$1$1.class", "size": 2130, "crc": -1402027234}, {"key": "androidx/compose/material3/DividerKt$VerticalDivider$2.class", "name": "androidx/compose/material3/DividerKt$VerticalDivider$2.class", "size": 1866, "crc": 2084069164}, {"key": "androidx/compose/material3/DividerKt.class", "name": "androidx/compose/material3/DividerKt.class", "size": 10182, "crc": 1919369064}, {"key": "androidx/compose/material3/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/material3/DragGestureDetectorCopyKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2092, "crc": 251861727}, {"key": "androidx/compose/material3/DragGestureDetectorCopyKt.class", "name": "androidx/compose/material3/DragGestureDetectorCopyKt.class", "size": 15769, "crc": -600576072}, {"key": "androidx/compose/material3/DraggableAnchors.class", "name": "androidx/compose/material3/DraggableAnchors.class", "size": 1237, "crc": 695684571}, {"key": "androidx/compose/material3/DraggableAnchorsConfig.class", "name": "androidx/compose/material3/DraggableAnchorsConfig.class", "size": 1730, "crc": -47716836}, {"key": "androidx/compose/material3/DrawerDefaults.class", "name": "androidx/compose/material3/DrawerDefaults.class", "size": 6212, "crc": -1424592739}, {"key": "androidx/compose/material3/DrawerState$1.class", "name": "androidx/compose/material3/DrawerState$1.class", "size": 1645, "crc": 706478993}, {"key": "androidx/compose/material3/DrawerState$Companion$Saver$1.class", "name": "androidx/compose/material3/DrawerState$Companion$Saver$1.class", "size": 2059, "crc": 1638199100}, {"key": "androidx/compose/material3/DrawerState$Companion$Saver$2.class", "name": "androidx/compose/material3/DrawerState$Companion$Saver$2.class", "size": 2104, "crc": -1024413619}, {"key": "androidx/compose/material3/DrawerState$Companion.class", "name": "androidx/compose/material3/DrawerState$Companion.class", "size": 2183, "crc": -1893457909}, {"key": "androidx/compose/material3/DrawerState$anchoredDraggableState$1.class", "name": "androidx/compose/material3/DrawerState$anchoredDraggableState$1.class", "size": 1571, "crc": -1552791990}, {"key": "androidx/compose/material3/DrawerState$anchoredDraggableState$2.class", "name": "androidx/compose/material3/DrawerState$anchoredDraggableState$2.class", "size": 2336, "crc": -1221435376}, {"key": "androidx/compose/material3/DrawerState$animateTo$3$1.class", "name": "androidx/compose/material3/DrawerState$animateTo$3$1.class", "size": 1824, "crc": -1011450278}, {"key": "androidx/compose/material3/DrawerState$animateTo$3.class", "name": "androidx/compose/material3/DrawerState$animateTo$3.class", "size": 5412, "crc": -1766579373}, {"key": "androidx/compose/material3/DrawerState$offset$1.class", "name": "androidx/compose/material3/DrawerState$offset$1.class", "size": 1624, "crc": **********}, {"key": "androidx/compose/material3/DrawerState.class", "name": "androidx/compose/material3/DrawerState.class", "size": 11677, "crc": -963935592}, {"key": "androidx/compose/material3/DrawerValue.class", "name": "androidx/compose/material3/DrawerValue.class", "size": 1390, "crc": 189824201}, {"key": "androidx/compose/material3/DropdownMenuPositionProvider$2.class", "name": "androidx/compose/material3/DropdownMenuPositionProvider$2.class", "size": 1726, "crc": **********}, {"key": "androidx/compose/material3/DropdownMenuPositionProvider.class", "name": "androidx/compose/material3/DropdownMenuPositionProvider.class", "size": 14234, "crc": **********}, {"key": "androidx/compose/material3/DynamicTonalPaletteKt.class", "name": "androidx/compose/material3/DynamicTonalPaletteKt.class", "size": 11135, "crc": -**********}, {"key": "androidx/compose/material3/ElevationDefaults.class", "name": "androidx/compose/material3/ElevationDefaults.class", "size": 2389, "crc": -410483066}, {"key": "androidx/compose/material3/ElevationKt.class", "name": "androidx/compose/material3/ElevationKt.class", "size": 4682, "crc": 574879791}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior$1.class", "size": 1466, "crc": **********}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "size": 2055, "crc": **********}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior$nestedScrollConnection$1.class", "size": 5318, "crc": -1862321125}, {"key": "androidx/compose/material3/EnterAlwaysScrollBehavior.class", "name": "androidx/compose/material3/EnterAlwaysScrollBehavior.class", "size": 4487, "crc": -673951200}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior$1.class", "size": 1466, "crc": 182557245}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "size": 2047, "crc": 1550477008}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior$nestedScrollConnection$1.class", "size": 4934, "crc": 1055359866}, {"key": "androidx/compose/material3/ExitAlwaysScrollBehavior.class", "name": "androidx/compose/material3/ExitAlwaysScrollBehavior.class", "size": 4570, "crc": 101385869}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$1.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$1.class", "size": 1487, "crc": -120374635}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1$onPostFling$1.class", "size": 2111, "crc": -774975943}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior$nestedScrollConnection$1.class", "size": 5529, "crc": 1691419727}, {"key": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior.class", "name": "androidx/compose/material3/ExitUntilCollapsedScrollBehavior.class", "size": 4529, "crc": -354871979}, {"key": "androidx/compose/material3/ExperimentalMaterial3Api.class", "name": "androidx/compose/material3/ExperimentalMaterial3Api.class", "size": 828, "crc": -1244049908}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$1.class", "size": 4792, "crc": **********}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$2.class", "size": 3101, "crc": **********}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope$ExposedDropdownMenu$popupPositionProvider$1$1.class", "size": 2545, "crc": **********}, {"key": "androidx/compose/material3/ExposedDropdownMenuBoxScope.class", "name": "androidx/compose/material3/ExposedDropdownMenuBoxScope.class", "size": 11997, "crc": 818002768}, {"key": "androidx/compose/material3/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "name": "androidx/compose/material3/ExposedDropdownMenuDefaults$TrailingIcon$1.class", "size": 1840, "crc": -**********}, {"key": "androidx/compose/material3/ExposedDropdownMenuDefaults.class", "name": "androidx/compose/material3/ExposedDropdownMenuDefaults.class", "size": 35575, "crc": **********}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$2$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$2$1.class", "size": 2659, "crc": -**********}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$3$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$3$1.class", "size": 1575, "crc": 1488416352}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$4.class", "size": 2729, "crc": 1316523955}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1$1.class", "size": 2238, "crc": 2040360820}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$exposedDropdownSize$1.class", "size": 3924, "crc": 1333211045}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$menuAnchor$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$menuAnchor$1.class", "size": 3562, "crc": -784581771}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$menuAnchor$2.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1$menuAnchor$2.class", "size": 1818, "crc": -412778552}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$ExposedDropdownMenuBox$scope$1$1.class", "size": 4828, "crc": -632757287}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$invoke$$inlined$onDispose$1.class", "size": 2432, "crc": 1943638594}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$listener$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1$listener$1.class", "size": 3152, "crc": -1110015241}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$1$1.class", "size": 3669, "crc": 1682670355}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$2.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$SoftKeyboardListener$2.class", "size": 2283, "crc": 1572969283}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1$1.class", "size": 4690, "crc": 1702311432}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$1.class", "size": 4101, "crc": 1884050211}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2$1.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2$1.class", "size": 1558, "crc": 1033484989}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt$expandable$2.class", "size": 3084, "crc": 2035385071}, {"key": "androidx/compose/material3/ExposedDropdownMenu_androidKt.class", "name": "androidx/compose/material3/ExposedDropdownMenu_androidKt.class", "size": 29250, "crc": -1887081099}, {"key": "androidx/compose/material3/FabPlacement.class", "name": "androidx/compose/material3/FabPlacement.class", "size": 1154, "crc": 103669038}, {"key": "androidx/compose/material3/FabPosition$Companion.class", "name": "androidx/compose/material3/FabPosition$Companion.class", "size": 1539, "crc": -1048372193}, {"key": "androidx/compose/material3/FabPosition.class", "name": "androidx/compose/material3/FabPosition.class", "size": 2866, "crc": -297428855}, {"key": "androidx/compose/material3/FadeInFadeOutAnimationItem.class", "name": "androidx/compose/material3/FadeInFadeOutAnimationItem.class", "size": 4399, "crc": 1217030143}, {"key": "androidx/compose/material3/FadeInFadeOutState.class", "name": "androidx/compose/material3/FadeInFadeOutState.class", "size": 2309, "crc": -197187820}, {"key": "androidx/compose/material3/FilterChipDefaults.class", "name": "androidx/compose/material3/FilterChipDefaults.class", "size": 14506, "crc": -510365887}, {"key": "androidx/compose/material3/FloatingActionButtonDefaults.class", "name": "androidx/compose/material3/FloatingActionButtonDefaults.class", "size": 8954, "crc": -500030661}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$1$1.class", "size": 4346, "crc": 290991357}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1$1.class", "size": 4095, "crc": 805750470}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1$1.class", "size": 4715, "crc": -1885605886}, {"key": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevation$animateElevation$2$1.class", "size": 4686, "crc": -718640016}, {"key": "androidx/compose/material3/FloatingActionButtonElevation.class", "name": "androidx/compose/material3/FloatingActionButtonElevation.class", "size": 8463, "crc": -271678286}, {"key": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$animateElevation$1.class", "size": 2016, "crc": -757065840}, {"key": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "name": "androidx/compose/material3/FloatingActionButtonElevationAnimatable$snapElevation$1.class", "size": 2073, "crc": 533381800}, {"key": "androidx/compose/material3/FloatingActionButtonElevationAnimatable.class", "name": "androidx/compose/material3/FloatingActionButtonElevationAnimatable.class", "size": 7130, "crc": -911370315}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$2.class", "size": 10501, "crc": -317560995}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$3.class", "size": 3526, "crc": 528388149}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$5$1$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$5$1$1$1.class", "size": 1842, "crc": 1025417560}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$5$1$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$5$1$1.class", "size": 11066, "crc": -1924500994}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$5.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$5.class", "size": 12486, "crc": -686829855}, {"key": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$6.class", "name": "androidx/compose/material3/FloatingActionButtonKt$ExtendedFloatingActionButton$6.class", "size": 3710, "crc": -1473545}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$2.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$2.class", "size": 2266, "crc": 1440889816}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$3$1.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$3$1.class", "size": 9657, "crc": 882389866}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$3.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$3.class", "size": 4262, "crc": -88077753}, {"key": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$4.class", "name": "androidx/compose/material3/FloatingActionButtonKt$FloatingActionButton$4.class", "size": 3411, "crc": -409791846}, {"key": "androidx/compose/material3/FloatingActionButtonKt$LargeFloatingActionButton$2.class", "name": "androidx/compose/material3/FloatingActionButtonKt$LargeFloatingActionButton$2.class", "size": 3426, "crc": 64684241}, {"key": "androidx/compose/material3/FloatingActionButtonKt$SmallFloatingActionButton$2.class", "name": "androidx/compose/material3/FloatingActionButtonKt$SmallFloatingActionButton$2.class", "size": 3426, "crc": 984131682}, {"key": "androidx/compose/material3/FloatingActionButtonKt.class", "name": "androidx/compose/material3/FloatingActionButtonKt.class", "size": 27207, "crc": 782994332}, {"key": "androidx/compose/material3/IconButtonColors.class", "name": "androidx/compose/material3/IconButtonColors.class", "size": 5740, "crc": -873161441}, {"key": "androidx/compose/material3/IconButtonDefaults.class", "name": "androidx/compose/material3/IconButtonDefaults.class", "size": 19112, "crc": -437589759}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconButton$2.class", "size": 2192, "crc": -1225390491}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconButton$3.class", "size": 9685, "crc": 399281092}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconButton$4.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconButton$4.class", "size": 3232, "crc": 870299615}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$2.class", "size": 2219, "crc": -2040365941}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$3.class", "size": 9746, "crc": 169725528}, {"key": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$4.class", "name": "androidx/compose/material3/IconButtonKt$FilledIconToggleButton$4.class", "size": 3373, "crc": -665086298}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$2.class", "size": 2207, "crc": 266934283}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$3.class", "size": 9740, "crc": -614489275}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$4.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconButton$4.class", "size": 3247, "crc": 1623147970}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$2.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$2.class", "size": 2234, "crc": -1891222301}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$3.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$3.class", "size": 9801, "crc": 625704161}, {"key": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$4.class", "name": "androidx/compose/material3/IconButtonKt$FilledTonalIconToggleButton$4.class", "size": 3388, "crc": -1093056807}, {"key": "androidx/compose/material3/IconButtonKt$IconButton$3.class", "name": "androidx/compose/material3/IconButtonKt$IconButton$3.class", "size": 3020, "crc": -356885608}, {"key": "androidx/compose/material3/IconButtonKt$IconToggleButton$3.class", "name": "androidx/compose/material3/IconButtonKt$IconToggleButton$3.class", "size": 3161, "crc": -704275600}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$2.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$2.class", "size": 2240, "crc": -122439515}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$3.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$3.class", "size": 9749, "crc": -507010098}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$4.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconButton$4.class", "size": 3457, "crc": -1799586046}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$2.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$2.class", "size": 2267, "crc": -297006322}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$3.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$3.class", "size": 9810, "crc": 1378628462}, {"key": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$4.class", "name": "androidx/compose/material3/IconButtonKt$OutlinedIconToggleButton$4.class", "size": 3598, "crc": 843362426}, {"key": "androidx/compose/material3/IconButtonKt.class", "name": "androidx/compose/material3/IconButtonKt.class", "size": 42337, "crc": 1214384506}, {"key": "androidx/compose/material3/IconKt$Icon$1.class", "name": "androidx/compose/material3/IconKt$Icon$1.class", "size": 2083, "crc": 1143906148}, {"key": "androidx/compose/material3/IconKt$Icon$2.class", "name": "androidx/compose/material3/IconKt$Icon$2.class", "size": 2057, "crc": -1339369659}, {"key": "androidx/compose/material3/IconKt$Icon$3.class", "name": "androidx/compose/material3/IconKt$Icon$3.class", "size": 2070, "crc": -1192334411}, {"key": "androidx/compose/material3/IconKt$Icon$semantics$1$1.class", "name": "androidx/compose/material3/IconKt$Icon$semantics$1$1.class", "size": 2165, "crc": -775967121}, {"key": "androidx/compose/material3/IconKt.class", "name": "androidx/compose/material3/IconKt.class", "size": 13717, "crc": -1719518814}, {"key": "androidx/compose/material3/IconToggleButtonColors.class", "name": "androidx/compose/material3/IconToggleButtonColors.class", "size": 5090, "crc": -1625293717}, {"key": "androidx/compose/material3/InputChipDefaults.class", "name": "androidx/compose/material3/InputChipDefaults.class", "size": 10821, "crc": -616430873}, {"key": "androidx/compose/material3/InputIdentifier$Companion.class", "name": "androidx/compose/material3/InputIdentifier$Companion.class", "size": 1467, "crc": 285504713}, {"key": "androidx/compose/material3/InputIdentifier.class", "name": "androidx/compose/material3/InputIdentifier.class", "size": 2808, "crc": -1239470600}, {"key": "androidx/compose/material3/InputPhase.class", "name": "androidx/compose/material3/InputPhase.class", "size": 1463, "crc": 663311529}, {"key": "androidx/compose/material3/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "name": "androidx/compose/material3/InteractiveComponentSizeKt$LocalMinimumInteractiveComponentEnforcement$1.class", "size": 1343, "crc": 852431926}, {"key": "androidx/compose/material3/InteractiveComponentSizeKt.class", "name": "androidx/compose/material3/InteractiveComponentSizeKt.class", "size": 4059, "crc": 717431437}, {"key": "androidx/compose/material3/InternalMutatorMutex$Mutator.class", "name": "androidx/compose/material3/InternalMutatorMutex$Mutator.class", "size": 2057, "crc": -338223598}, {"key": "androidx/compose/material3/InternalMutatorMutex$mutate$2.class", "name": "androidx/compose/material3/InternalMutatorMutex$mutate$2.class", "size": 7360, "crc": 1979779356}, {"key": "androidx/compose/material3/InternalMutatorMutex$mutateWith$2.class", "name": "androidx/compose/material3/InternalMutatorMutex$mutateWith$2.class", "size": 7536, "crc": -146557830}, {"key": "androidx/compose/material3/InternalMutatorMutex.class", "name": "androidx/compose/material3/InternalMutatorMutex.class", "size": 6756, "crc": -817889118}, {"key": "androidx/compose/material3/LabelKt$HandleInteractions$1$1$1.class", "name": "androidx/compose/material3/LabelKt$HandleInteractions$1$1$1.class", "size": 4456, "crc": 494336364}, {"key": "androidx/compose/material3/LabelKt$HandleInteractions$1$1.class", "name": "androidx/compose/material3/LabelKt$HandleInteractions$1$1.class", "size": 4263, "crc": 1622068241}, {"key": "androidx/compose/material3/LabelKt$HandleInteractions$2.class", "name": "androidx/compose/material3/LabelKt$HandleInteractions$2.class", "size": 2090, "crc": -1129875846}, {"key": "androidx/compose/material3/LabelKt$Label$2.class", "name": "androidx/compose/material3/LabelKt$Label$2.class", "size": 3219, "crc": -875031532}, {"key": "androidx/compose/material3/LabelKt$Label$3.class", "name": "androidx/compose/material3/LabelKt$Label$3.class", "size": 2946, "crc": 1810735035}, {"key": "androidx/compose/material3/LabelKt$Label$scope$1$1$drawCaret$1.class", "name": "androidx/compose/material3/LabelKt$Label$scope$1$1$drawCaret$1.class", "size": 2706, "crc": 2090110322}, {"key": "androidx/compose/material3/LabelKt$Label$scope$1$1.class", "name": "androidx/compose/material3/LabelKt$Label$scope$1$1.class", "size": 2615, "crc": 555498574}, {"key": "androidx/compose/material3/LabelKt$Label$wrappedContent$1$1$1.class", "name": "androidx/compose/material3/LabelKt$Label$wrappedContent$1$1$1.class", "size": 1981, "crc": -1389748486}, {"key": "androidx/compose/material3/LabelKt$Label$wrappedContent$1.class", "name": "androidx/compose/material3/LabelKt$Label$wrappedContent$1.class", "size": 10639, "crc": 432976103}, {"key": "androidx/compose/material3/LabelKt.class", "name": "androidx/compose/material3/LabelKt.class", "size": 13349, "crc": 1721741541}, {"key": "androidx/compose/material3/LabelStateImpl.class", "name": "androidx/compose/material3/LabelStateImpl.class", "size": 2079, "crc": 956698055}, {"key": "androidx/compose/material3/LayoutId.class", "name": "androidx/compose/material3/LayoutId.class", "size": 1371, "crc": 1389043630}, {"key": "androidx/compose/material3/LegacyCalendarModelImpl$Companion.class", "name": "androidx/compose/material3/LegacyCalendarModelImpl$Companion.class", "size": 4484, "crc": 1277053419}, {"key": "androidx/compose/material3/LegacyCalendarModelImpl.class", "name": "androidx/compose/material3/LegacyCalendarModelImpl.class", "size": 10574, "crc": -1571468047}, {"key": "androidx/compose/material3/ListItemColors.class", "name": "androidx/compose/material3/ListItemColors.class", "size": 3948, "crc": 837607362}, {"key": "androidx/compose/material3/ListItemDefaults.class", "name": "androidx/compose/material3/ListItemDefaults.class", "size": 6229, "crc": -1770693488}, {"key": "androidx/compose/material3/ListItemKt$ListItem$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$1.class", "size": 1797, "crc": 1690835717}, {"key": "androidx/compose/material3/ListItemKt$ListItem$2.class", "name": "androidx/compose/material3/ListItemKt$ListItem$2.class", "size": 3913, "crc": 609403399}, {"key": "androidx/compose/material3/ListItemKt$ListItem$3.class", "name": "androidx/compose/material3/ListItemKt$ListItem$3.class", "size": 3431, "crc": -398486159}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedHeadlineContent$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedHeadlineContent$1.class", "size": 3456, "crc": 1693916243}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedLeadingContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedLeadingContent$1$1.class", "size": 10570, "crc": 821946939}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedOverlineContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedOverlineContent$1$1.class", "size": 3558, "crc": 663739422}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedSupportingContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedSupportingContent$1$1.class", "size": 3570, "crc": -1154204787}, {"key": "androidx/compose/material3/ListItemKt$ListItem$decoratedTrailingContent$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItem$decoratedTrailingContent$1$1.class", "size": 10346, "crc": -616984286}, {"key": "androidx/compose/material3/ListItemKt$ListItemLayout$1$1.class", "name": "androidx/compose/material3/ListItemKt$ListItemLayout$1$1.class", "size": 7716, "crc": -636121564}, {"key": "androidx/compose/material3/ListItemKt$ListItemLayout$2.class", "name": "androidx/compose/material3/ListItemKt$ListItemLayout$2.class", "size": 2863, "crc": 1428198204}, {"key": "androidx/compose/material3/ListItemKt$ProvideTextStyleFromToken$1.class", "name": "androidx/compose/material3/ListItemKt$ProvideTextStyleFromToken$1.class", "size": 2350, "crc": 660334174}, {"key": "androidx/compose/material3/ListItemKt$place$1.class", "name": "androidx/compose/material3/ListItemKt$place$1.class", "size": 5034, "crc": -1253793567}, {"key": "androidx/compose/material3/ListItemKt.class", "name": "androidx/compose/material3/ListItemKt.class", "size": 30253, "crc": -1722129111}, {"key": "androidx/compose/material3/ListItemType$Companion.class", "name": "androidx/compose/material3/ListItemType$Companion.class", "size": 1813, "crc": 1309825736}, {"key": "androidx/compose/material3/ListItemType.class", "name": "androidx/compose/material3/ListItemType.class", "size": 3323, "crc": -1330597254}, {"key": "androidx/compose/material3/Listener.class", "name": "androidx/compose/material3/Listener.class", "size": 5564, "crc": 459682169}, {"key": "androidx/compose/material3/Locale24$Companion.class", "name": "androidx/compose/material3/Locale24$Companion.class", "size": 3492, "crc": -739633606}, {"key": "androidx/compose/material3/Locale24.class", "name": "androidx/compose/material3/Locale24.class", "size": 961, "crc": -232464323}, {"key": "androidx/compose/material3/MapDraggableAnchors.class", "name": "androidx/compose/material3/MapDraggableAnchors.class", "size": 5415, "crc": -304195185}, {"key": "androidx/compose/material3/MappedInteractionSource$special$$inlined$map$1$2$1.class", "name": "androidx/compose/material3/MappedInteractionSource$special$$inlined$map$1$2$1.class", "size": 2144, "crc": -706652715}, {"key": "androidx/compose/material3/MappedInteractionSource$special$$inlined$map$1$2.class", "name": "androidx/compose/material3/MappedInteractionSource$special$$inlined$map$1$2.class", "size": 5240, "crc": -669325702}, {"key": "androidx/compose/material3/MappedInteractionSource$special$$inlined$map$1.class", "name": "androidx/compose/material3/MappedInteractionSource$special$$inlined$map$1.class", "size": 3401, "crc": 814119618}, {"key": "androidx/compose/material3/MappedInteractionSource.class", "name": "androidx/compose/material3/MappedInteractionSource.class", "size": 5242, "crc": 354510967}, {"key": "androidx/compose/material3/MaterialRippleTheme.class", "name": "androidx/compose/material3/MaterialRippleTheme.class", "size": 4158, "crc": -2066287123}, {"key": "androidx/compose/material3/MaterialTheme.class", "name": "androidx/compose/material3/MaterialTheme.class", "size": 4639, "crc": -1493534524}, {"key": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$1.class", "name": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$1.class", "size": 3106, "crc": 571708741}, {"key": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$2.class", "name": "androidx/compose/material3/MaterialThemeKt$MaterialTheme$2.class", "size": 2620, "crc": -1731831470}, {"key": "androidx/compose/material3/MaterialThemeKt.class", "name": "androidx/compose/material3/MaterialThemeKt.class", "size": 9077, "crc": -234551273}, {"key": "androidx/compose/material3/MenuDefaults.class", "name": "androidx/compose/material3/MenuDefaults.class", "size": 6429, "crc": -1465751068}, {"key": "androidx/compose/material3/MenuItemColors.class", "name": "androidx/compose/material3/MenuItemColors.class", "size": 8836, "crc": -352145252}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$1$1.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$1$1.class", "size": 2873, "crc": -1515132547}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$2.class", "size": 10539, "crc": -638957465}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$3.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$3.class", "size": 3232, "crc": -1790783575}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$alpha$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$alpha$2.class", "size": 3599, "crc": -232922620}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuContent$scale$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuContent$scale$2.class", "size": 3747, "crc": -1816109035}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$1.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$1.class", "size": 9392, "crc": 502370322}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$2.class", "size": 10555, "crc": 1516693160}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$3.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1$3.class", "size": 9394, "crc": -2084726432}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$1$1.class", "size": 5810, "crc": -436564236}, {"key": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$2.class", "name": "androidx/compose/material3/MenuKt$DropdownMenuItemContent$2.class", "size": 3687, "crc": 1867383368}, {"key": "androidx/compose/material3/MenuKt.class", "name": "androidx/compose/material3/MenuKt.class", "size": 28298, "crc": -1420057653}, {"key": "androidx/compose/material3/MenuPosition$Horizontal.class", "name": "androidx/compose/material3/MenuPosition$Horizontal.class", "size": 1114, "crc": 1561825567}, {"key": "androidx/compose/material3/MenuPosition$Vertical.class", "name": "androidx/compose/material3/MenuPosition$Vertical.class", "size": 976, "crc": -101097588}, {"key": "androidx/compose/material3/MenuPosition.class", "name": "androidx/compose/material3/MenuPosition.class", "size": 5149, "crc": 1967394286}, {"key": "androidx/compose/material3/MinimumInteractiveComponentSizeModifier$measure$1.class", "name": "androidx/compose/material3/MinimumInteractiveComponentSizeModifier$measure$1.class", "size": 2286, "crc": -346396864}, {"key": "androidx/compose/material3/MinimumInteractiveComponentSizeModifier.class", "name": "androidx/compose/material3/MinimumInteractiveComponentSizeModifier.class", "size": 3367, "crc": -1100842569}, {"key": "androidx/compose/material3/MinimumInteractiveModifier.class", "name": "androidx/compose/material3/MinimumInteractiveModifier.class", "size": 3153, "crc": 893019336}, {"key": "androidx/compose/material3/MinimumInteractiveModifierNode$measure$1.class", "name": "androidx/compose/material3/MinimumInteractiveModifierNode$measure$1.class", "size": 2259, "crc": -97627670}, {"key": "androidx/compose/material3/MinimumInteractiveModifierNode.class", "name": "androidx/compose/material3/MinimumInteractiveModifierNode.class", "size": 3585, "crc": -1459306122}, {"key": "androidx/compose/material3/ModalBottomSheetDefaults.class", "name": "androidx/compose/material3/ModalBottomSheetDefaults.class", "size": 1923, "crc": -211906642}, {"key": "androidx/compose/material3/ModalBottomSheetProperties.class", "name": "androidx/compose/material3/ModalBottomSheetProperties.class", "size": 2199, "crc": 1540098518}, {"key": "androidx/compose/material3/ModalBottomSheetWindow$Api33Impl.class", "name": "androidx/compose/material3/ModalBottomSheetWindow$Api33Impl.class", "size": 3074, "crc": 1289083171}, {"key": "androidx/compose/material3/ModalBottomSheetWindow$Content$4.class", "name": "androidx/compose/material3/ModalBottomSheetWindow$Content$4.class", "size": 1700, "crc": 917729921}, {"key": "androidx/compose/material3/ModalBottomSheetWindow$WhenMappings.class", "name": "androidx/compose/material3/ModalBottomSheetWindow$WhenMappings.class", "size": 835, "crc": -702423652}, {"key": "androidx/compose/material3/ModalBottomSheetWindow.class", "name": "androidx/compose/material3/ModalBottomSheetWindow.class", "size": 14578, "crc": 304790553}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$1$1.class", "size": 1849, "crc": -45126339}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1$1.class", "size": 3586, "crc": -1610827986}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1$2.class", "size": 3577, "crc": -1607978225}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1$3.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1$3.class", "size": 1721, "crc": 1703616600}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$2$1.class", "size": 3291, "crc": 1079068022}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$1$1.class", "size": 2000, "crc": -999420776}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$2$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$2$1.class", "size": 2072, "crc": -1170795952}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$4$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$4$1.class", "size": 3676, "crc": -1734138478}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$1.class", "size": 1862, "crc": -582385103}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$2$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$2$1.class", "size": 4017, "crc": -1251074368}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$2.class", "size": 3030, "crc": 1738331765}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$3$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$3$1.class", "size": 4035, "crc": 2025409638}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$3.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1$1$3.class", "size": 2953, "crc": -1851077283}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5$1$1$1.class", "size": 4144, "crc": -12827455}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1$5.class", "size": 17136, "crc": 1958490579}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3$1.class", "size": 14048, "crc": 32612684}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$3.class", "size": 5766, "crc": 1315521537}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$4$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$4$1.class", "size": 3915, "crc": 1199250695}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$5.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$5.class", "size": 4247, "crc": -1536912386}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$animateToDismiss$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$animateToDismiss$1$1$1.class", "size": 3662, "crc": 968354849}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$animateToDismiss$1$1$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$animateToDismiss$1$1$2.class", "size": 2053, "crc": -1386495577}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$animateToDismiss$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$animateToDismiss$1$1.class", "size": 3518, "crc": -1745610130}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$settleToDismiss$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$settleToDismiss$1$1$1.class", "size": 3732, "crc": 2104422142}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$settleToDismiss$1$1$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$settleToDismiss$1$1$2.class", "size": 2047, "crc": 327435399}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$settleToDismiss$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheet$settleToDismiss$1$1.class", "size": 3084, "crc": -1123076191}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$1$1$invoke$$inlined$onDispose$1.class", "size": 2360, "crc": -934081633}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$1$1.class", "size": 3554, "crc": 1544891785}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$2.class", "size": 2807, "crc": -1296825325}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$id$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$id$1.class", "size": 1480, "crc": -1492370368}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$modalBottomSheetWindow$1$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$modalBottomSheetWindow$1$1$1$1.class", "size": 1828, "crc": -1536230007}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$modalBottomSheetWindow$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$ModalBottomSheetPopup$modalBottomSheetWindow$1$1$1.class", "size": 11280, "crc": -1389584898}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$1$1.class", "size": 2156, "crc": 464855857}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$2.class", "size": 2015, "crc": -125218702}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$dismissSheet$1$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$dismissSheet$1$1$1.class", "size": 1735, "crc": 1660940709}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$dismissSheet$1$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$dismissSheet$1$1.class", "size": 4287, "crc": -981256996}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$dismissSheet$2.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$Scrim$dismissSheet$2.class", "size": 1687, "crc": -1238681294}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$WhenMappings.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$WhenMappings.class", "size": 910, "crc": -1952419136}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$modalBottomSheetAnchors$1$WhenMappings.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$modalBottomSheetAnchors$1$WhenMappings.class", "size": 959, "crc": -1433630710}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$modalBottomSheetAnchors$1$newAnchors$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$modalBottomSheetAnchors$1$newAnchors$1.class", "size": 2529, "crc": -1303132849}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$modalBottomSheetAnchors$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$modalBottomSheetAnchors$1.class", "size": 3237, "crc": -1234751793}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt$rememberModalBottomSheetState$1.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt$rememberModalBottomSheetState$1.class", "size": 1668, "crc": 460632093}, {"key": "androidx/compose/material3/ModalBottomSheet_androidKt.class", "name": "androidx/compose/material3/ModalBottomSheet_androidKt.class", "size": 34494, "crc": -33335570}, {"key": "androidx/compose/material3/MultiChoiceSegmentedButtonRowScope.class", "name": "androidx/compose/material3/MultiChoiceSegmentedButtonRowScope.class", "size": 637, "crc": -1445555692}, {"key": "androidx/compose/material3/MultiChoiceSegmentedButtonScopeWrapper.class", "name": "androidx/compose/material3/MultiChoiceSegmentedButtonScopeWrapper.class", "size": 3177, "crc": 1090874130}, {"key": "androidx/compose/material3/MutableWindowInsets.class", "name": "androidx/compose/material3/MutableWindowInsets.class", "size": 4145, "crc": -2038001267}, {"key": "androidx/compose/material3/NavigationBarDefaults.class", "name": "androidx/compose/material3/NavigationBarDefaults.class", "size": 4110, "crc": -65935447}, {"key": "androidx/compose/material3/NavigationBarItemColors.class", "name": "androidx/compose/material3/NavigationBarItemColors.class", "size": 10054, "crc": -1749870127}, {"key": "androidx/compose/material3/NavigationBarItemDefaults.class", "name": "androidx/compose/material3/NavigationBarItemDefaults.class", "size": 6463, "crc": -756773272}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBar$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBar$1.class", "size": 10661, "crc": 533523321}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBar$2.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBar$2.class", "size": 2711, "crc": -1231696625}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$2$1.class", "size": 1947, "crc": -770238765}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$2$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$2$1.class", "size": 1790, "crc": 2018142585}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$indicator$1$1$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$indicator$1$1$1.class", "size": 1976, "crc": -1143551103}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$indicator$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$indicator$1.class", "size": 6387, "crc": -2102415053}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$indicatorRipple$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$3$indicatorRipple$1.class", "size": 4363, "crc": 326446185}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$4.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$4.class", "size": 3672, "crc": 2025655136}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1$1.class", "size": 1656, "crc": 565518059}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledIcon$1.class", "size": 12120, "crc": -1789032999}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledLabel$1$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItem$styledLabel$1$1.class", "size": 5874, "crc": 1839633294}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$1$2$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$1$2$1.class", "size": 2214, "crc": 1971277735}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$2$1.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$2$1.class", "size": 9415, "crc": -2060098028}, {"key": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$3.class", "name": "androidx/compose/material3/NavigationBarKt$NavigationBarItemLayout$3.class", "size": 3029, "crc": -621055021}, {"key": "androidx/compose/material3/NavigationBarKt$placeIcon$1.class", "name": "androidx/compose/material3/NavigationBarKt$placeIcon$1.class", "size": 2799, "crc": -836713298}, {"key": "androidx/compose/material3/NavigationBarKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material3/NavigationBarKt$placeLabelAndIcon$1.class", "size": 3598, "crc": -617210487}, {"key": "androidx/compose/material3/NavigationBarKt.class", "name": "androidx/compose/material3/NavigationBarKt.class", "size": 41997, "crc": 1860148383}, {"key": "androidx/compose/material3/NavigationDrawerItemColors.class", "name": "androidx/compose/material3/NavigationDrawerItemColors.class", "size": 1362, "crc": 2117084272}, {"key": "androidx/compose/material3/NavigationDrawerItemDefaults.class", "name": "androidx/compose/material3/NavigationDrawerItemDefaults.class", "size": 4958, "crc": -1830493903}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleDrawerSheet$1.class", "size": 2973, "crc": 2005941898}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$1$1$1.class", "size": 1982, "crc": 516074693}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$1$1.class", "size": 2463, "crc": -1174895355}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1$1.class", "size": 3733, "crc": 928350673}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1$1.class", "size": 2656, "crc": 2123895957}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$1$1$1.class", "size": 2746, "crc": 1404344636}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1$1.class", "size": 2471, "crc": -1898161806}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$2$2$1.class", "size": 2856, "crc": 4479671}, {"key": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$DismissibleNavigationDrawer$3.class", "size": 2739, "crc": 520350083}, {"key": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$1.class", "size": 10445, "crc": -1394380808}, {"key": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$DrawerSheet$2.class", "size": 2974, "crc": 827490249}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalDrawerSheet$1.class", "size": 2955, "crc": 1319035616}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$1$1$1.class", "size": 1964, "crc": -124460469}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$1$1.class", "size": 2448, "crc": 1313153899}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1$1.class", "size": 3570, "crc": 1292392541}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$2$1.class", "size": 2692, "crc": -1813746537}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$3$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$3$1.class", "size": 1780, "crc": -2118261022}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$4$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$4$1.class", "size": 2114, "crc": -1367940697}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1$1.class", "size": 3685, "crc": -949632074}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1$1.class", "size": 2624, "crc": 400678082}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$2$5$1.class", "size": 2725, "crc": -1446942915}, {"key": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$ModalNavigationDrawer$3.class", "size": 2788, "crc": 1747809276}, {"key": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$2.class", "size": 2299, "crc": 1259220527}, {"key": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$3.class", "name": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$3.class", "size": 15848, "crc": -1688699996}, {"key": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$4.class", "name": "androidx/compose/material3/NavigationDrawerKt$NavigationDrawerItem$4.class", "size": 3750, "crc": 1378769370}, {"key": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$1$1.class", "size": 1973, "crc": 1333755554}, {"key": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$PermanentDrawerSheet$2.class", "size": 2967, "crc": 839065645}, {"key": "androidx/compose/material3/NavigationDrawerKt$PermanentNavigationDrawer$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$PermanentNavigationDrawer$2.class", "size": 2453, "crc": -1973571338}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$1$1.class", "size": 2198, "crc": 1569067722}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$2.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$2.class", "size": 2197, "crc": -1655982427}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1$1.class", "size": 1693, "crc": 982708767}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$1$1.class", "size": 4259, "crc": -674775768}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1$1.class", "size": 1534, "crc": 1326028523}, {"key": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$Scrim$dismissDrawer$2$1.class", "size": 2479, "crc": -851844508}, {"key": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$1.class", "size": 1650, "crc": 548738708}, {"key": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$2$1.class", "name": "androidx/compose/material3/NavigationDrawerKt$rememberDrawerState$2$1.class", "size": 1940, "crc": -523291390}, {"key": "androidx/compose/material3/NavigationDrawerKt.class", "name": "androidx/compose/material3/NavigationDrawerKt.class", "size": 73994, "crc": 1691318993}, {"key": "androidx/compose/material3/NavigationRailDefaults.class", "name": "androidx/compose/material3/NavigationRailDefaults.class", "size": 3846, "crc": 788716506}, {"key": "androidx/compose/material3/NavigationRailItemColors.class", "name": "androidx/compose/material3/NavigationRailItemColors.class", "size": 10085, "crc": -1630621084}, {"key": "androidx/compose/material3/NavigationRailItemDefaults.class", "name": "androidx/compose/material3/NavigationRailItemDefaults.class", "size": 6519, "crc": 197354256}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRail$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRail$1.class", "size": 11761, "crc": 555139787}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRail$2.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRail$2.class", "size": 2933, "crc": -1412684388}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$2$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$2$1.class", "size": 1753, "crc": 1209104650}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$indicator$1$1$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$indicator$1$1$1.class", "size": 1983, "crc": -1823893895}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$indicator$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$indicator$1.class", "size": 6111, "crc": -1038520907}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$indicatorRipple$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$2$indicatorRipple$1.class", "size": 4028, "crc": 1215839836}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$3.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$3.class", "size": 3424, "crc": 806960802}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1$1.class", "size": 1663, "crc": 1373076311}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledIcon$1.class", "size": 12111, "crc": -829155523}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItem$styledLabel$1$1.class", "size": 5860, "crc": 67669915}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$1$2$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$1$2$1.class", "size": 2221, "crc": -226189812}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$2$1.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$2$1.class", "size": 9632, "crc": -1590133608}, {"key": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$3.class", "name": "androidx/compose/material3/NavigationRailKt$NavigationRailItemLayout$3.class", "size": 3037, "crc": 1609902616}, {"key": "androidx/compose/material3/NavigationRailKt$placeIcon$1.class", "name": "androidx/compose/material3/NavigationRailKt$placeIcon$1.class", "size": 2804, "crc": 108366304}, {"key": "androidx/compose/material3/NavigationRailKt$placeLabelAndIcon$1.class", "name": "androidx/compose/material3/NavigationRailKt$placeLabelAndIcon$1.class", "size": 3613, "crc": -134323374}, {"key": "androidx/compose/material3/NavigationRailKt.class", "name": "androidx/compose/material3/NavigationRailKt.class", "size": 40701, "crc": -1747015249}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$ContainerBox$1.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$ContainerBox$1.class", "size": 2648, "crc": -1190012126}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$1.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$1.class", "size": 3503, "crc": 2075176955}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$2.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults$DecorationBox$2.class", "size": 5472, "crc": 1746052618}, {"key": "androidx/compose/material3/OutlinedTextFieldDefaults.class", "name": "androidx/compose/material3/OutlinedTextFieldDefaults.class", "size": 28899, "crc": 595251821}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$10.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$10.class", "size": 5889, "crc": -1784944194}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2$1.class", "size": 1628, "crc": 1933298982}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2$2$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2$2$1.class", "size": 3459, "crc": 1336601910}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2$2.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2$2.class", "size": 6964, "crc": 437789054}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$2.class", "size": 11259, "crc": -439556637}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$3.class", "size": 6159, "crc": 2057668702}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5$1.class", "size": 1628, "crc": 2115078850}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5$2$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5$2$1.class", "size": 3459, "crc": 1897498438}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5$2.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5$2.class", "size": 7164, "crc": -1691547831}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$5.class", "size": 11492, "crc": 1281353048}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$6.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$6.class", "size": 6333, "crc": -765812972}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$8.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextField$8.class", "size": 5713, "crc": -51673856}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$OutlinedTextFieldLayout$2.class", "size": 4940, "crc": -861311974}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1$WhenMappings.class", "size": 819, "crc": 1656301562}, {"key": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1.class", "name": "androidx/compose/material3/OutlinedTextFieldKt$outlineCutout$1.class", "size": 5622, "crc": 400872195}, {"key": "androidx/compose/material3/OutlinedTextFieldKt.class", "name": "androidx/compose/material3/OutlinedTextFieldKt.class", "size": 79321, "crc": -41787679}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1922, "crc": 165739642}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1919, "crc": 899796337}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$measure$2.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$measure$2.class", "size": 4190, "crc": 1378930758}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1922, "crc": 670594688}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1919, "crc": 1179821501}, {"key": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy.class", "name": "androidx/compose/material3/OutlinedTextFieldMeasurePolicy.class", "size": 32417, "crc": 1971621445}, {"key": "androidx/compose/material3/PinnedScrollBehavior$1.class", "name": "androidx/compose/material3/PinnedScrollBehavior$1.class", "size": 1352, "crc": 1986825510}, {"key": "androidx/compose/material3/PinnedScrollBehavior$nestedScrollConnection$1.class", "name": "androidx/compose/material3/PinnedScrollBehavior$nestedScrollConnection$1.class", "size": 2205, "crc": 778330375}, {"key": "androidx/compose/material3/PinnedScrollBehavior.class", "name": "androidx/compose/material3/PinnedScrollBehavior.class", "size": 4044, "crc": 79591725}, {"key": "androidx/compose/material3/ProgressIndicatorDefaults.class", "name": "androidx/compose/material3/ProgressIndicatorDefaults.class", "size": 5823, "crc": 294215549}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$1$1.class", "size": 2511, "crc": -1394067744}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$2$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$2$1.class", "size": 2707, "crc": 1494802649}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$3.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$3.class", "size": 2357, "crc": -1410739489}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$4$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$4$1.class", "size": 3408, "crc": 439345494}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$5.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$5.class", "size": 2046, "crc": -1083438960}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$6$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$6$1.class", "size": 1360, "crc": -659385802}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$7.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$7.class", "size": 2097, "crc": -2124609064}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$8.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$8.class", "size": 1990, "crc": -234748066}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$9.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$9.class", "size": 1939, "crc": -236877793}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$coercedProgress$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$coercedProgress$1$1.class", "size": 1747, "crc": 731758086}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$endAngle$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$endAngle$1.class", "size": 2502, "crc": -984813678}, {"key": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$startAngle$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$CircularProgressIndicator$startAngle$1.class", "size": 2545, "crc": -842019661}, {"key": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1$1.class", "size": 2063, "crc": -1971869420}, {"key": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$1.class", "size": 3060, "crc": 204443580}, {"key": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$2.class", "name": "androidx/compose/material3/ProgressIndicatorKt$IncreaseSemanticsBounds$2.class", "size": 1561, "crc": 849045093}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$1$1.class", "size": 2504, "crc": -1986107528}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$2$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$2$1.class", "size": 2540, "crc": 1753024760}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$3.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$3.class", "size": 2291, "crc": 348117665}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$4$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$4$1.class", "size": 3165, "crc": -891934287}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$5.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$5.class", "size": 1981, "crc": -1136033845}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$6$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$6$1.class", "size": 1353, "crc": -131954077}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$7.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$7.class", "size": 2036, "crc": -1452255618}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$8.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$8.class", "size": 1983, "crc": 1292518566}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$9.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$9.class", "size": 1928, "crc": 403274464}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$coercedProgress$1$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$coercedProgress$1$1.class", "size": 1740, "crc": 2061950676}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineHead$1.class", "size": 2504, "crc": -764042316}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$firstLineTail$1.class", "size": 2506, "crc": -553984269}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineHead$1.class", "size": 2509, "crc": -451160153}, {"key": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$1.class", "name": "androidx/compose/material3/ProgressIndicatorKt$LinearProgressIndicator$secondLineTail$1.class", "size": 2509, "crc": -2052765166}, {"key": "androidx/compose/material3/ProgressIndicatorKt.class", "name": "androidx/compose/material3/ProgressIndicatorKt.class", "size": 44987, "crc": -1191246354}, {"key": "androidx/compose/material3/ProvideContentColorTextStyleKt$ProvideContentColorTextStyle$1.class", "name": "androidx/compose/material3/ProvideContentColorTextStyleKt$ProvideContentColorTextStyle$1.class", "size": 2322, "crc": -428276814}, {"key": "androidx/compose/material3/ProvideContentColorTextStyleKt.class", "name": "androidx/compose/material3/ProvideContentColorTextStyleKt.class", "size": 5376, "crc": 1315241712}, {"key": "androidx/compose/material3/RadioButtonColors.class", "name": "androidx/compose/material3/RadioButtonColors.class", "size": 7547, "crc": -1915993324}, {"key": "androidx/compose/material3/RadioButtonDefaults.class", "name": "androidx/compose/material3/RadioButtonDefaults.class", "size": 4887, "crc": 1134518494}, {"key": "androidx/compose/material3/RadioButtonKt$RadioButton$2$1.class", "name": "androidx/compose/material3/RadioButtonKt$RadioButton$2$1.class", "size": 4410, "crc": -1449777983}, {"key": "androidx/compose/material3/RadioButtonKt$RadioButton$3.class", "name": "androidx/compose/material3/RadioButtonKt$RadioButton$3.class", "size": 2721, "crc": -471026744}, {"key": "androidx/compose/material3/RadioButtonKt.class", "name": "androidx/compose/material3/RadioButtonKt.class", "size": 11169, "crc": -2050200039}, {"key": "androidx/compose/material3/RangeSliderComponents.class", "name": "androidx/compose/material3/RangeSliderComponents.class", "size": 1507, "crc": -1333406775}, {"key": "androidx/compose/material3/RangeSliderLogic$captureThumb$1.class", "name": "androidx/compose/material3/RangeSliderLogic$captureThumb$1.class", "size": 4048, "crc": 957580249}, {"key": "androidx/compose/material3/RangeSliderLogic.class", "name": "androidx/compose/material3/RangeSliderLogic.class", "size": 3753, "crc": 967735507}, {"key": "androidx/compose/material3/RangeSliderState$gestureEndAction$1.class", "name": "androidx/compose/material3/RangeSliderState$gestureEndAction$1.class", "size": 1634, "crc": -452807550}, {"key": "androidx/compose/material3/RangeSliderState.class", "name": "androidx/compose/material3/RangeSliderState.class", "size": 16520, "crc": -987087687}, {"key": "androidx/compose/material3/RichTooltipColors.class", "name": "androidx/compose/material3/RichTooltipColors.class", "size": 5346, "crc": -784656114}, {"key": "androidx/compose/material3/ScaffoldDefaults.class", "name": "androidx/compose/material3/ScaffoldDefaults.class", "size": 2434, "crc": -1040055806}, {"key": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1$1$bodyContentPlaceables$1.class", "name": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1$1$bodyContentPlaceables$1.class", "size": 5294, "crc": 632869152}, {"key": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1$1$bottomBarPlaceables$1.class", "name": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1$1$bottomBarPlaceables$1.class", "size": 3563, "crc": -12191322}, {"key": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1$1.class", "size": 19462, "crc": 547872597}, {"key": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$1$1.class", "size": 4595, "crc": 1426737299}, {"key": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$2.class", "name": "androidx/compose/material3/ScaffoldKt$LegacyScaffoldLayout$2.class", "size": 3451, "crc": -1084727309}, {"key": "androidx/compose/material3/ScaffoldKt$LocalFabPlacement$1.class", "name": "androidx/compose/material3/ScaffoldKt$LocalFabPlacement$1.class", "size": 1238, "crc": 24752645}, {"key": "androidx/compose/material3/ScaffoldKt$Scaffold$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$Scaffold$1$1.class", "size": 2286, "crc": -926706909}, {"key": "androidx/compose/material3/ScaffoldKt$Scaffold$2.class", "name": "androidx/compose/material3/ScaffoldKt$Scaffold$2.class", "size": 4484, "crc": 1282677669}, {"key": "androidx/compose/material3/ScaffoldKt$Scaffold$3.class", "name": "androidx/compose/material3/ScaffoldKt$Scaffold$3.class", "size": 3756, "crc": 562735776}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayout$1.class", "size": 3427, "crc": -1264836777}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1$1.class", "size": 7080, "crc": 1808906542}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1$bodyContentPlaceables$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1$bodyContentPlaceables$1.class", "size": 5165, "crc": 1278993730}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1$bottomBarPlaceables$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1$bottomBarPlaceables$1.class", "size": 3434, "crc": 623059980}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$1$1.class", "size": 17760, "crc": 322060870}, {"key": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$2.class", "name": "androidx/compose/material3/ScaffoldKt$ScaffoldLayoutWithMeasureFix$2.class", "size": 3483, "crc": 748426608}, {"key": "androidx/compose/material3/ScaffoldKt.class", "name": "androidx/compose/material3/ScaffoldKt.class", "size": 22702, "crc": -256086646}, {"key": "androidx/compose/material3/ScaffoldLayoutContent.class", "name": "androidx/compose/material3/ScaffoldLayoutContent.class", "size": 1615, "crc": 1643047727}, {"key": "androidx/compose/material3/ScrollableTabData$onLaidOut$1$1.class", "name": "androidx/compose/material3/ScrollableTabData$onLaidOut$1$1.class", "size": 3955, "crc": -1788072190}, {"key": "androidx/compose/material3/ScrollableTabData.class", "name": "androidx/compose/material3/ScrollableTabData.class", "size": 4245, "crc": -1207262978}, {"key": "androidx/compose/material3/SearchBarColors.class", "name": "androidx/compose/material3/SearchBarColors.class", "size": 2965, "crc": -1596064518}, {"key": "androidx/compose/material3/SearchBarDefaults.class", "name": "androidx/compose/material3/SearchBarDefaults.class", "size": 14190, "crc": -589220303}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$2$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$2$1$1.class", "size": 13594, "crc": -312492230}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$2.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$2.class", "size": 14034, "crc": -1673185246}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$3$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$3$1.class", "size": 4214, "crc": -1408773161}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$4$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$4$1.class", "size": 1997, "crc": -1198426927}, {"key": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$5.class", "name": "androidx/compose/material3/SearchBar_androidKt$DockedSearchBar$5.class", "size": 5030, "crc": 1758358143}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$2$1.class", "size": 2548, "crc": -502968893}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$3$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$3$1$1.class", "size": 2043, "crc": -297332042}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$3$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$3$1.class", "size": 5881, "crc": -434132916}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4$1$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4$1$1$1.class", "size": 1936, "crc": -2022320324}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4$1$showResults$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4$1$showResults$2$1.class", "size": 1670, "crc": 754578829}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$4.class", "size": 19652, "crc": 45397946}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$5$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$5$1.class", "size": 4233, "crc": 213565668}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$6$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$6$1.class", "size": 2028, "crc": -509877257}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$7.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$7.class", "size": 5265, "crc": -1537170759}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$animatedShape$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$animatedShape$1$1.class", "size": 4761, "crc": 668971499}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$topPadding$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$topPadding$1$1.class", "size": 3346, "crc": 121946380}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBar$useFullScreenShape$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBar$useFullScreenShape$2$1.class", "size": 2149, "crc": -906644819}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$2$1.class", "size": 2255, "crc": 18460383}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$3$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$3$1$1.class", "size": 1514, "crc": -1636011792}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$3$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$3$1.class", "size": 2895, "crc": 260621632}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$4$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$4$1.class", "size": 2283, "crc": -965536977}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$5$1$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$5$1$1.class", "size": 9588, "crc": -1090068039}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$5$2$1.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$5$2$1.class", "size": 9830, "crc": -587240168}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$5.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$5.class", "size": 8050, "crc": 2133000293}, {"key": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$6.class", "name": "androidx/compose/material3/SearchBar_androidKt$SearchBarInputField$6.class", "size": 4291, "crc": -1996317040}, {"key": "androidx/compose/material3/SearchBar_androidKt.class", "name": "androidx/compose/material3/SearchBar_androidKt.class", "size": 51756, "crc": -678818257}, {"key": "androidx/compose/material3/SegmentedButtonColors.class", "name": "androidx/compose/material3/SegmentedButtonColors.class", "size": 12491, "crc": 2004406847}, {"key": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$1.class", "name": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$1.class", "size": 4455, "crc": 64683672}, {"key": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$2.class", "name": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy$measure$2.class", "size": 5061, "crc": -723048800}, {"key": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy.class", "name": "androidx/compose/material3/SegmentedButtonContentMeasurePolicy.class", "size": 10825, "crc": -1665584431}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$ActiveIcon$1.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$ActiveIcon$1.class", "size": 1705, "crc": -1935366340}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$Icon$1.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$Icon$1.class", "size": 3036, "crc": -1942253839}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$Icon$2.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$Icon$2.class", "size": 3395, "crc": 2079532114}, {"key": "androidx/compose/material3/SegmentedButtonDefaults$Icon$3.class", "name": "androidx/compose/material3/SegmentedButtonDefaults$Icon$3.class", "size": 2504, "crc": -1447580936}, {"key": "androidx/compose/material3/SegmentedButtonDefaults.class", "name": "androidx/compose/material3/SegmentedButtonDefaults.class", "size": 16147, "crc": -887395605}, {"key": "androidx/compose/material3/SegmentedButtonKt$MultiChoiceSegmentedButtonRow$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$MultiChoiceSegmentedButtonRow$2.class", "size": 2420, "crc": -1358776968}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$2.class", "size": 2781, "crc": -68924374}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$3.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$3.class", "size": 3341, "crc": -1841226123}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$4.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$4.class", "size": 4192, "crc": 1846648366}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$6.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$6.class", "size": 2784, "crc": 1481154870}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$7.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$7.class", "size": 2359, "crc": 2076612438}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$8.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$8.class", "size": 3343, "crc": -1491484310}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$9.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButton$9.class", "size": 4150, "crc": 1211979821}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$1$1.class", "size": 12531, "crc": 1000892196}, {"key": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$SegmentedButtonContent$2.class", "size": 2242, "crc": -2004549271}, {"key": "androidx/compose/material3/SegmentedButtonKt$SingleChoiceSegmentedButtonRow$2.class", "name": "androidx/compose/material3/SegmentedButtonKt$SingleChoiceSegmentedButtonRow$2.class", "size": 2425, "crc": -658894564}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1$1.class", "size": 2678, "crc": -1469721932}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionCountAsState$1$1.class", "size": 4311, "crc": -1726320842}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1$1.class", "size": 2473, "crc": 14371746}, {"key": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1.class", "name": "androidx/compose/material3/SegmentedButtonKt$interactionZIndex$1.class", "size": 3096, "crc": 1627476503}, {"key": "androidx/compose/material3/SegmentedButtonKt.class", "name": "androidx/compose/material3/SegmentedButtonKt.class", "size": 37552, "crc": 510531951}, {"key": "androidx/compose/material3/SelectableChipColors.class", "name": "androidx/compose/material3/SelectableChipColors.class", "size": 13228, "crc": 1313661448}, {"key": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1$1.class", "name": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1$1.class", "size": 4204, "crc": 516785583}, {"key": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1.class", "name": "androidx/compose/material3/SelectableChipElevation$animateElevation$1$1.class", "size": 4491, "crc": -58226102}, {"key": "androidx/compose/material3/SelectableChipElevation$animateElevation$2$1.class", "name": "androidx/compose/material3/SelectableChipElevation$animateElevation$2$1.class", "size": 5483, "crc": 968617966}, {"key": "androidx/compose/material3/SelectableChipElevation.class", "name": "androidx/compose/material3/SelectableChipElevation.class", "size": 12966, "crc": -2082949357}, {"key": "androidx/compose/material3/SelectableDates.class", "name": "androidx/compose/material3/SelectableDates.class", "size": 890, "crc": -268256655}, {"key": "androidx/compose/material3/SelectedRangeInfo$Companion.class", "name": "androidx/compose/material3/SelectedRangeInfo$Companion.class", "size": 2509, "crc": 2004657892}, {"key": "androidx/compose/material3/SelectedRangeInfo.class", "name": "androidx/compose/material3/SelectedRangeInfo.class", "size": 2151, "crc": -1674309331}, {"key": "androidx/compose/material3/Selection$Companion.class", "name": "androidx/compose/material3/Selection$Companion.class", "size": 1211, "crc": 793168687}, {"key": "androidx/compose/material3/Selection.class", "name": "androidx/compose/material3/Selection.class", "size": 2697, "crc": -1531680045}, {"key": "androidx/compose/material3/ShapeDefaults.class", "name": "androidx/compose/material3/ShapeDefaults.class", "size": 2147, "crc": -543149424}, {"key": "androidx/compose/material3/Shapes.class", "name": "androidx/compose/material3/Shapes.class", "size": 5243, "crc": 368641808}, {"key": "androidx/compose/material3/ShapesKt$LocalShapes$1.class", "name": "androidx/compose/material3/ShapesKt$LocalShapes$1.class", "size": 1573, "crc": 558691532}, {"key": "androidx/compose/material3/ShapesKt$WhenMappings.class", "name": "androidx/compose/material3/ShapesKt$WhenMappings.class", "size": 1325, "crc": 789984016}, {"key": "androidx/compose/material3/ShapesKt.class", "name": "androidx/compose/material3/ShapesKt.class", "size": 6492, "crc": 1421432569}, {"key": "androidx/compose/material3/SheetDefaultsKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "name": "androidx/compose/material3/SheetDefaultsKt$ConsumeSwipeWithinBottomSheetBoundsNestedScrollConnection$1.class", "size": 5875, "crc": 1893988648}, {"key": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$1.class", "name": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$1.class", "size": 1631, "crc": -433628723}, {"key": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$2$1.class", "name": "androidx/compose/material3/SheetDefaultsKt$rememberSheetState$2$1.class", "size": 2221, "crc": -1527465135}, {"key": "androidx/compose/material3/SheetDefaultsKt.class", "name": "androidx/compose/material3/SheetDefaultsKt.class", "size": 8766, "crc": 1019391670}, {"key": "androidx/compose/material3/SheetState$1.class", "name": "androidx/compose/material3/SheetState$1.class", "size": 1636, "crc": 98457637}, {"key": "androidx/compose/material3/SheetState$2.class", "name": "androidx/compose/material3/SheetState$2.class", "size": 1670, "crc": -476649993}, {"key": "androidx/compose/material3/SheetState$Companion$Saver$1.class", "name": "androidx/compose/material3/SheetState$Companion$Saver$1.class", "size": 2080, "crc": 2009171998}, {"key": "androidx/compose/material3/SheetState$Companion$Saver$2.class", "name": "androidx/compose/material3/SheetState$Companion$Saver$2.class", "size": 2438, "crc": -1485905679}, {"key": "androidx/compose/material3/SheetState$Companion$Saver$3.class", "name": "androidx/compose/material3/SheetState$Companion$Saver$3.class", "size": 2046, "crc": -472932030}, {"key": "androidx/compose/material3/SheetState$Companion$Saver$4.class", "name": "androidx/compose/material3/SheetState$Companion$Saver$4.class", "size": 2217, "crc": -422084189}, {"key": "androidx/compose/material3/SheetState$Companion.class", "name": "androidx/compose/material3/SheetState$Companion.class", "size": 3457, "crc": -2006961189}, {"key": "androidx/compose/material3/SheetState$anchoredDraggableState$1.class", "name": "androidx/compose/material3/SheetState$anchoredDraggableState$1.class", "size": 2802, "crc": 2108122063}, {"key": "androidx/compose/material3/SheetState$anchoredDraggableState$2.class", "name": "androidx/compose/material3/SheetState$anchoredDraggableState$2.class", "size": 2663, "crc": 1929791981}, {"key": "androidx/compose/material3/SheetState.class", "name": "androidx/compose/material3/SheetState.class", "size": 12048, "crc": -1011105099}, {"key": "androidx/compose/material3/SheetValue.class", "name": "androidx/compose/material3/SheetValue.class", "size": 1558, "crc": 1581698191}, {"key": "androidx/compose/material3/SingleChoiceSegmentedButtonRowScope.class", "name": "androidx/compose/material3/SingleChoiceSegmentedButtonRowScope.class", "size": 639, "crc": 2068213649}, {"key": "androidx/compose/material3/SingleChoiceSegmentedButtonScopeWrapper.class", "name": "androidx/compose/material3/SingleChoiceSegmentedButtonScopeWrapper.class", "size": 3181, "crc": 1849670654}, {"key": "androidx/compose/material3/SliderColors.class", "name": "androidx/compose/material3/SliderColors.class", "size": 10418, "crc": -458405108}, {"key": "androidx/compose/material3/SliderComponents.class", "name": "androidx/compose/material3/SliderComponents.class", "size": 1410, "crc": 1577809743}, {"key": "androidx/compose/material3/SliderDefaults$Thumb$1$1$1.class", "name": "androidx/compose/material3/SliderDefaults$Thumb$1$1$1.class", "size": 3356, "crc": 1231051860}, {"key": "androidx/compose/material3/SliderDefaults$Thumb$1$1.class", "name": "androidx/compose/material3/SliderDefaults$Thumb$1$1.class", "size": 4458, "crc": 1623894654}, {"key": "androidx/compose/material3/SliderDefaults$Thumb$2.class", "name": "androidx/compose/material3/SliderDefaults$Thumb$2.class", "size": 2426, "crc": -1934277584}, {"key": "androidx/compose/material3/SliderDefaults$Track$1$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$1$1.class", "size": 8902, "crc": 1831001515}, {"key": "androidx/compose/material3/SliderDefaults$Track$2.class", "name": "androidx/compose/material3/SliderDefaults$Track$2.class", "size": 2293, "crc": 2066090539}, {"key": "androidx/compose/material3/SliderDefaults$Track$3$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$3$1.class", "size": 2354, "crc": -251155658}, {"key": "androidx/compose/material3/SliderDefaults$Track$4.class", "name": "androidx/compose/material3/SliderDefaults$Track$4.class", "size": 2277, "crc": -829978027}, {"key": "androidx/compose/material3/SliderDefaults$Track$5$1.class", "name": "androidx/compose/material3/SliderDefaults$Track$5$1.class", "size": 2461, "crc": 1977973401}, {"key": "androidx/compose/material3/SliderDefaults$Track$6.class", "name": "androidx/compose/material3/SliderDefaults$Track$6.class", "size": 2297, "crc": -1300238985}, {"key": "androidx/compose/material3/SliderDefaults.class", "name": "androidx/compose/material3/SliderDefaults.class", "size": 24995, "crc": 2037847111}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$1.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$1.class", "size": 3459, "crc": 1590680885}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$10$1.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$10$1.class", "size": 2337, "crc": 1894243065}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$11.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$11.class", "size": 4888, "crc": 468526585}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$14.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$14.class", "size": 3482, "crc": -145232686}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$15.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$15.class", "size": 3480, "crc": 607888933}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$16.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$16.class", "size": 3404, "crc": 942645294}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$18.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$18.class", "size": 3868, "crc": -927058130}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$2.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$2.class", "size": 3457, "crc": 1494898142}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$3.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$3.class", "size": 3381, "crc": 1842530120}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$4.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$4.class", "size": 3242, "crc": 1696322781}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$7.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$7.class", "size": 3581, "crc": 1271208678}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$8.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$8.class", "size": 3579, "crc": -409505327}, {"key": "androidx/compose/material3/SliderKt$RangeSlider$9.class", "name": "androidx/compose/material3/SliderKt$RangeSlider$9.class", "size": 3503, "crc": 1715388581}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$1$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$1$1.class", "size": 2085, "crc": -776986941}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$3$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$1$3$1.class", "size": 2083, "crc": -484587122}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1$1.class", "size": 2537, "crc": -751062696}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$2$1.class", "size": 7469, "crc": 1965185547}, {"key": "androidx/compose/material3/SliderKt$RangeSliderImpl$3.class", "name": "androidx/compose/material3/SliderKt$RangeSliderImpl$3.class", "size": 3640, "crc": 196966659}, {"key": "androidx/compose/material3/SliderKt$Slider$10.class", "name": "androidx/compose/material3/SliderKt$Slider$10.class", "size": 3334, "crc": -1879888012}, {"key": "androidx/compose/material3/SliderKt$Slider$11.class", "name": "androidx/compose/material3/SliderKt$Slider$11.class", "size": 3251, "crc": 472112969}, {"key": "androidx/compose/material3/SliderKt$Slider$13.class", "name": "androidx/compose/material3/SliderKt$Slider$13.class", "size": 3277, "crc": -1462763574}, {"key": "androidx/compose/material3/SliderKt$Slider$2.class", "name": "androidx/compose/material3/SliderKt$Slider$2.class", "size": 3440, "crc": 1848373987}, {"key": "androidx/compose/material3/SliderKt$Slider$3.class", "name": "androidx/compose/material3/SliderKt$Slider$3.class", "size": 3357, "crc": -1980660576}, {"key": "androidx/compose/material3/SliderKt$Slider$4.class", "name": "androidx/compose/material3/SliderKt$Slider$4.class", "size": 3329, "crc": 872016009}, {"key": "androidx/compose/material3/SliderKt$Slider$6.class", "name": "androidx/compose/material3/SliderKt$Slider$6.class", "size": 3399, "crc": -554786613}, {"key": "androidx/compose/material3/SliderKt$Slider$7.class", "name": "androidx/compose/material3/SliderKt$Slider$7.class", "size": 3316, "crc": 1269855820}, {"key": "androidx/compose/material3/SliderKt$Slider$8.class", "name": "androidx/compose/material3/SliderKt$Slider$8.class", "size": 4091, "crc": 1378804339}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$2$1$1.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$2$1$1.class", "size": 2271, "crc": 2122771436}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$2$1.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$2$1.class", "size": 6027, "crc": 265117007}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$3.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$3.class", "size": 3044, "crc": 1743412354}, {"key": "androidx/compose/material3/SliderKt$SliderImpl$drag$1$1.class", "name": "androidx/compose/material3/SliderKt$SliderImpl$drag$1$1.class", "size": 3487, "crc": 605986980}, {"key": "androidx/compose/material3/SliderKt$awaitSlop$1.class", "name": "androidx/compose/material3/SliderKt$awaitSlop$1.class", "size": 1564, "crc": 1383934450}, {"key": "androidx/compose/material3/SliderKt$awaitSlop$postPointerSlop$1.class", "name": "androidx/compose/material3/SliderKt$awaitSlop$postPointerSlop$1.class", "size": 2007, "crc": 973408972}, {"key": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1$1.class", "size": 4267, "crc": 816437240}, {"key": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderEndThumbSemantics$1.class", "size": 2595, "crc": -1644635052}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$2.class", "size": 4541, "crc": 427769765}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1$finishInteraction$success$1.class", "size": 2577, "crc": -723639762}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1$1.class", "size": 10277, "crc": 996955553}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1$1.class", "size": 4401, "crc": 1046782167}, {"key": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderPressDragModifier$1.class", "size": 5062, "crc": -797519039}, {"key": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1$1.class", "size": 4293, "crc": -755240971}, {"key": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1.class", "name": "androidx/compose/material3/SliderKt$rangeSliderStartThumbSemantics$1.class", "size": 2603, "crc": 1515669135}, {"key": "androidx/compose/material3/SliderKt$sliderSemantics$1$1.class", "name": "androidx/compose/material3/SliderKt$sliderSemantics$1$1.class", "size": 3047, "crc": 622522058}, {"key": "androidx/compose/material3/SliderKt$sliderSemantics$1.class", "name": "androidx/compose/material3/SliderKt$sliderSemantics$1.class", "size": 2160, "crc": -686308667}, {"key": "androidx/compose/material3/SliderKt$sliderTapModifier$1$1.class", "name": "androidx/compose/material3/SliderKt$sliderTapModifier$1$1.class", "size": 3428, "crc": -466987256}, {"key": "androidx/compose/material3/SliderKt$sliderTapModifier$1$2.class", "name": "androidx/compose/material3/SliderKt$sliderTapModifier$1$2.class", "size": 1722, "crc": 1122116836}, {"key": "androidx/compose/material3/SliderKt$sliderTapModifier$1.class", "name": "androidx/compose/material3/SliderKt$sliderTapModifier$1.class", "size": 4321, "crc": 678178294}, {"key": "androidx/compose/material3/SliderKt.class", "name": "androidx/compose/material3/SliderKt.class", "size": 75298, "crc": -341718064}, {"key": "androidx/compose/material3/SliderPositions.class", "name": "androidx/compose/material3/SliderPositions.class", "size": 4867, "crc": 352447174}, {"key": "androidx/compose/material3/SliderRange$Companion.class", "name": "androidx/compose/material3/SliderRange$Companion.class", "size": 1254, "crc": -574250146}, {"key": "androidx/compose/material3/SliderRange.class", "name": "androidx/compose/material3/SliderRange.class", "size": 4721, "crc": -878846924}, {"key": "androidx/compose/material3/SliderState$drag$2.class", "name": "androidx/compose/material3/SliderState$drag$2.class", "size": 4618, "crc": 1336286039}, {"key": "androidx/compose/material3/SliderState$dragScope$1.class", "name": "androidx/compose/material3/SliderState$dragScope$1.class", "size": 1148, "crc": 2035543968}, {"key": "androidx/compose/material3/SliderState$gestureEndAction$1.class", "name": "androidx/compose/material3/SliderState$gestureEndAction$1.class", "size": 1435, "crc": 457307102}, {"key": "androidx/compose/material3/SliderState.class", "name": "androidx/compose/material3/SliderState.class", "size": 15355, "crc": -1319429140}, {"key": "androidx/compose/material3/SnackbarData.class", "name": "androidx/compose/material3/SnackbarData.class", "size": 779, "crc": -1081789074}, {"key": "androidx/compose/material3/SnackbarDefaults.class", "name": "androidx/compose/material3/SnackbarDefaults.class", "size": 4693, "crc": -1071588426}, {"key": "androidx/compose/material3/SnackbarDuration.class", "name": "androidx/compose/material3/SnackbarDuration.class", "size": 1479, "crc": 927455586}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1$1.class", "size": 1576, "crc": -419846082}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$1$1.class", "size": 2437, "crc": 822472958}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1$1.class", "size": 2175, "crc": -260243709}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1$opacity$1$1.class", "size": 2449, "crc": -151515084}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$1$1.class", "size": 14377, "crc": 1576923973}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$2$1$1.class", "size": 3319, "crc": -613379067}, {"key": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "name": "androidx/compose/material3/SnackbarHostKt$FadeInFadeOutWithScale$3.class", "size": 2534, "crc": -196539452}, {"key": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$1$1.class", "size": 4460, "crc": 72459445}, {"key": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$2.class", "name": "androidx/compose/material3/SnackbarHostKt$SnackbarHost$2.class", "size": 2490, "crc": -477887326}, {"key": "androidx/compose/material3/SnackbarHostKt$WhenMappings.class", "name": "androidx/compose/material3/SnackbarHostKt$WhenMappings.class", "size": 869, "crc": -2096692653}, {"key": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$1.class", "name": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$1.class", "size": 1194, "crc": 23707154}, {"key": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$2$1.class", "name": "androidx/compose/material3/SnackbarHostKt$animatedOpacity$2$1.class", "size": 4814, "crc": 1458723836}, {"key": "androidx/compose/material3/SnackbarHostKt$animatedScale$1$1.class", "name": "androidx/compose/material3/SnackbarHostKt$animatedScale$1$1.class", "size": 4486, "crc": -398169945}, {"key": "androidx/compose/material3/SnackbarHostKt.class", "name": "androidx/compose/material3/SnackbarHostKt.class", "size": 24880, "crc": -1675976665}, {"key": "androidx/compose/material3/SnackbarHostState$SnackbarDataImpl.class", "name": "androidx/compose/material3/SnackbarHostState$SnackbarDataImpl.class", "size": 3080, "crc": 392740051}, {"key": "androidx/compose/material3/SnackbarHostState$SnackbarVisualsImpl.class", "name": "androidx/compose/material3/SnackbarHostState$SnackbarVisualsImpl.class", "size": 2868, "crc": 2062351211}, {"key": "androidx/compose/material3/SnackbarHostState$showSnackbar$2.class", "name": "androidx/compose/material3/SnackbarHostState$showSnackbar$2.class", "size": 1941, "crc": -18543649}, {"key": "androidx/compose/material3/SnackbarHostState.class", "name": "androidx/compose/material3/SnackbarHostState.class", "size": 8873, "crc": 1501292763}, {"key": "androidx/compose/material3/SnackbarKt$NewLineButtonSnackbar$2.class", "name": "androidx/compose/material3/SnackbarKt$NewLineButtonSnackbar$2.class", "size": 2796, "crc": -1939660508}, {"key": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1$4.class", "name": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1$4.class", "size": 2518, "crc": 620581135}, {"key": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1.class", "name": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$2$1.class", "size": 9226, "crc": -180287988}, {"key": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$3.class", "name": "androidx/compose/material3/SnackbarKt$OneRowSnackbar$3.class", "size": 2758, "crc": 699172589}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$1$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$1$1.class", "size": 3949, "crc": -1070750950}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$1.class", "size": 5118, "crc": -502168489}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$2.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$2.class", "size": 3112, "crc": -322601755}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$3.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$3.class", "size": 3154, "crc": -725694185}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$4.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$4.class", "size": 2444, "crc": 66755046}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$1$1.class", "size": 1336, "crc": -473299760}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$2.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1$2.class", "size": 3149, "crc": -344741198}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$actionComposable$1.class", "size": 5670, "crc": -1466810458}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1$1$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1$1$1.class", "size": 1351, "crc": -131097538}, {"key": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1.class", "name": "androidx/compose/material3/SnackbarKt$Snackbar$dismissActionComposable$1.class", "size": 4893, "crc": -1112392368}, {"key": "androidx/compose/material3/SnackbarKt.class", "name": "androidx/compose/material3/SnackbarKt.class", "size": 39858, "crc": -498073108}, {"key": "androidx/compose/material3/SnackbarResult.class", "name": "androidx/compose/material3/SnackbarResult.class", "size": 1418, "crc": 2080037151}, {"key": "androidx/compose/material3/SnackbarVisuals.class", "name": "androidx/compose/material3/SnackbarVisuals.class", "size": 1031, "crc": -1403874004}, {"key": "androidx/compose/material3/SnapFlingBehavior$animateDecay$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$animateDecay$1.class", "size": 2271, "crc": -951863664}, {"key": "androidx/compose/material3/SnapFlingBehavior$animateDecay$2.class", "name": "androidx/compose/material3/SnapFlingBehavior$animateDecay$2.class", "size": 3182, "crc": -1188626342}, {"key": "androidx/compose/material3/SnapFlingBehavior$animateSnap$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$animateSnap$1.class", "size": 2325, "crc": 1786530188}, {"key": "androidx/compose/material3/SnapFlingBehavior$animateSnap$2.class", "name": "androidx/compose/material3/SnapFlingBehavior$animateSnap$2.class", "size": 3075, "crc": -1792830079}, {"key": "androidx/compose/material3/SnapFlingBehavior$fling$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$fling$1.class", "size": 1913, "crc": 998610806}, {"key": "androidx/compose/material3/SnapFlingBehavior$fling$result$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$fling$result$1.class", "size": 4591, "crc": 1693379777}, {"key": "androidx/compose/material3/SnapFlingBehavior$longSnap$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$longSnap$1.class", "size": 1999, "crc": 2083248613}, {"key": "androidx/compose/material3/SnapFlingBehavior$motionScaleDuration$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$motionScaleDuration$1.class", "size": 3558, "crc": 1371724471}, {"key": "androidx/compose/material3/SnapFlingBehavior$performFling$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$performFling$1.class", "size": 1769, "crc": 1038968172}, {"key": "androidx/compose/material3/SnapFlingBehavior$runApproach$1.class", "name": "androidx/compose/material3/SnapFlingBehavior$runApproach$1.class", "size": 1969, "crc": 651717219}, {"key": "androidx/compose/material3/SnapFlingBehavior.class", "name": "androidx/compose/material3/SnapFlingBehavior.class", "size": 25415, "crc": 27448855}, {"key": "androidx/compose/material3/Strings$Companion.class", "name": "androidx/compose/material3/Strings$Companion.class", "size": 17393, "crc": 2009925688}, {"key": "androidx/compose/material3/Strings.class", "name": "androidx/compose/material3/Strings.class", "size": 2521, "crc": 1990998622}, {"key": "androidx/compose/material3/Strings_androidKt.class", "name": "androidx/compose/material3/Strings_androidKt.class", "size": 4922, "crc": -1001080568}, {"key": "androidx/compose/material3/SuggestionChipDefaults.class", "name": "androidx/compose/material3/SuggestionChipDefaults.class", "size": 13062, "crc": 492042061}, {"key": "androidx/compose/material3/SurfaceKt$LocalAbsoluteTonalElevation$1.class", "name": "androidx/compose/material3/SurfaceKt$LocalAbsoluteTonalElevation$1.class", "size": 2054, "crc": -1125438549}, {"key": "androidx/compose/material3/SurfaceKt$Surface$1$2.class", "name": "androidx/compose/material3/SurfaceKt$Surface$1$2.class", "size": 1713, "crc": 741605838}, {"key": "androidx/compose/material3/SurfaceKt$Surface$1$3.class", "name": "androidx/compose/material3/SurfaceKt$Surface$1$3.class", "size": 3179, "crc": 831009491}, {"key": "androidx/compose/material3/SurfaceKt$Surface$1.class", "name": "androidx/compose/material3/SurfaceKt$Surface$1.class", "size": 11733, "crc": -575185579}, {"key": "androidx/compose/material3/SurfaceKt$Surface$3.class", "name": "androidx/compose/material3/SurfaceKt$Surface$3.class", "size": 12400, "crc": 1490809330}, {"key": "androidx/compose/material3/SurfaceKt$Surface$5.class", "name": "androidx/compose/material3/SurfaceKt$Surface$5.class", "size": 12451, "crc": -187289348}, {"key": "androidx/compose/material3/SurfaceKt$Surface$7.class", "name": "androidx/compose/material3/SurfaceKt$Surface$7.class", "size": 12532, "crc": 592500723}, {"key": "androidx/compose/material3/SurfaceKt.class", "name": "androidx/compose/material3/SurfaceKt.class", "size": 19665, "crc": 1945989062}, {"key": "androidx/compose/material3/SwipeToDismissAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/SwipeToDismissAnchorsElement$inspectableProperties$$inlined$debugInspectorInfo$1.class", "size": 3418, "crc": -1347065925}, {"key": "androidx/compose/material3/SwipeToDismissAnchorsElement.class", "name": "androidx/compose/material3/SwipeToDismissAnchorsElement.class", "size": 5284, "crc": -428164542}, {"key": "androidx/compose/material3/SwipeToDismissAnchorsNode$measure$1.class", "name": "androidx/compose/material3/SwipeToDismissAnchorsNode$measure$1.class", "size": 3095, "crc": -633459434}, {"key": "androidx/compose/material3/SwipeToDismissAnchorsNode$measure$newAnchors$1.class", "name": "androidx/compose/material3/SwipeToDismissAnchorsNode$measure$newAnchors$1.class", "size": 2427, "crc": -1797172562}, {"key": "androidx/compose/material3/SwipeToDismissAnchorsNode.class", "name": "androidx/compose/material3/SwipeToDismissAnchorsNode.class", "size": 4694, "crc": 350993922}, {"key": "androidx/compose/material3/SwipeToDismissBoxDefaults$positionalThreshold$1$1$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxDefaults$positionalThreshold$1$1$1.class", "size": 2583, "crc": 1252406194}, {"key": "androidx/compose/material3/SwipeToDismissBoxDefaults.class", "name": "androidx/compose/material3/SwipeToDismissBoxDefaults.class", "size": 5039, "crc": -1766285944}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismiss$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismiss$1.class", "size": 3067, "crc": 561402627}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$2.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$SwipeToDismissBox$2.class", "size": 2976, "crc": 1024479880}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$1.class", "size": 1785, "crc": 1634226702}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$2$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt$rememberSwipeToDismissBoxState$2$1.class", "size": 2524, "crc": 47744368}, {"key": "androidx/compose/material3/SwipeToDismissBoxKt.class", "name": "androidx/compose/material3/SwipeToDismissBoxKt.class", "size": 24942, "crc": -897377607}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$1.class", "size": 1800, "crc": -247428532}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$1.class", "size": 2247, "crc": 89681253}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$2.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$Companion$Saver$2.class", "size": 2792, "crc": 1857206491}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$Companion.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$Companion.class", "size": 2775, "crc": 358473098}, {"key": "androidx/compose/material3/SwipeToDismissBoxState$anchoredDraggableState$1.class", "name": "androidx/compose/material3/SwipeToDismissBoxState$anchoredDraggableState$1.class", "size": 2467, "crc": 501632344}, {"key": "androidx/compose/material3/SwipeToDismissBoxState.class", "name": "androidx/compose/material3/SwipeToDismissBoxState.class", "size": 7478, "crc": -1301242160}, {"key": "androidx/compose/material3/SwipeToDismissBoxValue.class", "name": "androidx/compose/material3/SwipeToDismissBoxValue.class", "size": 1630, "crc": -2083139214}, {"key": "androidx/compose/material3/SwitchColors.class", "name": "androidx/compose/material3/SwitchColors.class", "size": 15607, "crc": -753442583}, {"key": "androidx/compose/material3/SwitchDefaults.class", "name": "androidx/compose/material3/SwitchDefaults.class", "size": 8484, "crc": 1465825856}, {"key": "androidx/compose/material3/SwitchKt$Switch$2$1.class", "name": "androidx/compose/material3/SwitchKt$Switch$2$1.class", "size": 1958, "crc": 281041369}, {"key": "androidx/compose/material3/SwitchKt$Switch$3$1$1.class", "name": "androidx/compose/material3/SwitchKt$Switch$3$1$1.class", "size": 4258, "crc": 12701859}, {"key": "androidx/compose/material3/SwitchKt$Switch$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/SwitchKt$Switch$3$1$invoke$$inlined$onDispose$1.class", "size": 1826, "crc": -1269367172}, {"key": "androidx/compose/material3/SwitchKt$Switch$3$1.class", "name": "androidx/compose/material3/SwitchKt$Switch$3$1.class", "size": 3926, "crc": -1650437547}, {"key": "androidx/compose/material3/SwitchKt$Switch$5.class", "name": "androidx/compose/material3/SwitchKt$Switch$5.class", "size": 3080, "crc": -409209138}, {"key": "androidx/compose/material3/SwitchKt$Switch$valueToOffset$1$1.class", "name": "androidx/compose/material3/SwitchKt$Switch$valueToOffset$1$1.class", "size": 1709, "crc": 379055524}, {"key": "androidx/compose/material3/SwitchKt$SwitchImpl$1$1$1.class", "name": "androidx/compose/material3/SwitchKt$SwitchImpl$1$1$1.class", "size": 1968, "crc": -499356301}, {"key": "androidx/compose/material3/SwitchKt$SwitchImpl$2.class", "name": "androidx/compose/material3/SwitchKt$SwitchImpl$2.class", "size": 3528, "crc": -259583606}, {"key": "androidx/compose/material3/SwitchKt.class", "name": "androidx/compose/material3/SwitchKt.class", "size": 35527, "crc": 813676915}, {"key": "androidx/compose/material3/SystemBarsDefaultInsets_androidKt.class", "name": "androidx/compose/material3/SystemBarsDefaultInsets_androidKt.class", "size": 2096, "crc": -578496342}, {"key": "androidx/compose/material3/TabIndicatorModifier.class", "name": "androidx/compose/material3/TabIndicatorModifier.class", "size": 5615, "crc": -1950133337}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$1.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$1.class", "size": 1726, "crc": 852123697}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$2.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$2.class", "size": 4191, "crc": 1695384320}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$3.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$3.class", "size": 4192, "crc": -935263648}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode$measure$4.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode$measure$4.class", "size": 2400, "crc": -1255774260}, {"key": "androidx/compose/material3/TabIndicatorOffsetNode.class", "name": "androidx/compose/material3/TabIndicatorOffsetNode.class", "size": 7707, "crc": -1132920390}, {"key": "androidx/compose/material3/TabIndicatorScope.class", "name": "androidx/compose/material3/TabIndicatorScope.class", "size": 2240, "crc": 251449341}, {"key": "androidx/compose/material3/TabKt$LeadingIconTab$2.class", "name": "androidx/compose/material3/TabKt$LeadingIconTab$2.class", "size": 14222, "crc": 783404269}, {"key": "androidx/compose/material3/TabKt$LeadingIconTab$3.class", "name": "androidx/compose/material3/TabKt$LeadingIconTab$3.class", "size": 3188, "crc": 907576915}, {"key": "androidx/compose/material3/TabKt$Tab$2.class", "name": "androidx/compose/material3/TabKt$Tab$2.class", "size": 3354, "crc": -58591026}, {"key": "androidx/compose/material3/TabKt$Tab$3.class", "name": "androidx/compose/material3/TabKt$Tab$3.class", "size": 3155, "crc": 459075184}, {"key": "androidx/compose/material3/TabKt$Tab$5.class", "name": "androidx/compose/material3/TabKt$Tab$5.class", "size": 10826, "crc": -846508954}, {"key": "androidx/compose/material3/TabKt$Tab$6.class", "name": "androidx/compose/material3/TabKt$Tab$6.class", "size": 3034, "crc": -552442761}, {"key": "androidx/compose/material3/TabKt$Tab$styledText$1$1.class", "name": "androidx/compose/material3/TabKt$Tab$styledText$1$1.class", "size": 4742, "crc": 1763025808}, {"key": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1$1.class", "name": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1$1.class", "size": 2979, "crc": -924431272}, {"key": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1.class", "name": "androidx/compose/material3/TabKt$TabBaselineLayout$2$1.class", "size": 7181, "crc": -1513198938}, {"key": "androidx/compose/material3/TabKt$TabBaselineLayout$3.class", "name": "androidx/compose/material3/TabKt$TabBaselineLayout$3.class", "size": 2171, "crc": 1552610163}, {"key": "androidx/compose/material3/TabKt$TabTransition$1.class", "name": "androidx/compose/material3/TabKt$TabTransition$1.class", "size": 2129, "crc": 640664002}, {"key": "androidx/compose/material3/TabKt$TabTransition$color$2.class", "name": "androidx/compose/material3/TabKt$TabTransition$color$2.class", "size": 3644, "crc": -1794227154}, {"key": "androidx/compose/material3/TabKt.class", "name": "androidx/compose/material3/TabKt.class", "size": 37931, "crc": 1288353070}, {"key": "androidx/compose/material3/TabPosition.class", "name": "androidx/compose/material3/TabPosition.class", "size": 3540, "crc": 1023175087}, {"key": "androidx/compose/material3/TabPositionsHolder.class", "name": "androidx/compose/material3/TabPositionsHolder.class", "size": 737, "crc": -854100607}, {"key": "androidx/compose/material3/TabRowDefaults$Indicator$1.class", "name": "androidx/compose/material3/TabRowDefaults$Indicator$1.class", "size": 1999, "crc": 1602101991}, {"key": "androidx/compose/material3/TabRowDefaults$PrimaryIndicator$1.class", "name": "androidx/compose/material3/TabRowDefaults$PrimaryIndicator$1.class", "size": 2227, "crc": -208495768}, {"key": "androidx/compose/material3/TabRowDefaults$SecondaryIndicator$1.class", "name": "androidx/compose/material3/TabRowDefaults$SecondaryIndicator$1.class", "size": 2026, "crc": 1749437472}, {"key": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$$inlined$debugInspectorInfo$1.class", "size": 2789, "crc": 338713159}, {"key": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$2.class", "name": "androidx/compose/material3/TabRowDefaults$tabIndicatorOffset$2.class", "size": 5928, "crc": 675257519}, {"key": "androidx/compose/material3/TabRowDefaults.class", "name": "androidx/compose/material3/TabRowDefaults.class", "size": 13830, "crc": 564203042}, {"key": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$1.class", "size": 5045, "crc": -1019170538}, {"key": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$PrimaryScrollableTabRow$2.class", "size": 3349, "crc": -907256033}, {"key": "androidx/compose/material3/TabRowKt$PrimaryTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$PrimaryTabRow$1.class", "size": 3556, "crc": -886812320}, {"key": "androidx/compose/material3/TabRowKt$PrimaryTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$PrimaryTabRow$2.class", "size": 3015, "crc": -1277723101}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRow$1.class", "size": 3208, "crc": 592849940}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRow$2.class", "size": 3108, "crc": -1784857129}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1$1$1$2$3.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1$1$1$2$3.class", "size": 3379, "crc": 1816351686}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1$1$1$2.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1$1$1$2.class", "size": 7667, "crc": 1182770963}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1$1$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1$1$1.class", "size": 8525, "crc": 201720563}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$1.class", "size": 10154, "crc": 106077210}, {"key": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$2.class", "name": "androidx/compose/material3/TabRowKt$ScrollableTabRowImp$2.class", "size": 3379, "crc": 1450772673}, {"key": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$1.class", "size": 3285, "crc": -1226741237}, {"key": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$SecondaryScrollableTabRow$2.class", "size": 3355, "crc": 1039057404}, {"key": "androidx/compose/material3/TabRowKt$SecondaryTabRow$1.class", "name": "androidx/compose/material3/TabRowKt$SecondaryTabRow$1.class", "size": 3326, "crc": -1457215710}, {"key": "androidx/compose/material3/TabRowKt$SecondaryTabRow$2.class", "name": "androidx/compose/material3/TabRowKt$SecondaryTabRow$2.class", "size": 3021, "crc": -2059488096}, {"key": "androidx/compose/material3/TabRowKt$TabRow$1.class", "name": "androidx/compose/material3/TabRowKt$TabRow$1.class", "size": 3202, "crc": -1016099481}, {"key": "androidx/compose/material3/TabRowKt$TabRow$2.class", "name": "androidx/compose/material3/TabRowKt$TabRow$2.class", "size": 3018, "crc": -529068662}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$1.class", "size": 3014, "crc": 1857698407}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1$2.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1$2.class", "size": 4521, "crc": 259586667}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$2$1.class", "size": 8907, "crc": 92744928}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1$tabIndicatorLayout$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1$tabIndicatorLayout$1.class", "size": 3577, "crc": -745211085}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1$scope$1$1.class", "size": 4378, "crc": -250722243}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$1.class", "size": 11444, "crc": 248674415}, {"key": "androidx/compose/material3/TabRowKt$TabRowImpl$2.class", "name": "androidx/compose/material3/TabRowKt$TabRowImpl$2.class", "size": 2926, "crc": -1773830348}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1$3.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1$3.class", "size": 3409, "crc": 1852288795}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1$1.class", "size": 6869, "crc": 108825312}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1$1$1.class", "size": 8591, "crc": 581695734}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$1.class", "size": 5670, "crc": -1059235084}, {"key": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$2.class", "name": "androidx/compose/material3/TabRowKt$TabRowWithSubcomposeImpl$2.class", "size": 3006, "crc": -806790731}, {"key": "androidx/compose/material3/TabRowKt.class", "name": "androidx/compose/material3/TabRowKt.class", "size": 28290, "crc": -2138136168}, {"key": "androidx/compose/material3/TabSlots.class", "name": "androidx/compose/material3/TabSlots.class", "size": 1426, "crc": -163064338}, {"key": "androidx/compose/material3/TextFieldColors$copy$11.class", "name": "androidx/compose/material3/TextFieldColors$copy$11.class", "size": 1578, "crc": 496965945}, {"key": "androidx/compose/material3/TextFieldColors.class", "name": "androidx/compose/material3/TextFieldColors.class", "size": 58905, "crc": 1416685731}, {"key": "androidx/compose/material3/TextFieldDefaults$ContainerBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$ContainerBox$1.class", "size": 2462, "crc": 1590717660}, {"key": "androidx/compose/material3/TextFieldDefaults$DecorationBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$DecorationBox$1.class", "size": 3580, "crc": 653545410}, {"key": "androidx/compose/material3/TextFieldDefaults$DecorationBox$2.class", "name": "androidx/compose/material3/TextFieldDefaults$DecorationBox$2.class", "size": 5618, "crc": -261769357}, {"key": "androidx/compose/material3/TextFieldDefaults$FilledContainerBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$FilledContainerBox$1.class", "size": 2480, "crc": -1883095229}, {"key": "androidx/compose/material3/TextFieldDefaults$OutlinedBorderContainerBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$OutlinedBorderContainerBox$1.class", "size": 2650, "crc": 977611893}, {"key": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$1.class", "size": 3572, "crc": 99311004}, {"key": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$2.class", "name": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$2.class", "size": 5475, "crc": -1370653578}, {"key": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "name": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$3.class", "size": 3508, "crc": 1972455635}, {"key": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$4.class", "name": "androidx/compose/material3/TextFieldDefaults$OutlinedTextFieldDecorationBox$4.class", "size": 5029, "crc": 1083900607}, {"key": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$1.class", "name": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$1.class", "size": 3618, "crc": -736647719}, {"key": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$2.class", "name": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$2.class", "size": 5645, "crc": -569131620}, {"key": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$3.class", "name": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$3.class", "size": 3554, "crc": 1699028835}, {"key": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$4.class", "name": "androidx/compose/material3/TextFieldDefaults$TextFieldDecorationBox$4.class", "size": 5199, "crc": 1721414190}, {"key": "androidx/compose/material3/TextFieldDefaults$indicatorLine$2.class", "name": "androidx/compose/material3/TextFieldDefaults$indicatorLine$2.class", "size": 3957, "crc": -1234053858}, {"key": "androidx/compose/material3/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TextFieldDefaults$indicatorLine-gv0btCI$$inlined$debugInspectorInfo$1.class", "size": 3857, "crc": -1146772040}, {"key": "androidx/compose/material3/TextFieldDefaults.class", "name": "androidx/compose/material3/TextFieldDefaults.class", "size": 87383, "crc": 591826661}, {"key": "androidx/compose/material3/TextFieldDefaultsKt.class", "name": "androidx/compose/material3/TextFieldDefaultsKt.class", "size": 6055, "crc": 799602912}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$1$1.class", "size": 2297, "crc": -1619578301}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$WhenMappings.class", "size": 870, "crc": 178577121}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$borderContainerWithId$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$borderContainerWithId$1.class", "size": 10393, "crc": -2108347965}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$containerWithId$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$containerWithId$1.class", "size": 9489, "crc": -1046267426}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedLabel$1$1.class", "size": 4876, "crc": 525999317}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedLeading$1$1.class", "size": 2971, "crc": 1889643076}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedPlaceholder$1.class", "size": 10300, "crc": -2034779398}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedPrefix$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedPrefix$1.class", "size": 9980, "crc": -1770653106}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedSuffix$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedSuffix$1.class", "size": 9980, "crc": 964130272}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedSupporting$1$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedSupporting$1$1.class", "size": 3141, "crc": 222426093}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3$decoratedTrailing$1$1.class", "size": 2974, "crc": -906635272}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$3.class", "size": 16501, "crc": -2147413349}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$4.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$4.class", "size": 5457, "crc": -1550293975}, {"key": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "name": "androidx/compose/material3/TextFieldImplKt$CommonDecorationBox$labelColor$1.class", "size": 3932, "crc": 2000980752}, {"key": "androidx/compose/material3/TextFieldImplKt$Decoration$1.class", "name": "androidx/compose/material3/TextFieldImplKt$Decoration$1.class", "size": 2262, "crc": 639692938}, {"key": "androidx/compose/material3/TextFieldImplKt$Decoration$contentWithColor$1.class", "name": "androidx/compose/material3/TextFieldImplKt$Decoration$contentWithColor$1.class", "size": 3336, "crc": 833669954}, {"key": "androidx/compose/material3/TextFieldImplKt$defaultErrorSemantics$1.class", "name": "androidx/compose/material3/TextFieldImplKt$defaultErrorSemantics$1.class", "size": 1837, "crc": -967589301}, {"key": "androidx/compose/material3/TextFieldImplKt.class", "name": "androidx/compose/material3/TextFieldImplKt.class", "size": 24231, "crc": -531444134}, {"key": "androidx/compose/material3/TextFieldKt$TextField$10.class", "name": "androidx/compose/material3/TextFieldKt$TextField$10.class", "size": 5833, "crc": -520043266}, {"key": "androidx/compose/material3/TextFieldKt$TextField$2$1.class", "name": "androidx/compose/material3/TextFieldKt$TextField$2$1.class", "size": 6419, "crc": -1081203934}, {"key": "androidx/compose/material3/TextFieldKt$TextField$2.class", "name": "androidx/compose/material3/TextFieldKt$TextField$2.class", "size": 10352, "crc": -1398797740}, {"key": "androidx/compose/material3/TextFieldKt$TextField$3.class", "name": "androidx/compose/material3/TextFieldKt$TextField$3.class", "size": 6103, "crc": 420003309}, {"key": "androidx/compose/material3/TextFieldKt$TextField$5$1.class", "name": "androidx/compose/material3/TextFieldKt$TextField$5$1.class", "size": 6619, "crc": 1472307691}, {"key": "androidx/compose/material3/TextFieldKt$TextField$5.class", "name": "androidx/compose/material3/TextFieldKt$TextField$5.class", "size": 10584, "crc": 1356561797}, {"key": "androidx/compose/material3/TextFieldKt$TextField$6.class", "name": "androidx/compose/material3/TextFieldKt$TextField$6.class", "size": 6277, "crc": -1471348637}, {"key": "androidx/compose/material3/TextFieldKt$TextField$8.class", "name": "androidx/compose/material3/TextFieldKt$TextField$8.class", "size": 5657, "crc": -1768213878}, {"key": "androidx/compose/material3/TextFieldKt$TextFieldLayout$2.class", "name": "androidx/compose/material3/TextFieldKt$TextFieldLayout$2.class", "size": 4552, "crc": -1677695838}, {"key": "androidx/compose/material3/TextFieldKt$drawIndicatorLine$1.class", "name": "androidx/compose/material3/TextFieldKt$drawIndicatorLine$1.class", "size": 2842, "crc": -713720471}, {"key": "androidx/compose/material3/TextFieldKt.class", "name": "androidx/compose/material3/TextFieldKt.class", "size": 78995, "crc": -659157492}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicHeight$1.class", "size": 1890, "crc": 270401723}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$maxIntrinsicWidth$1.class", "size": 1887, "crc": -1278043439}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$measure$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$measure$1.class", "size": 4658, "crc": 898725890}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicHeight$1.class", "size": 1890, "crc": -1642712113}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy$minIntrinsicWidth$1.class", "size": 1887, "crc": -348789556}, {"key": "androidx/compose/material3/TextFieldMeasurePolicy.class", "name": "androidx/compose/material3/TextFieldMeasurePolicy.class", "size": 30568, "crc": -1525649146}, {"key": "androidx/compose/material3/TextFieldTransitionScope$Transition$1.class", "name": "androidx/compose/material3/TextFieldTransitionScope$Transition$1.class", "size": 3339, "crc": 193991028}, {"key": "androidx/compose/material3/TextFieldTransitionScope$Transition$labelContentColor$2.class", "name": "androidx/compose/material3/TextFieldTransitionScope$Transition$labelContentColor$2.class", "size": 3513, "crc": 1437500175}, {"key": "androidx/compose/material3/TextFieldTransitionScope$Transition$labelProgress$2.class", "name": "androidx/compose/material3/TextFieldTransitionScope$Transition$labelProgress$2.class", "size": 3431, "crc": -961631878}, {"key": "androidx/compose/material3/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "name": "androidx/compose/material3/TextFieldTransitionScope$Transition$labelTextStyleColor$2.class", "size": 3517, "crc": 2124869297}, {"key": "androidx/compose/material3/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "name": "androidx/compose/material3/TextFieldTransitionScope$Transition$placeholderOpacity$2.class", "size": 4059, "crc": 2070039636}, {"key": "androidx/compose/material3/TextFieldTransitionScope$Transition$prefixSuffixOpacity$2.class", "name": "androidx/compose/material3/TextFieldTransitionScope$Transition$prefixSuffixOpacity$2.class", "size": 3443, "crc": 60747730}, {"key": "androidx/compose/material3/TextFieldTransitionScope$WhenMappings.class", "name": "androidx/compose/material3/TextFieldTransitionScope$WhenMappings.class", "size": 891, "crc": 346564325}, {"key": "androidx/compose/material3/TextFieldTransitionScope.class", "name": "androidx/compose/material3/TextFieldTransitionScope.class", "size": 19716, "crc": -498182531}, {"key": "androidx/compose/material3/TextFieldType.class", "name": "androidx/compose/material3/TextFieldType.class", "size": 1399, "crc": -710633676}, {"key": "androidx/compose/material3/TextKt$LocalTextStyle$1.class", "name": "androidx/compose/material3/TextKt$LocalTextStyle$1.class", "size": 1290, "crc": -1466300071}, {"key": "androidx/compose/material3/TextKt$ProvideTextStyle$1.class", "name": "androidx/compose/material3/TextKt$ProvideTextStyle$1.class", "size": 2116, "crc": 2115156873}, {"key": "androidx/compose/material3/TextKt$Text$1.class", "name": "androidx/compose/material3/TextKt$Text$1.class", "size": 4043, "crc": 748765154}, {"key": "androidx/compose/material3/TextKt$Text$2.class", "name": "androidx/compose/material3/TextKt$Text$2.class", "size": 1712, "crc": 349571783}, {"key": "androidx/compose/material3/TextKt$Text$3.class", "name": "androidx/compose/material3/TextKt$Text$3.class", "size": 3990, "crc": 1115649894}, {"key": "androidx/compose/material3/TextKt$Text$4.class", "name": "androidx/compose/material3/TextKt$Text$4.class", "size": 1752, "crc": 1427071083}, {"key": "androidx/compose/material3/TextKt$Text$5.class", "name": "androidx/compose/material3/TextKt$Text$5.class", "size": 4427, "crc": -575660362}, {"key": "androidx/compose/material3/TextKt$Text$6.class", "name": "androidx/compose/material3/TextKt$Text$6.class", "size": 1751, "crc": -926646274}, {"key": "androidx/compose/material3/TextKt$Text$7.class", "name": "androidx/compose/material3/TextKt$Text$7.class", "size": 4374, "crc": -498261071}, {"key": "androidx/compose/material3/TextKt.class", "name": "androidx/compose/material3/TextKt.class", "size": 31603, "crc": 38653476}, {"key": "androidx/compose/material3/TimeFormat_androidKt.class", "name": "androidx/compose/material3/TimeFormat_androidKt.class", "size": 2805, "crc": -1114745755}, {"key": "androidx/compose/material3/TimePickerColors.class", "name": "androidx/compose/material3/TimePickerColors.class", "size": 14594, "crc": 838931033}, {"key": "androidx/compose/material3/TimePickerDefaults.class", "name": "androidx/compose/material3/TimePickerDefaults.class", "size": 7529, "crc": -1271193320}, {"key": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1$1.class", "size": 4373, "crc": 1743111815}, {"key": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1.class", "name": "androidx/compose/material3/TimePickerKt$CircularLayout$1$1.class", "size": 7178, "crc": -584604738}, {"key": "androidx/compose/material3/TimePickerKt$CircularLayout$2.class", "name": "androidx/compose/material3/TimePickerKt$CircularLayout$2.class", "size": 2266, "crc": 2111236457}, {"key": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$1.class", "size": 11071, "crc": -595579306}, {"key": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$2.class", "name": "androidx/compose/material3/TimePickerKt$ClockDisplayNumbers$2.class", "size": 1979, "crc": -351613933}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$1.class", "size": 1743, "crc": 1304350249}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1$1$1$1.class", "size": 1856, "crc": 1430953480}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1$2$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1$2$1$1$1.class", "size": 1935, "crc": 1337511804}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1$2.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1$2.class", "size": 5304, "crc": 951620160}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$2$1$1.class", "size": 7652, "crc": -1062161796}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$2$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$2$1.class", "size": 4047, "crc": 1533900991}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$2.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$2.class", "size": 4456, "crc": -100109126}, {"key": "androidx/compose/material3/TimePickerKt$ClockFace$3.class", "name": "androidx/compose/material3/TimePickerKt$ClockFace$3.class", "size": 1981, "crc": -1042941423}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$1$1.class", "size": 2174, "crc": 982170558}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1$1.class", "size": 4265, "crc": -1469135205}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$2$1$1.class", "size": 2515, "crc": 968592832}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$2$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$2$1.class", "size": 2969, "crc": 1141326510}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$3$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$3$1$1.class", "size": 1861, "crc": -1170220895}, {"key": "androidx/compose/material3/TimePickerKt$ClockText$4.class", "name": "androidx/compose/material3/TimePickerKt$ClockText$4.class", "size": 2011, "crc": 1257233694}, {"key": "androidx/compose/material3/TimePickerKt$DisplaySeparator$1.class", "name": "androidx/compose/material3/TimePickerKt$DisplaySeparator$1.class", "size": 1626, "crc": -1327658866}, {"key": "androidx/compose/material3/TimePickerKt$DisplaySeparator$3.class", "name": "androidx/compose/material3/TimePickerKt$DisplaySeparator$3.class", "size": 1743, "crc": 230635761}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalClockDisplay$2.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalClockDisplay$2.class", "size": 1991, "crc": 1585198710}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$1.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$1.class", "size": 2134, "crc": -1123392602}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1$1.class", "size": 2441, "crc": 1838037639}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalPeriodToggle$measurePolicy$1$1.class", "size": 6818, "crc": -1201136250}, {"key": "androidx/compose/material3/TimePickerKt$HorizontalTimePicker$2.class", "name": "androidx/compose/material3/TimePickerKt$HorizontalTimePicker$2.class", "size": 2209, "crc": 245264519}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$1$1.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$1$1.class", "size": 2127, "crc": -167064002}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$1$1.class", "size": 1573, "crc": 507251505}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$2$1.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$2$2$1.class", "size": 1573, "crc": 211023714}, {"key": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$3.class", "name": "androidx/compose/material3/TimePickerKt$PeriodToggleImpl$3.class", "size": 2579, "crc": 604936227}, {"key": "androidx/compose/material3/TimePickerKt$TimeInput$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInput$1.class", "size": 2110, "crc": 259370647}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$1$1.class", "size": 3202, "crc": -1945029515}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$2$1$1.class", "size": 2035, "crc": -1422940934}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$2$1.class", "size": 2963, "crc": -2033132061}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$3$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$3$1.class", "size": 1966, "crc": -483496704}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$4$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$4$1.class", "size": 3032, "crc": 555539005}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$5$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$5$1$1.class", "size": 2037, "crc": -1630561641}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$5$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$5$1.class", "size": 2806, "crc": -183315487}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$6$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1$6$1.class", "size": 1966, "crc": 667911207}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$1$1.class", "size": 11323, "crc": 1599217785}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$2.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$2.class", "size": 2098, "crc": -615972297}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$hourValue$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$hourValue$2$1.class", "size": 2337, "crc": -1562790637}, {"key": "androidx/compose/material3/TimePickerKt$TimeInputImpl$minuteValue$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeInputImpl$minuteValue$2$1.class", "size": 2315, "crc": 235305141}, {"key": "androidx/compose/material3/TimePickerKt$TimePicker$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePicker$1.class", "size": 2175, "crc": -786765647}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$1$1.class", "size": 2116, "crc": 692789206}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2$1.class", "size": 3592, "crc": 1237014221}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$1$2.class", "size": 6523, "crc": -1490890824}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$2.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$1$2.class", "size": 1917, "crc": -718787868}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$2$1.class", "size": 4175, "crc": 1582100939}, {"key": "androidx/compose/material3/TimePickerKt$TimePickerTextField$3.class", "name": "androidx/compose/material3/TimePickerKt$TimePickerTextField$3.class", "size": 3491, "crc": 2080697211}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$1$1.class", "size": 2232, "crc": -1361970129}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$2$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$2$1$1.class", "size": 3518, "crc": -728779161}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$2$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$2$1.class", "size": 2278, "crc": -1977695644}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$3$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$3$1$1$1.class", "size": 1796, "crc": -995730628}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$3.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$3.class", "size": 11547, "crc": -1796755952}, {"key": "androidx/compose/material3/TimePickerKt$TimeSelector$4.class", "name": "androidx/compose/material3/TimePickerKt$TimeSelector$4.class", "size": 2212, "crc": 204542919}, {"key": "androidx/compose/material3/TimePickerKt$ToggleItem$1$1.class", "name": "androidx/compose/material3/TimePickerKt$ToggleItem$1$1.class", "size": 1843, "crc": 287690336}, {"key": "androidx/compose/material3/TimePickerKt$ToggleItem$2.class", "name": "androidx/compose/material3/TimePickerKt$ToggleItem$2.class", "size": 2778, "crc": -2056589584}, {"key": "androidx/compose/material3/TimePickerKt$VerticalClockDisplay$2.class", "name": "androidx/compose/material3/TimePickerKt$VerticalClockDisplay$2.class", "size": 1983, "crc": -213610352}, {"key": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$1.class", "size": 2126, "crc": 659340384}, {"key": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1$1.class", "size": 2436, "crc": -2102355061}, {"key": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalPeriodToggle$measurePolicy$1$1.class", "size": 6794, "crc": 674083775}, {"key": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$1.class", "name": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$1.class", "size": 1868, "crc": -897571330}, {"key": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$3.class", "name": "androidx/compose/material3/TimePickerKt$VerticalTimePicker$3.class", "size": 2203, "crc": -1938976764}, {"key": "androidx/compose/material3/TimePickerKt$drawSelector$1.class", "name": "androidx/compose/material3/TimePickerKt$drawSelector$1.class", "size": 4799, "crc": -327511214}, {"key": "androidx/compose/material3/TimePickerKt$rememberTimePickerState$1$1.class", "name": "androidx/compose/material3/TimePickerKt$rememberTimePickerState$1$1.class", "size": 1547, "crc": 2144832355}, {"key": "androidx/compose/material3/TimePickerKt$visible$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TimePickerKt$visible$$inlined$debugInspectorInfo$1.class", "size": 2829, "crc": -163085424}, {"key": "androidx/compose/material3/TimePickerKt.class", "name": "androidx/compose/material3/TimePickerKt.class", "size": 117062, "crc": -378856228}, {"key": "androidx/compose/material3/TimePickerLayoutType$Companion.class", "name": "androidx/compose/material3/TimePickerLayoutType$Companion.class", "size": 1279, "crc": 1081499881}, {"key": "androidx/compose/material3/TimePickerLayoutType.class", "name": "androidx/compose/material3/TimePickerLayoutType.class", "size": 2735, "crc": -705239603}, {"key": "androidx/compose/material3/TimePickerState$Companion$Saver$1.class", "name": "androidx/compose/material3/TimePickerState$Companion$Saver$1.class", "size": 2437, "crc": -1203387507}, {"key": "androidx/compose/material3/TimePickerState$Companion$Saver$2.class", "name": "androidx/compose/material3/TimePickerState$Companion$Saver$2.class", "size": 2220, "crc": 451424471}, {"key": "androidx/compose/material3/TimePickerState$Companion.class", "name": "androidx/compose/material3/TimePickerState$Companion.class", "size": 1835, "crc": -1891522468}, {"key": "androidx/compose/material3/TimePickerState$animateToCurrent$1.class", "name": "androidx/compose/material3/TimePickerState$animateToCurrent$1.class", "size": 1791, "crc": -2141946357}, {"key": "androidx/compose/material3/TimePickerState$isAfternoon$2.class", "name": "androidx/compose/material3/TimePickerState$isAfternoon$2.class", "size": 1486, "crc": 835252791}, {"key": "androidx/compose/material3/TimePickerState$onTap$1.class", "name": "androidx/compose/material3/TimePickerState$onTap$1.class", "size": 1923, "crc": -1900861343}, {"key": "androidx/compose/material3/TimePickerState$selectorPos$2.class", "name": "androidx/compose/material3/TimePickerState$selectorPos$2.class", "size": 4411, "crc": -2066245441}, {"key": "androidx/compose/material3/TimePickerState$settle$1.class", "name": "androidx/compose/material3/TimePickerState$settle$1.class", "size": 1737, "crc": -653760235}, {"key": "androidx/compose/material3/TimePickerState$update$2.class", "name": "androidx/compose/material3/TimePickerState$update$2.class", "size": 4366, "crc": 1306960458}, {"key": "androidx/compose/material3/TimePickerState.class", "name": "androidx/compose/material3/TimePickerState.class", "size": 19205, "crc": -1718328931}, {"key": "androidx/compose/material3/TimePicker_androidKt.class", "name": "androidx/compose/material3/TimePicker_androidKt.class", "size": 3575, "crc": -**********}, {"key": "androidx/compose/material3/TonalPalette.class", "name": "androidx/compose/material3/TonalPalette.class", "size": 18292, "crc": -529710909}, {"key": "androidx/compose/material3/TonalPaletteKt.class", "name": "androidx/compose/material3/TonalPaletteKt.class", "size": 5511, "crc": 117419447}, {"key": "androidx/compose/material3/TooltipDefaults$rememberPlainTooltipPositionProvider$1$1.class", "name": "androidx/compose/material3/TooltipDefaults$rememberPlainTooltipPositionProvider$1$1.class", "size": 2141, "crc": 248171444}, {"key": "androidx/compose/material3/TooltipDefaults$rememberRichTooltipPositionProvider$1$1.class", "name": "androidx/compose/material3/TooltipDefaults$rememberRichTooltipPositionProvider$1$1.class", "size": 2224, "crc": **********}, {"key": "androidx/compose/material3/TooltipDefaults.class", "name": "androidx/compose/material3/TooltipDefaults.class", "size": 13017, "crc": **********}, {"key": "androidx/compose/material3/TooltipKt$RichTooltip$1.class", "name": "androidx/compose/material3/TooltipKt$RichTooltip$1.class", "size": 20303, "crc": **********}, {"key": "androidx/compose/material3/TooltipKt$RichTooltip$2.class", "name": "androidx/compose/material3/TooltipKt$RichTooltip$2.class", "size": 3147, "crc": -**********}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$1.class", "size": 9933, "crc": -451968508}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$2.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$2.class", "size": 3177, "crc": -**********}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$scope$1$1$drawCaret$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$scope$1$1$drawCaret$1.class", "size": 2736, "crc": -958924830}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$scope$1$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$scope$1$1.class", "size": 2671, "crc": -810018163}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1$1$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1$1$1.class", "size": 2011, "crc": 1634641357}, {"key": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1.class", "name": "androidx/compose/material3/TooltipKt$TooltipBox$wrappedContent$1.class", "size": 10786, "crc": 533518065}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$$inlined$debugInspectorInfo$1.class", "size": 2914, "crc": 1119490952}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$2$alpha$2.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$2$alpha$2.class", "size": 3524, "crc": -2067335514}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$2$scale$2.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$2$scale$2.class", "size": 3533, "crc": -1277848516}, {"key": "androidx/compose/material3/TooltipKt$animateTooltip$2.class", "name": "androidx/compose/material3/TooltipKt$animateTooltip$2.class", "size": 8925, "crc": 2092505869}, {"key": "androidx/compose/material3/TooltipKt.class", "name": "androidx/compose/material3/TooltipKt.class", "size": 22918, "crc": 1562745642}, {"key": "androidx/compose/material3/TooltipState.class", "name": "androidx/compose/material3/TooltipState.class", "size": 954, "crc": 292790406}, {"key": "androidx/compose/material3/TooltipStateImpl$show$2$1.class", "name": "androidx/compose/material3/TooltipStateImpl$show$2$1.class", "size": 3524, "crc": -1031016556}, {"key": "androidx/compose/material3/TooltipStateImpl$show$2.class", "name": "androidx/compose/material3/TooltipStateImpl$show$2.class", "size": 4011, "crc": -350400638}, {"key": "androidx/compose/material3/TooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/material3/TooltipStateImpl$show$cancellableShow$1.class", "size": 5375, "crc": -2001751119}, {"key": "androidx/compose/material3/TooltipStateImpl.class", "name": "androidx/compose/material3/TooltipStateImpl.class", "size": 4571, "crc": -119792859}, {"key": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$1.class", "name": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$1.class", "size": 11489, "crc": -2019729446}, {"key": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$2.class", "name": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$2.class", "size": 3091, "crc": 604079177}, {"key": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$customModifier$1$1.class", "name": "androidx/compose/material3/Tooltip_androidKt$PlainTooltip$customModifier$1$1.class", "size": 2866, "crc": 572298390}, {"key": "androidx/compose/material3/Tooltip_androidKt$drawCaretWithPath$4.class", "name": "androidx/compose/material3/Tooltip_androidKt$drawCaretWithPath$4.class", "size": 2484, "crc": 989720414}, {"key": "androidx/compose/material3/Tooltip_androidKt.class", "name": "androidx/compose/material3/Tooltip_androidKt.class", "size": 14913, "crc": 1267925706}, {"key": "androidx/compose/material3/TopAppBarColors.class", "name": "androidx/compose/material3/TopAppBarColors.class", "size": 6583, "crc": -1013159788}, {"key": "androidx/compose/material3/TopAppBarDefaults$enterAlwaysScrollBehavior$1.class", "name": "androidx/compose/material3/TopAppBarDefaults$enterAlwaysScrollBehavior$1.class", "size": 1477, "crc": 1877585435}, {"key": "androidx/compose/material3/TopAppBarDefaults$exitUntilCollapsedScrollBehavior$1.class", "name": "androidx/compose/material3/TopAppBarDefaults$exitUntilCollapsedScrollBehavior$1.class", "size": 1498, "crc": 7804039}, {"key": "androidx/compose/material3/TopAppBarDefaults$pinnedScrollBehavior$1.class", "name": "androidx/compose/material3/TopAppBarDefaults$pinnedScrollBehavior$1.class", "size": 1363, "crc": -1867738229}, {"key": "androidx/compose/material3/TopAppBarDefaults.class", "name": "androidx/compose/material3/TopAppBarDefaults.class", "size": 19305, "crc": 1081884934}, {"key": "androidx/compose/material3/TopAppBarScrollBehavior.class", "name": "androidx/compose/material3/TopAppBarScrollBehavior.class", "size": 1740, "crc": 695649808}, {"key": "androidx/compose/material3/TopAppBarState$Companion$Saver$1.class", "name": "androidx/compose/material3/TopAppBarState$Companion$Saver$1.class", "size": 2166, "crc": -843164995}, {"key": "androidx/compose/material3/TopAppBarState$Companion$Saver$2.class", "name": "androidx/compose/material3/TopAppBarState$Companion$Saver$2.class", "size": 1803, "crc": 39080490}, {"key": "androidx/compose/material3/TopAppBarState$Companion.class", "name": "androidx/compose/material3/TopAppBarState$Companion.class", "size": 1324, "crc": -**********}, {"key": "androidx/compose/material3/TopAppBarState.class", "name": "androidx/compose/material3/TopAppBarState.class", "size": 5199, "crc": **********}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$1.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$1.class", "size": 1638, "crc": -**********}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$2.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$2.class", "size": 1268, "crc": 465154447}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$3$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$3$1$invoke$$inlined$onDispose$1.class", "size": 2724, "crc": **********}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$3$1.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$3$1.class", "size": 4932, "crc": 329652251}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$4.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$ObserveState$4.class", "size": 2628, "crc": -643457042}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$touchExplorationState$1$1.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$touchExplorationState$1$1.class", "size": 2103, "crc": -962568451}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$touchExplorationState$2$1.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt$touchExplorationState$2$1.class", "size": 1659, "crc": -551013709}, {"key": "androidx/compose/material3/TouchExplorationStateProvider_androidKt.class", "name": "androidx/compose/material3/TouchExplorationStateProvider_androidKt.class", "size": 10681, "crc": 293863465}, {"key": "androidx/compose/material3/Typography.class", "name": "androidx/compose/material3/Typography.class", "size": 10230, "crc": -**********}, {"key": "androidx/compose/material3/TypographyKt$LocalTypography$1.class", "name": "androidx/compose/material3/TypographyKt$LocalTypography$1.class", "size": 1904, "crc": -416198796}, {"key": "androidx/compose/material3/TypographyKt$WhenMappings.class", "name": "androidx/compose/material3/TypographyKt$WhenMappings.class", "size": 1531, "crc": 20337912}, {"key": "androidx/compose/material3/TypographyKt.class", "name": "androidx/compose/material3/TypographyKt.class", "size": 3001, "crc": 998926827}, {"key": "androidx/compose/material3/VisibleModifier$measure$1.class", "name": "androidx/compose/material3/VisibleModifier$measure$1.class", "size": 1709, "crc": **********}, {"key": "androidx/compose/material3/VisibleModifier$measure$2.class", "name": "androidx/compose/material3/VisibleModifier$measure$2.class", "size": 1920, "crc": 696324047}, {"key": "androidx/compose/material3/VisibleModifier.class", "name": "androidx/compose/material3/VisibleModifier.class", "size": 3428, "crc": -235154734}, {"key": "androidx/compose/material3/WindowAlignmentMarginPosition$Horizontal.class", "name": "androidx/compose/material3/WindowAlignmentMarginPosition$Horizontal.class", "size": 4278, "crc": -1987750339}, {"key": "androidx/compose/material3/WindowAlignmentMarginPosition$Vertical.class", "name": "androidx/compose/material3/WindowAlignmentMarginPosition$Vertical.class", "size": 4051, "crc": -2119230618}, {"key": "androidx/compose/material3/WindowAlignmentMarginPosition.class", "name": "androidx/compose/material3/WindowAlignmentMarginPosition.class", "size": 1019, "crc": 1530457846}, {"key": "androidx/compose/material3/carousel/Arrangement$Companion.class", "name": "androidx/compose/material3/carousel/Arrangement$Companion.class", "size": 3999, "crc": -920858939}, {"key": "androidx/compose/material3/carousel/Arrangement.class", "name": "androidx/compose/material3/carousel/Arrangement.class", "size": 2566, "crc": 260205765}, {"key": "androidx/compose/material3/carousel/CarouselAlignment.class", "name": "androidx/compose/material3/carousel/CarouselAlignment.class", "size": 1521, "crc": 183226750}, {"key": "androidx/compose/material3/carousel/Keyline.class", "name": "androidx/compose/material3/carousel/Keyline.class", "size": 4278, "crc": 1954901873}, {"key": "androidx/compose/material3/carousel/KeylineKt.class", "name": "androidx/compose/material3/carousel/KeylineKt.class", "size": 2468, "crc": -796351386}, {"key": "androidx/compose/material3/carousel/KeylineList.class", "name": "androidx/compose/material3/carousel/KeylineList.class", "size": 14729, "crc": -1168572715}, {"key": "androidx/compose/material3/carousel/KeylineListScope.class", "name": "androidx/compose/material3/carousel/KeylineListScope.class", "size": 923, "crc": 882954738}, {"key": "androidx/compose/material3/carousel/KeylineListScopeImpl$TmpKeyline.class", "name": "androidx/compose/material3/carousel/KeylineListScopeImpl$TmpKeyline.class", "size": 2702, "crc": 1703055574}, {"key": "androidx/compose/material3/carousel/KeylineListScopeImpl$WhenMappings.class", "name": "androidx/compose/material3/carousel/KeylineListScopeImpl$WhenMappings.class", "size": 919, "crc": 166462786}, {"key": "androidx/compose/material3/carousel/KeylineListScopeImpl.class", "name": "androidx/compose/material3/carousel/KeylineListScopeImpl.class", "size": 7945, "crc": -563470936}, {"key": "androidx/compose/material3/carousel/Strategy$Companion$ShiftPointRange.class", "name": "androidx/compose/material3/carousel/Strategy$Companion$ShiftPointRange.class", "size": 3220, "crc": 1739723478}, {"key": "androidx/compose/material3/carousel/Strategy$Companion$moveKeylineAndCreateShiftedKeylineList$1.class", "name": "androidx/compose/material3/carousel/Strategy$Companion$moveKeylineAndCreateShiftedKeylineList$1.class", "size": 3852, "crc": -868056737}, {"key": "androidx/compose/material3/carousel/Strategy$Companion.class", "name": "androidx/compose/material3/carousel/Strategy$Companion.class", "size": 10673, "crc": **********}, {"key": "androidx/compose/material3/carousel/Strategy.class", "name": "androidx/compose/material3/carousel/Strategy.class", "size": 4975, "crc": 317691584}, {"key": "androidx/compose/material3/carousel/StrategyKt.class", "name": "androidx/compose/material3/carousel/StrategyKt.class", "size": 4284, "crc": 999164563}, {"key": "androidx/compose/material3/carousel/StrategyProvider.class", "name": "androidx/compose/material3/carousel/StrategyProvider.class", "size": 848, "crc": -818248903}, {"key": "androidx/compose/material3/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda-1$1.class", "name": "androidx/compose/material3/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt$lambda-1$1.class", "size": 2397, "crc": -**********}, {"key": "androidx/compose/material3/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material3/internal/ComposableSingletons$ExposedDropdownMenuPopup_androidKt.class", "size": 1649, "crc": -408696459}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1$invoke$$inlined$onDispose$1.class", "size": 2470, "crc": **********}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$1$1.class", "size": 4069, "crc": 1431941395}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$2$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$2$1.class", "size": 2156, "crc": 372216406}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$4$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$4$1.class", "size": 2894, "crc": 852189955}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1$1.class", "size": 1871, "crc": -81987891}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$5$1.class", "size": 2792, "crc": 1858220830}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$6.class", "size": 2666, "crc": 125365704}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupId$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupId$1.class", "size": 1505, "crc": -1426349781}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$1.class", "size": 1863, "crc": 1258807859}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$2$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$2$1.class", "size": 1966, "crc": 1645794384}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$3.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1$3.class", "size": 3213, "crc": -1199010060}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$ExposedDropdownMenuPopup$popupLayout$1$1$1.class", "size": 11627, "crc": -2131601695}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$1.class", "size": 2369, "crc": 1670548799}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$2.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$2.class", "size": 2580, "crc": -590988252}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$3.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1$3.class", "size": 2982, "crc": -112061813}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt$SimpleStack$1.class", "size": 5371, "crc": 416660993}, {"key": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt.class", "name": "androidx/compose/material3/internal/ExposedDropdownMenuPopup_androidKt.class", "size": 20113, "crc": 772651172}, {"key": "androidx/compose/material3/internal/PopupLayout$2.class", "name": "androidx/compose/material3/internal/PopupLayout$2.class", "size": 1443, "crc": -1059590041}, {"key": "androidx/compose/material3/internal/PopupLayout$Content$4.class", "name": "androidx/compose/material3/internal/PopupLayout$Content$4.class", "size": 1698, "crc": -266650190}, {"key": "androidx/compose/material3/internal/PopupLayout$WhenMappings.class", "name": "androidx/compose/material3/internal/PopupLayout$WhenMappings.class", "size": 839, "crc": -1183739928}, {"key": "androidx/compose/material3/internal/PopupLayout$canCalculatePosition$2.class", "name": "androidx/compose/material3/internal/PopupLayout$canCalculatePosition$2.class", "size": 1715, "crc": -316083291}, {"key": "androidx/compose/material3/internal/PopupLayout.class", "name": "androidx/compose/material3/internal/PopupLayout.class", "size": 18816, "crc": -1218354009}, {"key": "androidx/compose/material3/pulltorefresh/ArrowValues.class", "name": "androidx/compose/material3/pulltorefresh/ArrowValues.class", "size": 1305, "crc": 1964281501}, {"key": "androidx/compose/material3/pulltorefresh/ComposableSingletons$PullToRefreshKt$lambda-1$1.class", "name": "androidx/compose/material3/pulltorefresh/ComposableSingletons$PullToRefreshKt$lambda-1$1.class", "size": 3249, "crc": 1654379777}, {"key": "androidx/compose/material3/pulltorefresh/ComposableSingletons$PullToRefreshKt.class", "name": "androidx/compose/material3/pulltorefresh/ComposableSingletons$PullToRefreshKt.class", "size": 1696, "crc": 1028964040}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1$1$1$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1$1$1$1.class", "size": 1546, "crc": -1442680889}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$1.class", "size": 11639, "crc": -426674427}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$2.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults$Indicator$2.class", "size": 2290, "crc": 1084149239}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshDefaults.class", "size": 8628, "crc": -504775408}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$1$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$1$1.class", "size": 2470, "crc": 741319838}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$2$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$2$1.class", "size": 6103, "crc": 1875784655}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$3.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$3.class", "size": 2057, "crc": 526930628}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$targetAlpha$2$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$CircularArrowProgressIndicator$targetAlpha$2$1.class", "size": 1726, "crc": 639400897}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$1$1$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$1$1$1.class", "size": 2008, "crc": 1565675915}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$1.class", "size": 12664, "crc": 1727672979}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$2.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$2.class", "size": 2985, "crc": 1996419474}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$showElevation$1$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshContainer$showElevation$1$1.class", "size": 1843, "crc": -1148739489}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshState$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$PullToRefreshState$1.class", "size": 1444, "crc": 548183945}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$rememberPullToRefreshState$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$rememberPullToRefreshState$1.class", "size": 1399, "crc": 213666093}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$rememberPullToRefreshState$2$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt$rememberPullToRefreshState$2$1.class", "size": 1994, "crc": -1401584211}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshKt.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshKt.class", "size": 28135, "crc": -1671306366}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshState.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshState.class", "size": 1513, "crc": 1632953382}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$1.class", "size": 2240, "crc": -1521083093}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$2.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion$Saver$2.class", "size": 2106, "crc": 1288094162}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$Companion.class", "size": 2306, "crc": -1892709572}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$animateTo$2.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$animateTo$2.class", "size": 1733, "crc": -1282544590}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$nestedScrollConnection$1$onPreFling$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$nestedScrollConnection$1$onPreFling$1.class", "size": 2092, "crc": -1126913170}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$nestedScrollConnection$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$nestedScrollConnection$1.class", "size": 4760, "crc": -444182222}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$onRelease$1.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl$onRelease$1.class", "size": 1915, "crc": -2045687549}, {"key": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl.class", "name": "androidx/compose/material3/pulltorefresh/PullToRefreshStateImpl.class", "size": 10663, "crc": 1667098602}, {"key": "androidx/compose/material3/tokens/AssistChipTokens.class", "name": "androidx/compose/material3/tokens/AssistChipTokens.class", "size": 8767, "crc": 142408555}, {"key": "androidx/compose/material3/tokens/BadgeTokens.class", "name": "androidx/compose/material3/tokens/BadgeTokens.class", "size": 3729, "crc": -2146870693}, {"key": "androidx/compose/material3/tokens/BottomAppBarTokens.class", "name": "androidx/compose/material3/tokens/BottomAppBarTokens.class", "size": 3263, "crc": 925789308}, {"key": "androidx/compose/material3/tokens/CheckboxTokens.class", "name": "androidx/compose/material3/tokens/CheckboxTokens.class", "size": 13810, "crc": -1087397714}, {"key": "androidx/compose/material3/tokens/CircularProgressIndicatorTokens.class", "name": "androidx/compose/material3/tokens/CircularProgressIndicatorTokens.class", "size": 4049, "crc": 1103737046}, {"key": "androidx/compose/material3/tokens/ColorDarkTokens.class", "name": "androidx/compose/material3/tokens/ColorDarkTokens.class", "size": 10454, "crc": 841605945}, {"key": "androidx/compose/material3/tokens/ColorLightTokens.class", "name": "androidx/compose/material3/tokens/ColorLightTokens.class", "size": 10639, "crc": 2059669808}, {"key": "androidx/compose/material3/tokens/ColorSchemeKeyTokens.class", "name": "androidx/compose/material3/tokens/ColorSchemeKeyTokens.class", "size": 4723, "crc": 2078108095}, {"key": "androidx/compose/material3/tokens/DateInputModalTokens.class", "name": "androidx/compose/material3/tokens/DateInputModalTokens.class", "size": 5092, "crc": 32050931}, {"key": "androidx/compose/material3/tokens/DatePickerModalTokens.class", "name": "androidx/compose/material3/tokens/DatePickerModalTokens.class", "size": 13066, "crc": 587150555}, {"key": "androidx/compose/material3/tokens/DialogTokens.class", "name": "androidx/compose/material3/tokens/DialogTokens.class", "size": 5387, "crc": -383532482}, {"key": "androidx/compose/material3/tokens/DividerTokens.class", "name": "androidx/compose/material3/tokens/DividerTokens.class", "size": 2227, "crc": -221222592}, {"key": "androidx/compose/material3/tokens/ElevatedButtonTokens.class", "name": "androidx/compose/material3/tokens/ElevatedButtonTokens.class", "size": 7019, "crc": 1972634339}, {"key": "androidx/compose/material3/tokens/ElevatedCardTokens.class", "name": "androidx/compose/material3/tokens/ElevatedCardTokens.class", "size": 4755, "crc": -2026475392}, {"key": "androidx/compose/material3/tokens/ElevationTokens.class", "name": "androidx/compose/material3/tokens/ElevationTokens.class", "size": 3022, "crc": 291242160}, {"key": "androidx/compose/material3/tokens/ExtendedFabPrimaryTokens.class", "name": "androidx/compose/material3/tokens/ExtendedFabPrimaryTokens.class", "size": 6692, "crc": -357585186}, {"key": "androidx/compose/material3/tokens/FabPrimaryLargeTokens.class", "name": "androidx/compose/material3/tokens/FabPrimaryLargeTokens.class", "size": 5772, "crc": -434647797}, {"key": "androidx/compose/material3/tokens/FabPrimarySmallTokens.class", "name": "androidx/compose/material3/tokens/FabPrimarySmallTokens.class", "size": 5768, "crc": -163622250}, {"key": "androidx/compose/material3/tokens/FabPrimaryTokens.class", "name": "androidx/compose/material3/tokens/FabPrimaryTokens.class", "size": 5702, "crc": -244809914}, {"key": "androidx/compose/material3/tokens/FabSecondaryTokens.class", "name": "androidx/compose/material3/tokens/FabSecondaryTokens.class", "size": 5732, "crc": -1937576666}, {"key": "androidx/compose/material3/tokens/FilledAutocompleteTokens.class", "name": "androidx/compose/material3/tokens/FilledAutocompleteTokens.class", "size": 17733, "crc": -676431066}, {"key": "androidx/compose/material3/tokens/FilledButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledButtonTokens.class", "size": 6733, "crc": 1993416755}, {"key": "androidx/compose/material3/tokens/FilledCardTokens.class", "name": "androidx/compose/material3/tokens/FilledCardTokens.class", "size": 4504, "crc": 819434119}, {"key": "androidx/compose/material3/tokens/FilledIconButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledIconButtonTokens.class", "size": 6199, "crc": 280221918}, {"key": "androidx/compose/material3/tokens/FilledTextFieldTokens.class", "name": "androidx/compose/material3/tokens/FilledTextFieldTokens.class", "size": 14760, "crc": -830165080}, {"key": "androidx/compose/material3/tokens/FilledTonalButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledTonalButtonTokens.class", "size": 6820, "crc": -1579895162}, {"key": "androidx/compose/material3/tokens/FilledTonalIconButtonTokens.class", "name": "androidx/compose/material3/tokens/FilledTonalIconButtonTokens.class", "size": 6315, "crc": 686440789}, {"key": "androidx/compose/material3/tokens/FilterChipTokens.class", "name": "androidx/compose/material3/tokens/FilterChipTokens.class", "size": 15956, "crc": 155142056}, {"key": "androidx/compose/material3/tokens/IconButtonTokens.class", "name": "androidx/compose/material3/tokens/IconButtonTokens.class", "size": 4600, "crc": 850101510}, {"key": "androidx/compose/material3/tokens/InputChipTokens.class", "name": "androidx/compose/material3/tokens/InputChipTokens.class", "size": 13218, "crc": 758830646}, {"key": "androidx/compose/material3/tokens/LinearProgressIndicatorTokens.class", "name": "androidx/compose/material3/tokens/LinearProgressIndicatorTokens.class", "size": 4393, "crc": -830727460}, {"key": "androidx/compose/material3/tokens/ListTokens.class", "name": "androidx/compose/material3/tokens/ListTokens.class", "size": 13772, "crc": -367293739}, {"key": "androidx/compose/material3/tokens/MenuTokens.class", "name": "androidx/compose/material3/tokens/MenuTokens.class", "size": 7734, "crc": -444084874}, {"key": "androidx/compose/material3/tokens/MotionTokens.class", "name": "androidx/compose/material3/tokens/MotionTokens.class", "size": 4324, "crc": -1327106094}, {"key": "androidx/compose/material3/tokens/NavigationBarTokens.class", "name": "androidx/compose/material3/tokens/NavigationBarTokens.class", "size": 8036, "crc": 1536160102}, {"key": "androidx/compose/material3/tokens/NavigationDrawerTokens.class", "name": "androidx/compose/material3/tokens/NavigationDrawerTokens.class", "size": 9366, "crc": -753708730}, {"key": "androidx/compose/material3/tokens/NavigationRailTokens.class", "name": "androidx/compose/material3/tokens/NavigationRailTokens.class", "size": 9251, "crc": 70097342}, {"key": "androidx/compose/material3/tokens/OutlinedAutocompleteTokens.class", "name": "androidx/compose/material3/tokens/OutlinedAutocompleteTokens.class", "size": 17248, "crc": 1204600202}, {"key": "androidx/compose/material3/tokens/OutlinedButtonTokens.class", "name": "androidx/compose/material3/tokens/OutlinedButtonTokens.class", "size": 6397, "crc": 1801866540}, {"key": "androidx/compose/material3/tokens/OutlinedCardTokens.class", "name": "androidx/compose/material3/tokens/OutlinedCardTokens.class", "size": 5728, "crc": 2039261703}, {"key": "androidx/compose/material3/tokens/OutlinedIconButtonTokens.class", "name": "androidx/compose/material3/tokens/OutlinedIconButtonTokens.class", "size": 5878, "crc": 1032434144}, {"key": "androidx/compose/material3/tokens/OutlinedSegmentedButtonTokens.class", "name": "androidx/compose/material3/tokens/OutlinedSegmentedButtonTokens.class", "size": 8213, "crc": -1731811874}, {"key": "androidx/compose/material3/tokens/OutlinedTextFieldTokens.class", "name": "androidx/compose/material3/tokens/OutlinedTextFieldTokens.class", "size": 14132, "crc": 1221104266}, {"key": "androidx/compose/material3/tokens/PaletteTokens.class", "name": "androidx/compose/material3/tokens/PaletteTokens.class", "size": 16917, "crc": -1325442024}, {"key": "androidx/compose/material3/tokens/PlainTooltipTokens.class", "name": "androidx/compose/material3/tokens/PlainTooltipTokens.class", "size": 2233, "crc": -1179624714}, {"key": "androidx/compose/material3/tokens/PrimaryNavigationTabTokens.class", "name": "androidx/compose/material3/tokens/PrimaryNavigationTabTokens.class", "size": 8211, "crc": -664937612}, {"key": "androidx/compose/material3/tokens/RadioButtonTokens.class", "name": "androidx/compose/material3/tokens/RadioButtonTokens.class", "size": 4533, "crc": 354920032}, {"key": "androidx/compose/material3/tokens/RichTooltipTokens.class", "name": "androidx/compose/material3/tokens/RichTooltipTokens.class", "size": 4237, "crc": 1503074962}, {"key": "androidx/compose/material3/tokens/ScrimTokens.class", "name": "androidx/compose/material3/tokens/ScrimTokens.class", "size": 1302, "crc": -1824485867}, {"key": "androidx/compose/material3/tokens/SearchBarTokens.class", "name": "androidx/compose/material3/tokens/SearchBarTokens.class", "size": 5377, "crc": -907074203}, {"key": "androidx/compose/material3/tokens/SearchViewTokens.class", "name": "androidx/compose/material3/tokens/SearchViewTokens.class", "size": 5391, "crc": 510440166}, {"key": "androidx/compose/material3/tokens/SecondaryNavigationTabTokens.class", "name": "androidx/compose/material3/tokens/SecondaryNavigationTabTokens.class", "size": 6106, "crc": 862567262}, {"key": "androidx/compose/material3/tokens/ShapeKeyTokens.class", "name": "androidx/compose/material3/tokens/ShapeKeyTokens.class", "size": 2087, "crc": -1445594464}, {"key": "androidx/compose/material3/tokens/ShapeTokens.class", "name": "androidx/compose/material3/tokens/ShapeTokens.class", "size": 5622, "crc": 1953410883}, {"key": "androidx/compose/material3/tokens/SheetBottomTokens.class", "name": "androidx/compose/material3/tokens/SheetBottomTokens.class", "size": 4347, "crc": -1656251894}, {"key": "androidx/compose/material3/tokens/SliderTokens.class", "name": "androidx/compose/material3/tokens/SliderTokens.class", "size": 9466, "crc": 480313539}, {"key": "androidx/compose/material3/tokens/SnackbarTokens.class", "name": "androidx/compose/material3/tokens/SnackbarTokens.class", "size": 5849, "crc": -1158779719}, {"key": "androidx/compose/material3/tokens/StateTokens.class", "name": "androidx/compose/material3/tokens/StateTokens.class", "size": 1116, "crc": 1330702846}, {"key": "androidx/compose/material3/tokens/SuggestionChipTokens.class", "name": "androidx/compose/material3/tokens/SuggestionChipTokens.class", "size": 8933, "crc": -1757040817}, {"key": "androidx/compose/material3/tokens/SwitchTokens.class", "name": "androidx/compose/material3/tokens/SwitchTokens.class", "size": 13562, "crc": -1520758497}, {"key": "androidx/compose/material3/tokens/TextButtonTokens.class", "name": "androidx/compose/material3/tokens/TextButtonTokens.class", "size": 5113, "crc": -440774255}, {"key": "androidx/compose/material3/tokens/TimeInputTokens.class", "name": "androidx/compose/material3/tokens/TimeInputTokens.class", "size": 10549, "crc": 208103588}, {"key": "androidx/compose/material3/tokens/TimePickerTokens.class", "name": "androidx/compose/material3/tokens/TimePickerTokens.class", "size": 15129, "crc": 1798105980}, {"key": "androidx/compose/material3/tokens/TopAppBarLargeTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarLargeTokens.class", "size": 4761, "crc": 1877663758}, {"key": "androidx/compose/material3/tokens/TopAppBarMediumTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarMediumTokens.class", "size": 4773, "crc": 2142543751}, {"key": "androidx/compose/material3/tokens/TopAppBarSmallCenteredTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarSmallCenteredTokens.class", "size": 5522, "crc": -1233067995}, {"key": "androidx/compose/material3/tokens/TopAppBarSmallTokens.class", "name": "androidx/compose/material3/tokens/TopAppBarSmallTokens.class", "size": 4984, "crc": -1533401926}, {"key": "androidx/compose/material3/tokens/TypeScaleTokens.class", "name": "androidx/compose/material3/tokens/TypeScaleTokens.class", "size": 16257, "crc": 2004633663}, {"key": "androidx/compose/material3/tokens/TypefaceTokens.class", "name": "androidx/compose/material3/tokens/TypefaceTokens.class", "size": 2376, "crc": -2023065903}, {"key": "androidx/compose/material3/tokens/TypographyKeyTokens.class", "name": "androidx/compose/material3/tokens/TypographyKeyTokens.class", "size": 2361, "crc": -1318219516}, {"key": "androidx/compose/material3/tokens/TypographyTokens.class", "name": "androidx/compose/material3/tokens/TypographyTokens.class", "size": 9558, "crc": -1961738367}, {"key": "androidx/compose/material3/tokens/TypographyTokensKt.class", "name": "androidx/compose/material3/tokens/TypographyTokensKt.class", "size": 2924, "crc": -677701567}, {"key": "META-INF/androidx.compose.material3_material3.version", "name": "META-INF/androidx.compose.material3_material3.version", "size": 6, "crc": 1467441379}, {"key": "META-INF/material3_release.kotlin_module", "name": "META-INF/material3_release.kotlin_module", "size": 1607, "crc": -1299288371}]