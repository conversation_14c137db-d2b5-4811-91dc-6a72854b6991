[{"key": "androidx/compose/ui/tooling/ComposableInvoker.class", "name": "androidx/compose/ui/tooling/ComposableInvoker.class", "size": 16751, "crc": -1062987722}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda$-1163195098$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda$-1163195098$1.class", "size": 2174, "crc": -959840887}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda$2086912010$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt$lambda$2086912010$1.class", "size": 2172, "crc": 57944475}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$ComposeViewAdapter_androidKt.class", "size": 2022, "crc": -2000822612}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt$lambda$-426398407$1.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt$lambda$-426398407$1.class", "size": 2647, "crc": -708566757}, {"key": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt.class", "name": "androidx/compose/ui/tooling/ComposableSingletons$PreviewActivity_androidKt.class", "size": 1596, "crc": 1286113077}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1$activityResultRegistry$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1$activityResultRegistry$1.class", "size": 1777, "crc": 891775548}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeActivityResultRegistryOwner$1.class", "size": 1620, "crc": -779300638}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeOnBackPressedDispatcherOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeOnBackPressedDispatcherOwner$1.class", "size": 2141, "crc": 441810124}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeSavedStateRegistryOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeSavedStateRegistryOwner$1.class", "size": 3473, "crc": 1963599180}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeViewModelStoreOwner$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$FakeViewModelStoreOwner$1.class", "size": 1216, "crc": -1475323222}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$WrapPreview$1.class", "size": 2975, "crc": 499071547}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$1.class", "size": 1385, "crc": -2014321125}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$2.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$findAndTrackAnimations$2.class", "size": 1256, "crc": -1280642543}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3$1.class", "size": 8789, "crc": 1206433896}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter$init$3.class", "size": 3943, "crc": 877328865}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter.class", "size": 36177, "crc": -994549150}, {"key": "androidx/compose/ui/tooling/ComposeViewAdapter_androidKt.class", "name": "androidx/compose/ui/tooling/ComposeViewAdapter_androidKt.class", "size": 1400, "crc": 1613698139}, {"key": "androidx/compose/ui/tooling/CompositionDataRecord$Companion.class", "name": "androidx/compose/ui/tooling/CompositionDataRecord$Companion.class", "size": 1111, "crc": -1512268983}, {"key": "androidx/compose/ui/tooling/CompositionDataRecord.class", "name": "androidx/compose/ui/tooling/CompositionDataRecord.class", "size": 1034, "crc": -1716296249}, {"key": "androidx/compose/ui/tooling/CompositionDataRecordImpl.class", "name": "androidx/compose/ui/tooling/CompositionDataRecordImpl.class", "size": 1345, "crc": -1715534489}, {"key": "androidx/compose/ui/tooling/InspectableKt.class", "name": "androidx/compose/ui/tooling/InspectableKt.class", "size": 7958, "crc": -157424610}, {"key": "androidx/compose/ui/tooling/LayoutlibFontResourceLoader.class", "name": "androidx/compose/ui/tooling/LayoutlibFontResourceLoader.class", "size": 2729, "crc": 1948288967}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setComposableContent$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setComposableContent$2.class", "size": 2427, "crc": -501482158}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$1.class", "size": 5651, "crc": -864875301}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1$2.class", "size": 10139, "crc": 1007873002}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$1.class", "size": 5483, "crc": -434014140}, {"key": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$2.class", "name": "androidx/compose/ui/tooling/PreviewActivity$setParameterizedContent$2.class", "size": 2688, "crc": -1161244621}, {"key": "androidx/compose/ui/tooling/PreviewActivity.class", "name": "androidx/compose/ui/tooling/PreviewActivity.class", "size": 5226, "crc": 871467586}, {"key": "androidx/compose/ui/tooling/PreviewLogger$Companion.class", "name": "androidx/compose/ui/tooling/PreviewLogger$Companion.class", "size": 1957, "crc": 1620256373}, {"key": "androidx/compose/ui/tooling/PreviewLogger.class", "name": "androidx/compose/ui/tooling/PreviewLogger.class", "size": 1021, "crc": 608352882}, {"key": "androidx/compose/ui/tooling/PreviewLogger_androidKt.class", "name": "androidx/compose/ui/tooling/PreviewLogger_androidKt.class", "size": 510, "crc": -716677202}, {"key": "androidx/compose/ui/tooling/PreviewUtils_androidKt.class", "name": "androidx/compose/ui/tooling/PreviewUtils_androidKt.class", "size": 11586, "crc": -424862176}, {"key": "androidx/compose/ui/tooling/ResourceFontHelper.class", "name": "androidx/compose/ui/tooling/ResourceFontHelper.class", "size": 1500, "crc": 1089183918}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo$allNodes$1.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo$allNodes$1.class", "size": 5947, "crc": -1580618675}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo.class", "size": 6439, "crc": 656251167}, {"key": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt.class", "name": "androidx/compose/ui/tooling/ShadowViewInfo_androidKt.class", "size": 9311, "crc": 1406432360}, {"key": "androidx/compose/ui/tooling/ThreadSafeException.class", "name": "androidx/compose/ui/tooling/ThreadSafeException.class", "size": 2292, "crc": 1479405054}, {"key": "androidx/compose/ui/tooling/ViewInfo.class", "name": "androidx/compose/ui/tooling/ViewInfo.class", "size": 8868, "crc": -1342749098}, {"key": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt.class", "name": "androidx/compose/ui/tooling/ViewInfoUtil_androidKt.class", "size": 8197, "crc": -664171239}, {"key": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation$Companion.class", "size": 3213, "crc": 23130063}, {"key": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimateXAsStateComposeAnimation.class", "size": 7525, "crc": -2035086732}, {"key": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation$Companion.class", "size": 3282, "crc": 535214927}, {"key": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimatedContentComposeAnimation.class", "size": 5462, "crc": 1106562348}, {"key": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation.class", "size": 4116, "crc": -2131961917}, {"key": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation_androidKt.class", "name": "androidx/compose/ui/tooling/animation/AnimatedVisibilityComposeAnimation_androidKt.class", "size": 1492, "crc": 2139575095}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateContentSizeSearch.class", "size": 6733, "crc": 1376933902}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearch.class", "size": 20184, "crc": 948207188}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearchInfo.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimateXAsStateSearchInfo.class", "size": 5541, "crc": -845644112}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedContentSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedContentSearch.class", "size": 10076, "crc": -1391559342}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedVisibilitySearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$AnimatedVisibilitySearch.class", "size": 10114, "crc": -1208419705}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$DecaySearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$DecaySearch.class", "size": 1865, "crc": -561171624}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearch.class", "size": 12616, "crc": 1034669000}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearchInfo.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$InfiniteTransitionSearchInfo.class", "size": 4370, "crc": -1953896959}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$RememberSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$RememberSearch.class", "size": 7186, "crc": -1388561291}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$Search.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$Search.class", "size": 4391, "crc": 307001411}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$TargetBasedSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$TargetBasedSearch.class", "size": 1907, "crc": -551603376}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch$TransitionSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch$TransitionSearch.class", "size": 9462, "crc": -1074315239}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch.class", "size": 15316, "crc": -1633611044}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt$findRememberedData$rememberCalls$1$1.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt$findRememberedData$rememberCalls$1$1.class", "size": 1625, "crc": 1804183328}, {"key": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt.class", "name": "androidx/compose/ui/tooling/animation/AnimationSearch_androidKt.class", "size": 14500, "crc": 1480874210}, {"key": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation$Companion.class", "size": 2651, "crc": 813195769}, {"key": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/InfiniteTransitionComposeAnimation.class", "size": 5830, "crc": 948903836}, {"key": "androidx/compose/ui/tooling/animation/PreviewAnimationClock.class", "name": "androidx/compose/ui/tooling/animation/PreviewAnimationClock.class", "size": 29928, "crc": 1154435558}, {"key": "androidx/compose/ui/tooling/animation/ToolingState.class", "name": "androidx/compose/ui/tooling/animation/ToolingState.class", "size": 2759, "crc": 1243003510}, {"key": "androidx/compose/ui/tooling/animation/TransitionBasedAnimation.class", "name": "androidx/compose/ui/tooling/animation/TransitionBasedAnimation.class", "size": 1034, "crc": 1566730151}, {"key": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation.class", "size": 3124, "crc": -113864163}, {"key": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation_androidKt.class", "name": "androidx/compose/ui/tooling/animation/TransitionComposeAnimation_androidKt.class", "size": 2212, "crc": -2110565230}, {"key": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation$Companion.class", "name": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation$Companion.class", "size": 1961, "crc": -400568492}, {"key": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation.class", "name": "androidx/compose/ui/tooling/animation/UnsupportedComposeAnimation.class", "size": 4572, "crc": -1166695677}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimateXAsStateClock.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimateXAsStateClock.class", "size": 8764, "crc": -40916011}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getAnimatedProperties$lambda$8$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getAnimatedProperties$lambda$8$$inlined$sortedBy$1.class", "size": 2536, "crc": -32315689}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getTransitions$lambda$4$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock$getTransitions$lambda$4$$inlined$sortedBy$1.class", "size": 2491, "crc": 1300721865}, {"key": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock.class", "name": "androidx/compose/ui/tooling/animation/clock/AnimatedVisibilityClock.class", "size": 11899, "crc": 1786523770}, {"key": "androidx/compose/ui/tooling/animation/clock/ComposeAnimationClock.class", "name": "androidx/compose/ui/tooling/animation/clock/ComposeAnimationClock.class", "size": 2739, "crc": 1729715557}, {"key": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock.class", "name": "androidx/compose/ui/tooling/animation/clock/InfiniteTransitionClock.class", "size": 13327, "crc": -1636193694}, {"key": "androidx/compose/ui/tooling/animation/clock/TransitionClock.class", "name": "androidx/compose/ui/tooling/animation/clock/TransitionClock.class", "size": 10150, "crc": 13179982}, {"key": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt.class", "name": "androidx/compose/ui/tooling/animation/clock/Utils_androidKt.class", "size": 20189, "crc": -570813517}, {"key": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState$Companion.class", "name": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState$Companion.class", "size": 1447, "crc": -58961604}, {"key": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState.class", "name": "androidx/compose/ui/tooling/animation/states/AnimatedVisibilityState.class", "size": 3105, "crc": -1896511754}, {"key": "androidx/compose/ui/tooling/animation/states/ComposeAnimationState.class", "name": "androidx/compose/ui/tooling/animation/states/ComposeAnimationState.class", "size": 461, "crc": -727643107}, {"key": "androidx/compose/ui/tooling/animation/states/TargetState.class", "name": "androidx/compose/ui/tooling/animation/states/TargetState.class", "size": 3354, "crc": 959723201}, {"key": "META-INF/androidx.compose.ui_ui-tooling.version", "name": "META-INF/androidx.compose.ui_ui-tooling.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/ui-tooling.kotlin_module", "name": "META-INF/ui-tooling.kotlin_module", "size": 417, "crc": -1618997375}]