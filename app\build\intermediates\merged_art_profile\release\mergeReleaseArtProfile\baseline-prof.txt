# Baseline profiles for androidx.activity

HSPLandroidx/activity/ComponentActivity$1;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$2;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$4;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$4;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$5;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$5;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/ComponentActivity$6;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$7;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$7;->onContextAvailable(Landroid/content/Context;)V
HSPLandroidx/activity/ComponentActivity;-><init>()V
HSPLandroidx/activity/ComponentActivity;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/ComponentActivity;->ensureViewModelStore()V
HSPLandroidx/activity/ComponentActivity;->getActivityResultRegistry()Landroidx/activity/result/ActivityResultRegistry;
HSPLandroidx/activity/ComponentActivity;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/activity/ComponentActivity;->getOnBackPressedDispatcher()Landroidx/activity/OnBackPressedDispatcher;
HSPLandroidx/activity/ComponentActivity;->getSavedStateRegistry()Landroidx/savedstate/SavedStateRegistry;
HSPLandroidx/activity/ComponentActivity;->getViewModelStore()Landroidx/lifecycle/ViewModelStore;
HSPLandroidx/activity/ComponentActivity;->onCreate(Landroid/os/Bundle;)V
HSPLandroidx/activity/OnBackPressedCallback;-><init>(Z)V
HSPLandroidx/activity/OnBackPressedCallback;->addCancellable(Landroidx/activity/Cancellable;)V
HSPLandroidx/activity/OnBackPressedCallback;->remove()V
HSPLandroidx/activity/OnBackPressedCallback;->setEnabled(Z)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/lifecycle/Lifecycle;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;-><init>(Ljava/lang/Runnable;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCallback(Landroidx/lifecycle/LifecycleOwner;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCancellableCallback(Landroidx/activity/OnBackPressedCallback;)Landroidx/activity/Cancellable;
HSPLandroidx/activity/contextaware/ContextAwareHelper;-><init>()V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->dispatchOnContextAvailable(Landroid/content/Context;)V
HSPLandroidx/activity/result/ActivityResultLauncher;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry$3;-><init>(Landroidx/activity/result/ActivityResultRegistry;Ljava/lang/String;ILandroidx/activity/result/contract/ActivityResultContract;)V
HSPLandroidx/activity/result/ActivityResultRegistry$CallbackAndContract;-><init>(Landroidx/activity/result/ActivityResultCallback;Landroidx/activity/result/contract/ActivityResultContract;)V
HSPLandroidx/activity/result/ActivityResultRegistry;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry;->bindRcKey(ILjava/lang/String;)V
HSPLandroidx/activity/result/ActivityResultRegistry;->generateRandomNumber()I
HSPLandroidx/activity/result/ActivityResultRegistry;->register(Ljava/lang/String;Landroidx/activity/result/contract/ActivityResultContract;Landroidx/activity/result/ActivityResultCallback;)Landroidx/activity/result/ActivityResultLauncher;
HSPLandroidx/activity/result/ActivityResultRegistry;->registerKey(Ljava/lang/String;)I
HSPLandroidx/activity/result/contract/ActivityResultContract;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;-><init>()V
Landroidx/activity/Cancellable;
Landroidx/activity/ComponentActivity$1;
Landroidx/activity/ComponentActivity$2;
Landroidx/activity/ComponentActivity$3;
Landroidx/activity/ComponentActivity$4;
Landroidx/activity/ComponentActivity$5;
Landroidx/activity/ComponentActivity$6;
Landroidx/activity/ComponentActivity$7;
Landroidx/activity/ComponentActivity$NonConfigurationInstances;
Landroidx/activity/ComponentActivity;
Landroidx/activity/OnBackPressedCallback;
Landroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;
Landroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;
Landroidx/activity/OnBackPressedDispatcher;
Landroidx/activity/OnBackPressedDispatcherOwner;
Landroidx/activity/contextaware/ContextAware;
Landroidx/activity/contextaware/ContextAwareHelper;
Landroidx/activity/contextaware/OnContextAvailableListener;
Landroidx/activity/result/ActivityResult;
Landroidx/activity/result/ActivityResultCallback;
Landroidx/activity/result/ActivityResultCaller;
Landroidx/activity/result/ActivityResultLauncher;
Landroidx/activity/result/ActivityResultRegistry$3;
Landroidx/activity/result/ActivityResultRegistry$CallbackAndContract;
Landroidx/activity/result/ActivityResultRegistry;
Landroidx/activity/result/ActivityResultRegistryOwner;
Landroidx/activity/result/contract/ActivityResultContract;
Landroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;
Landroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;
PLandroidx/activity/ComponentActivity$1;->run()V
PLandroidx/activity/ComponentActivity;->access$001(Landroidx/activity/ComponentActivity;)V
PLandroidx/activity/ComponentActivity;->onBackPressed()V
PLandroidx/activity/OnBackPressedCallback;->isEnabled()Z
PLandroidx/activity/OnBackPressedCallback;->removeCancellable(Landroidx/activity/Cancellable;)V
PLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->cancel()V
PLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;->cancel()V
PLandroidx/activity/OnBackPressedDispatcher;->onBackPressed()V
PLandroidx/activity/contextaware/ContextAwareHelper;->clearAvailableContext()V
PLandroidx/activity/result/ActivityResultRegistry$3;->unregister()V
PLandroidx/activity/result/ActivityResultRegistry;->unregister(Ljava/lang/String;)V

Landroidx/activity/Cancellable;
Landroidx/activity/ComponentActivity;
HSPLandroidx/activity/ComponentActivity;-><init>()V
HSPLandroidx/activity/ComponentActivity;-><init>(I)V
HSPLandroidx/activity/ComponentActivity;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/ComponentActivity;->createFullyDrawnExecutor()Landroidx/activity/ComponentActivity$ReportFullyDrawnExecutor;
HSPLandroidx/activity/ComponentActivity;->ensureViewModelStore()V
HSPLandroidx/activity/ComponentActivity;->getActivityResultRegistry()Landroidx/activity/result/ActivityResultRegistry;
HSPLandroidx/activity/ComponentActivity;->getDefaultViewModelCreationExtras()Landroidx/lifecycle/viewmodel/CreationExtras;
HSPLandroidx/activity/ComponentActivity;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/activity/ComponentActivity;->getOnBackPressedDispatcher()Landroidx/activity/OnBackPressedDispatcher;
HSPLandroidx/activity/ComponentActivity;->getSavedStateRegistry()Landroidx/savedstate/SavedStateRegistry;
HSPLandroidx/activity/ComponentActivity;->getViewModelStore()Landroidx/lifecycle/ViewModelStore;
HSPLandroidx/activity/ComponentActivity;->lambda$new$2$androidx-activity-ComponentActivity(Landroid/content/Context;)V
HSPLandroidx/activity/ComponentActivity;->onCreate(Landroid/os/Bundle;)V
HSPLandroidx/activity/ComponentActivity;->onTrimMemory(I)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda0;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda0;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda1;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda1;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda2;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda2;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$$ExternalSyntheticLambda3;
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda3;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$$ExternalSyntheticLambda3;->onContextAvailable(Landroid/content/Context;)V
Landroidx/activity/ComponentActivity$1;
HSPLandroidx/activity/ComponentActivity$1;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$2;
HSPLandroidx/activity/ComponentActivity$2;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentActivity$3;
HSPLandroidx/activity/ComponentActivity$3;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$3;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/ComponentActivity$4;
HSPLandroidx/activity/ComponentActivity$4;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$4;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/ComponentActivity$5;
HSPLandroidx/activity/ComponentActivity$5;-><init>(Landroidx/activity/ComponentActivity;)V
HSPLandroidx/activity/ComponentActivity$5;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/ComponentActivity$Api33Impl;
HSPLandroidx/activity/ComponentActivity$Api33Impl;->getOnBackInvokedDispatcher(Landroid/app/Activity;)Landroid/window/OnBackInvokedDispatcher;
Landroidx/activity/ComponentActivity$NonConfigurationInstances;
Landroidx/activity/ComponentActivity$ReportFullyDrawnExecutor;
Landroidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi16Impl;
HSPLandroidx/activity/ComponentActivity$ReportFullyDrawnExecutorApi16Impl;-><init>(Landroidx/activity/ComponentActivity;)V
Landroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m$1(Landroid/view/Window;I)V
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m$1(Landroid/view/Window;Z)V
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m(Landroid/view/Window;I)V
HSPLandroidx/activity/ComponentDialog$$ExternalSyntheticApiModelOutline0;->m(Landroid/view/Window;Z)V
Landroidx/activity/EdgeToEdge;
HSPLandroidx/activity/EdgeToEdge;-><clinit>()V
HSPLandroidx/activity/EdgeToEdge;->enable$default(Landroidx/activity/ComponentActivity;Landroidx/activity/SystemBarStyle;Landroidx/activity/SystemBarStyle;ILjava/lang/Object;)V
HSPLandroidx/activity/EdgeToEdge;->enable(Landroidx/activity/ComponentActivity;Landroidx/activity/SystemBarStyle;Landroidx/activity/SystemBarStyle;)V
Landroidx/activity/EdgeToEdgeApi29;
HSPLandroidx/activity/EdgeToEdgeApi29;-><init>()V
HSPLandroidx/activity/EdgeToEdgeApi29;->setUp(Landroidx/activity/SystemBarStyle;Landroidx/activity/SystemBarStyle;Landroid/view/Window;Landroid/view/View;ZZ)V
Landroidx/activity/EdgeToEdgeImpl;
Landroidx/activity/FullyDrawnReporter;
HSPLandroidx/activity/FullyDrawnReporter;-><init>(Ljava/util/concurrent/Executor;Lkotlin/jvm/functions/Function0;)V
Landroidx/activity/FullyDrawnReporter$$ExternalSyntheticLambda0;
HSPLandroidx/activity/FullyDrawnReporter$$ExternalSyntheticLambda0;-><init>(Landroidx/activity/FullyDrawnReporter;)V
Landroidx/activity/FullyDrawnReporterOwner;
Landroidx/activity/OnBackPressedCallback;
HSPLandroidx/activity/OnBackPressedCallback;-><init>(Z)V
HSPLandroidx/activity/OnBackPressedCallback;->addCancellable(Landroidx/activity/Cancellable;)V
HSPLandroidx/activity/OnBackPressedCallback;->isEnabled()Z
HSPLandroidx/activity/OnBackPressedCallback;->setEnabled(Z)V
HSPLandroidx/activity/OnBackPressedCallback;->setEnabledChangedCallback$activity_release(Lkotlin/jvm/functions/Function0;)V
Landroidx/activity/OnBackPressedDispatcher;
HSPLandroidx/activity/OnBackPressedDispatcher;-><init>(Ljava/lang/Runnable;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCallback(Landroidx/lifecycle/LifecycleOwner;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->addCancellableCallback$activity_release(Landroidx/activity/OnBackPressedCallback;)Landroidx/activity/Cancellable;
HSPLandroidx/activity/OnBackPressedDispatcher;->hasEnabledCallbacks()Z
HSPLandroidx/activity/OnBackPressedDispatcher;->setOnBackInvokedDispatcher(Landroid/window/OnBackInvokedDispatcher;)V
HSPLandroidx/activity/OnBackPressedDispatcher;->updateBackInvokedCallbackState$activity_release()V
Landroidx/activity/OnBackPressedDispatcher$1;
HSPLandroidx/activity/OnBackPressedDispatcher$1;-><init>(Landroidx/activity/OnBackPressedDispatcher;)V
HSPLandroidx/activity/OnBackPressedDispatcher$1;->invoke()Ljava/lang/Object;
HSPLandroidx/activity/OnBackPressedDispatcher$1;->invoke()V
Landroidx/activity/OnBackPressedDispatcher$2;
HSPLandroidx/activity/OnBackPressedDispatcher$2;-><init>(Landroidx/activity/OnBackPressedDispatcher;)V
Landroidx/activity/OnBackPressedDispatcher$Api33Impl;
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl;-><clinit>()V
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl;-><init>()V
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl;->createOnBackInvokedCallback(Lkotlin/jvm/functions/Function0;)Landroid/window/OnBackInvokedCallback;
Landroidx/activity/OnBackPressedDispatcher$Api33Impl$$ExternalSyntheticLambda0;
HSPLandroidx/activity/OnBackPressedDispatcher$Api33Impl$$ExternalSyntheticLambda0;-><init>(Lkotlin/jvm/functions/Function0;)V
Landroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/lifecycle/Lifecycle;Landroidx/activity/OnBackPressedCallback;)V
HSPLandroidx/activity/OnBackPressedDispatcher$LifecycleOnBackPressedCancellable;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
Landroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;
HSPLandroidx/activity/OnBackPressedDispatcher$OnBackPressedCancellable;-><init>(Landroidx/activity/OnBackPressedDispatcher;Landroidx/activity/OnBackPressedCallback;)V
Landroidx/activity/OnBackPressedDispatcherOwner;
Landroidx/activity/R$id;
Landroidx/activity/SystemBarStyle;
HSPLandroidx/activity/SystemBarStyle;-><clinit>()V
HSPLandroidx/activity/SystemBarStyle;-><init>(III)V
HSPLandroidx/activity/SystemBarStyle;-><init>(IIILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/activity/SystemBarStyle;->getNightMode$activity_release()I
HSPLandroidx/activity/SystemBarStyle;->getScrimWithEnforcedContrast$activity_release(Z)I
HSPLandroidx/activity/SystemBarStyle;->isDark$activity_release(Landroid/content/res/Resources;)Z
Landroidx/activity/SystemBarStyle$Companion;
HSPLandroidx/activity/SystemBarStyle$Companion;-><init>()V
HSPLandroidx/activity/SystemBarStyle$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/activity/SystemBarStyle$Companion;->auto(II)Landroidx/activity/SystemBarStyle;
Landroidx/activity/ViewTreeOnBackPressedDispatcherOwner;
HSPLandroidx/activity/ViewTreeOnBackPressedDispatcherOwner;->set(Landroid/view/View;Landroidx/activity/OnBackPressedDispatcherOwner;)V
Landroidx/activity/contextaware/ContextAware;
Landroidx/activity/contextaware/ContextAwareHelper;
HSPLandroidx/activity/contextaware/ContextAwareHelper;-><init>()V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->addOnContextAvailableListener(Landroidx/activity/contextaware/OnContextAvailableListener;)V
HSPLandroidx/activity/contextaware/ContextAwareHelper;->dispatchOnContextAvailable(Landroid/content/Context;)V
Landroidx/activity/contextaware/OnContextAvailableListener;
Landroidx/activity/result/ActivityResult;
Landroidx/activity/result/ActivityResultCallback;
Landroidx/activity/result/ActivityResultCaller;
Landroidx/activity/result/ActivityResultLauncher;
HSPLandroidx/activity/result/ActivityResultLauncher;-><init>()V
Landroidx/activity/result/ActivityResultRegistry;
HSPLandroidx/activity/result/ActivityResultRegistry;-><init>()V
HSPLandroidx/activity/result/ActivityResultRegistry;->bindRcKey(ILjava/lang/String;)V
HSPLandroidx/activity/result/ActivityResultRegistry;->generateRandomNumber()I
HSPLandroidx/activity/result/ActivityResultRegistry;->register(Ljava/lang/String;Landroidx/activity/result/contract/ActivityResultContract;Landroidx/activity/result/ActivityResultCallback;)Landroidx/activity/result/ActivityResultLauncher;
HSPLandroidx/activity/result/ActivityResultRegistry;->registerKey(Ljava/lang/String;)V
Landroidx/activity/result/ActivityResultRegistry$3;
HSPLandroidx/activity/result/ActivityResultRegistry$3;-><init>(Landroidx/activity/result/ActivityResultRegistry;Ljava/lang/String;Landroidx/activity/result/contract/ActivityResultContract;)V
Landroidx/activity/result/ActivityResultRegistry$CallbackAndContract;
HSPLandroidx/activity/result/ActivityResultRegistry$CallbackAndContract;-><init>(Landroidx/activity/result/ActivityResultCallback;Landroidx/activity/result/contract/ActivityResultContract;)V
Landroidx/activity/result/ActivityResultRegistryOwner;
Landroidx/activity/result/contract/ActivityResultContract;
HSPLandroidx/activity/result/contract/ActivityResultContract;-><init>()V
Landroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;-><clinit>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions;-><init>()V
Landroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion;
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$RequestMultiplePermissions$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
Landroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;-><clinit>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult;-><init>()V
Landroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion;
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion;-><init>()V
HSPLandroidx/activity/result/contract/ActivityResultContracts$StartActivityForResult$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
# Baseline profile rules for androidx.tv.tv-material
# ===================================================

HSPLandroidx/tv/material3/Border;->**(**)**
Landroidx/tv/material3/BorderIndication;
HSPLandroidx/tv/material3/ColorScheme;->**(**)**
HSPLandroidx/tv/material3/ColorSchemeKt**->**(**)**
HSPLandroidx/tv/material3/ContentColorKt**->**(**)**
HSPLandroidx/tv/material3/Glow;->**(**)**
HSPLandroidx/tv/material3/GlowIndication;->**(**)**
HSPLandroidx/tv/material3/GlowIndicationInstance;->**(**)**
HSPLandroidx/tv/material3/ScaleIndication;->**(**)**
HSPLandroidx/tv/material3/ScaleIndicationInstance;->**(**)**
HSPLandroidx/tv/material3/ScaleIndicationTokens;->**(**)**
HSPLandroidx/tv/material3/ShapesKt**->**(**)**
HSPLandroidx/tv/material3/SurfaceKt**->**(**)**
HSPLandroidx/tv/material3/TabColors;->**(**)**
HSPLandroidx/tv/material3/TabKt**->**(**)**
HSPLandroidx/tv/material3/TabRowDefaults;->**(**)**
HSPLandroidx/tv/material3/TabRowKt**->**(**)**
HSPLandroidx/tv/material3/TabRowScopeImpl;->**(**)**
HSPLandroidx/tv/material3/TabRowSlots;->**(**)**
HSPLandroidx/tv/material3/TextKt**->**(**)**
HSPLandroidx/tv/material3/ToggleableSurfaceBorder;->**(**)**
HSPLandroidx/tv/material3/ToggleableSurfaceColors;->**(**)**
HSPLandroidx/tv/material3/ToggleableSurfaceGlow;->**(**)**
HSPLandroidx/tv/material3/ToggleableSurfaceScale;->**(**)**
HSPLandroidx/tv/material3/ToggleableSurfaceShape;->**(**)**
HSPLandroidx/tv/material3/tokens/ColorLightTokens;->**(**)**
HSPLandroidx/tv/material3/tokens/Elevation;->**(**)**
HSPLandroidx/tv/material3/tokens/PaletteTokens;->**(**)**
HSPLandroidx/tv/material3/tokens/ShapeTokens;->**(**)**
HSPLandroidx/tv/material3/tokens/TypographyTokensKt**->**(**)**

# Baseline profile rules for androidx.compose.foundation.layout
# =============================================
# Layout is incredibly important for performance, and represents many hot code paths. For now, we
# will include all of the layout namespace
HSPLandroidx/compose/foundation/layout/**->**(**)**
Landroidx/compose/foundation/layout/**;
# Baseline profile rules for androidx.compose.foundation
# =============================================
#
# Include all methods in common top level classes
HSPLandroidx/compose/foundation/AbstractClickable**->**(**)**
HSPLandroidx/compose/foundation/AndroidEdgeEffectOverscrollEffect**->**(**)**
HSPLandroidx/compose/foundation/AndroidOverscroll_**->**(**)**
HSPLandroidx/compose/foundation/BackgroundElement;->**(**)**
HSPLandroidx/compose/foundation/BackgroundNode;->**(**)**
HSPLandroidx/compose/foundation/BorderKt**->**(**)**
HSPLandroidx/compose/foundation/BorderStroke;->**(**)**
HSPLandroidx/compose/foundation/CanvasKt**->**(**)**
HSPLandroidx/compose/foundation/Clickable**->**(**)**
HSPLandroidx/compose/foundation/DrawOverscrollModifier;->**(**)**
HSPLandroidx/compose/foundation/EdgeEffectWrapper;->**(**)**
HSPLandroidx/compose/foundation/Focusable**->**(**)**
HSPLandroidx/compose/foundation/FocusedBounds**->**(**)**
HSPLandroidx/compose/foundation/Hoverable**->**(**)**
HSPLandroidx/compose/foundation/ImageKt**->**(**)**
HSPLandroidx/compose/foundation/IndicationKt**->**(**)**
HSPLandroidx/compose/foundation/IndicationModifier;->**(**)**
HSPLandroidx/compose/foundation/MutatePriority;->**(**)**
HSPLandroidx/compose/foundation/MutatorMutex**->**(**)**
HSPLandroidx/compose/foundation/PinnableParentConsumer;->**(**)**
HSPLandroidx/compose/foundation/ScrollKt**->**(**)**
HSPLandroidx/compose/foundation/ScrollState**->**(**)**
HSPLandroidx/compose/foundation/ScrollingLayoutModifier**->**(**)**
#
# Include everything inside of the gestures namespace
HSPLandroidx/compose/foundation/gestures/**->**(**)**
#
# Include everything inside of the interaction namespace
HSPLandroidx/compose/foundation/interaction/*;->**(**)**

# Include everything inside of the lazy namespaces
HSPLandroidx/compose/foundation/lazy/**->**(**)**

# Include everything inside relocation namespace
HSPLandroidx/compose/foundation/relocation/**->**(**)**

# Include everything inside selection namespace
HSPLandroidx/compose/foundation/selection/**->**(**)**

#
# common shape classes
HSPLandroidx/compose/foundation/shape/CornerBasedShape;->**(**)**
HSPLandroidx/compose/foundation/shape/CornerSizeKt;->**(**)**
HSPLandroidx/compose/foundation/shape/DpCornerSize;->**(**)**
HSPLandroidx/compose/foundation/shape/RoundedCornerShape;->**(**)**
HSPLandroidx/compose/foundation/shape/PercentCornerSize;->**(**)**
#

# Include everything inside of the text namespace
HSPLandroidx/compose/foundation/text/*;->**(**)**
HSPLandroidx/compose/foundation/text/modifiers/**->**(**)**
HSPLandroidx/compose/foundation/text/selection/SimpleLayoutKt**->**(**)**
HSPLandroidx/compose/foundation/text/selection/TextFieldSelectionManager;->**(**)**

#
# Include all of foundation
Landroidx/compose/foundation/**;

HSPLandroidx/compose/foundation/AbstractClickableNode;->**(**)**
HSPLandroidx/compose/foundation/AndroidEdgeEffectOverscrollEffect;->**(**)**
HSPLandroidx/compose/foundation/AndroidEdgeEffectOverscrollFactory;->**(**)**
HSPLandroidx/compose/foundation/AndroidOverscroll_androidKt**->**(**)**
HPLandroidx/compose/foundation/Api31Impl;->**(**)**
HSPLandroidx/compose/foundation/BackgroundElement;->**(**)**
HSPLandroidx/compose/foundation/BackgroundKt**->**(**)**
HSPLandroidx/compose/foundation/BackgroundNode;->**(**)**
PLandroidx/compose/foundation/BorderKt**->**(**)**
HPLandroidx/compose/foundation/BorderModifierNode;->**(**)**
PLandroidx/compose/foundation/BorderModifierNodeElement;->**(**)**
PLandroidx/compose/foundation/BorderStroke;->**(**)**
PLandroidx/compose/foundation/BorderStrokeKt**->**(**)**
HSPLandroidx/compose/foundation/CanvasKt**->**(**)**
HSPLandroidx/compose/foundation/CheckScrollableContainerConstraintsKt**->**(**)**
HSPLandroidx/compose/foundation/ClickableElement;->**(**)**
HSPLandroidx/compose/foundation/ClickableKt**->**(**)**
HSPLandroidx/compose/foundation/ClickableNode;->**(**)**
SPLandroidx/compose/foundation/Clickable_androidKt**->**(**)**
HSPLandroidx/compose/foundation/ClipScrollableContainerKt**->**(**)**
HSPLandroidx/compose/foundation/CombinedClickableElement;->**(**)**
HSPLandroidx/compose/foundation/CombinedClickableNode;->**(**)**
SPLandroidx/compose/foundation/ComposeFoundationFlags;->**(**)**
PLandroidx/compose/foundation/EdgeEffectCompat;->**(**)**
HSPLandroidx/compose/foundation/EdgeEffectWrapper;->**(**)**
HSPLandroidx/compose/foundation/ExcludeFromSystemGestureElement;->**(**)**
HSPLandroidx/compose/foundation/ExcludeFromSystemGestureNode;->**(**)**
HPLandroidx/compose/foundation/FocusableElement;->**(**)**
HPLandroidx/compose/foundation/FocusableKt**->**(**)**
HSPLandroidx/compose/foundation/FocusableNode;->**(**)**
SPLandroidx/compose/foundation/FocusedBoundsObserverNode;->**(**)**
SPLandroidx/compose/foundation/HorizontalScrollableClipShape;->**(**)**
PLandroidx/compose/foundation/ImageKt**->**(**)**
Landroidx/compose/foundation/Indication;
SPLandroidx/compose/foundation/IndicationKt**->**(**)**
PLandroidx/compose/foundation/IndicationModifierElement;->**(**)**
PLandroidx/compose/foundation/IndicationModifierNode;->**(**)**
Landroidx/compose/foundation/IndicationNodeFactory;
SPLandroidx/compose/foundation/MutatePriority;->**(**)**
PLandroidx/compose/foundation/MutationInterruptedException;->**(**)**
HSPLandroidx/compose/foundation/MutatorMutex;->**(**)**
SPLandroidx/compose/foundation/OverscrollConfiguration;->**(**)**
SPLandroidx/compose/foundation/OverscrollConfiguration_androidKt**->**(**)**
PLandroidx/compose/foundation/OverscrollEffect;->**(**)**
Landroidx/compose/foundation/OverscrollFactory;
HSPLandroidx/compose/foundation/OverscrollKt**->**(**)**
HSPLandroidx/compose/foundation/OverscrollModifierElement;->**(**)**
HSPLandroidx/compose/foundation/OverscrollModifierNode;->**(**)**
PLandroidx/compose/foundation/ProgressSemanticsKt**->**(**)**
HSPLandroidx/compose/foundation/RectListNode;->**(**)**
HSPLandroidx/compose/foundation/ScrollKt**->**(**)**
HSPLandroidx/compose/foundation/ScrollNode;->**(**)**
HSPLandroidx/compose/foundation/ScrollState;->**(**)**
HSPLandroidx/compose/foundation/ScrollingContainerElement;->**(**)**
HSPLandroidx/compose/foundation/ScrollingContainerKt**->**(**)**
HSPLandroidx/compose/foundation/ScrollingContainerNode;->**(**)**
HSPLandroidx/compose/foundation/ScrollingLayoutElement;->**(**)**
HSPLandroidx/compose/foundation/StretchOverscrollNode;->**(**)**
HSPLandroidx/compose/foundation/SystemGestureExclusionKt**->**(**)**
HSPLandroidx/compose/foundation/VerticalScrollableClipShape;->**(**)**
PLandroidx/compose/foundation/WrappedOverscrollEffect;->**(**)**
HSPLandroidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue;->**(**)**
SPLandroidx/compose/foundation/gestures/BringIntoViewSpec;->**(**)**
SPLandroidx/compose/foundation/gestures/BringIntoViewSpec_androidKt**->**(**)**
HSPLandroidx/compose/foundation/gestures/ContentInViewNode;->**(**)**
HSPLandroidx/compose/foundation/gestures/DefaultFlingBehavior;->**(**)**
HSPLandroidx/compose/foundation/gestures/DefaultScrollableState;->**(**)**
HPLandroidx/compose/foundation/gestures/DragEvent;->**(**)**
HSPLandroidx/compose/foundation/gestures/DragGestureDetectorKt**->**(**)**
HSPLandroidx/compose/foundation/gestures/DragGestureNode;->**(**)**
Landroidx/compose/foundation/gestures/DragScope;
HSPLandroidx/compose/foundation/gestures/DraggableElement;->**(**)**
HSPLandroidx/compose/foundation/gestures/DraggableKt**->**(**)**
HSPLandroidx/compose/foundation/gestures/DraggableNode;->**(**)**
Landroidx/compose/foundation/gestures/DraggableState;
Landroidx/compose/foundation/gestures/FlingBehavior;
PLandroidx/compose/foundation/gestures/FlingCancellationException;->**(**)**
HSPLandroidx/compose/foundation/gestures/ForEachGestureKt**->**(**)**
Landroidx/compose/foundation/gestures/NestedScrollScope;
SPLandroidx/compose/foundation/gestures/Orientation;->**(**)**
Landroidx/compose/foundation/gestures/PressGestureScope;
HSPLandroidx/compose/foundation/gestures/PressGestureScopeImpl;->**(**)**
Landroidx/compose/foundation/gestures/ScrollScope;
HSPLandroidx/compose/foundation/gestures/ScrollableContainerNode;->**(**)**
Landroidx/compose/foundation/gestures/ScrollableDefaultFlingBehavior;
HSPLandroidx/compose/foundation/gestures/ScrollableDefaults;->**(**)**
PLandroidx/compose/foundation/gestures/ScrollableElement;->**(**)**
HSPLandroidx/compose/foundation/gestures/ScrollableKt**->**(**)**
HSPLandroidx/compose/foundation/gestures/ScrollableNestedScrollConnection;->**(**)**
HSPLandroidx/compose/foundation/gestures/ScrollableNode;->**(**)**
SPLandroidx/compose/foundation/gestures/ScrollableState;->**(**)**
SPLandroidx/compose/foundation/gestures/ScrollableStateKt**->**(**)**
HSPLandroidx/compose/foundation/gestures/ScrollingLogic;->**(**)**
HSPLandroidx/compose/foundation/gestures/TapGestureDetectorKt**->**(**)**
SPLandroidx/compose/foundation/gestures/TapGestureDetector_androidKt**->**(**)**
Landroidx/compose/foundation/gestures/TargetedFlingBehavior;
HSPLandroidx/compose/foundation/gestures/TouchSlopDetector;->**(**)**
PLandroidx/compose/foundation/gestures/snapping/AnimationResult;->**(**)**
PLandroidx/compose/foundation/gestures/snapping/FinalSnappingItem;->**(**)**
HPLandroidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt**->**(**)**
HSPLandroidx/compose/foundation/gestures/snapping/PagerSnapLayoutInfoProviderKt**->**(**)**
HSPLandroidx/compose/foundation/gestures/snapping/SnapFlingBehavior;->**(**)**
HSPLandroidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt**->**(**)**
Landroidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider;
SPLandroidx/compose/foundation/gestures/snapping/SnapPosition;->**(**)**
HSPLandroidx/compose/foundation/gestures/snapping/SnapPositionKt**->**(**)**
PLandroidx/compose/foundation/interaction/DragInteraction;->**(**)**
Landroidx/compose/foundation/interaction/FocusInteraction;
Landroidx/compose/foundation/interaction/Interaction;
Landroidx/compose/foundation/interaction/InteractionSource;
HSPLandroidx/compose/foundation/interaction/InteractionSourceKt**->**(**)**
Landroidx/compose/foundation/interaction/MutableInteractionSource;
HSPLandroidx/compose/foundation/interaction/MutableInteractionSourceImpl;->**(**)**
SPLandroidx/compose/foundation/interaction/PressInteraction;->**(**)**
PLandroidx/compose/foundation/internal/PlatformOptimizedCancellationException;->**(**)**
PLandroidx/compose/foundation/internal/PlatformOptimizedCancellationException_jvmKt**->**(**)**
HSPLandroidx/compose/foundation/layout/Arrangement;->**(**)**
HSPLandroidx/compose/foundation/layout/BoxChildDataElement;->**(**)**
HSPLandroidx/compose/foundation/layout/BoxChildDataNode;->**(**)**
HSPLandroidx/compose/foundation/layout/BoxKt**->**(**)**
HSPLandroidx/compose/foundation/layout/BoxMeasurePolicy;->**(**)**
Landroidx/compose/foundation/layout/BoxScope;
HSPLandroidx/compose/foundation/layout/BoxScopeInstance;->**(**)**
HSPLandroidx/compose/foundation/layout/BoxWithConstraintsKt**->**(**)**
Landroidx/compose/foundation/layout/BoxWithConstraintsScope;
HSPLandroidx/compose/foundation/layout/BoxWithConstraintsScopeImpl;->**(**)**
HSPLandroidx/compose/foundation/layout/ColumnKt**->**(**)**
HSPLandroidx/compose/foundation/layout/ColumnMeasurePolicy;->**(**)**
PLandroidx/compose/foundation/layout/ColumnScope;->**(**)**
HSPLandroidx/compose/foundation/layout/ColumnScopeInstance;->**(**)**
SPLandroidx/compose/foundation/layout/CrossAxisAlignment;->**(**)**
SPLandroidx/compose/foundation/layout/Direction;->**(**)**
HSPLandroidx/compose/foundation/layout/FillElement;->**(**)**
HSPLandroidx/compose/foundation/layout/FillNode;->**(**)**
HPLandroidx/compose/foundation/layout/FlowLayoutBuildingBlocks;->**(**)**
Landroidx/compose/foundation/layout/FlowLayoutData;
HPLandroidx/compose/foundation/layout/FlowLayoutKt**->**(**)**
PLandroidx/compose/foundation/layout/FlowLayoutOverflow;->**(**)**
HPLandroidx/compose/foundation/layout/FlowLayoutOverflowState;->**(**)**
HPLandroidx/compose/foundation/layout/FlowLineMeasurePolicy;->**(**)**
HPLandroidx/compose/foundation/layout/FlowMeasurePolicy;->**(**)**
PLandroidx/compose/foundation/layout/FlowRowOverflow;->**(**)**
PLandroidx/compose/foundation/layout/FlowRowScopeInstance;->**(**)**
HPLandroidx/compose/foundation/layout/HorizontalAlignElement;->**(**)**
PLandroidx/compose/foundation/layout/HorizontalAlignNode;->**(**)**
PLandroidx/compose/foundation/layout/IntrinsicHeightElement;->**(**)**
PLandroidx/compose/foundation/layout/IntrinsicHeightNode;->**(**)**
HSPLandroidx/compose/foundation/layout/IntrinsicKt**->**(**)**
HSPLandroidx/compose/foundation/layout/IntrinsicMeasureBlocks;->**(**)**
SPLandroidx/compose/foundation/layout/IntrinsicSize;->**(**)**
HSPLandroidx/compose/foundation/layout/IntrinsicSizeModifier;->**(**)**
HSPLandroidx/compose/foundation/layout/IntrinsicWidthElement;->**(**)**
HSPLandroidx/compose/foundation/layout/IntrinsicWidthNode;->**(**)**
PLandroidx/compose/foundation/layout/LayoutOrientation;->**(**)**
HPLandroidx/compose/foundation/layout/LayoutWeightElement;->**(**)**
HPLandroidx/compose/foundation/layout/LayoutWeightNode;->**(**)**
HSPLandroidx/compose/foundation/layout/OffsetKt**->**(**)**
HSPLandroidx/compose/foundation/layout/OffsetPxElement;->**(**)**
SPLandroidx/compose/foundation/layout/OffsetPxNode;->**(**)**
HPLandroidx/compose/foundation/layout/OrientationIndependentConstraints;->**(**)**
HSPLandroidx/compose/foundation/layout/PaddingElement;->**(**)**
HSPLandroidx/compose/foundation/layout/PaddingKt**->**(**)**
HSPLandroidx/compose/foundation/layout/PaddingNode;->**(**)**
Landroidx/compose/foundation/layout/PaddingValues;
HSPLandroidx/compose/foundation/layout/PaddingValuesElement;->**(**)**
HSPLandroidx/compose/foundation/layout/PaddingValuesImpl;->**(**)**
HSPLandroidx/compose/foundation/layout/PaddingValuesModifier;->**(**)**
HSPLandroidx/compose/foundation/layout/RowColumnImplKt**->**(**)**
HSPLandroidx/compose/foundation/layout/RowColumnMeasurePolicy;->**(**)**
HSPLandroidx/compose/foundation/layout/RowColumnMeasurePolicyKt**->**(**)**
HSPLandroidx/compose/foundation/layout/RowColumnParentData;->**(**)**
HSPLandroidx/compose/foundation/layout/RowKt**->**(**)**
HSPLandroidx/compose/foundation/layout/RowMeasurePolicy;->**(**)**
HPLandroidx/compose/foundation/layout/RowScope;->**(**)**
HSPLandroidx/compose/foundation/layout/RowScopeInstance;->**(**)**
HSPLandroidx/compose/foundation/layout/SizeElement;->**(**)**
HSPLandroidx/compose/foundation/layout/SizeKt**->**(**)**
HSPLandroidx/compose/foundation/layout/SizeNode;->**(**)**
HSPLandroidx/compose/foundation/layout/SpacerKt**->**(**)**
HSPLandroidx/compose/foundation/layout/SpacerMeasurePolicy;->**(**)**
HSPLandroidx/compose/foundation/layout/UnspecifiedConstraintsElement;->**(**)**
HSPLandroidx/compose/foundation/layout/UnspecifiedConstraintsNode;->**(**)**
SPLandroidx/compose/foundation/layout/VerticalAlignElement;->**(**)**
HSPLandroidx/compose/foundation/layout/VerticalAlignNode;->**(**)**
HSPLandroidx/compose/foundation/layout/WrapContentElement;->**(**)**
HPLandroidx/compose/foundation/layout/WrapContentNode;->**(**)**
Landroidx/compose/foundation/lazy/CacheWindowListPrefetchStrategy;
HSPLandroidx/compose/foundation/lazy/DefaultLazyListPrefetchStrategy;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyDslKt**->**(**)**
Landroidx/compose/foundation/lazy/LazyItemScope;
HSPLandroidx/compose/foundation/lazy/LazyItemScopeImpl;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyLayoutSemanticStateKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt**->**(**)**
SPLandroidx/compose/foundation/lazy/LazyListBeyondBoundsState;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListInterval;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListIntervalContent;->**(**)**
Landroidx/compose/foundation/lazy/LazyListItemInfo;
Landroidx/compose/foundation/lazy/LazyListItemProvider;
HSPLandroidx/compose/foundation/lazy/LazyListItemProviderImpl;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListItemProviderKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListKt**->**(**)**
Landroidx/compose/foundation/lazy/LazyListLayoutInfo;
HSPLandroidx/compose/foundation/lazy/LazyListMeasureKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListMeasureResult;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListMeasuredItem;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListMeasuredItemProvider;->**(**)**
Landroidx/compose/foundation/lazy/LazyListPrefetchScope;
SPLandroidx/compose/foundation/lazy/LazyListPrefetchStrategy;->**(**)**
SPLandroidx/compose/foundation/lazy/LazyListPrefetchStrategyKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListScope;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListScrollPosition;->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListSemanticsKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyListState;->**(**)**
SPLandroidx/compose/foundation/lazy/LazyListStateKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/LazyList_androidKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/AndroidPrefetchScheduler;->**(**)**
PLandroidx/compose/foundation/lazy/layout/Averages;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/DefaultLazyKey;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/IntervalList;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/IntervalListKt**->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierElement;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierNode;->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent;->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutItemAnimation;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutItemAnimator;->**(**)**
SPLandroidx/compose/foundation/lazy/layout/LazyLayoutItemAnimatorKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt**->**(**)**
SPLandroidx/compose/foundation/lazy/layout/LazyLayoutItemProvider;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy;->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutKt**->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl;->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItem;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemKt**->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutMeasuredItemProvider;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt**->**(**)**
SPLandroidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutPrefetchStateKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutScrollDeltaBetweenPasses;->**(**)**
Landroidx/compose/foundation/lazy/layout/LazyLayoutSemanticState;
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifier;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutSemanticsModifierNode;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazyLayoutStickyItemsKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazySaveableStateHolder;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt**->**(**)**
SPLandroidx/compose/foundation/lazy/layout/Lazy_androidKt**->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/MutableIntervalList;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/ObservableScopeInvalidator;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/PrefetchHandleProvider;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/PrefetchMetrics;->**(**)**
Landroidx/compose/foundation/lazy/layout/PrefetchRequest;
Landroidx/compose/foundation/lazy/layout/PrefetchRequestScope;
Landroidx/compose/foundation/lazy/layout/PrefetchScheduler;
HSPLandroidx/compose/foundation/lazy/layout/PrefetchScheduler_androidKt**->**(**)**
SPLandroidx/compose/foundation/lazy/layout/StableValue;->**(**)**
SPLandroidx/compose/foundation/lazy/layout/StickyItemsPlacement;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/TraversablePrefetchStateModifierElement;->**(**)**
HSPLandroidx/compose/foundation/lazy/layout/TraversablePrefetchStateNode;->**(**)**
HSPLandroidx/compose/foundation/pager/DefaultPagerNestedScrollConnection;->**(**)**
HSPLandroidx/compose/foundation/pager/LazyLayoutPagerKt**->**(**)**
SPLandroidx/compose/foundation/pager/LazyLayoutSemanticStateKt**->**(**)**
HSPLandroidx/compose/foundation/pager/MeasuredPage;->**(**)**
Landroidx/compose/foundation/pager/PageInfo;
SPLandroidx/compose/foundation/pager/PageSize;->**(**)**
SPLandroidx/compose/foundation/pager/PagerBeyondBoundsModifierKt**->**(**)**
SPLandroidx/compose/foundation/pager/PagerBeyondBoundsState;->**(**)**
SPLandroidx/compose/foundation/pager/PagerBringIntoViewSpec;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerDefaults;->**(**)**
SPLandroidx/compose/foundation/pager/PagerIntervalContent;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerKt**->**(**)**
Landroidx/compose/foundation/pager/PagerLayoutInfo;
HPLandroidx/compose/foundation/pager/PagerLayoutInfoKt**->**(**)**
HSPLandroidx/compose/foundation/pager/PagerLayoutIntervalContent;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerLazyLayoutItemProvider;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerMeasureKt**->**(**)**
HSPLandroidx/compose/foundation/pager/PagerMeasurePolicyKt**->**(**)**
HSPLandroidx/compose/foundation/pager/PagerMeasureResult;->**(**)**
Landroidx/compose/foundation/pager/PagerScope;
SPLandroidx/compose/foundation/pager/PagerScopeImpl;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerScrollPosition;->**(**)**
HPLandroidx/compose/foundation/pager/PagerScrollPositionKt**->**(**)**
SPLandroidx/compose/foundation/pager/PagerSemanticsKt**->**(**)**
SPLandroidx/compose/foundation/pager/PagerSnapDistance;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerSnapDistanceMaxPages;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerState;->**(**)**
HSPLandroidx/compose/foundation/pager/PagerStateKt**->**(**)**
HSPLandroidx/compose/foundation/pager/PagerWrapperFlingBehavior;->**(**)**
Landroidx/compose/foundation/relocation/BringIntoViewResponder;
HSPLandroidx/compose/foundation/relocation/BringIntoViewResponderNode;->**(**)**
PLandroidx/compose/foundation/selection/SelectableElement;->**(**)**
PLandroidx/compose/foundation/selection/SelectableGroupKt**->**(**)**
PLandroidx/compose/foundation/selection/SelectableKt**->**(**)**
PLandroidx/compose/foundation/selection/SelectableNode;->**(**)**
HPLandroidx/compose/foundation/selection/ToggleableElement;->**(**)**
HPLandroidx/compose/foundation/selection/ToggleableKt**->**(**)**
HPLandroidx/compose/foundation/selection/ToggleableNode;->**(**)**
HSPLandroidx/compose/foundation/shape/CornerBasedShape;->**(**)**
Landroidx/compose/foundation/shape/CornerSize;
SPLandroidx/compose/foundation/shape/CornerSizeKt**->**(**)**
HSPLandroidx/compose/foundation/shape/DpCornerSize;->**(**)**
HSPLandroidx/compose/foundation/shape/PercentCornerSize;->**(**)**
HSPLandroidx/compose/foundation/shape/RoundedCornerShape;->**(**)**
HSPLandroidx/compose/foundation/shape/RoundedCornerShapeKt**->**(**)**
HSPLandroidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt**->**(**)**
HSPLandroidx/compose/foundation/text/BasicTextKt**->**(**)**
HSPLandroidx/compose/foundation/text/BasicText_androidKt**->**(**)**
HSPLandroidx/compose/foundation/text/EmptyMeasurePolicy;->**(**)**
SPLandroidx/compose/foundation/text/HeightInLinesModifierKt**->**(**)**
Landroidx/compose/foundation/text/TextAutoSize;
HSPLandroidx/compose/foundation/text/TextDelegateKt**->**(**)**
HSPLandroidx/compose/foundation/text/modifiers/InlineDensity;->**(**)**
HSPLandroidx/compose/foundation/text/modifiers/LayoutUtilsKt**->**(**)**
HSPLandroidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache;->**(**)**
Landroidx/compose/foundation/text/modifiers/SelectionController;
HSPLandroidx/compose/foundation/text/modifiers/TextAnnotatedStringElement;->**(**)**
HSPLandroidx/compose/foundation/text/modifiers/TextAnnotatedStringNode;->**(**)**
HSPLandroidx/compose/foundation/text/modifiers/TextAnnotatedStringNodeKt**->**(**)**
Landroidx/compose/foundation/text/selection/SelectionRegistrar;
SPLandroidx/compose/foundation/text/selection/SelectionRegistrarKt**->**(**)**
SPLandroidx/compose/foundation/text/selection/TextSelectionColors;->**(**)**
SPLandroidx/compose/foundation/text/selection/TextSelectionColorsKt**->**(**)**

# Baseline profile rules for androidx.compose.animation.core
# =============================================
# In practice it seems like almost every class in animation/core ends up getting loaded in even a
# relatively small sample, and most end up getting marked as "HSP". Since Animation is a high value
# target for performance - fade in, scroll, etc we are going to be liberal in the animation profile
# rules and just mark the entire module.

HSPLandroidx/compose/animation/core/**->**(**)**

Landroidx/compose/animation/core/**;

# Baseline profile rules for androidx.compose.animation
# =============================================
HSPLandroidx/compose/animation/AndroidFlingSpline**;->**(**)**
HSPLandroidx/compose/animation/AnimatedVisibilityKt**->**(**)**
HSPLandroidx/compose/animation/AnimatedVisibilityScope;-><init>(**)V
HSPLandroidx/compose/animation/ChangeSize;->**(**)**
HSPLandroidx/compose/animation/ColorVectorConverterKt**->**(**)**
HSPLandroidx/compose/animation/CrossfadeAnimationItem;->**(**)**
HSPLandroidx/compose/animation/CrossfadeKt**->**(**)**
HSPLandroidx/compose/animation/EnterExitTransitionKt;->**(**)**
HSPLandroidx/compose/animation/FlingCalculator**;->**(**)**
HSPLandroidx/compose/animation/SingleValueAnimationKt;->**(**)**
HSPLandroidx/compose/animation/SplineBasedFloatDecayAnimationSpec**->**(**)**
HSPLandroidx/compose/animation/SplineBasedDecayKt;->**(**)**
HSPLandroidx/compose/animation/TransitionData;->**(**)**

# Include all of androidx.compose.animation
Landroidx/compose/animation/*;
# Baseline profile rules for androidx.compose.ui.unit
# =============================================
# everything in unit is relatively small and in the hot path, so we just add everything
HSPLandroidx/compose/ui/unit/**->**(**)**
Landroidx/compose/ui/unit/**

# Baseline profile rules for androidx.compose.ui.geometry
# =============================================
# All of geometry is highly used, small, and mathy, so we include everything
HSPLandroidx/compose/ui/geometry/*;->**(**)**
Landroidx/compose/ui/geometry/*;
# Baseline profile rules for androidx.compose.ui.util
# =============================================
HSPLandroidx/compose/ui/util/MathHelpersKt;->lerp(FFF)F
Landroidx/compose/ui/util/MathHelpersKt;

# Baseline profile rules for androidx.compose.ui.text
# =============================================

HSPLandroidx/compose/ui/text/AndroidParagraph**->**(**)**
HSPLandroidx/compose/ui/text/AnnotatedString**->**(**)**
HSPLandroidx/compose/ui/text/MultiParagraph;->**(**)**
HSPLandroidx/compose/ui/text/MultiParagraphIntrinsics**->**(**)**
HSPLandroidx/compose/ui/text/ParagraphInfo;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphIntrinsicInfo;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphStyle;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphStyleKt;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphKt;->**(**)**
HSPLandroidx/compose/ui/text/PlatformTextStyle;->**(**)**
HSPLandroidx/compose/ui/text/SpanStyle;->**(**)**
HSPLandroidx/compose/ui/text/SpanStyleKt;->**(**)**
HSPLandroidx/compose/ui/text/TextLayoutInput;->**(**)**
HSPLandroidx/compose/ui/text/TextLayoutResult;->**(**)**
HSPLandroidx/compose/ui/text/TextPainter;->**(**)**
HSPLandroidx/compose/ui/text/TextRange**->**(**)**
HSPLandroidx/compose/ui/text/TextStyle**->**(**)**
HSPLandroidx/compose/ui/text/android/BoringLayoutFactory**->**(**)**
HSPLandroidx/compose/ui/text/android/CharSequenceCharacterIterator;->**(**)**
HSPLandroidx/compose/ui/text/android/LayoutIntrinsics**->**(**)**
HSPLandroidx/compose/ui/text/android/Paint**->**(**)**
HSPLandroidx/compose/ui/text/android/StaticLayoutFactory**->**(**)**
HSPLandroidx/compose/ui/text/android/StaticLayoutParams;->**(**)**
HSPLandroidx/compose/ui/text/android/TextAlignmentAdapter;->**(**)**
HSPLandroidx/compose/ui/text/android/TextLayout;->**(**)**
HSPLandroidx/compose/ui/text/android/TextLayoutKt;->**(**)**
HSPLandroidx/compose/ui/text/android/style/BaselineShiftSpan;->**(**)**
HSPLandroidx/compose/ui/text/android/style/LetterSpacingSpanPx;->**(**)**
HSPLandroidx/compose/ui/text/android/style/LineHeightSpan;->**(**)**
HSPLandroidx/compose/ui/text/android/style/TypefaceSpan;->**(**)**
HSPLandroidx/compose/ui/text/font/AndroidFontLoader;->**(**)**
HSPLandroidx/compose/ui/text/font/AndroidFontResolveInterceptor**->**(**)**
HSPLandroidx/compose/ui/text/font/AsyncTypefaceCache;->**(**)**
HSPLandroidx/compose/ui/text/font/DefaultFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/FileBasedFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/FontFamily**->**(**)**
HSPLandroidx/compose/ui/text/font/FontFamilyResolverImpl**->**(**)**
HSPLandroidx/compose/ui/text/font/FontListFontFamilyTypefaceAdapter**->**(**)**
HSPLandroidx/compose/ui/text/font/FontKt;->**(**)**
HSPLandroidx/compose/ui/text/font/FontListFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/FontMatcher;->**(**)**
HSPLandroidx/compose/ui/text/font/FontStyle;->**(**)**
HSPLandroidx/compose/ui/text/font/FontSynthesis;->**(**)**
HSPLandroidx/compose/ui/text/font/FontWeight**->**(**)**
HSPLandroidx/compose/ui/text/font/PlatformFontFamilyTypefaceAdapter;->**(**)**
HSPLandroidx/compose/ui/text/font/PlatformTypefacesApi28;->**(**)**
HSPLandroidx/compose/ui/text/font/GenericFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/SystemFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/TypefaceRequest**->**(**)**
HSPLandroidx/compose/ui/text/font/ResourceFont;->**(**)**
HSPLandroidx/compose/ui/text/font/SystemFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/input/InputMethodManagerImpl**->**(**)**
HSPLandroidx/compose/ui/text/input/EditProcessor;->**(**)**
HSPLandroidx/compose/ui/text/input/EditingBuffer;->**(**)**
HSPLandroidx/compose/ui/text/input/ImeAction**->**(**)**
HSPLandroidx/compose/ui/text/input/ImeOptions**->**(**)**
HSPLandroidx/compose/ui/text/input/KeyboardType**->**(**)**
HSPLandroidx/compose/ui/text/input/TextFieldValue;->**(**)**
HSPLandroidx/compose/ui/text/input/TextInputService**->**(**)**
HSPLandroidx/compose/ui/text/input/TransformedText;->**(**)**
HSPLandroidx/compose/ui/text/intl/AndroidLocale**->**(**)**
HSPLandroidx/compose/ui/text/intl/Locale**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidAccessibility**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraphIntrinsics**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraph_androidKt;->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidTextPaint;->**(**)**
HSPLandroidx/compose/ui/text/platform/DefaultImpl;->**(**)**
HSPLandroidx/compose/ui/text/platform/DispatcherKt;->**(**)**
HSPLandroidx/compose/ui/text/platform/EmojiCompatStatus;->**(**)**
HSPLandroidx/compose/ui/text/platform/ImmutableBool;->**(**)**
HSPLandroidx/compose/ui/text/platform/SynchronizedObject;->**(**)**
HSPLandroidx/compose/ui/text/platform/TypefaceDirtyTracker;->**(**)**
HSPLandroidx/compose/ui/text/platform/TypefaceAdapter;->**(**)**
HSPLandroidx/compose/ui/text/platform/TypefaceAdapterHelperMethods;->**(**)**
HSPLandroidx/compose/ui/text/platform/URLSpanCache;->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/SpanRange;->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/SpannableExtensions_androidKt;->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/TextPaintExtensions_androidKt;->**(**)**
HSPLandroidx/compose/ui/text/style/BaselineShift**->**(**)**
HSPLandroidx/compose/ui/text/style/ColorStyle;->**(**)**
HSPLandroidx/compose/ui/text/style/LineHeightStyle;->**(**)**
HSPLandroidx/compose/ui/text/style/LineHeightStyle$Alignment;->**(**)**
HSPLandroidx/compose/ui/text/style/ResolvedTextDirection;->**(**)**
HSPLandroidx/compose/ui/text/style/TextAlign;->**(**)**
HSPLandroidx/compose/ui/text/style/TextDecoration;->**(**)**
HSPLandroidx/compose/ui/text/style/TextDrawStyle**->**(**)**
HSPLandroidx/compose/ui/text/style/TextDirection;->**(**)**
HSPLandroidx/compose/ui/text/style/TextForegroundStyle**;->**(**)**
HSPLandroidx/compose/ui/text/style/TextGeometricTransform;->**(**)**
HSPLandroidx/compose/ui/text/style/TextIndent;->**(**)**
HSPLandroidx/compose/ui/text/style/TextMotion;->**(**)**

Landroidx/compose/ui/text/**;

#
# We rely heavily on some text methods in kotlin stdlib, so makes sense to include them here
HSPLkotlin/text/CharsKt__CharJVMKt;->isWhitespace(C)Z
HSPLkotlin/text/MatcherMatchResult$groups$1;-><init>(Lkotlin/text/MatcherMatchResult;)V
HSPLkotlin/text/MatcherMatchResult;-><init>(Ljava/util/regex/Matcher;Ljava/lang/CharSequence;)V
HSPLkotlin/text/MatcherMatchResult;->getMatchResult()Ljava/util/regex/MatchResult;
HSPLkotlin/text/MatcherMatchResult;->getRange()Lkotlin/ranges/IntRange;
HSPLkotlin/text/MatcherMatchResult;->getValue()Ljava/lang/String;
HSPLkotlin/text/MatcherMatchResult;->next()Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex$Companion;-><init>()V
HSPLkotlin/text/Regex$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/text/Regex$findAll$1;-><init>(Lkotlin/text/Regex;Ljava/lang/CharSequence;I)V
HSPLkotlin/text/Regex$findAll$1;->invoke()Ljava/lang/Object;
HSPLkotlin/text/Regex$findAll$1;->invoke()Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex$findAll$2;-><init>()V
HSPLkotlin/text/Regex$findAll$2;->invoke(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/text/Regex$findAll$2;->invoke(Lkotlin/text/MatchResult;)Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex;-><init>(Ljava/lang/String;)V
HSPLkotlin/text/Regex;-><init>(Ljava/util/regex/Pattern;)V
HSPLkotlin/text/Regex;->find(Ljava/lang/CharSequence;I)Lkotlin/text/MatchResult;
HSPLkotlin/text/Regex;->findAll$default(Lkotlin/text/Regex;Ljava/lang/CharSequence;IILjava/lang/Object;)Lkotlin/sequences/Sequence;
HSPLkotlin/text/Regex;->findAll(Ljava/lang/CharSequence;I)Lkotlin/sequences/Sequence;
HSPLkotlin/text/RegexKt;->access$findNext(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Lkotlin/text/MatchResult;
HSPLkotlin/text/RegexKt;->access$range(Ljava/util/regex/MatchResult;)Lkotlin/ranges/IntRange;
HSPLkotlin/text/RegexKt;->findNext(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Lkotlin/text/MatchResult;
HSPLkotlin/text/RegexKt;->range(Ljava/util/regex/MatchResult;)Lkotlin/ranges/IntRange;
HSPLkotlin/text/StringsKt__StringsJVMKt;->endsWith$default(Ljava/lang/String;Ljava/lang/String;ZILjava/lang/Object;)Z
HSPLkotlin/text/StringsKt__StringsJVMKt;->endsWith(Ljava/lang/String;Ljava/lang/String;Z)Z
HSPLkotlin/text/StringsKt__StringsJVMKt;->isBlank(Ljava/lang/CharSequence;)Z
HSPLkotlin/text/StringsKt__StringsJVMKt;->repeat(Ljava/lang/CharSequence;I)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->endsWith$default(Ljava/lang/CharSequence;Ljava/lang/CharSequence;ZILjava/lang/Object;)Z
HSPLkotlin/text/StringsKt__StringsKt;->endsWith(Ljava/lang/CharSequence;Ljava/lang/CharSequence;Z)Z
HSPLkotlin/text/StringsKt__StringsKt;->getIndices(Ljava/lang/CharSequence;)Lkotlin/ranges/IntRange;
HSPLkotlin/text/StringsKt__StringsKt;->getLastIndex(Ljava/lang/CharSequence;)I
HSPLkotlin/text/StringsKt__StringsKt;->lastIndexOf$default(Ljava/lang/CharSequence;CIZILjava/lang/Object;)I
HSPLkotlin/text/StringsKt__StringsKt;->lastIndexOf(Ljava/lang/CharSequence;CIZ)I
HSPLkotlin/text/StringsKt__StringsKt;->substring(Ljava/lang/String;Lkotlin/ranges/IntRange;)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->substringAfterLast$default(Ljava/lang/String;CLjava/lang/String;ILjava/lang/Object;)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->substringAfterLast(Ljava/lang/String;CLjava/lang/String;)Ljava/lang/String;
HSPLkotlin/text/StringsKt__StringsKt;->trim(Ljava/lang/String;[C)Ljava/lang/String;
HSPLkotlin/text/StringsKt___StringsKt;->first(Ljava/lang/CharSequence;)C
HSPLkotlin/text/StringsKt___StringsKt;->slice(Ljava/lang/String;Lkotlin/ranges/IntRange;)Ljava/lang/String;

SPLandroidx/compose/ui/AbsoluteAlignment;->**(**)**
HSPLandroidx/compose/ui/Actual_androidKt**->**(**)**
HSPLandroidx/compose/ui/Actual_jvmKt**->**(**)**
HSPLandroidx/compose/ui/Alignment;->**(**)**
SPLandroidx/compose/ui/BiasAbsoluteAlignment;->**(**)**
HSPLandroidx/compose/ui/BiasAlignment;->**(**)**
HSPLandroidx/compose/ui/CombinedModifier;->**(**)**
SPLandroidx/compose/ui/ComposeUiFlags;->**(**)**
PLandroidx/compose/ui/ComposedModifier;->**(**)**
HSPLandroidx/compose/ui/ComposedModifierKt**->**(**)**
HSPLandroidx/compose/ui/Modifier;->**(**)**
SPLandroidx/compose/ui/ModifierNodeDetachedCancellationException;->**(**)**
HSPLandroidx/compose/ui/MotionDurationScale;->**(**)**
Landroidx/compose/ui/R;
SPLandroidx/compose/ui/SessionMutex;->**(**)**
SPLandroidx/compose/ui/autofill/AndroidAutofill;->**(**)**
HSPLandroidx/compose/ui/autofill/AndroidAutofillManager;->**(**)**
HSPLandroidx/compose/ui/autofill/AndroidAutofillManager_androidKt**->**(**)**
Landroidx/compose/ui/autofill/Autofill;
SPLandroidx/compose/ui/autofill/AutofillCallback;->**(**)**
SPLandroidx/compose/ui/autofill/AutofillManager;->**(**)**
SPLandroidx/compose/ui/autofill/AutofillTree;->**(**)**
Landroidx/compose/ui/autofill/PlatformAutofillManager;
SPLandroidx/compose/ui/autofill/PlatformAutofillManagerImpl;->**(**)**
HSPLandroidx/compose/ui/contentcapture/AndroidContentCaptureManager;->**(**)**
HSPLandroidx/compose/ui/contentcapture/ContentCaptureManager;->**(**)**
SPLandroidx/compose/ui/draganddrop/AndroidDragAndDropManager;->**(**)**
Landroidx/compose/ui/draganddrop/DragAndDropManager;
Landroidx/compose/ui/draganddrop/DragAndDropModifierNode;
HSPLandroidx/compose/ui/draganddrop/DragAndDropNode;->**(**)**
Landroidx/compose/ui/draganddrop/DragAndDropSourceModifierNode;
Landroidx/compose/ui/draganddrop/DragAndDropTarget;
Landroidx/compose/ui/draganddrop/DragAndDropTargetModifierNode;
PLandroidx/compose/ui/draw/AlphaKt**->**(**)**
Landroidx/compose/ui/draw/BuildDrawCacheParams;
Landroidx/compose/ui/draw/CacheDrawModifierNode;
HSPLandroidx/compose/ui/draw/CacheDrawModifierNodeImpl;->**(**)**
HSPLandroidx/compose/ui/draw/CacheDrawScope;->**(**)**
HSPLandroidx/compose/ui/draw/ClipKt**->**(**)**
HSPLandroidx/compose/ui/draw/DrawBackgroundModifier;->**(**)**
HSPLandroidx/compose/ui/draw/DrawBehindElement;->**(**)**
Landroidx/compose/ui/draw/DrawModifier;
HSPLandroidx/compose/ui/draw/DrawModifierKt**->**(**)**
SPLandroidx/compose/ui/draw/DrawResult;->**(**)**
HSPLandroidx/compose/ui/draw/DrawWithCacheElement;->**(**)**
HSPLandroidx/compose/ui/draw/DrawWithContentElement;->**(**)**
HSPLandroidx/compose/ui/draw/DrawWithContentModifier;->**(**)**
SPLandroidx/compose/ui/draw/EmptyBuildDrawCacheParams;->**(**)**
HSPLandroidx/compose/ui/draw/PainterElement;->**(**)**
HSPLandroidx/compose/ui/draw/PainterModifierKt**->**(**)**
HSPLandroidx/compose/ui/draw/PainterNode;->**(**)**
SPLandroidx/compose/ui/draw/RotateKt**->**(**)**
SPLandroidx/compose/ui/focus/CancelIndicatingFocusBoundaryScope;->**(**)**
SPLandroidx/compose/ui/focus/CustomDestinationResult;->**(**)**
SPLandroidx/compose/ui/focus/FocusDirection;->**(**)**
Landroidx/compose/ui/focus/FocusEnterExitScope;
Landroidx/compose/ui/focus/FocusEventModifier;
Landroidx/compose/ui/focus/FocusEventModifierNode;
SPLandroidx/compose/ui/focus/FocusInteropUtils_androidKt**->**(**)**
HSPLandroidx/compose/ui/focus/FocusInvalidationManager;->**(**)**
Landroidx/compose/ui/focus/FocusListener;
PLandroidx/compose/ui/focus/FocusManager;->**(**)**
Landroidx/compose/ui/focus/FocusOrderModifier;
Landroidx/compose/ui/focus/FocusOwner;
HSPLandroidx/compose/ui/focus/FocusOwnerImpl;->**(**)**
Landroidx/compose/ui/focus/FocusProperties;
HSPLandroidx/compose/ui/focus/FocusPropertiesImpl;->**(**)**
Landroidx/compose/ui/focus/FocusPropertiesModifierNode;
HSPLandroidx/compose/ui/focus/FocusRequester;->**(**)**
HSPLandroidx/compose/ui/focus/FocusRequesterElement;->**(**)**
Landroidx/compose/ui/focus/FocusRequesterModifier;
HSPLandroidx/compose/ui/focus/FocusRequesterModifierKt**->**(**)**
Landroidx/compose/ui/focus/FocusRequesterModifierNode;
HSPLandroidx/compose/ui/focus/FocusRequesterNode;->**(**)**
Landroidx/compose/ui/focus/FocusState;
HSPLandroidx/compose/ui/focus/FocusStateImpl;->**(**)**
Landroidx/compose/ui/focus/FocusTargetModifierNode;
HSPLandroidx/compose/ui/focus/FocusTargetModifierNodeKt**->**(**)**
HSPLandroidx/compose/ui/focus/FocusTargetNode;->**(**)**
HSPLandroidx/compose/ui/focus/FocusTargetNodeKt**->**(**)**
SPLandroidx/compose/ui/focus/FocusTransactionManager;->**(**)**
HSPLandroidx/compose/ui/focus/FocusTransactionsKt**->**(**)**
HPLandroidx/compose/ui/focus/FocusTraversalKt**->**(**)**
HSPLandroidx/compose/ui/focus/Focusability;->**(**)**
SPLandroidx/compose/ui/geometry/CornerRadius;->**(**)**
HSPLandroidx/compose/ui/geometry/MutableRect;->**(**)**
HSPLandroidx/compose/ui/geometry/MutableRectKt**->**(**)**
HSPLandroidx/compose/ui/geometry/Offset;->**(**)**
HSPLandroidx/compose/ui/geometry/OffsetKt**->**(**)**
HSPLandroidx/compose/ui/geometry/Rect;->**(**)**
HSPLandroidx/compose/ui/geometry/RectKt**->**(**)**
HSPLandroidx/compose/ui/geometry/RoundRect;->**(**)**
HSPLandroidx/compose/ui/geometry/RoundRectKt**->**(**)**
HSPLandroidx/compose/ui/geometry/Size;->**(**)**
HSPLandroidx/compose/ui/geometry/SizeKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidBlendMode_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidCanvas;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidCanvas_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidColorFilter_androidKt**->**(**)**
SPLandroidx/compose/ui/graphics/AndroidColorSpace_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidGraphicsContext;->**(**)**
SPLandroidx/compose/ui/graphics/AndroidGraphicsContext_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidImageBitmap;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidImageBitmap_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidMatrixConversions_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPaint;->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPaint_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPath;->**(**)**
HPLandroidx/compose/ui/graphics/AndroidPathMeasure;->**(**)**
PLandroidx/compose/ui/graphics/AndroidPathMeasure_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/AndroidPath_androidKt**->**(**)**
HPLandroidx/compose/ui/graphics/AndroidShader_androidKt**->**(**)**
HPLandroidx/compose/ui/graphics/AndroidTileMode_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/Api26Bitmap;->**(**)**
HSPLandroidx/compose/ui/graphics/BezierKt**->**(**)**
HSPLandroidx/compose/ui/graphics/BlendMode;->**(**)**
HSPLandroidx/compose/ui/graphics/BlendModeColorFilter;->**(**)**
HSPLandroidx/compose/ui/graphics/BlendModeColorFilterHelper;->**(**)**
HSPLandroidx/compose/ui/graphics/BlockGraphicsLayerElement;->**(**)**
HSPLandroidx/compose/ui/graphics/BlockGraphicsLayerModifier;->**(**)**
HSPLandroidx/compose/ui/graphics/Brush;->**(**)**
PLandroidx/compose/ui/graphics/Canvas;->**(**)**
HSPLandroidx/compose/ui/graphics/CanvasHolder;->**(**)**
SPLandroidx/compose/ui/graphics/CanvasKt**->**(**)**
PLandroidx/compose/ui/graphics/ClipOp;->**(**)**
HSPLandroidx/compose/ui/graphics/Color;->**(**)**
HSPLandroidx/compose/ui/graphics/ColorFilter;->**(**)**
HSPLandroidx/compose/ui/graphics/ColorKt**->**(**)**
Landroidx/compose/ui/graphics/ColorProducer;
HSPLandroidx/compose/ui/graphics/ColorSpaceVerificationHelper;->**(**)**
HSPLandroidx/compose/ui/graphics/CompositingStrategy;->**(**)**
HSPLandroidx/compose/ui/graphics/FilterQuality;->**(**)**
Landroidx/compose/ui/graphics/GraphicsContext;
HSPLandroidx/compose/ui/graphics/GraphicsLayerElement;->**(**)**
HSPLandroidx/compose/ui/graphics/GraphicsLayerModifierKt**->**(**)**
Landroidx/compose/ui/graphics/GraphicsLayerScope;
HSPLandroidx/compose/ui/graphics/GraphicsLayerScopeKt**->**(**)**
PLandroidx/compose/ui/graphics/ImageBitmap;->**(**)**
SPLandroidx/compose/ui/graphics/ImageBitmapConfig;->**(**)**
HSPLandroidx/compose/ui/graphics/ImageBitmapKt**->**(**)**
HPLandroidx/compose/ui/graphics/LinearGradient;->**(**)**
HSPLandroidx/compose/ui/graphics/Matrix;->**(**)**
HSPLandroidx/compose/ui/graphics/MatrixKt**->**(**)**
HSPLandroidx/compose/ui/graphics/Outline;->**(**)**
HSPLandroidx/compose/ui/graphics/OutlineKt**->**(**)**
Landroidx/compose/ui/graphics/Paint;
SPLandroidx/compose/ui/graphics/PaintingStyle;->**(**)**
SPLandroidx/compose/ui/graphics/Path;->**(**)**
Landroidx/compose/ui/graphics/PathEffect;
HSPLandroidx/compose/ui/graphics/PathFillType;->**(**)**
SPLandroidx/compose/ui/graphics/PathOperation;->**(**)**
HPLandroidx/compose/ui/graphics/RadialGradient;->**(**)**
HSPLandroidx/compose/ui/graphics/RectHelper_androidKt**->**(**)**
HSPLandroidx/compose/ui/graphics/RectangleShapeKt**->**(**)**
Landroidx/compose/ui/graphics/RenderEffect;
HSPLandroidx/compose/ui/graphics/ReusableGraphicsLayerScope;->**(**)**
HPLandroidx/compose/ui/graphics/ShaderBrush;->**(**)**
PLandroidx/compose/ui/graphics/ShaderKt**->**(**)**
HSPLandroidx/compose/ui/graphics/Shadow;->**(**)**
Landroidx/compose/ui/graphics/Shape;
HSPLandroidx/compose/ui/graphics/SimpleGraphicsLayerModifier;->**(**)**
HSPLandroidx/compose/ui/graphics/SolidColor;->**(**)**
HSPLandroidx/compose/ui/graphics/StrokeCap;->**(**)**
HSPLandroidx/compose/ui/graphics/StrokeJoin;->**(**)**
HSPLandroidx/compose/ui/graphics/TileMode;->**(**)**
HSPLandroidx/compose/ui/graphics/TransformOrigin;->**(**)**
HSPLandroidx/compose/ui/graphics/TransformOriginKt**->**(**)**
SPLandroidx/compose/ui/graphics/WrapperVerificationHelperMethods;->**(**)**
SPLandroidx/compose/ui/graphics/colorspace/Adaptation;->**(**)**
SPLandroidx/compose/ui/graphics/colorspace/ColorModel;->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/ColorSpace;->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/ColorSpaceKt**->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/ColorSpaces;->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/Connector;->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/ConnectorKt**->**(**)**
Landroidx/compose/ui/graphics/colorspace/DoubleFunction;
SPLandroidx/compose/ui/graphics/colorspace/Illuminant;->**(**)**
SPLandroidx/compose/ui/graphics/colorspace/Lab;->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/Oklab;->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/RenderIntent;->**(**)**
HSPLandroidx/compose/ui/graphics/colorspace/Rgb;->**(**)**
SPLandroidx/compose/ui/graphics/colorspace/TransferParameters;->**(**)**
SPLandroidx/compose/ui/graphics/colorspace/TransferParametersKt**->**(**)**
SPLandroidx/compose/ui/graphics/colorspace/WhitePoint;->**(**)**
SPLandroidx/compose/ui/graphics/colorspace/Xyz;->**(**)**
HSPLandroidx/compose/ui/graphics/drawscope/CanvasDrawScope;->**(**)**
HSPLandroidx/compose/ui/graphics/drawscope/CanvasDrawScopeKt**->**(**)**
Landroidx/compose/ui/graphics/drawscope/ContentDrawScope;
Landroidx/compose/ui/graphics/drawscope/DrawContext;
HSPLandroidx/compose/ui/graphics/drawscope/DrawContextKt**->**(**)**
HSPLandroidx/compose/ui/graphics/drawscope/DrawScope;->**(**)**
HSPLandroidx/compose/ui/graphics/drawscope/DrawStyle;->**(**)**
Landroidx/compose/ui/graphics/drawscope/DrawTransform;
SPLandroidx/compose/ui/graphics/drawscope/EmptyCanvas;->**(**)**
SPLandroidx/compose/ui/graphics/drawscope/Fill;->**(**)**
HSPLandroidx/compose/ui/graphics/drawscope/Stroke;->**(**)**
HSPLandroidx/compose/ui/graphics/layer/ChildLayerDependenciesTracker;->**(**)**
HSPLandroidx/compose/ui/graphics/layer/CompositingStrategy;->**(**)**
HSPLandroidx/compose/ui/graphics/layer/GraphicsLayer;->**(**)**
SPLandroidx/compose/ui/graphics/layer/GraphicsLayerImpl;->**(**)**
HSPLandroidx/compose/ui/graphics/layer/GraphicsLayerKt**->**(**)**
HSPLandroidx/compose/ui/graphics/layer/GraphicsLayerV29;->**(**)**
SPLandroidx/compose/ui/graphics/layer/LayerManager;->**(**)**
Landroidx/compose/ui/graphics/layer/LayerSnapshotImpl;
SPLandroidx/compose/ui/graphics/layer/LayerSnapshotV28;->**(**)**
HSPLandroidx/compose/ui/graphics/layer/OutlineVerificationHelper;->**(**)**
PLandroidx/compose/ui/graphics/painter/BitmapPainter;->**(**)**
HSPLandroidx/compose/ui/graphics/painter/ColorPainter;->**(**)**
HSPLandroidx/compose/ui/graphics/painter/Painter;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/DrawCache;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/FastFloatParserKt**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/GroupComponent;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/ImageVector;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/ImageVectorKt**->**(**)**
SPLandroidx/compose/ui/graphics/vector/PathBuilder;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathComponent;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathNode;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathNodeKt**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathParser;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/PathParserKt**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VNode;->**(**)**
HPLandroidx/compose/ui/graphics/vector/VectorApplier;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorComponent;->**(**)**
HPLandroidx/compose/ui/graphics/vector/VectorComposeKt**->**(**)**
PLandroidx/compose/ui/graphics/vector/VectorConfig;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorGroup;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorKt**->**(**)**
SPLandroidx/compose/ui/graphics/vector/VectorNode;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorPainter;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorPainterKt**->**(**)**
HSPLandroidx/compose/ui/graphics/vector/VectorPath;->**(**)**
PLandroidx/compose/ui/graphics/vector/VectorProperty;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/compat/AndroidVectorParser;->**(**)**
SPLandroidx/compose/ui/graphics/vector/compat/AndroidVectorResources;->**(**)**
HSPLandroidx/compose/ui/graphics/vector/compat/XmlVectorParser_androidKt**->**(**)**
Landroidx/compose/ui/hapticfeedback/HapticFeedback;
PLandroidx/compose/ui/hapticfeedback/HapticFeedbackType;->**(**)**
SPLandroidx/compose/ui/hapticfeedback/PlatformHapticFeedback;->**(**)**
PLandroidx/compose/ui/hapticfeedback/PlatformHapticFeedbackType;->**(**)**
SPLandroidx/compose/ui/input/InputMode;->**(**)**
Landroidx/compose/ui/input/InputModeManager;
SPLandroidx/compose/ui/input/InputModeManagerImpl;->**(**)**
Landroidx/compose/ui/input/indirect/IndirectTouchInputModifierNode;
SPLandroidx/compose/ui/input/key/KeyInputElement;->**(**)**
SPLandroidx/compose/ui/input/key/KeyInputModifierKt**->**(**)**
Landroidx/compose/ui/input/key/KeyInputModifierNode;
SPLandroidx/compose/ui/input/key/KeyInputNode;->**(**)**
Landroidx/compose/ui/input/key/SoftKeyboardInterceptionModifierNode;
HPLandroidx/compose/ui/input/nestedscroll/NestedScrollConnection;->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollDispatcher;->**(**)**
SPLandroidx/compose/ui/input/nestedscroll/NestedScrollElement;->**(**)**
SPLandroidx/compose/ui/input/nestedscroll/NestedScrollModifierKt**->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollNode;->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollNodeKt**->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollSource;->**(**)**
SPLandroidx/compose/ui/input/pointer/AndroidPointerIconType;->**(**)**
HSPLandroidx/compose/ui/input/pointer/AwaitPointerEventScope;->**(**)**
HPLandroidx/compose/ui/input/pointer/HistoricalChange;->**(**)**
HSPLandroidx/compose/ui/input/pointer/HitPathTracker;->**(**)**
HSPLandroidx/compose/ui/input/pointer/InternalPointerEvent;->**(**)**
Landroidx/compose/ui/input/pointer/MatrixPositionCalculator;
HSPLandroidx/compose/ui/input/pointer/MotionEventAdapter;->**(**)**
HSPLandroidx/compose/ui/input/pointer/Node;->**(**)**
HSPLandroidx/compose/ui/input/pointer/NodeParent;->**(**)**
SPLandroidx/compose/ui/input/pointer/PointerButtons;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEventKt**->**(**)**
SPLandroidx/compose/ui/input/pointer/PointerEventPass;->**(**)**
Landroidx/compose/ui/input/pointer/PointerEventTimeoutCancellationException;
HSPLandroidx/compose/ui/input/pointer/PointerEventType;->**(**)**
SPLandroidx/compose/ui/input/pointer/PointerEvent_androidKt**->**(**)**
SPLandroidx/compose/ui/input/pointer/PointerIcon;->**(**)**
Landroidx/compose/ui/input/pointer/PointerIconService;
SPLandroidx/compose/ui/input/pointer/PointerIcon_androidKt**->**(**)**
SPLandroidx/compose/ui/input/pointer/PointerId;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputChange;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputChangeEventProducer;->**(**)**
SPLandroidx/compose/ui/input/pointer/PointerInputEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputEventData;->**(**)**
Landroidx/compose/ui/input/pointer/PointerInputEventHandler;
HSPLandroidx/compose/ui/input/pointer/PointerInputEventProcessor;->**(**)**
SPLandroidx/compose/ui/input/pointer/PointerInputEventProcessorKt**->**(**)**
Landroidx/compose/ui/input/pointer/PointerInputModifier;
SPLandroidx/compose/ui/input/pointer/PointerInputResetException;->**(**)**
Landroidx/compose/ui/input/pointer/PointerInputScope;
SPLandroidx/compose/ui/input/pointer/PointerKeyboardModifiers;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerType;->**(**)**
Landroidx/compose/ui/input/pointer/PositionCalculator;
SPLandroidx/compose/ui/input/pointer/ProcessResult;->**(**)**
HSPLandroidx/compose/ui/input/pointer/SuspendPointerInputElement;->**(**)**
HSPLandroidx/compose/ui/input/pointer/SuspendingPointerInputFilterKt**->**(**)**
Landroidx/compose/ui/input/pointer/SuspendingPointerInputModifierNode;
HSPLandroidx/compose/ui/input/pointer/SuspendingPointerInputModifierNodeImpl;->**(**)**
HPLandroidx/compose/ui/input/pointer/util/DataPointAtTime;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/PointerIdArray;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/VelocityTracker1D;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/VelocityTracker;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/VelocityTrackerKt**->**(**)**
SPLandroidx/compose/ui/input/rotary/RotaryInputElement;->**(**)**
SPLandroidx/compose/ui/input/rotary/RotaryInputModifierKt**->**(**)**
Landroidx/compose/ui/input/rotary/RotaryInputModifierNode;
SPLandroidx/compose/ui/input/rotary/RotaryInputNode;->**(**)**
HSPLandroidx/compose/ui/internal/PlatformOptimizedCancellationException;->**(**)**
HSPLandroidx/compose/ui/internal/PlatformOptimizedCancellationException_jvmKt**->**(**)**
SPLandroidx/compose/ui/layout/AlignmentLine;->**(**)**
SPLandroidx/compose/ui/layout/AlignmentLineKt**->**(**)**
Landroidx/compose/ui/layout/ApproachIntrinsicMeasureScope;
Landroidx/compose/ui/layout/ApproachLayoutModifierNode;
Landroidx/compose/ui/layout/ApproachMeasureScope;
Landroidx/compose/ui/layout/ApproachMeasureScopeImpl;
Landroidx/compose/ui/layout/BeyondBoundsLayout;
SPLandroidx/compose/ui/layout/ComposableSingletons;->**(**)**
HSPLandroidx/compose/ui/layout/ContentScale;->**(**)**
HSPLandroidx/compose/ui/layout/ContentScaleKt**->**(**)**
HSPLandroidx/compose/ui/layout/DefaultIntrinsicMeasurable;->**(**)**
SPLandroidx/compose/ui/layout/FixedScale;->**(**)**
HPLandroidx/compose/ui/layout/FixedSizeIntrinsicsPlaceable;->**(**)**
Landroidx/compose/ui/layout/GraphicLayerInfo;
SPLandroidx/compose/ui/layout/HorizontalAlignmentLine;->**(**)**
Landroidx/compose/ui/layout/IntrinsicMeasurable;
Landroidx/compose/ui/layout/IntrinsicMeasureScope;
SPLandroidx/compose/ui/layout/IntrinsicMinMax;->**(**)**
SPLandroidx/compose/ui/layout/IntrinsicWidthHeight;->**(**)**
HSPLandroidx/compose/ui/layout/IntrinsicsMeasureScope;->**(**)**
HSPLandroidx/compose/ui/layout/LayoutCoordinates;->**(**)**
HSPLandroidx/compose/ui/layout/LayoutCoordinatesKt**->**(**)**
HSPLandroidx/compose/ui/layout/LayoutElement;->**(**)**
Landroidx/compose/ui/layout/LayoutInfo;
HPLandroidx/compose/ui/layout/LayoutKt**->**(**)**
Landroidx/compose/ui/layout/LayoutModifier;
HSPLandroidx/compose/ui/layout/LayoutModifierImpl;->**(**)**
HSPLandroidx/compose/ui/layout/LayoutModifierKt**->**(**)**
HSPLandroidx/compose/ui/layout/LayoutNodeSubcompositionsState;->**(**)**
HSPLandroidx/compose/ui/layout/LookaheadCapablePlacementScope;->**(**)**
Landroidx/compose/ui/layout/LookaheadLayoutCoordinates;
Landroidx/compose/ui/layout/LookaheadScope;
Landroidx/compose/ui/layout/Measurable;
HPLandroidx/compose/ui/layout/MeasurePolicy;->**(**)**
Landroidx/compose/ui/layout/MeasureResult;
Landroidx/compose/ui/layout/MeasureScope;
Landroidx/compose/ui/layout/Measured;
PLandroidx/compose/ui/layout/MultiContentMeasurePolicyImpl;->**(**)**
PLandroidx/compose/ui/layout/MultiContentMeasurePolicyKt**->**(**)**
SPLandroidx/compose/ui/layout/NoOpSubcomposeSlotReusePolicy;->**(**)**
HSPLandroidx/compose/ui/layout/OnGloballyPositionedElement;->**(**)**
Landroidx/compose/ui/layout/OnGloballyPositionedModifier;
HSPLandroidx/compose/ui/layout/OnGloballyPositionedModifierKt**->**(**)**
HSPLandroidx/compose/ui/layout/OnGloballyPositionedNode;->**(**)**
SPLandroidx/compose/ui/layout/OnPlacedElement;->**(**)**
Landroidx/compose/ui/layout/OnPlacedModifier;
SPLandroidx/compose/ui/layout/OnPlacedModifierKt**->**(**)**
HSPLandroidx/compose/ui/layout/OnPlacedNode;->**(**)**
Landroidx/compose/ui/layout/OnRemeasuredModifier;
SPLandroidx/compose/ui/layout/OnRemeasuredModifierKt**->**(**)**
SPLandroidx/compose/ui/layout/OnSizeChangedModifier;->**(**)**
SPLandroidx/compose/ui/layout/OnSizeChangedNode;->**(**)**
SPLandroidx/compose/ui/layout/OuterPlacementScope;->**(**)**
Landroidx/compose/ui/layout/ParentDataModifier;
Landroidx/compose/ui/layout/PinnableContainer;
SPLandroidx/compose/ui/layout/PinnableContainerKt**->**(**)**
HSPLandroidx/compose/ui/layout/Placeable;->**(**)**
HSPLandroidx/compose/ui/layout/PlaceableKt**->**(**)**
Landroidx/compose/ui/layout/Remeasurement;
Landroidx/compose/ui/layout/RemeasurementModifier;
SPLandroidx/compose/ui/layout/RootMeasurePolicy;->**(**)**
SPLandroidx/compose/ui/layout/ScaleFactor;->**(**)**
HSPLandroidx/compose/ui/layout/ScaleFactorKt**->**(**)**
HSPLandroidx/compose/ui/layout/SubcomposeLayoutKt**->**(**)**
HSPLandroidx/compose/ui/layout/SubcomposeLayoutState;->**(**)**
Landroidx/compose/ui/layout/SubcomposeMeasureScope;
HSPLandroidx/compose/ui/layout/SubcomposeSlotReusePolicy;->**(**)**
Landroidx/compose/ui/modifier/ModifierLocal;
Landroidx/compose/ui/modifier/ModifierLocalConsumer;
SPLandroidx/compose/ui/modifier/ModifierLocalManager;->**(**)**
Landroidx/compose/ui/modifier/ModifierLocalModifierNode;
Landroidx/compose/ui/modifier/ModifierLocalProvider;
Landroidx/compose/ui/modifier/ModifierLocalReadScope;
HSPLandroidx/compose/ui/node/AlignmentLines;->**(**)**
Landroidx/compose/ui/node/AlignmentLinesOwner;
HSPLandroidx/compose/ui/node/BackwardsCompatNode;->**(**)**
HSPLandroidx/compose/ui/node/BackwardsCompatNodeKt**->**(**)**
SPLandroidx/compose/ui/node/CenteredArray;->**(**)**
HSPLandroidx/compose/ui/node/ComposeUiNode;->**(**)**
Landroidx/compose/ui/node/CompositionLocalConsumerModifierNode;
HSPLandroidx/compose/ui/node/CompositionLocalConsumerModifierNodeKt**->**(**)**
SPLandroidx/compose/ui/node/DelegatableNode;->**(**)**
HSPLandroidx/compose/ui/node/DelegatableNodeKt**->**(**)**
HSPLandroidx/compose/ui/node/DelegatableNode_androidKt**->**(**)**
HSPLandroidx/compose/ui/node/DelegatingNode;->**(**)**
HSPLandroidx/compose/ui/node/DepthSortedSet;->**(**)**
HSPLandroidx/compose/ui/node/DepthSortedSetKt**->**(**)**
HSPLandroidx/compose/ui/node/DepthSortedSetsForDifferentPasses;->**(**)**
Landroidx/compose/ui/node/DiffCallback;
HSPLandroidx/compose/ui/node/DistanceAndFlags;->**(**)**
SPLandroidx/compose/ui/node/DrawModifierNode;->**(**)**
HSPLandroidx/compose/ui/node/DrawModifierNodeKt**->**(**)**
Landroidx/compose/ui/node/GlobalPositionAwareModifierNode;
HSPLandroidx/compose/ui/node/HitTestResult;->**(**)**
HSPLandroidx/compose/ui/node/HitTestResultKt**->**(**)**
HSPLandroidx/compose/ui/node/InnerNodeCoordinator;->**(**)**
SPLandroidx/compose/ui/node/IntStack;->**(**)**
Landroidx/compose/ui/node/InteroperableComposeUiNode;
HSPLandroidx/compose/ui/node/IntrinsicsPolicy;->**(**)**
HSPLandroidx/compose/ui/node/LayerPositionalProperties;->**(**)**
SPLandroidx/compose/ui/node/LayoutAwareModifierNode;->**(**)**
HSPLandroidx/compose/ui/node/LayoutModifierNode;->**(**)**
HSPLandroidx/compose/ui/node/LayoutModifierNodeCoordinator;->**(**)**
HSPLandroidx/compose/ui/node/LayoutModifierNodeKt**->**(**)**
HSPLandroidx/compose/ui/node/LayoutNode;->**(**)**
SPLandroidx/compose/ui/node/LayoutNodeAlignmentLines;->**(**)**
HSPLandroidx/compose/ui/node/LayoutNodeDrawScope;->**(**)**
SPLandroidx/compose/ui/node/LayoutNodeDrawScopeKt**->**(**)**
HSPLandroidx/compose/ui/node/LayoutNodeKt**->**(**)**
HSPLandroidx/compose/ui/node/LayoutNodeLayoutDelegate;->**(**)**
HSPLandroidx/compose/ui/node/LayoutNodeLayoutDelegateKt**->**(**)**
Landroidx/compose/ui/node/LayoutTreeConsistencyChecker;
HSPLandroidx/compose/ui/node/LookaheadCapablePlaceable;->**(**)**
Landroidx/compose/ui/node/LookaheadDelegate;
HSPLandroidx/compose/ui/node/MeasureAndLayoutDelegate;->**(**)**
HSPLandroidx/compose/ui/node/MeasurePassDelegate;->**(**)**
Landroidx/compose/ui/node/MeasureScopeWithLayoutNode;
HPLandroidx/compose/ui/node/MeasureScopeWithLayoutNodeKt**->**(**)**
HSPLandroidx/compose/ui/node/ModifierNodeElement;->**(**)**
Landroidx/compose/ui/node/MotionReferencePlacementDelegate;
HSPLandroidx/compose/ui/node/MutableVectorWithMutationTracking;->**(**)**
HSPLandroidx/compose/ui/node/MyersDiffKt**->**(**)**
HSPLandroidx/compose/ui/node/NodeChain;->**(**)**
HSPLandroidx/compose/ui/node/NodeChainKt**->**(**)**
HSPLandroidx/compose/ui/node/NodeCoordinator;->**(**)**
HSPLandroidx/compose/ui/node/NodeCoordinatorKt**->**(**)**
SPLandroidx/compose/ui/node/NodeKind;->**(**)**
HSPLandroidx/compose/ui/node/NodeKindKt**->**(**)**
HSPLandroidx/compose/ui/node/NodeMeasuringIntrinsics;->**(**)**
Landroidx/compose/ui/node/ObserverModifierNode;
HSPLandroidx/compose/ui/node/ObserverModifierNodeKt**->**(**)**
HSPLandroidx/compose/ui/node/ObserverNodeOwnerScope;->**(**)**
HSPLandroidx/compose/ui/node/OnPositionedDispatcher;->**(**)**
Landroidx/compose/ui/node/OnUnplacedModifierNode;
Landroidx/compose/ui/node/OwnedLayer;
HSPLandroidx/compose/ui/node/Owner;->**(**)**
Landroidx/compose/ui/node/OwnerScope;
HSPLandroidx/compose/ui/node/OwnerSnapshotObserver;->**(**)**
Landroidx/compose/ui/node/ParentDataModifierNode;
SPLandroidx/compose/ui/node/ParentDataModifierNodeKt**->**(**)**
SPLandroidx/compose/ui/node/PointerInputModifierNode;->**(**)**
HSPLandroidx/compose/ui/node/PointerInputModifierNodeKt**->**(**)**
Landroidx/compose/ui/node/RootForTest;
SPLandroidx/compose/ui/node/SemanticsModifierNode;->**(**)**
HSPLandroidx/compose/ui/node/SemanticsModifierNodeKt**->**(**)**
SPLandroidx/compose/ui/node/Snake;->**(**)**
SPLandroidx/compose/ui/node/SortedSet;->**(**)**
HSPLandroidx/compose/ui/node/TailModifierNode;->**(**)**
PLandroidx/compose/ui/node/TraversableNode;->**(**)**
HSPLandroidx/compose/ui/node/TraversableNodeKt**->**(**)**
HSPLandroidx/compose/ui/node/UiApplier;->**(**)**
HSPLandroidx/compose/ui/platform/AbstractComposeView;->**(**)**
Landroidx/compose/ui/platform/AccessibilityManager;
HSPLandroidx/compose/ui/platform/AndroidAccessibilityManager;->**(**)**
SPLandroidx/compose/ui/platform/AndroidClipboard;->**(**)**
SPLandroidx/compose/ui/platform/AndroidClipboardManager;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeView;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat_androidKt**->**(**)**
SPLandroidx/compose/ui/platform/AndroidComposeViewForceDarkModeQ;->**(**)**
SPLandroidx/compose/ui/platform/AndroidComposeViewTranslationCallback;->**(**)**
SPLandroidx/compose/ui/platform/AndroidComposeViewTranslationCallbackS;->**(**)**
SPLandroidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethodsO;->**(**)**
SPLandroidx/compose/ui/platform/AndroidComposeView_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidCompositionLocals_androidKt**->**(**)**
SPLandroidx/compose/ui/platform/AndroidFontResourceLoader;->**(**)**
Landroidx/compose/ui/platform/AndroidPlatformTextInputSession;
SPLandroidx/compose/ui/platform/AndroidTextToolbar;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUiDispatcher;->**(**)**
SPLandroidx/compose/ui/platform/AndroidUiDispatcher_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUiFrameClock;->**(**)**
SPLandroidx/compose/ui/platform/AndroidUriHandler;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidViewConfiguration;->**(**)**
SPLandroidx/compose/ui/platform/AndroidViewsHandler;->**(**)**
PLandroidx/compose/ui/platform/Api29Impl;->**(**)**
SPLandroidx/compose/ui/platform/BringIntoViewOnScreenResponderNode;->**(**)**
Landroidx/compose/ui/platform/CalculateMatrixToWindow;
HSPLandroidx/compose/ui/platform/CalculateMatrixToWindowApi29;->**(**)**
Landroidx/compose/ui/platform/Clipboard;
Landroidx/compose/ui/platform/ClipboardManager;
SPLandroidx/compose/ui/platform/ComposableSingletons;->**(**)**
HSPLandroidx/compose/ui/platform/ComposeView;->**(**)**
HSPLandroidx/compose/ui/platform/CompositionLocalsKt**->**(**)**
SPLandroidx/compose/ui/platform/DefaultHapticFeedback;->**(**)**
SPLandroidx/compose/ui/platform/DelegatingSoftwareKeyboardController;->**(**)**
HSPLandroidx/compose/ui/platform/DisposableSaveableStateRegistry;->**(**)**
HSPLandroidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/GlobalSnapshotManager;->**(**)**
HSPLandroidx/compose/ui/platform/GraphicsLayerOwnerLayer;->**(**)**
SPLandroidx/compose/ui/platform/HapticDefaults;->**(**)**
PLandroidx/compose/ui/platform/InfiniteAnimationPolicy;->**(**)**
Landroidx/compose/ui/platform/InspectableValue;
HSPLandroidx/compose/ui/platform/InspectableValueKt**->**(**)**
SPLandroidx/compose/ui/platform/InspectionModeKt**->**(**)**
PLandroidx/compose/ui/platform/InspectorValueInfo;->**(**)**
SPLandroidx/compose/ui/platform/InvertMatrixKt**->**(**)**
SPLandroidx/compose/ui/platform/LazyWindowInfo;->**(**)**
HSPLandroidx/compose/ui/platform/MotionDurationScaleImpl;->**(**)**
Landroidx/compose/ui/platform/PlatformTextInputSession;
Landroidx/compose/ui/platform/PlatformTextInputSessionScope;
SPLandroidx/compose/ui/platform/SemanticsNodeCopy;->**(**)**
HSPLandroidx/compose/ui/platform/SemanticsUtils_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/ShapeContainingUtilKt**->**(**)**
Landroidx/compose/ui/platform/SoftwareKeyboardController;
SPLandroidx/compose/ui/platform/SubcompositionKt**->**(**)**
Landroidx/compose/ui/platform/TextToolbar;
SPLandroidx/compose/ui/platform/TextToolbarStatus;->**(**)**
Landroidx/compose/ui/platform/UriHandler;
SPLandroidx/compose/ui/platform/ViewCompositionStrategy;->**(**)**
HSPLandroidx/compose/ui/platform/ViewConfiguration;->**(**)**
SPLandroidx/compose/ui/platform/ViewLayer;->**(**)**
SPLandroidx/compose/ui/platform/ViewRootForTest;->**(**)**
HSPLandroidx/compose/ui/platform/WeakCache;->**(**)**
Landroidx/compose/ui/platform/WindowInfo;
SPLandroidx/compose/ui/platform/WindowInfoImpl;->**(**)**
SPLandroidx/compose/ui/platform/WindowRecomposerFactory;->**(**)**
SPLandroidx/compose/ui/platform/WindowRecomposerPolicy;->**(**)**
SPLandroidx/compose/ui/platform/WindowRecomposer_androidKt**->**(**)**
SPLandroidx/compose/ui/platform/WrappedComposition;->**(**)**
HSPLandroidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods;->**(**)**
SPLandroidx/compose/ui/platform/Wrapper_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/accessibility/CollectionInfo_androidKt**->**(**)**
SPLandroidx/compose/ui/platform/actionmodecallback/TextActionModeCallback;->**(**)**
SPLandroidx/compose/ui/platform/coreshims/AutofillIdCompat;->**(**)**
Landroidx/compose/ui/platform/coreshims/ContentCaptureSessionCompat;
SPLandroidx/compose/ui/platform/coreshims/ViewCompatShims;->**(**)**
Landroidx/compose/ui/relocation/BringIntoViewModifierNode;
PLandroidx/compose/ui/res/ImageResources_androidKt**->**(**)**
HSPLandroidx/compose/ui/res/ImageVectorCache;->**(**)**
HSPLandroidx/compose/ui/res/PainterResources_androidKt**->**(**)**
HSPLandroidx/compose/ui/res/ResourceIdCache;->**(**)**
HSPLandroidx/compose/ui/res/StringResources_androidKt**->**(**)**
HSPLandroidx/compose/ui/res/VectorResources_androidKt**->**(**)**
Landroidx/compose/ui/scrollcapture/ComposeScrollCaptureCallback;
SPLandroidx/compose/ui/scrollcapture/ScrollCapture;->**(**)**
HSPLandroidx/compose/ui/semantics/AccessibilityAction;->**(**)**
HSPLandroidx/compose/ui/semantics/AppendedSemanticsElement;->**(**)**
HPLandroidx/compose/ui/semantics/ClearAndSetSemanticsElement;->**(**)**
HSPLandroidx/compose/ui/semantics/CollectionInfo;->**(**)**
Landroidx/compose/ui/semantics/CollectionItemInfo;
HSPLandroidx/compose/ui/semantics/CoreSemanticsModifierNode;->**(**)**
PLandroidx/compose/ui/semantics/CustomAccessibilityAction;->**(**)**
SPLandroidx/compose/ui/semantics/EmptySemanticsElement;->**(**)**
SPLandroidx/compose/ui/semantics/EmptySemanticsModifier;->**(**)**
Landroidx/compose/ui/semantics/LiveRegionMode;
HPLandroidx/compose/ui/semantics/ProgressBarRangeInfo;->**(**)**
HSPLandroidx/compose/ui/semantics/Role;->**(**)**
HSPLandroidx/compose/ui/semantics/ScrollAxisRange;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticRegionImpl;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsActions;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsConfiguration;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsConfigurationKt**->**(**)**
Landroidx/compose/ui/semantics/SemanticsInfo;
Landroidx/compose/ui/semantics/SemanticsListener;
Landroidx/compose/ui/semantics/SemanticsModifier;
HSPLandroidx/compose/ui/semantics/SemanticsModifierKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNode;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNodeKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNodeWithAdjustedBounds;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsOwner;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsOwnerKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsProperties;->**(**)**
Landroidx/compose/ui/semantics/SemanticsPropertiesAndroid;
HSPLandroidx/compose/ui/semantics/SemanticsPropertiesKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsPropertyKey;->**(**)**
Landroidx/compose/ui/semantics/SemanticsPropertyReceiver;
Landroidx/compose/ui/semantics/SemanticsRegion;
HSPLandroidx/compose/ui/semantics/SemanticsRegion_androidKt**->**(**)**
HSPLandroidx/compose/ui/spatial/RectList;->**(**)**
HSPLandroidx/compose/ui/spatial/RectManager;->**(**)**
HSPLandroidx/compose/ui/spatial/RectManagerKt**->**(**)**
HSPLandroidx/compose/ui/spatial/ThrottledCallbacks;->**(**)**
PLandroidx/compose/ui/state/ToggleableState;->**(**)**
PLandroidx/compose/ui/state/ToggleableStateKt**->**(**)**
HSPLandroidx/compose/ui/text/AndroidParagraph;->**(**)**
HSPLandroidx/compose/ui/text/AndroidParagraph_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/AndroidTextStyle_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/AnnotatedString;->**(**)**
HSPLandroidx/compose/ui/text/AnnotatedStringKt**->**(**)**
PLandroidx/compose/ui/text/CacheTextLayoutInput;->**(**)**
HSPLandroidx/compose/ui/text/EmojiSupportMatch;->**(**)**
HSPLandroidx/compose/ui/text/MultiParagraph;->**(**)**
HSPLandroidx/compose/ui/text/MultiParagraphIntrinsics;->**(**)**
HSPLandroidx/compose/ui/text/MultiParagraphIntrinsicsKt**->**(**)**
PLandroidx/compose/ui/text/MultiParagraphKt**->**(**)**
Landroidx/compose/ui/text/NonNullValueClassSaver;
Landroidx/compose/ui/text/Paragraph;
HSPLandroidx/compose/ui/text/ParagraphInfo;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphIntrinsicInfo;->**(**)**
Landroidx/compose/ui/text/ParagraphIntrinsics;
SPLandroidx/compose/ui/text/ParagraphIntrinsicsKt**->**(**)**
SPLandroidx/compose/ui/text/ParagraphKt**->**(**)**
HSPLandroidx/compose/ui/text/ParagraphStyle;->**(**)**
HSPLandroidx/compose/ui/text/ParagraphStyleKt**->**(**)**
HSPLandroidx/compose/ui/text/PlatformParagraphStyle;->**(**)**
Landroidx/compose/ui/text/PlatformSpanStyle;
HSPLandroidx/compose/ui/text/PlatformTextStyle;->**(**)**
SPLandroidx/compose/ui/text/SaversKt**->**(**)**
HSPLandroidx/compose/ui/text/SpanStyle;->**(**)**
HSPLandroidx/compose/ui/text/SpanStyleKt**->**(**)**
PLandroidx/compose/ui/text/TextLayoutCache;->**(**)**
HSPLandroidx/compose/ui/text/TextLayoutInput;->**(**)**
HSPLandroidx/compose/ui/text/TextLayoutResult;->**(**)**
PLandroidx/compose/ui/text/TextMeasurer;->**(**)**
HPLandroidx/compose/ui/text/TextMeasurerHelperKt**->**(**)**
SPLandroidx/compose/ui/text/TextRange;->**(**)**
SPLandroidx/compose/ui/text/TextRangeKt**->**(**)**
HSPLandroidx/compose/ui/text/TextStyle;->**(**)**
HSPLandroidx/compose/ui/text/TextStyleKt**->**(**)**
SPLandroidx/compose/ui/text/android/BoringLayoutFactory33;->**(**)**
HSPLandroidx/compose/ui/text/android/BoringLayoutFactory;->**(**)**
HSPLandroidx/compose/ui/text/android/CanvasCompatM;->**(**)**
PLandroidx/compose/ui/text/android/LayoutCompat;->**(**)**
PLandroidx/compose/ui/text/android/LayoutCompat_androidKt**->**(**)**
HPLandroidx/compose/ui/text/android/LayoutHelper;->**(**)**
HSPLandroidx/compose/ui/text/android/LayoutIntrinsics;->**(**)**
HSPLandroidx/compose/ui/text/android/LayoutIntrinsics_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/android/SpannedExtensions_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/android/StaticLayoutFactory23;->**(**)**
SPLandroidx/compose/ui/text/android/StaticLayoutFactory26;->**(**)**
SPLandroidx/compose/ui/text/android/StaticLayoutFactory28;->**(**)**
HSPLandroidx/compose/ui/text/android/StaticLayoutFactory33;->**(**)**
SPLandroidx/compose/ui/text/android/StaticLayoutFactory35;->**(**)**
HSPLandroidx/compose/ui/text/android/StaticLayoutFactory;->**(**)**
Landroidx/compose/ui/text/android/StaticLayoutFactoryImpl;
HSPLandroidx/compose/ui/text/android/StaticLayoutParams;->**(**)**
HSPLandroidx/compose/ui/text/android/TextAlignmentAdapter;->**(**)**
HSPLandroidx/compose/ui/text/android/TextAndroidCanvas;->**(**)**
HSPLandroidx/compose/ui/text/android/TextLayout;->**(**)**
HSPLandroidx/compose/ui/text/android/TextLayout_androidKt**->**(**)**
SPLandroidx/compose/ui/text/android/VerticalPaddings;->**(**)**
Landroidx/compose/ui/text/android/style/BaselineShiftSpan;
HSPLandroidx/compose/ui/text/android/style/IndentationFixSpan;->**(**)**
HSPLandroidx/compose/ui/text/android/style/IndentationFixSpan_androidKt**->**(**)**
Landroidx/compose/ui/text/android/style/LetterSpacingSpanEm;
Landroidx/compose/ui/text/android/style/LetterSpacingSpanPx;
HSPLandroidx/compose/ui/text/android/style/LineHeightStyleSpan;->**(**)**
SPLandroidx/compose/ui/text/android/style/LineHeightStyleSpan_androidKt**->**(**)**
Landroidx/compose/ui/text/android/style/PlaceholderSpan;
SPLandroidx/compose/ui/text/font/AndroidFont;->**(**)**
HSPLandroidx/compose/ui/text/font/AndroidFontLoader;->**(**)**
PLandroidx/compose/ui/text/font/AndroidFontLoader_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/font/AndroidFontResolveInterceptor;->**(**)**
SPLandroidx/compose/ui/text/font/AndroidFontResolveInterceptor_androidKt**->**(**)**
PLandroidx/compose/ui/text/font/AndroidFontUtils_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/font/AsyncTypefaceCache;->**(**)**
SPLandroidx/compose/ui/text/font/DefaultFontFamily;->**(**)**
SPLandroidx/compose/ui/text/font/DeviceFontFamilyName;->**(**)**
HSPLandroidx/compose/ui/text/font/DeviceFontFamilyNameFont;->**(**)**
SPLandroidx/compose/ui/text/font/DeviceFontFamilyNameFontKt**->**(**)**
SPLandroidx/compose/ui/text/font/FileBasedFontFamily;->**(**)**
SPLandroidx/compose/ui/text/font/Font;->**(**)**
SPLandroidx/compose/ui/text/font/FontFamily;->**(**)**
SPLandroidx/compose/ui/text/font/FontFamilyKt**->**(**)**
HSPLandroidx/compose/ui/text/font/FontFamilyResolverImpl;->**(**)**
SPLandroidx/compose/ui/text/font/FontFamilyResolverKt**->**(**)**
SPLandroidx/compose/ui/text/font/FontFamilyResolver_androidKt**->**(**)**
Landroidx/compose/ui/text/font/FontFamilyTypefaceAdapter;
SPLandroidx/compose/ui/text/font/FontKt**->**(**)**
HSPLandroidx/compose/ui/text/font/FontListFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/FontListFontFamilyTypefaceAdapter;->**(**)**
HSPLandroidx/compose/ui/text/font/FontListFontFamilyTypefaceAdapterKt**->**(**)**
SPLandroidx/compose/ui/text/font/FontLoadingStrategy;->**(**)**
HSPLandroidx/compose/ui/text/font/FontMatcher;->**(**)**
HSPLandroidx/compose/ui/text/font/FontStyle;->**(**)**
HSPLandroidx/compose/ui/text/font/FontSynthesis;->**(**)**
HSPLandroidx/compose/ui/text/font/FontSynthesis_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/font/FontVariation;->**(**)**
HSPLandroidx/compose/ui/text/font/FontWeight;->**(**)**
SPLandroidx/compose/ui/text/font/FontWeightAdjustmentHelper;->**(**)**
SPLandroidx/compose/ui/text/font/FontWeightAdjustmentHelperApi31;->**(**)**
SPLandroidx/compose/ui/text/font/GenericFontFamily;->**(**)**
SPLandroidx/compose/ui/text/font/NamedFontLoader;->**(**)**
SPLandroidx/compose/ui/text/font/PlatformFontFamilyTypefaceAdapter;->**(**)**
Landroidx/compose/ui/text/font/PlatformFontLoader;
SPLandroidx/compose/ui/text/font/PlatformResolveInterceptor;->**(**)**
Landroidx/compose/ui/text/font/PlatformTypefaces;
HSPLandroidx/compose/ui/text/font/PlatformTypefacesApi28;->**(**)**
SPLandroidx/compose/ui/text/font/PlatformTypefaces_androidKt**->**(**)**
PLandroidx/compose/ui/text/font/ResourceFont;->**(**)**
SPLandroidx/compose/ui/text/font/SystemFontFamily;->**(**)**
HSPLandroidx/compose/ui/text/font/TypefaceCompatApi26;->**(**)**
SPLandroidx/compose/ui/text/font/TypefaceHelperMethodsApi28;->**(**)**
HSPLandroidx/compose/ui/text/font/TypefaceRequest;->**(**)**
HSPLandroidx/compose/ui/text/font/TypefaceRequestCache;->**(**)**
HSPLandroidx/compose/ui/text/font/TypefaceResult;->**(**)**
SPLandroidx/compose/ui/text/input/CursorAnchorInfoController;->**(**)**
SPLandroidx/compose/ui/text/input/ImeAction;->**(**)**
SPLandroidx/compose/ui/text/input/ImeOptions;->**(**)**
Landroidx/compose/ui/text/input/InputMethodManager;
SPLandroidx/compose/ui/text/input/InputMethodManagerImpl;->**(**)**
SPLandroidx/compose/ui/text/input/KeyboardCapitalization;->**(**)**
SPLandroidx/compose/ui/text/input/KeyboardType;->**(**)**
Landroidx/compose/ui/text/input/PlatformImeOptions;
Landroidx/compose/ui/text/input/PlatformTextInputService;
SPLandroidx/compose/ui/text/input/TextFieldValue;->**(**)**
SPLandroidx/compose/ui/text/input/TextInputService;->**(**)**
SPLandroidx/compose/ui/text/input/TextInputServiceAndroid;->**(**)**
SPLandroidx/compose/ui/text/input/TextInputServiceAndroid_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/intl/AndroidLocaleDelegateAPI24;->**(**)**
SPLandroidx/compose/ui/text/intl/AndroidPlatformLocale_androidKt**->**(**)**
SPLandroidx/compose/ui/text/intl/Locale;->**(**)**
HSPLandroidx/compose/ui/text/intl/LocaleList;->**(**)**
Landroidx/compose/ui/text/intl/PlatformLocaleDelegate;
SPLandroidx/compose/ui/text/intl/PlatformLocaleKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidAccessibilitySpannableString_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraphHelper_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraphIntrinsics;->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraphIntrinsics_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidParagraph_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/AndroidTextPaint;->**(**)**
HSPLandroidx/compose/ui/text/platform/DefaultImpl;->**(**)**
SPLandroidx/compose/ui/text/platform/DispatcherKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/EmojiCompatStatus;->**(**)**
Landroidx/compose/ui/text/platform/EmojiCompatStatusDelegate;
SPLandroidx/compose/ui/text/platform/EmojiCompatStatus_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/ImmutableBool;->**(**)**
SPLandroidx/compose/ui/text/platform/SynchronizedObject;->**(**)**
SPLandroidx/compose/ui/text/platform/URLSpanCache;->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/PlaceholderExtensions_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/SpannableExtensions_androidKt**->**(**)**
HSPLandroidx/compose/ui/text/platform/extensions/TextPaintExtensions_androidKt**->**(**)**
Landroidx/compose/ui/text/platform/style/ShaderBrushSpan;
HSPLandroidx/compose/ui/text/style/BaselineShift;->**(**)**
Landroidx/compose/ui/text/style/BrushStyle;
HSPLandroidx/compose/ui/text/style/ColorStyle;->**(**)**
HSPLandroidx/compose/ui/text/style/Hyphens;->**(**)**
HSPLandroidx/compose/ui/text/style/LineBreak;->**(**)**
SPLandroidx/compose/ui/text/style/LineBreak_androidKt**->**(**)**
SPLandroidx/compose/ui/text/style/LineHeightStyle;->**(**)**
HSPLandroidx/compose/ui/text/style/TextAlign;->**(**)**
HSPLandroidx/compose/ui/text/style/TextDecoration;->**(**)**
HSPLandroidx/compose/ui/text/style/TextDirection;->**(**)**
HSPLandroidx/compose/ui/text/style/TextForegroundStyle;->**(**)**
SPLandroidx/compose/ui/text/style/TextGeometricTransform;->**(**)**
SPLandroidx/compose/ui/text/style/TextIndent;->**(**)**
SPLandroidx/compose/ui/text/style/TextMotion;->**(**)**
HSPLandroidx/compose/ui/text/style/TextOverflow;->**(**)**
HSPLandroidx/compose/ui/unit/AndroidDensity_androidKt**->**(**)**
HSPLandroidx/compose/ui/unit/Constraints;->**(**)**
HSPLandroidx/compose/ui/unit/ConstraintsKt**->**(**)**
HSPLandroidx/compose/ui/unit/Density;->**(**)**
HSPLandroidx/compose/ui/unit/DensityImpl;->**(**)**
SPLandroidx/compose/ui/unit/DensityKt**->**(**)**
HSPLandroidx/compose/ui/unit/DensityWithConverter;->**(**)**
HSPLandroidx/compose/ui/unit/Dp;->**(**)**
HSPLandroidx/compose/ui/unit/DpKt**->**(**)**
SPLandroidx/compose/ui/unit/DpOffset;->**(**)**
HSPLandroidx/compose/ui/unit/DpSize;->**(**)**
HSPLandroidx/compose/ui/unit/FontScaling;->**(**)**
HSPLandroidx/compose/ui/unit/IntOffset;->**(**)**
HSPLandroidx/compose/ui/unit/IntOffsetKt**->**(**)**
HSPLandroidx/compose/ui/unit/IntRect;->**(**)**
HSPLandroidx/compose/ui/unit/IntRectKt**->**(**)**
HSPLandroidx/compose/ui/unit/IntSize;->**(**)**
HSPLandroidx/compose/ui/unit/IntSizeKt**->**(**)**
SPLandroidx/compose/ui/unit/LayoutDirection;->**(**)**
SPLandroidx/compose/ui/unit/LinearFontScaleConverter;->**(**)**
HSPLandroidx/compose/ui/unit/TextUnit;->**(**)**
HSPLandroidx/compose/ui/unit/TextUnitKt**->**(**)**
HSPLandroidx/compose/ui/unit/TextUnitType;->**(**)**
HPLandroidx/compose/ui/unit/Velocity;->**(**)**
HPLandroidx/compose/ui/unit/VelocityKt**->**(**)**
Landroidx/compose/ui/unit/fontscaling/FontScaleConverter;
SPLandroidx/compose/ui/unit/fontscaling/FontScaleConverterFactory;->**(**)**
SPLandroidx/compose/ui/unit/fontscaling/FontScaleConverterTable;->**(**)**
HSPLandroidx/compose/ui/util/AndroidTrace_androidKt**->**(**)**
HSPLandroidx/compose/ui/util/ListUtilsKt**->**(**)**
HSPLandroidx/compose/ui/util/MathHelpersKt**->**(**)**
Landroidx/compose/ui/viewinterop/AndroidViewHolder;
HSPLandroidx/compose/ui/window/DialogProperties;->**(**)**
SPLandroidx/compose/ui/window/SecureFlagPolicy;->**(**)**

# Baseline profile rules for androidx.compose.ui
# =============================================
#

#
# root level things
HSPLandroidx/compose/ui/Alignment**->**(**)**
HSPLandroidx/compose/ui/BiasAlignment**->**(**)**
HSPLandroidx/compose/ui/Modifier**->**(**)**
HSPLandroidx/compose/ui/CombinedModifier**->**(**)**
HSPLandroidx/compose/ui/ComposedModifier**->**(**)**
HSPLandroidx/compose/ui/KeyedComposedModifier**->**(**)**
HSPLandroidx/compose/ui/MotionDurationScale**->**(**)**
#
# autofill
HSPLandroidx/compose/ui/autofill/AndroidAutofill**->**(**)**
HSPLandroidx/compose/ui/autofill/AutofillCallback;->**(**)**
HSPLandroidx/compose/ui/autofill/AutofillTree;->**(**)**
#
# draw
HSPLandroidx/compose/ui/draw/ClipKt**->**(**)**
HSPLandroidx/compose/ui/draw/DrawBackgroundModifier;->**(**)**
HSPLandroidx/compose/ui/draw/DrawBehindElement;->**(**)**
HSPLandroidx/compose/ui/draw/DrawResult;->**(**)**
HSPLandroidx/compose/ui/draw/DrawModifier**->**(**)**
HSPLandroidx/compose/ui/draw/ShadowKt**->**(**)**
#
# focus
HSPLandroidx/compose/ui/focus/FocusChangedModifier**->**(**)**
HSPLandroidx/compose/ui/focus/FocusDirection;->**(**)**
HSPLandroidx/compose/ui/focus/FocusEventModifierKt**->**(**)**
HSPLandroidx/compose/ui/focus/FocusEventModifierLocal;->**(**)**
HSPLandroidx/compose/ui/focus/FocusInvalidationManager;->**(**)**
HSPLandroidx/compose/ui/focus/FocusManagerImpl;->**(**)**
HSPLandroidx/compose/ui/focus/FocusManagerKt**->**(**)**
HSPLandroidx/compose/ui/focus/FocusModifier**->**(**)**
HSPLandroidx/compose/ui/focus/FocusOwnerImpl**->**(**)**
HSPLandroidx/compose/ui/focus/FocusProperties**->**(**)**
HSPLandroidx/compose/ui/focus/FocusRequester**->**(**)**
HSPLandroidx/compose/ui/focus/FocusStateImpl;->**(**)**
HSPLandroidx/compose/ui/focus/FocusTargetNode**->**(**)**
HSPLandroidx/compose/ui/focus/FocusTransactionManager;->**(**)**

#
# geometry include everything
HSPLandroidx/compose/ui/geometry/**->**(**)**

#
# graphics include everything
HSPLandroidx/compose/ui/graphics/**->**(**)**

#
# spatial indexing include everything
HSPLandroidx/compose/ui/spatial/**->**(**)**

# input
HSPLandroidx/compose/ui/input/InputMode;->**(**)**
HSPLandroidx/compose/ui/input/InputModeManagerImpl;->**(**)**
HSPLandroidx/compose/ui/input/key/KeyInputElement**->**(**)**

# nested scroll
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollDelegatingWrapper;->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollDispatcher**->**(**)**
HSPLandroidx/compose/ui/input/nestedscroll/NestedScrollNode**->**(**)**
#
# pointer input
HSPLandroidx/compose/ui/input/pointer/AwaitPointerEventScope**->**(**)**
HSPLandroidx/compose/ui/input/pointer/ConsumedData;->**(**)**
HSPLandroidx/compose/ui/input/pointer/HistoricalChange;->**(**)**
HSPLandroidx/compose/ui/input/pointer/HitPathTracker;->**(**)**
HSPLandroidx/compose/ui/input/pointer/InternalPointerEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/MotionEventAdapter;->**(**)**
HSPLandroidx/compose/ui/input/pointer/MotionEventAdapter_androidKt;->**(**)**
HSPLandroidx/compose/ui/input/pointer/Node;->**(**)**
HSPLandroidx/compose/ui/input/pointer/NodeParent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEventKt;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerEventPass;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerId;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputChange;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputChangeEventProducer**->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputEventData;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputEventProcessor;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputFilter;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInputModifier$DefaultImpls;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerInteropFilter**->**(**)**
HSPLandroidx/compose/ui/input/pointer/RequestDisallowInterceptTouchEvent;->**(**)**
HSPLandroidx/compose/ui/input/pointer/ProcessResult;->**(**)**
HSPLandroidx/compose/ui/input/pointer/PointerType;->**(**)**
HSPLandroidx/compose/ui/input/pointer/SuspendingPointerInputFilter**->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/ImpulseCalculator;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/Matrix;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/PointAtTime;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/PointerIdArray;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/PolynomialFit;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/VelocityEstimate;->**(**)**
HSPLandroidx/compose/ui/input/pointer/util/VelocityTracker**->**(**)**

#
# rotary
HSPLandroidx/compose/ui/input/rotary/RotaryInputModifier**->**(**)**

#
# layout. include everything
HSPLandroidx/compose/ui/layout/**->**(**)**
#
# modifier. include everything
HSPLandroidx/compose/ui/modifier/**->**(**)**
#
# node. include everything
HSPLandroidx/compose/ui/node/**->**(**)**
#
# platform
HSPLandroidx/compose/ui/platform/AndroidComposeView**->**(**)**
HSPLandroidx/compose/ui/platform/AbstractComposeView**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewAccessibilityDelegateCompat**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewForceDarkMode**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeViewVerificationHelperMethods**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidComposeView_androidKt;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidCompositionLocals_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidFontResourceLoader;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidTextToolbar;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUiDispatcher**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUiFrameClock**->**(**)**
HSPLandroidx/compose/ui/platform/AndroidUriHandler;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidViewConfiguration;->**(**)**
HSPLandroidx/compose/ui/platform/AndroidViewsHandler;->**(**)**
HSPLandroidx/compose/ui/platform/ComposableSingletons**->**(**)**
HSPLandroidx/compose/ui/platform/ComposeView**->**(**)**
HSPLandroidx/compose/ui/platform/CompositionLocalsKt**->**(**)**
HSPLandroidx/compose/ui/platform/DisposableSaveableStateRegistry;->**(**)**
HSPLandroidx/compose/ui/platform/DisposableSaveableStateRegistry_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/GlobalSnapshotManager**->**(**)**
HSPLandroidx/compose/ui/platform/InspectableModifier**->**(**)**
HSPLandroidx/compose/ui/platform/InspectableValueKt**->**(**)**
HSPLandroidx/compose/ui/platform/InspectorValueInfo;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLandroidx/compose/ui/platform/InvertMatrixKt;->**(**)**
HSPLandroidx/compose/ui/platform/LayerMatrixCache;->**(**)**
HSPLandroidx/compose/ui/platform/MotionDurationScaleImpl;->**(**)**
HSPLandroidx/compose/ui/platform/RenderNodeLayer**->**(**)**
HSPLandroidx/compose/ui/platform/RenderNodeApi**->**(**)**
HSPLandroidx/compose/ui/platform/OutlineResolver;->**(**)**
HSPLandroidx/compose/ui/platform/RenderNodeMatrixCache;->**(**)**
HSPLandroidx/compose/ui/platform/ViewCompositionStrategy**->**(**)**
HSPLandroidx/compose/ui/platform/ViewLayer;->**(**)**
HSPLandroidx/compose/ui/platform/WeakCache;->**(**)**
HSPLandroidx/compose/ui/platform/WindowInfoImpl;->**(**)**
HSPLandroidx/compose/ui/platform/WindowRecomposerPolicy**->**(**)**
HSPLandroidx/compose/ui/platform/WindowRecomposer_androidKt**->**(**)**
HSPLandroidx/compose/ui/platform/WrappedComposition**->**(**)**
HSPLandroidx/compose/ui/platform/WrapperRenderNodeLayerHelperMethods**->**(**)**
HSPLandroidx/compose/ui/platform/Wrapper**->**(**)**
HSPLandroidx/compose/ui/platform/accessibility/CollectionInfoKt;->**(**)**
#
# semantics
HSPLandroidx/compose/ui/semantics/AccessibilityAction;->**(**)**
HSPLandroidx/compose/ui/semantics/AppendedSemanticsElement;->**(**)**
HSPLandroidx/compose/ui/semantics/CollectionInfo;->**(**)**
HSPLandroidx/compose/ui/semantics/CoreSemanticsModifierNode;->**(**)**
HSPLandroidx/compose/ui/semantics/EmptySemanticsElement;->**(**)**
HSPLandroidx/compose/ui/semantics/NodeLocationHolder;->**(**)**
HSPLandroidx/compose/ui/semantics/Role;->**(**)**
HSPLandroidx/compose/ui/semantics/ScrollAxisRange;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsActions;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsConfiguration;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsEntity;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifier$DefaultImpls;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifierCore$Companion;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifierCore;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsModifierKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNode;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNode$parent$1;->**(**)**
HSPLandroidx/compose/ui/platform/SemanticsNodeWithAdjustedBounds;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsNodeKt;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsOwner;->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsProperties**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsPropertiesKt**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsPropertyKey**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsSort**->**(**)**
HSPLandroidx/compose/ui/semantics/SemanticsWrapper;->**(**)**
#
# res
HSPLandroidx/compose/ui/res/ImageVectorCache;->**(**)**
HSPLandroidx/compose/ui/res/StringResources_androidKt;->**(**)**
HSPLandroidx/compose/ui/res/PainterResources_androidKt;->**(**)**
HSPLandroidx/compose/ui/res/ImageResources_androidKt;->**(**)**

# Baseline profile rules for androidx.compose.runtime.saveable
# =============================================
HSPLandroidx/compose/runtime/saveable/RememberSaveableKt**->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateHolderImpl**->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateHolderKt**->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateRegistryKt;->**(**)**
HSPLandroidx/compose/runtime/saveable/SaveableStateRegistryImpl;->**(**)**
Landroidx/compose/runtime/saveable/*;
# Baseline profiles for lifecycle-livedata-core

HSPLandroidx/lifecycle/LiveData$1;-><init>(Landroidx/lifecycle/LiveData;)V
HSPLandroidx/lifecycle/LiveData$1;->run()V
HSPLandroidx/lifecycle/LiveData$AlwaysActiveObserver;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$AlwaysActiveObserver;->shouldBeActive()Z
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->shouldBeActive()Z
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;->activeStateChanged(Z)V
HSPLandroidx/lifecycle/LiveData$ObserverWrapper;->detachObserver()V
HSPLandroidx/lifecycle/LiveData;-><clinit>()V
HSPLandroidx/lifecycle/LiveData;-><init>()V
HSPLandroidx/lifecycle/LiveData;->assertMainThread(Ljava/lang/String;)V
HSPLandroidx/lifecycle/LiveData;->changeActiveCounter(I)V
HSPLandroidx/lifecycle/LiveData;->considerNotify(Landroidx/lifecycle/LiveData$ObserverWrapper;)V
HSPLandroidx/lifecycle/LiveData;->dispatchingValue(Landroidx/lifecycle/LiveData$ObserverWrapper;)V
HSPLandroidx/lifecycle/LiveData;->getValue()Ljava/lang/Object;
HSPLandroidx/lifecycle/LiveData;->getVersion()I
HSPLandroidx/lifecycle/LiveData;->hasActiveObservers()Z
HSPLandroidx/lifecycle/LiveData;->observe(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->observeForever(Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->onActive()V
HSPLandroidx/lifecycle/LiveData;->onInactive()V
HSPLandroidx/lifecycle/LiveData;->postValue(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/LiveData;->removeObserver(Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/LiveData;->setValue(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;-><init>(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->onChanged(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->plug()V
HSPLandroidx/lifecycle/MediatorLiveData$Source;->unplug()V
HSPLandroidx/lifecycle/MediatorLiveData;-><init>()V
HSPLandroidx/lifecycle/MediatorLiveData;->addSource(Landroidx/lifecycle/LiveData;Landroidx/lifecycle/Observer;)V
HSPLandroidx/lifecycle/MediatorLiveData;->onActive()V
HSPLandroidx/lifecycle/MediatorLiveData;->onInactive()V
HSPLandroidx/lifecycle/MediatorLiveData;->removeSource(Landroidx/lifecycle/LiveData;)V
HSPLandroidx/lifecycle/MutableLiveData;-><init>()V
HSPLandroidx/lifecycle/MutableLiveData;->setValue(Ljava/lang/Object;)V
PLandroidx/lifecycle/LiveData$LifecycleBoundObserver;->detachObserver()V

# Baseline profiles for Lifecycle ViewModel

HSPLandroidx/lifecycle/ViewModel;-><init>()V
HSPLandroidx/lifecycle/ViewModelLazy;-><init>(Lkotlin/reflect/KClass;Lkotlin/jvm/functions/Function0;Lkotlin/jvm/functions/Function0;)V
HSPLandroidx/lifecycle/ViewModelLazy;->getValue()Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelLazy;->getValue()Ljava/lang/Object;
HSPLandroidx/lifecycle/ViewModelProvider;-><init>(Landroidx/lifecycle/ViewModelStore;Landroidx/lifecycle/ViewModelProvider$Factory;)V
HSPLandroidx/lifecycle/ViewModelProvider;->get(Ljava/lang/Class;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelProvider;->get(Ljava/lang/String;Ljava/lang/Class;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelStore;-><init>()V
HSPLandroidx/lifecycle/ViewModelStore;->get(Ljava/lang/String;)Landroidx/lifecycle/ViewModel;
HSPLandroidx/lifecycle/ViewModelStore;->put(Ljava/lang/String;Landroidx/lifecycle/ViewModel;)V
PLandroidx/lifecycle/ViewModel;->clear()V
PLandroidx/lifecycle/ViewModel;->onCleared()V
PLandroidx/lifecycle/ViewModelStore;->clear()V

# Baseline Profile rules for lifecycle-runtime

HPLandroidx/lifecycle/LifecycleRegistry;->backwardPass(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry$ObserverWithState;-><init>(Landroidx/lifecycle/LifecycleObserver;Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry$ObserverWithState;->dispatchEvent(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LifecycleRegistry;-><init>(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry;-><init>(Landroidx/lifecycle/LifecycleOwner;Z)V
HSPLandroidx/lifecycle/LifecycleRegistry;->addObserver(Landroidx/lifecycle/LifecycleObserver;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->calculateTargetState(Landroidx/lifecycle/LifecycleObserver;)Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->enforceMainThreadIfNeeded(Ljava/lang/String;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->forwardPass(Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->getCurrentState()Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->handleLifecycleEvent(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->isSynced()Z
HSPLandroidx/lifecycle/LifecycleRegistry;->min(Landroidx/lifecycle/Lifecycle$State;Landroidx/lifecycle/Lifecycle$State;)Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/LifecycleRegistry;->moveToState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->popParentState()V
HSPLandroidx/lifecycle/LifecycleRegistry;->pushParentState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->removeObserver(Landroidx/lifecycle/LifecycleObserver;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->setCurrentState(Landroidx/lifecycle/Lifecycle$State;)V
HSPLandroidx/lifecycle/LifecycleRegistry;->sync()V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;-><init>()V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPostStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->registerIn(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment;-><init>()V
HSPLandroidx/lifecycle/ReportFragment;->dispatch(Landroid/app/Activity;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatch(Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchCreate(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchResume(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->dispatchStart(Landroidx/lifecycle/ReportFragment$ActivityInitializationListener;)V
HSPLandroidx/lifecycle/ReportFragment;->injectIfNeededIn(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ReportFragment;->onActivityCreated(Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ReportFragment;->onResume()V
HSPLandroidx/lifecycle/ReportFragment;->onStart()V
HSPLandroidx/lifecycle/ViewTreeLifecycleOwner;->set(Landroid/view/View;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/ViewTreeViewModelStoreOwner;->set(Landroid/view/View;Landroidx/lifecycle/ViewModelStoreOwner;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPreDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPrePaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityPreStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment$LifecycleCallbacks;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ReportFragment;->onDestroy()V
PLandroidx/lifecycle/ReportFragment;->onPause()V
PLandroidx/lifecycle/ReportFragment;->onStop()V

# Baseline Profiles for lifecycle-common

HPLandroidx/lifecycle/Lifecycle$Event;->downFrom(Landroidx/lifecycle/Lifecycle$State;)Landroidx/lifecycle/Lifecycle$Event;
HSPLandroidx/lifecycle/ClassesInfoCache$CallbackInfo;-><init>(Ljava/util/Map;)V
HSPLandroidx/lifecycle/ClassesInfoCache$CallbackInfo;->invokeCallbacks(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ClassesInfoCache$CallbackInfo;->invokeMethodsForEvent(Ljava/util/List;Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ClassesInfoCache$MethodReference;-><init>(ILjava/lang/reflect/Method;)V
HSPLandroidx/lifecycle/ClassesInfoCache$MethodReference;->hashCode()I
HSPLandroidx/lifecycle/ClassesInfoCache$MethodReference;->invokeCallback(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ClassesInfoCache;-><clinit>()V
HSPLandroidx/lifecycle/ClassesInfoCache;-><init>()V
HSPLandroidx/lifecycle/ClassesInfoCache;->createInfo(Ljava/lang/Class;[Ljava/lang/reflect/Method;)Landroidx/lifecycle/ClassesInfoCache$CallbackInfo;
HSPLandroidx/lifecycle/ClassesInfoCache;->getDeclaredMethods(Ljava/lang/Class;)[Ljava/lang/reflect/Method;
HSPLandroidx/lifecycle/ClassesInfoCache;->getInfo(Ljava/lang/Class;)Landroidx/lifecycle/ClassesInfoCache$CallbackInfo;
HSPLandroidx/lifecycle/ClassesInfoCache;->hasLifecycleMethods(Ljava/lang/Class;)Z
HSPLandroidx/lifecycle/ClassesInfoCache;->verifyAndPutHandler(Ljava/util/Map;Landroidx/lifecycle/ClassesInfoCache$MethodReference;Landroidx/lifecycle/Lifecycle$Event;Ljava/lang/Class;)V
HSPLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onCreate(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onResume(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onStart(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
HSPLandroidx/lifecycle/FullLifecycleObserverAdapter$1;-><clinit>()V
HSPLandroidx/lifecycle/FullLifecycleObserverAdapter;-><init>(Landroidx/lifecycle/FullLifecycleObserver;Landroidx/lifecycle/LifecycleEventObserver;)V
HSPLandroidx/lifecycle/FullLifecycleObserverAdapter;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
HSPLandroidx/lifecycle/Lifecycle$1;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycle$Event;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycle$Event;-><init>(Ljava/lang/String;I)V
HSPLandroidx/lifecycle/Lifecycle$Event;->getTargetState()Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/Lifecycle$Event;->upFrom(Landroidx/lifecycle/Lifecycle$State;)Landroidx/lifecycle/Lifecycle$Event;
HSPLandroidx/lifecycle/Lifecycle$Event;->values()[Landroidx/lifecycle/Lifecycle$Event;
HSPLandroidx/lifecycle/Lifecycle$State;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycle$State;-><init>(Ljava/lang/String;I)V
HSPLandroidx/lifecycle/Lifecycle$State;->isAtLeast(Landroidx/lifecycle/Lifecycle$State;)Z
HSPLandroidx/lifecycle/Lifecycle$State;->values()[Landroidx/lifecycle/Lifecycle$State;
HSPLandroidx/lifecycle/Lifecycle;-><init>()V
HSPLandroidx/lifecycle/Lifecycling;-><clinit>()V
HSPLandroidx/lifecycle/Lifecycling;->generatedConstructor(Ljava/lang/Class;)Ljava/lang/reflect/Constructor;
HSPLandroidx/lifecycle/Lifecycling;->getAdapterName(Ljava/lang/String;)Ljava/lang/String;
HSPLandroidx/lifecycle/Lifecycling;->getObserverConstructorType(Ljava/lang/Class;)I
HSPLandroidx/lifecycle/Lifecycling;->lifecycleEventObserver(Ljava/lang/Object;)Landroidx/lifecycle/LifecycleEventObserver;
HSPLandroidx/lifecycle/Lifecycling;->resolveObserverCallbackType(Ljava/lang/Class;)I
HSPLandroidx/lifecycle/ReflectiveGenericLifecycleObserver;-><init>(Ljava/lang/Object;)V
HSPLandroidx/lifecycle/ReflectiveGenericLifecycleObserver;->onStateChanged(Landroidx/lifecycle/LifecycleOwner;Landroidx/lifecycle/Lifecycle$Event;)V
PLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onDestroy(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
PLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onPause(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V
PLandroidx/lifecycle/DefaultLifecycleObserver$-CC;->$default$onStop(Landroidx/lifecycle/DefaultLifecycleObserver;Landroidx/lifecycle/LifecycleOwner;)V

# Baseline profiles for lifecycle-process

HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;-><init>()V
HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/LifecycleDispatcher$DispatcherActivityCallback;-><init>()V
HSPLandroidx/lifecycle/LifecycleDispatcher$DispatcherActivityCallback;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/LifecycleDispatcher;-><clinit>()V
HSPLandroidx/lifecycle/LifecycleDispatcher;->init(Landroid/content/Context;)V
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;-><init>()V
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;->create(Landroid/content/Context;)Landroidx/lifecycle/LifecycleOwner;
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;->create(Landroid/content/Context;)Ljava/lang/Object;
HSPLandroidx/lifecycle/ProcessLifecycleInitializer;->dependencies()Ljava/util/List;
HSPLandroidx/lifecycle/ProcessLifecycleOwner$1;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$2;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3$1;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner$3;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3$1;->onActivityPostResumed(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3$1;->onActivityPostStarted(Landroid/app/Activity;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3;-><init>(Landroidx/lifecycle/ProcessLifecycleOwner;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityPreCreated(Landroid/app/Activity;Landroid/os/Bundle;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;-><clinit>()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;-><init>()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->activityResumed()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->activityStarted()V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->attach(Landroid/content/Context;)V
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->get()Landroidx/lifecycle/LifecycleOwner;
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->getLifecycle()Landroidx/lifecycle/Lifecycle;
HSPLandroidx/lifecycle/ProcessLifecycleOwner;->init(Landroid/content/Context;)V
PLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityDestroyed(Landroid/app/Activity;)V
PLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityPaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/EmptyActivityLifecycleCallbacks;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/LifecycleDispatcher$DispatcherActivityCallback;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ProcessLifecycleOwner$1;->run()V
PLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityPaused(Landroid/app/Activity;)V
PLandroidx/lifecycle/ProcessLifecycleOwner$3;->onActivityStopped(Landroid/app/Activity;)V
PLandroidx/lifecycle/ProcessLifecycleOwner;->activityPaused()V
PLandroidx/lifecycle/ProcessLifecycleOwner;->activityStopped()V
PLandroidx/lifecycle/ProcessLifecycleOwner;->dispatchPauseIfNeeded()V
PLandroidx/lifecycle/ProcessLifecycleOwner;->dispatchStopIfNeeded()V

# Baseline profile rules for androidx.compose.runtime
# =============================================
#
# We prioritize everything at the top level, and a few sub-namespaces
Landroidx/compose/runtime/*;
Landroidx/compose/runtime/snapshots/*;
Landroidx/compose/runtime/internal/*;
Landroidx/compose/runtime/external/kotlinx/collections/immutable/**;
#
# Core runtime classes
# ====
# Note: AbstractApplier might benefit from inline caches. consider removing.
HSPLandroidx/compose/runtime/AbstractApplier;->**(**)**
HSPLandroidx/compose/runtime/ActualJvm_jvmKt;->identityHashCode(Ljava/lang/Object;)I
HSPLandroidx/compose/runtime/ActualAndroid**->**(**)**
HSPLandroidx/compose/runtime/Anchor;->**(**)**
HSPLandroidx/compose/runtime/Applier$DefaultImpls;->**(**)**
HSPLandroidx/compose/runtime/BroadcastFrameClock**->**(**)**
HSPLandroidx/compose/runtime/ComposablesKt;->**(**)**
HSPLandroidx/compose/runtime/ComposableSingletons**->**(**)**
HSPLandroidx/compose/runtime/ComposerImpl**->**(**)**
HSPLandroidx/compose/runtime/ComposerKt**->**(**)**
HSPLandroidx/compose/runtime/CompositionContext;->**(**)**
HSPLandroidx/compose/runtime/CompositionImpl**->**(**)**
HSPLandroidx/compose/runtime/CompositionKt;->**(**)**
HSPLandroidx/compose/runtime/CompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/CompositionLocalKt;->**(**)**
HSPLandroidx/compose/runtime/CompositionScopedCoroutineScopeCanceller;->**(**)**
HSPLandroidx/compose/runtime/DerivedSnapshotState**->**(**)**
HSPLandroidx/compose/runtime/DisposableEffectImpl;->**(**)**
HSPLandroidx/compose/runtime/DisposableEffectScope;->**(**)**
HSPLandroidx/compose/runtime/DynamicProvidableCompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/EffectsKt;->**(**)**
HSPLandroidx/compose/runtime/GroupInfo;->**(**)**
HSPLandroidx/compose/runtime/GroupKind**->**(**)**
HSPLandroidx/compose/runtime/InvalidationResult;->**(**)**
HSPLandroidx/compose/runtime/Invalidation;->**(**)**
HSPLandroidx/compose/runtime/KeyInfo;->**(**)**
HSPLandroidx/compose/runtime/Latch**->**(**)**
HSPLandroidx/compose/runtime/LaunchedEffectImpl;->**(**)**
HSPLandroidx/compose/runtime/LazyValueHolder;->**(**)**
HSPLandroidx/compose/runtime/MonotonicFrameClock**->**(**)**
HSPLandroidx/compose/runtime/NeverEqualPolicy;->**(**)**
HSPLandroidx/compose/runtime/OpaqueKey;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableFloatState;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableIntState;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableLongState;->**(**)**
HSPLandroidx/compose/runtime/ParcelableSnapshotMutableState**->**(**)**
HSPLandroidx/compose/runtime/PausableMonotonicFrameClock;->**(**)**
HSPLandroidx/compose/runtime/Pending**->**(**)**
HSPLandroidx/compose/runtime/ProvidableCompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/ProvidedValue;->**(**)**
HSPLandroidx/compose/runtime/RecomposeScopeImpl;->**(**)**
HSPLandroidx/compose/runtime/Recomposer**->**(**)**
HSPLandroidx/compose/runtime/ReferentialEqualityPolicy;->**(**)**
HSPLandroidx/compose/runtime/SkippableUpdater;->**(**)**
HSPLandroidx/compose/runtime/SlotReader;->**(**)**
HSPLandroidx/compose/runtime/SlotTable;->**(**)**
HSPLandroidx/compose/runtime/SlotTableKt;->**(**)**
HSPLandroidx/compose/runtime/SlotWriter**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableFloatStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableIntStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableLongStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotMutableStateImpl**->**(**)**
HSPLandroidx/compose/runtime/SnapshotDoubleStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotIntStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotLongStateKt**->**(**)**
HSPLandroidx/compose/runtime/PrimitiveSnapshotStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotStateKt**->**(**)**
HSPLandroidx/compose/runtime/SnapshotThreadLocal;->**(**)**
HSPLandroidx/compose/runtime/StaticProvidableCompositionLocal;->**(**)**
HSPLandroidx/compose/runtime/StaticValueHolder;->**(**)**
HSPLandroidx/compose/runtime/StructuralEqualityPolicy;->**(**)**
HSPLandroidx/compose/runtime/Trace;->**(**)**
HSPLandroidx/compose/runtime/Updater**->**(**)**
HSPLandroidx/compose/runtime/changelist/**->**(**)**
HSPLandroidx/compose/runtime/internal/ComposableLambdaImpl**->**(**)**
HSPLandroidx/compose/runtime/internal/ComposableLambdaKt;->**(**)**
HSPLandroidx/compose/runtime/internal/IntRef;->**(**)**
HSPLandroidx/compose/runtime/tooling/**->**(**)**
HSPLandroidx/compose/runtime/tracing/**->**(**)**

#
# Snapshot related stuff
HSPLandroidx/compose/runtime/snapshots/MutableSnapshot;->**(**)**
HSPLandroidx/compose/runtime/snapshots/NestedMutableSnapshot;->**(**)**
HSPLandroidx/compose/runtime/snapshots/Snapshot**->**(**)**
HSPLandroidx/compose/runtime/snapshots/ListUtilsKt;->fastToSet(Ljava/util/List;)Ljava/util/Set;
HSPLandroidx/compose/runtime/snapshots/SnapshotApplyResult**->**(**)**
HSPLandroidx/compose/runtime/snapshots/SnapshotIdSet**->**(**)**
HSPLandroidx/compose/runtime/snapshots/SnapshotStateList**->**(**)**
HSPLandroidx/compose/runtime/snapshots/SnapshotStateObserver**->**(**)**
HSPLandroidx/compose/runtime/snapshots/StateRecord;->**(**)**
HSPLandroidx/compose/runtime/snapshots/TransparentObserverMutableSnapshot;->**(**)**
#
# MutableVector and other purpose-built data structures are hot paths
HSPLandroidx/compose/runtime/collection/**->**(**)**
HSPLandroidx/compose/runtime/Stack;->**(**)**
HSPLandroidx/compose/runtime/IntStack;->**(**)**
HSPLandroidx/compose/runtime/internal/PersistentCompositionLocalHashMap**->**(**)**
HSPLandroidx/compose/runtime/internal/ThreadMap;->**(**)**
HSPLandroidx/compose/runtime/PrioritySet;->**(**)**
#
# AndroidX collections
Landroidx/collection/**;
HSPLandroidx/collection/ArraySet**->**(**)**
HSPLandroidx/collection/IntSetKt;->**(**)**
HSPLandroidx/collection/LongSparseArray**->**(**)**
HSPLandroidx/collection/MutableIntIntMap;->**(**)**
HSPLandroidx/collection/MutableObjectIntMap;->**(**)**
HSPLandroidx/collection/MutableScatterMap;->**(**)**
HSPLandroidx/collection/MutableScatterSet**->**(**)**
HSPLandroidx/collection/ObjectIntMapKt;->**(**)**
HSPLandroidx/collection/ScatterMapKt;->**(**)**
HSPLandroidx/collection/ScatterSet**->**(**)**
HSPLandroidx/collection/SimpleArrayMap;->**(**)**
HSPLandroidx/collection/SparseArrayCompat;->**(**)**
HSPLandroidx/collection/internal/ContainerHelpersKt;->**(**)**
#
# kotlinx.collections.immutable copy
# ====
# We only use a subset of these methods but haven't gotten rid of all of the APIs to preserve
# source. Since this is very niche usage, this should stay pretty consistent.
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt;->persistentHashMapOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt;->persistentListOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/ExtensionsKt;->persistentSetOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/AbstractPersistentList;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector$Companion;->getEMPTY()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;-><init>([Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->add(Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->addAll(Ljava/util/Collection;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->get(I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/SmallPersistentVector;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableList/UtilsKt;->persistentVectorOf()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentList;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry;->getKey()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/MapEntry;->getValue()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap$Companion;->emptyOf$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->builder()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap$Builder;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->builder()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->containsKey(Ljava/lang/Object;)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->createEntries()Landroidx/compose/runtime/external/kotlinx/collections/immutable/ImmutableSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->get(Ljava/lang/Object;)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->getEntries()Ljava/util/Set;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->getNode$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;->put(Ljava/lang/Object;Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;[Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->checkHasNext()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->ensureNextEntryIsReady()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->hasNext()Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->moveToNextNodeWithData(I)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBaseIterator;->next()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->build()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->build()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->getModCount$runtime_release()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->getOwnership$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->put(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->putAll(Ljava/util/Map;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->setModCount$runtime_release(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->setOperationResult$runtime_release(Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;->setSize(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntries;->iterator()Ljava/util/Iterator;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapEntriesIterator;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$Companion;->getEMPTY$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;-><init>(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;->getNode()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;->getSizeDelta()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;-><init>(II[Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;-><init>(II[Ljava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->asInsertResult()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->containsKey(ILjava/lang/Object;I)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->elementsIdentityEquals(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->entryCount$runtime_release()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->entryKeyIndex$runtime_release(I)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->get(ILjava/lang/Object;I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->getBuffer$runtime_release()[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->hasEntryAt$runtime_release(I)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->hasNodeAt(I)Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->insertEntryAt(ILjava/lang/Object;Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->keyAtIndex(I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->makeNode(ILjava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;Ljava/lang/Object;ILandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutableInsertEntryAt(ILjava/lang/Object;Ljava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutablePut(ILjava/lang/Object;Ljava/lang/Object;ILandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutablePutAll(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;ILandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutablePutAllFromOtherNodeCell(Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;IILandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->mutableUpdateValueAtIndex(ILjava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMapBuilder;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->nodeAtIndex$runtime_release(I)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->nodeIndex$runtime_release(I)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->put(ILjava/lang/Object;Ljava/lang/Object;I)Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode$ModificationResult;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNode;->valueAtKeyIndex(I)Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->getBuffer()[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->getIndex()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->hasNextKey()Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->hasNextNode()Z
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->reset([Ljava/lang/Object;I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->reset([Ljava/lang/Object;II)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeBaseIterator;->setIndex(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator;->next()Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeEntriesIterator;->next()Ljava/util/Map$Entry;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt;->access$insertEntryAtIndex([Ljava/lang/Object;ILjava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt;->indexSegment(II)I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/TrieNodeKt;->insertEntryAtIndex([Ljava/lang/Object;ILjava/lang/Object;Ljava/lang/Object;)[Ljava/lang/Object;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/Links;-><init>(Ljava/lang/Object;Ljava/lang/Object;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet$Companion;->emptyOf$runtime_release()Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;-><init>(Ljava/lang/Object;Ljava/lang/Object;Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/immutableMap/PersistentHashMap;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;->access$getEMPTY$cp()Landroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;->add(Ljava/lang/Object;)Landroidx/compose/runtime/external/kotlinx/collections/immutable/PersistentSet;
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/implementations/persistentOrderedSet/PersistentOrderedSet;->getSize()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/CommonFunctionsKt;->assert(Z)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;-><init>(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;-><init>(IILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;->getCount()I
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/DeltaCounter;->setCount(I)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/EndOfChain;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation;-><init>()V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/ListImplementation;->checkElementIndex$runtime_release(II)V
HSPLandroidx/compose/runtime/external/kotlinx/collections/immutable/internal/MutabilityOwnership;-><init>()V
#
# important external / stdlib methods and classes
# Since compose heavily relies on various kotlin standard libraries, it is important that these get
# compiled as well. Since the std libraries are large and we don't use everything, we are
# conservative here and avoid wildcards and instead use profile dumps to guide us
HSPLkotlin/ULong$Companion;-><init>()V
HSPLkotlin/ULong$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/ULong;->constructor-impl(J)J
HSPLkotlin/UnsignedKt;->ulongToDouble(J)D
HSPLkotlin/collections/AbstractCollection;-><init>()V
HSPLkotlin/collections/AbstractCollection;->isEmpty()Z
HSPLkotlin/collections/AbstractCollection;->size()I
HSPLkotlin/collections/AbstractList$Companion;-><init>()V
HSPLkotlin/collections/AbstractList$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/AbstractList$IteratorImpl;-><init>(Lkotlin/collections/AbstractList;)V
HSPLkotlin/collections/AbstractList$IteratorImpl;->hasNext()Z
HSPLkotlin/collections/AbstractList$IteratorImpl;->next()Ljava/lang/Object;
HSPLkotlin/collections/AbstractList;-><init>()V
HSPLkotlin/collections/AbstractList;->iterator()Ljava/util/Iterator;
HSPLkotlin/collections/AbstractMap$Companion;-><init>()V
HSPLkotlin/collections/AbstractMap$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/AbstractMap;-><init>()V
HSPLkotlin/collections/AbstractMap;->containsEntry$kotlin_stdlib(Ljava/util/Map$Entry;)Z
HSPLkotlin/collections/AbstractMap;->entrySet()Ljava/util/Set;
HSPLkotlin/collections/AbstractMap;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/AbstractMap;->size()I
HSPLkotlin/collections/AbstractMutableList;-><init>()V
HSPLkotlin/collections/AbstractMutableList;->size()I
HSPLkotlin/collections/AbstractMutableMap;-><init>()V
HSPLkotlin/collections/AbstractMutableMap;->size()I
HSPLkotlin/collections/AbstractSet$Companion;-><init>()V
HSPLkotlin/collections/AbstractSet$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/AbstractSet$Companion;->setEquals$kotlin_stdlib(Ljava/util/Set;Ljava/util/Set;)Z
HSPLkotlin/collections/AbstractSet;-><init>()V
HSPLkotlin/collections/AbstractSet;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/ArrayAsCollection;-><init>([Ljava/lang/Object;Z)V
HSPLkotlin/collections/ArrayAsCollection;->toArray()[Ljava/lang/Object;
HSPLkotlin/collections/ArrayDeque$Companion;-><init>()V
HSPLkotlin/collections/ArrayDeque$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/collections/ArrayDeque$Companion;->newCapacity$kotlin_stdlib(II)I
HSPLkotlin/collections/ArrayDeque;-><init>()V
HSPLkotlin/collections/ArrayDeque;->access$getElementData$p(Lkotlin/collections/ArrayDeque;)[Ljava/lang/Object;
HSPLkotlin/collections/ArrayDeque;->access$getHead$p(Lkotlin/collections/ArrayDeque;)I
HSPLkotlin/collections/ArrayDeque;->access$positiveMod(Lkotlin/collections/ArrayDeque;I)I
HSPLkotlin/collections/ArrayDeque;->addLast(Ljava/lang/Object;)V
HSPLkotlin/collections/ArrayDeque;->copyElements(I)V
HSPLkotlin/collections/ArrayDeque;->ensureCapacity(I)V
HSPLkotlin/collections/ArrayDeque;->getSize()I
HSPLkotlin/collections/ArrayDeque;->incremented(I)I
HSPLkotlin/collections/ArrayDeque;->isEmpty()Z
HSPLkotlin/collections/ArrayDeque;->positiveMod(I)I
HSPLkotlin/collections/ArrayDeque;->removeFirst()Ljava/lang/Object;
HSPLkotlin/collections/ArrayDeque;->removeFirstOrNull()Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt__ArraysJVMKt;->copyOfRangeToIndexCheck(II)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;-><init>([F)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;->get(I)Ljava/lang/Float;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;->get(I)Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt$asList$5;->getSize()I
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->asList([F)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->asList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto$default([F[FIIIILjava/lang/Object;)[F
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto$default([I[IIIIILjava/lang/Object;)[I
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto$default([Ljava/lang/Object;[Ljava/lang/Object;IIIILjava/lang/Object;)[Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto([F[FIII)[F
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto([I[IIII)[I
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyInto([Ljava/lang/Object;[Ljava/lang/Object;III)[Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->copyOfRange([FII)[F
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->fill$default([IIIIILjava/lang/Object;)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->fill([IIII)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->fill([Ljava/lang/Object;Ljava/lang/Object;II)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->sort([Ljava/lang/Object;)V
HSPLkotlin/collections/ArraysKt___ArraysJvmKt;->sortWith([Ljava/lang/Object;Ljava/util/Comparator;II)V
HSPLkotlin/collections/ArraysKt___ArraysKt;->contains([CC)Z
HSPLkotlin/collections/ArraysKt___ArraysKt;->first([Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/ArraysKt___ArraysKt;->getLastIndex([Ljava/lang/Object;)I
HSPLkotlin/collections/ArraysKt___ArraysKt;->indexOf([CC)I
HSPLkotlin/collections/ArraysKt___ArraysKt;->slice([FLkotlin/ranges/IntRange;)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysKt;->toList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/ArraysKt___ArraysKt;->toMutableList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/ArraysUtilJVM;->asList([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__CollectionsJVMKt;->copyToArrayOfAny([Ljava/lang/Object;Z)[Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt__CollectionsJVMKt;->listOf(Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->arrayListOf([Ljava/lang/Object;)Ljava/util/ArrayList;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->asCollection([Ljava/lang/Object;)Ljava/util/Collection;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->emptyList()Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->getLastIndex(Ljava/util/List;)I
HSPLkotlin/collections/CollectionsKt__CollectionsKt;->listOf([Ljava/lang/Object;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt__IterablesKt;->collectionSizeOrDefault(Ljava/lang/Iterable;I)I
HSPLkotlin/collections/CollectionsKt__MutableCollectionsJVMKt;->sortWith(Ljava/util/List;Ljava/util/Comparator;)V
HSPLkotlin/collections/CollectionsKt__MutableCollectionsKt;->addAll(Ljava/util/Collection;Ljava/lang/Iterable;)Z
HSPLkotlin/collections/CollectionsKt__MutableCollectionsKt;->removeFirstOrNull(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->distinct(Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->filterNotNull(Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->filterNotNullTo(Ljava/lang/Iterable;Ljava/util/Collection;)Ljava/util/Collection;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->first(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->firstOrNull(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->getOrNull(Ljava/util/List;I)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->last(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->lastOrNull(Ljava/util/List;)Ljava/lang/Object;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->maxOrNull(Ljava/lang/Iterable;)Ljava/lang/Float;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->minOrNull(Ljava/lang/Iterable;)Ljava/lang/Float;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->plus(Ljava/util/Collection;Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toFloatArray(Ljava/util/Collection;)[F
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toIntArray(Ljava/util/Collection;)[I
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toList(Ljava/lang/Iterable;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toMutableList(Ljava/util/Collection;)Ljava/util/List;
HSPLkotlin/collections/CollectionsKt___CollectionsKt;->toMutableSet(Ljava/lang/Iterable;)Ljava/util/Set;
HSPLkotlin/collections/EmptyList;-><init>()V
HSPLkotlin/collections/EmptyList;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/EmptyList;->getSize()I
HSPLkotlin/collections/EmptyList;->isEmpty()Z
HSPLkotlin/collections/EmptyList;->size()I
HSPLkotlin/collections/EmptyList;->toArray()[Ljava/lang/Object;
HSPLkotlin/collections/EmptyMap;-><init>()V
HSPLkotlin/collections/EmptyMap;->containsKey(Ljava/lang/Object;)Z
HSPLkotlin/collections/EmptyMap;->equals(Ljava/lang/Object;)Z
HSPLkotlin/collections/EmptyMap;->get(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/EmptyMap;->get(Ljava/lang/Object;)Ljava/lang/Void;
HSPLkotlin/collections/EmptyMap;->isEmpty()Z
HSPLkotlin/collections/IntIterator;-><init>()V
HSPLkotlin/collections/MapsKt__MapWithDefaultKt;->getOrImplicitDefaultNullable(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/MapsKt__MapsJVMKt;->mapCapacity(I)I
HSPLkotlin/collections/MapsKt__MapsKt;->emptyMap()Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->getValue(Ljava/util/Map;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlin/collections/MapsKt__MapsKt;->mapOf([Lkotlin/Pair;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->putAll(Ljava/util/Map;Ljava/lang/Iterable;)V
HSPLkotlin/collections/MapsKt__MapsKt;->putAll(Ljava/util/Map;[Lkotlin/Pair;)V
HSPLkotlin/collections/MapsKt__MapsKt;->toMap(Ljava/lang/Iterable;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->toMap(Ljava/lang/Iterable;Ljava/util/Map;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->toMap([Lkotlin/Pair;Ljava/util/Map;)Ljava/util/Map;
HSPLkotlin/collections/MapsKt__MapsKt;->toMutableMap(Ljava/util/Map;)Ljava/util/Map;
HSPLkotlin/comparisons/ComparisonsKt__ComparisonsKt;->compareValues(Ljava/lang/Comparable;Ljava/lang/Comparable;)I
HSPLkotlin/jvm/internal/CollectionToArray;->toArray(Ljava/util/Collection;)[Ljava/lang/Object;
HSPLkotlin/jvm/internal/FloatCompanionObject;-><init>()V
HSPLkotlin/jvm/internal/FunctionReference;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
HSPLkotlin/jvm/internal/FunctionReference;->equals(Ljava/lang/Object;)Z
HSPLkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
HSPLkotlin/jvm/internal/FunctionReferenceImpl;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V
HSPLkotlin/jvm/internal/InlineMarker;->mark(I)V
HSPLkotlin/jvm/internal/IntCompanionObject;-><init>()V
HSPLkotlin/jvm/internal/Intrinsics;->areEqual(Ljava/lang/Object;Ljava/lang/Object;)Z
HSPLkotlin/jvm/internal/Intrinsics;->checkExpressionValueIsNotNull(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkNotNull(Ljava/lang/Object;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkNotNullExpressionValue(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkNotNullParameter(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->checkParameterIsNotNull(Ljava/lang/Object;Ljava/lang/String;)V
HSPLkotlin/jvm/internal/Intrinsics;->compare(II)I
HSPLkotlin/jvm/internal/Lambda;-><init>(I)V
HSPLkotlin/jvm/internal/Lambda;->getArity()I
HSPLkotlin/math/MathKt__MathJVMKt;->getSign(I)I
HSPLkotlin/math/MathKt__MathJVMKt;->roundToInt(F)I
HSPLkotlin/ranges/IntRange$Companion;-><init>()V
HSPLkotlin/ranges/IntRange$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlin/ranges/IntRange;-><init>(II)V
HSPLkotlin/ranges/IntRange;->getEndInclusive()Ljava/lang/Integer;
HSPLkotlin/ranges/IntRange;->getStart()Ljava/lang/Integer;
HSPLkotlin/ranges/IntRange;->isEmpty()Z
HSPLkotlin/ranges/RangesKt__RangesKt;->checkStepIsPositive(ZLjava/lang/Number;)V
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtLeast(II)I
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtLeast(JJ)J
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtLeast(Ljava/lang/Comparable;Ljava/lang/Comparable;)Ljava/lang/Comparable;
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtMost(II)I
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceAtMost(JJ)J
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceIn(DDD)D
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceIn(FFF)F
HSPLkotlin/ranges/RangesKt___RangesKt;->coerceIn(III)I
HSPLkotlin/ranges/RangesKt___RangesKt;->step(Lkotlin/ranges/IntProgression;I)Lkotlin/ranges/IntProgression;
HSPLkotlin/ranges/RangesKt___RangesKt;->until(II)Lkotlin/ranges/IntRange;
HSPLkotlinx/coroutines/AbstractCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Z)V
HSPLkotlinx/coroutines/AbstractCoroutine;->afterResume(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->cancellationExceptionMessage()Ljava/lang/String;
HSPLkotlinx/coroutines/AbstractCoroutine;->getContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/AbstractCoroutine;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/AbstractCoroutine;->initParentJob$kotlinx_coroutines_core()V
HSPLkotlinx/coroutines/AbstractCoroutine;->isActive()Z
HSPLkotlinx/coroutines/AbstractCoroutine;->onCancelled(Ljava/lang/Throwable;Z)V
HSPLkotlinx/coroutines/AbstractCoroutine;->onCompleted(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->onCompletionInternal(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->resumeWith(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/AbstractCoroutine;->start(Lkotlinx/coroutines/CoroutineStart;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)V
HSPLkotlinx/coroutines/Active;-><init>()V
HSPLkotlinx/coroutines/BeforeResumeCancelHandler;-><init>()V
HSPLkotlinx/coroutines/BlockingEventLoop;-><init>(Ljava/lang/Thread;)V
HSPLkotlinx/coroutines/BuildersKt;->launch$default(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt;->launch(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt;->withContext(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/BuildersKt__Builders_commonKt;->launch$default(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;ILjava/lang/Object;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt__Builders_commonKt;->launch(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;Lkotlinx/coroutines/CoroutineStart;Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/BuildersKt__Builders_commonKt;->withContext(Lkotlin/coroutines/CoroutineContext;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancelHandler;-><init>()V
HSPLkotlinx/coroutines/CancelHandlerBase;-><init>()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;-><init>(Lkotlin/coroutines/Continuation;I)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->callCancelHandler(Lkotlinx/coroutines/CancelHandler;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->cancel(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->cancelCompletedResult$kotlinx_coroutines_core(Ljava/lang/Object;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->cancelLater(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->checkCompleted()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->completeResume(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->detachChild$kotlinx_coroutines_core()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->detachChildIfNonResuable()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->dispatchResume(I)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getContinuationCancellationCause(Lkotlinx/coroutines/Job;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getDelegate$kotlinx_coroutines_core()Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getExceptionalResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getParentHandle()Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getResult()Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->getSuccessfulResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->initCancellability()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->invokeOnCancellation(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->isCompleted()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->isReusable()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->makeCancelHandler(Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/CancelHandler;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->parentCancelled$kotlinx_coroutines_core(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resetStateReusable()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeImpl$default(Lkotlinx/coroutines/CancellableContinuationImpl;Ljava/lang/Object;ILkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeImpl(Ljava/lang/Object;ILkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeUndispatched(Lkotlinx/coroutines/CoroutineDispatcher;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumeWith(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->resumedState(Lkotlinx/coroutines/NotCompleted;Ljava/lang/Object;ILkotlin/jvm/functions/Function1;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->setParentHandle(Lkotlinx/coroutines/DisposableHandle;)V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->setupCancellation()V
HSPLkotlinx/coroutines/CancellableContinuationImpl;->takeState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->tryResume()Z
HSPLkotlinx/coroutines/CancellableContinuationImpl;->tryResume(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->tryResumeImpl(Ljava/lang/Object;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/CancellableContinuationImpl;->trySuspend()Z
HSPLkotlinx/coroutines/CancellableContinuationKt;->disposeOnCancellation(Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/DisposableHandle;)V
HSPLkotlinx/coroutines/CancellableContinuationKt;->getOrCreateCancellableContinuation(Lkotlin/coroutines/Continuation;)Lkotlinx/coroutines/CancellableContinuationImpl;
HSPLkotlinx/coroutines/CancellableContinuationKt;->removeOnCancellation(Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/CancelledContinuation;-><init>(Lkotlin/coroutines/Continuation;Ljava/lang/Throwable;Z)V
HSPLkotlinx/coroutines/CancelledContinuation;->makeResumed()Z
HSPLkotlinx/coroutines/ChildContinuation;-><init>(Lkotlinx/coroutines/CancellableContinuationImpl;)V
HSPLkotlinx/coroutines/ChildContinuation;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/ChildHandleNode;-><init>(Lkotlinx/coroutines/ChildJob;)V
HSPLkotlinx/coroutines/ChildHandleNode;->childCancelled(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/ChildHandleNode;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CompletedContinuation;-><init>(Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CompletedContinuation;-><init>(Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/CompletedContinuation;->copy$default(Lkotlinx/coroutines/CompletedContinuation;Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;ILjava/lang/Object;)Lkotlinx/coroutines/CompletedContinuation;
HSPLkotlinx/coroutines/CompletedContinuation;->copy(Ljava/lang/Object;Lkotlinx/coroutines/CancelHandler;Lkotlin/jvm/functions/Function1;Ljava/lang/Object;Ljava/lang/Throwable;)Lkotlinx/coroutines/CompletedContinuation;
HSPLkotlinx/coroutines/CompletedContinuation;->getCancelled()Z
HSPLkotlinx/coroutines/CompletedContinuation;->invokeHandlers(Lkotlinx/coroutines/CancellableContinuationImpl;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/CompletedExceptionally;-><init>(Ljava/lang/Throwable;Z)V
HSPLkotlinx/coroutines/CompletedExceptionally;-><init>(Ljava/lang/Throwable;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/CompletedExceptionally;->getHandled()Z
HSPLkotlinx/coroutines/CompletedExceptionally;->makeHandled()Z
HSPLkotlinx/coroutines/CompletionHandlerBase;-><init>()V
HSPLkotlinx/coroutines/CompletionStateKt;->recoverResult(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CompletionStateKt;->toState$default(Ljava/lang/Object;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CompletionStateKt;->toState(Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CompletionStateKt;->toState(Ljava/lang/Object;Lkotlinx/coroutines/CancellableContinuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CoroutineContextKt;->createDefaultDispatcher()Lkotlinx/coroutines/CoroutineDispatcher;
HSPLkotlinx/coroutines/CoroutineContextKt;->newCoroutineContext(Lkotlinx/coroutines/CoroutineScope;Lkotlin/coroutines/CoroutineContext;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/CoroutineDispatcher$Key$1;-><init>()V
HSPLkotlinx/coroutines/CoroutineDispatcher$Key;-><init>()V
HSPLkotlinx/coroutines/CoroutineDispatcher$Key;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/CoroutineDispatcher;-><init>()V
HSPLkotlinx/coroutines/CoroutineDispatcher;->get(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/CoroutineDispatcher;->interceptContinuation(Lkotlin/coroutines/Continuation;)Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/CoroutineDispatcher;->isDispatchNeeded(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/CoroutineDispatcher;->minusKey(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/CoroutineDispatcher;->releaseInterceptedContinuation(Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/CoroutineExceptionHandler$Key;-><init>()V
HSPLkotlinx/coroutines/CoroutineScopeKt;->CoroutineScope(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/CoroutineScope;
HSPLkotlinx/coroutines/CoroutineScopeKt;->cancel$default(Lkotlinx/coroutines/CoroutineScope;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/CoroutineScopeKt;->cancel(Lkotlinx/coroutines/CoroutineScope;Ljava/util/concurrent/CancellationException;)V
HSPLkotlinx/coroutines/CoroutineScopeKt;->coroutineScope(Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/CoroutineScopeKt;->isActive(Lkotlinx/coroutines/CoroutineScope;)Z
HSPLkotlinx/coroutines/CoroutineStart;-><init>(Ljava/lang/String;I)V
HSPLkotlinx/coroutines/CoroutineStart;->invoke(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/CoroutineStart;->isLazy()Z
HSPLkotlinx/coroutines/CoroutineStart;->values()[Lkotlinx/coroutines/CoroutineStart;
HSPLkotlinx/coroutines/DebugKt;->getASSERTIONS_ENABLED()Z
HSPLkotlinx/coroutines/DebugKt;->getDEBUG()Z
HSPLkotlinx/coroutines/DebugKt;->getRECOVER_STACK_TRACES()Z
HSPLkotlinx/coroutines/DebugStringsKt;->getClassSimpleName(Ljava/lang/Object;)Ljava/lang/String;
HSPLkotlinx/coroutines/DefaultExecutor;-><init>()V
HSPLkotlinx/coroutines/DefaultExecutor;->createThreadSync()Ljava/lang/Thread;
HSPLkotlinx/coroutines/DefaultExecutor;->getThread()Ljava/lang/Thread;
HSPLkotlinx/coroutines/DefaultExecutor;->isShutdownRequested()Z
HSPLkotlinx/coroutines/DefaultExecutor;->notifyStartup()Z
HSPLkotlinx/coroutines/DefaultExecutor;->run()V
HSPLkotlinx/coroutines/DefaultExecutorKt;->getDefaultDelay()Lkotlinx/coroutines/Delay;
HSPLkotlinx/coroutines/DelayKt;->delay(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/DelayKt;->getDelay(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/Delay;
HSPLkotlinx/coroutines/DispatchedTask;-><init>(I)V
HSPLkotlinx/coroutines/DispatchedTask;->getExceptionalResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/DispatchedTask;->getSuccessfulResult$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/DispatchedTask;->handleFatalException(Ljava/lang/Throwable;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/DispatchedTask;->run()V
HSPLkotlinx/coroutines/DispatchedTaskKt;->dispatch(Lkotlinx/coroutines/DispatchedTask;I)V
HSPLkotlinx/coroutines/DispatchedTaskKt;->isCancellableMode(I)Z
HSPLkotlinx/coroutines/DispatchedTaskKt;->isReusableMode(I)Z
HSPLkotlinx/coroutines/DispatchedTaskKt;->resume(Lkotlinx/coroutines/DispatchedTask;Lkotlin/coroutines/Continuation;Z)V
HSPLkotlinx/coroutines/DispatchedTaskKt;->resumeUnconfined(Lkotlinx/coroutines/DispatchedTask;)V
HSPLkotlinx/coroutines/Dispatchers;-><init>()V
HSPLkotlinx/coroutines/Dispatchers;->getDefault()Lkotlinx/coroutines/CoroutineDispatcher;
HSPLkotlinx/coroutines/DisposeOnCancel;-><init>(Lkotlinx/coroutines/DisposableHandle;)V
HSPLkotlinx/coroutines/Empty;-><init>(Z)V
HSPLkotlinx/coroutines/Empty;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/Empty;->isActive()Z
HSPLkotlinx/coroutines/EventLoop;-><init>()V
HSPLkotlinx/coroutines/EventLoop;->decrementUseCount(Z)V
HSPLkotlinx/coroutines/EventLoop;->delta(Z)J
HSPLkotlinx/coroutines/EventLoop;->getNextTime()J
HSPLkotlinx/coroutines/EventLoop;->incrementUseCount$default(Lkotlinx/coroutines/EventLoop;ZILjava/lang/Object;)V
HSPLkotlinx/coroutines/EventLoop;->incrementUseCount(Z)V
HSPLkotlinx/coroutines/EventLoop;->isUnconfinedLoopActive()Z
HSPLkotlinx/coroutines/EventLoop;->processUnconfinedEvent()Z
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedResumeTask;-><init>(Lkotlinx/coroutines/EventLoopImplBase;JLkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedResumeTask;->run()V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;-><init>(J)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->scheduleTask(JLkotlinx/coroutines/EventLoopImplBase$DelayedTaskQueue;Lkotlinx/coroutines/EventLoopImplBase;)I
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->setHeap(Lkotlinx/coroutines/internal/ThreadSafeHeap;)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->setIndex(I)V
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTask;->timeToExecute(J)Z
HSPLkotlinx/coroutines/EventLoopImplBase$DelayedTaskQueue;-><init>(J)V
HSPLkotlinx/coroutines/EventLoopImplBase;-><init>()V
HSPLkotlinx/coroutines/EventLoopImplBase;->access$isCompleted$p(Lkotlinx/coroutines/EventLoopImplBase;)Z
HSPLkotlinx/coroutines/EventLoopImplBase;->dequeue()Ljava/lang/Runnable;
HSPLkotlinx/coroutines/EventLoopImplBase;->enqueueImpl(Ljava/lang/Runnable;)Z
HSPLkotlinx/coroutines/EventLoopImplBase;->getNextTime()J
HSPLkotlinx/coroutines/EventLoopImplBase;->isCompleted()Z
HSPLkotlinx/coroutines/EventLoopImplBase;->processNextEvent()J
HSPLkotlinx/coroutines/EventLoopImplBase;->schedule(JLkotlinx/coroutines/EventLoopImplBase$DelayedTask;)V
HSPLkotlinx/coroutines/EventLoopImplBase;->scheduleImpl(JLkotlinx/coroutines/EventLoopImplBase$DelayedTask;)I
HSPLkotlinx/coroutines/EventLoopImplBase;->scheduleResumeAfterDelay(JLkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/EventLoopImplBase;->shouldUnpark(Lkotlinx/coroutines/EventLoopImplBase$DelayedTask;)Z
HSPLkotlinx/coroutines/EventLoopImplPlatform;-><init>()V
HSPLkotlinx/coroutines/EventLoopImplPlatform;->unpark()V
HSPLkotlinx/coroutines/EventLoopKt;->createEventLoop()Lkotlinx/coroutines/EventLoop;
HSPLkotlinx/coroutines/EventLoop_commonKt;->access$getCLOSED_EMPTY$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/EventLoop_commonKt;->access$getDISPOSED_TASK$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/EventLoop_commonKt;->delayToNanos(J)J
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher$Key$1;-><init>()V
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher$Key;-><init>()V
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher$Key;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/ExecutorCoroutineDispatcher;-><init>()V
HSPLkotlinx/coroutines/GlobalScope;-><init>()V
HSPLkotlinx/coroutines/GlobalScope;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/InvokeOnCancel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/InvokeOnCancel;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/InvokeOnCompletion;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/Job$DefaultImpls;->cancel$default(Lkotlinx/coroutines/Job;Ljava/util/concurrent/CancellationException;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/Job$DefaultImpls;->fold(Lkotlinx/coroutines/Job;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/Job$DefaultImpls;->get(Lkotlinx/coroutines/Job;Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/Job$DefaultImpls;->invokeOnCompletion$default(Lkotlinx/coroutines/Job;ZZLkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/Job$DefaultImpls;->minusKey(Lkotlinx/coroutines/Job;Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/Job$Key;-><init>()V
HSPLkotlinx/coroutines/JobCancellationException;-><init>(Ljava/lang/String;Ljava/lang/Throwable;Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobCancellationException;->equals(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/JobCancellationException;->fillInStackTrace()Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobCancellingNode;-><init>()V
HSPLkotlinx/coroutines/JobImpl;-><init>(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobImpl;->getHandlesException$kotlinx_coroutines_core()Z
HSPLkotlinx/coroutines/JobImpl;->getOnCancelComplete$kotlinx_coroutines_core()Z
HSPLkotlinx/coroutines/JobImpl;->handlesException()Z
HSPLkotlinx/coroutines/JobKt;->Job$default(Lkotlinx/coroutines/Job;ILjava/lang/Object;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt;->Job(Lkotlinx/coroutines/Job;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt;->ensureActive(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobKt;->getJob(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/JobKt;->isActive(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/JobKt__JobKt;->Job$default(Lkotlinx/coroutines/Job;ILjava/lang/Object;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt__JobKt;->Job(Lkotlinx/coroutines/Job;)Lkotlinx/coroutines/CompletableJob;
HSPLkotlinx/coroutines/JobKt__JobKt;->ensureActive(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobKt__JobKt;->getJob(Lkotlin/coroutines/CoroutineContext;)Lkotlinx/coroutines/Job;
HSPLkotlinx/coroutines/JobKt__JobKt;->isActive(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/JobNode;-><init>()V
HSPLkotlinx/coroutines/JobNode;->dispose()V
HSPLkotlinx/coroutines/JobNode;->getJob()Lkotlinx/coroutines/JobSupport;
HSPLkotlinx/coroutines/JobNode;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/JobNode;->isActive()Z
HSPLkotlinx/coroutines/JobNode;->setJob(Lkotlinx/coroutines/JobSupport;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;-><init>(Lkotlinx/coroutines/NodeList;ZLjava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->addExceptionLocked(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->allocateList()Ljava/util/ArrayList;
HSPLkotlinx/coroutines/JobSupport$Finishing;->getExceptionsHolder()Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport$Finishing;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/JobSupport$Finishing;->getRootCause()Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobSupport$Finishing;->isActive()Z
HSPLkotlinx/coroutines/JobSupport$Finishing;->isCancelling()Z
HSPLkotlinx/coroutines/JobSupport$Finishing;->isCompleting()Z
HSPLkotlinx/coroutines/JobSupport$Finishing;->sealLocked(Ljava/lang/Throwable;)Ljava/util/List;
HSPLkotlinx/coroutines/JobSupport$Finishing;->setCompleting(Z)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->setExceptionsHolder(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport$Finishing;->setRootCause(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport$addLastAtomic$$inlined$addLastIf$1;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/JobSupport;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport$addLastAtomic$$inlined$addLastIf$1;->prepare(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport$addLastAtomic$$inlined$addLastIf$1;->prepare(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;-><init>(Z)V
HSPLkotlinx/coroutines/JobSupport;->access$cancellationExceptionMessage(Lkotlinx/coroutines/JobSupport;)Ljava/lang/String;
HSPLkotlinx/coroutines/JobSupport;->addLastAtomic(Ljava/lang/Object;Lkotlinx/coroutines/NodeList;Lkotlinx/coroutines/JobNode;)Z
HSPLkotlinx/coroutines/JobSupport;->addSuppressedExceptions(Ljava/lang/Throwable;Ljava/util/List;)V
HSPLkotlinx/coroutines/JobSupport;->afterCompletion(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport;->attachChild(Lkotlinx/coroutines/ChildJob;)Lkotlinx/coroutines/ChildHandle;
HSPLkotlinx/coroutines/JobSupport;->cancel(Ljava/util/concurrent/CancellationException;)V
HSPLkotlinx/coroutines/JobSupport;->cancelImpl$kotlinx_coroutines_core(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/JobSupport;->cancelInternal(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->cancelMakeCompleting(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->cancelParent(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/JobSupport;->cancellationExceptionMessage()Ljava/lang/String;
HSPLkotlinx/coroutines/JobSupport;->childCancelled(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/JobSupport;->completeStateFinalization(Lkotlinx/coroutines/Incomplete;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport;->createCauseException(Ljava/lang/Object;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobSupport;->finalizeFinishingState(Lkotlinx/coroutines/JobSupport$Finishing;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->firstChild(Lkotlinx/coroutines/Incomplete;)Lkotlinx/coroutines/ChildHandleNode;
HSPLkotlinx/coroutines/JobSupport;->fold(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->get(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/JobSupport;->getCancellationException()Ljava/util/concurrent/CancellationException;
HSPLkotlinx/coroutines/JobSupport;->getChildJobCancellationCause()Ljava/util/concurrent/CancellationException;
HSPLkotlinx/coroutines/JobSupport;->getFinalRootCause(Lkotlinx/coroutines/JobSupport$Finishing;Ljava/util/List;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/JobSupport;->getKey()Lkotlin/coroutines/CoroutineContext$Key;
HSPLkotlinx/coroutines/JobSupport;->getOnCancelComplete$kotlinx_coroutines_core()Z
HSPLkotlinx/coroutines/JobSupport;->getOrPromoteCancellingList(Lkotlinx/coroutines/Incomplete;)Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/JobSupport;->getParentHandle$kotlinx_coroutines_core()Lkotlinx/coroutines/ChildHandle;
HSPLkotlinx/coroutines/JobSupport;->getState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->initParentJobInternal$kotlinx_coroutines_core(Lkotlinx/coroutines/Job;)V
HSPLkotlinx/coroutines/JobSupport;->invokeOnCompletion(Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/JobSupport;->invokeOnCompletion(ZZLkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/DisposableHandle;
HSPLkotlinx/coroutines/JobSupport;->isActive()Z
HSPLkotlinx/coroutines/JobSupport;->isCompleted()Z
HSPLkotlinx/coroutines/JobSupport;->isScopedCoroutine()Z
HSPLkotlinx/coroutines/JobSupport;->makeCancelling(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->makeCompletingOnce$kotlinx_coroutines_core(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->makeNode(Lkotlin/jvm/functions/Function1;Z)Lkotlinx/coroutines/JobNode;
HSPLkotlinx/coroutines/JobSupport;->minusKey(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/JobSupport;->nextChild(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Lkotlinx/coroutines/ChildHandleNode;
HSPLkotlinx/coroutines/JobSupport;->notifyCancelling(Lkotlinx/coroutines/NodeList;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->notifyCompletion(Lkotlinx/coroutines/NodeList;Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->onCancelling(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/JobSupport;->onCompletionInternal(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/JobSupport;->parentCancelled(Lkotlinx/coroutines/ParentJob;)V
HSPLkotlinx/coroutines/JobSupport;->promoteSingleToNodeList(Lkotlinx/coroutines/JobNode;)V
HSPLkotlinx/coroutines/JobSupport;->removeNode$kotlinx_coroutines_core(Lkotlinx/coroutines/JobNode;)V
HSPLkotlinx/coroutines/JobSupport;->setParentHandle$kotlinx_coroutines_core(Lkotlinx/coroutines/ChildHandle;)V
HSPLkotlinx/coroutines/JobSupport;->start()Z
HSPLkotlinx/coroutines/JobSupport;->startInternal(Ljava/lang/Object;)I
HSPLkotlinx/coroutines/JobSupport;->toCancellationException(Ljava/lang/Throwable;Ljava/lang/String;)Ljava/util/concurrent/CancellationException;
HSPLkotlinx/coroutines/JobSupport;->tryFinalizeSimpleState(Lkotlinx/coroutines/Incomplete;Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/JobSupport;->tryMakeCancelling(Lkotlinx/coroutines/Incomplete;Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/JobSupport;->tryMakeCompleting(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupport;->tryMakeCompletingSlowPath(Lkotlinx/coroutines/Incomplete;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupportKt;->access$getCOMPLETING_ALREADY$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->access$getCOMPLETING_RETRY$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->access$getEMPTY_ACTIVE$p()Lkotlinx/coroutines/Empty;
HSPLkotlinx/coroutines/JobSupportKt;->access$getSEALED$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->access$getTOO_LATE_TO_CANCEL$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/JobSupportKt;->boxIncomplete(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/JobSupportKt;->unboxState(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/MainCoroutineDispatcher;-><init>()V
HSPLkotlinx/coroutines/NodeList;-><init>()V
HSPLkotlinx/coroutines/NodeList;->getList()Lkotlinx/coroutines/NodeList;
HSPLkotlinx/coroutines/NodeList;->isActive()Z
HSPLkotlinx/coroutines/NonDisposableHandle;-><init>()V
HSPLkotlinx/coroutines/NonDisposableHandle;->dispose()V
HSPLkotlinx/coroutines/RemoveOnCancel;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/StandaloneCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Z)V
HSPLkotlinx/coroutines/ThreadLocalEventLoop;-><init>()V
HSPLkotlinx/coroutines/ThreadLocalEventLoop;->getEventLoop$kotlinx_coroutines_core()Lkotlinx/coroutines/EventLoop;
HSPLkotlinx/coroutines/ThreadLocalEventLoop;->setEventLoop$kotlinx_coroutines_core(Lkotlinx/coroutines/EventLoop;)V
HSPLkotlinx/coroutines/TimeSourceKt;->getTimeSource()Lkotlinx/coroutines/TimeSource;
HSPLkotlinx/coroutines/Unconfined;-><init>()V
HSPLkotlinx/coroutines/UndispatchedCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/UndispatchedMarker;-><init>()V
HSPLkotlinx/coroutines/UndispatchedMarker;->fold(Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/UndispatchedMarker;->get(Lkotlin/coroutines/CoroutineContext$Key;)Lkotlin/coroutines/CoroutineContext$Element;
HSPLkotlinx/coroutines/UndispatchedMarker;->getKey()Lkotlin/coroutines/CoroutineContext$Key;
HSPLkotlinx/coroutines/YieldKt;->checkCompletion(Lkotlin/coroutines/CoroutineContext;)V
HSPLkotlinx/coroutines/android/HandlerContext;-><init>(Landroid/os/Handler;Ljava/lang/String;)V
HSPLkotlinx/coroutines/android/HandlerContext;-><init>(Landroid/os/Handler;Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/android/HandlerContext;-><init>(Landroid/os/Handler;Ljava/lang/String;Z)V
HSPLkotlinx/coroutines/android/HandlerContext;->getImmediate()Lkotlinx/coroutines/android/HandlerContext;
HSPLkotlinx/coroutines/android/HandlerContext;->getImmediate()Lkotlinx/coroutines/android/HandlerDispatcher;
HSPLkotlinx/coroutines/android/HandlerContext;->isDispatchNeeded(Lkotlin/coroutines/CoroutineContext;)Z
HSPLkotlinx/coroutines/android/HandlerDispatcher;-><init>()V
HSPLkotlinx/coroutines/android/HandlerDispatcher;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/android/HandlerDispatcherKt;->asHandler(Landroid/os/Looper;Z)Landroid/os/Handler;
HSPLkotlinx/coroutines/android/HandlerDispatcherKt;->from(Landroid/os/Handler;Ljava/lang/String;)Lkotlinx/coroutines/android/HandlerDispatcher;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;-><init>(Lkotlinx/coroutines/channels/AbstractChannel;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->hasNext(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->hasNextResult(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->hasNextSuspend(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->next()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$Itr;->setResult(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;-><init>(Lkotlinx/coroutines/CancellableContinuation;I)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;->completeResumeReceive(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;->resumeValue(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveElement;->tryResumeReceive(Ljava/lang/Object;Lkotlinx/coroutines/internal/LockFreeLinkedListNode$PrepareOp;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;-><init>(Lkotlinx/coroutines/channels/AbstractChannel$Itr;Lkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;->completeResumeReceive(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;->resumeOnCancellationFun(Ljava/lang/Object;)Lkotlin/jvm/functions/Function1;
HSPLkotlinx/coroutines/channels/AbstractChannel$ReceiveHasNext;->tryResumeReceive(Ljava/lang/Object;Lkotlinx/coroutines/internal/LockFreeLinkedListNode$PrepareOp;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/AbstractChannel$RemoveReceiveOnCancel;-><init>(Lkotlinx/coroutines/channels/AbstractChannel;Lkotlinx/coroutines/channels/Receive;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$RemoveReceiveOnCancel;->invoke(Ljava/lang/Throwable;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$enqueueReceiveInternal$$inlined$addLastIfPrevAndIf$1;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/channels/AbstractChannel;)V
HSPLkotlinx/coroutines/channels/AbstractChannel$enqueueReceiveInternal$$inlined$addLastIfPrevAndIf$1;->prepare(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel$enqueueReceiveInternal$$inlined$addLastIfPrevAndIf$1;->prepare(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/AbstractChannel;->access$enqueueReceive(Lkotlinx/coroutines/channels/AbstractChannel;Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel;->access$removeReceiveOnCancel(Lkotlinx/coroutines/channels/AbstractChannel;Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/channels/Receive;)V
HSPLkotlinx/coroutines/channels/AbstractChannel;->enqueueReceive(Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel;->enqueueReceiveInternal(Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/AbstractChannel;->iterator()Lkotlinx/coroutines/channels/ChannelIterator;
HSPLkotlinx/coroutines/channels/AbstractChannel;->onReceiveDequeued()V
HSPLkotlinx/coroutines/channels/AbstractChannel;->onReceiveEnqueued()V
HSPLkotlinx/coroutines/channels/AbstractChannel;->pollInternal()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;->receive(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;->receiveSuspend(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractChannel;->removeReceiveOnCancel(Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/channels/Receive;)V
HSPLkotlinx/coroutines/channels/AbstractChannel;->takeFirstReceiveOrPeekClosed()Lkotlinx/coroutines/channels/ReceiveOrClosed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;->completeResumeSend()V
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;->getPollResult()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractSendChannel$SendBuffered;->tryResumeSend(Lkotlinx/coroutines/internal/LockFreeLinkedListNode$PrepareOp;)Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->getClosedForSend()Lkotlinx/coroutines/channels/Closed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->getQueue()Lkotlinx/coroutines/internal/LockFreeLinkedListHead;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->offer(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->offerInternal(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->sendBuffered(Ljava/lang/Object;)Lkotlinx/coroutines/channels/ReceiveOrClosed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->takeFirstReceiveOrPeekClosed()Lkotlinx/coroutines/channels/ReceiveOrClosed;
HSPLkotlinx/coroutines/channels/AbstractSendChannel;->takeFirstSendOrPeekClosed()Lkotlinx/coroutines/channels/Send;
HSPLkotlinx/coroutines/channels/BufferOverflow;-><init>(Ljava/lang/String;I)V
HSPLkotlinx/coroutines/channels/ChannelKt;->Channel$default(ILkotlinx/coroutines/channels/BufferOverflow;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lkotlinx/coroutines/channels/Channel;
HSPLkotlinx/coroutines/channels/ChannelKt;->Channel(ILkotlinx/coroutines/channels/BufferOverflow;Lkotlin/jvm/functions/Function1;)Lkotlinx/coroutines/channels/Channel;
HSPLkotlinx/coroutines/channels/ConflatedChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/ConflatedChannel;->enqueueReceiveInternal(Lkotlinx/coroutines/channels/Receive;)Z
HSPLkotlinx/coroutines/channels/ConflatedChannel;->isBufferAlwaysEmpty()Z
HSPLkotlinx/coroutines/channels/ConflatedChannel;->isBufferEmpty()Z
HSPLkotlinx/coroutines/channels/ConflatedChannel;->offerInternal(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/ConflatedChannel;->pollInternal()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/ConflatedChannel;->updateValueLocked(Ljava/lang/Object;)Lkotlinx/coroutines/internal/UndeliveredElementException;
HSPLkotlinx/coroutines/channels/LinkedListChannel;-><init>(Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/channels/LinkedListChannel;->isBufferAlwaysEmpty()Z
HSPLkotlinx/coroutines/channels/LinkedListChannel;->offerInternal(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/Receive;-><init>()V
HSPLkotlinx/coroutines/channels/Receive;->getOfferResult()Ljava/lang/Object;
HSPLkotlinx/coroutines/channels/Receive;->getOfferResult()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/channels/Receive;->resumeOnCancellationFun(Ljava/lang/Object;)Lkotlin/jvm/functions/Function1;
HSPLkotlinx/coroutines/channels/Send;-><init>()V
HSPLkotlinx/coroutines/flow/AbstractFlow;-><init>()V
HSPLkotlinx/coroutines/flow/FlowKt;->first(Lkotlinx/coroutines/flow/Flow;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/FlowKt;->flow(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt;->take(Lkotlinx/coroutines/flow/Flow;I)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt__BuildersKt;->flow(Lkotlin/jvm/functions/Function2;)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt__LimitKt$take$$inlined$unsafeFlow$1;-><init>(Lkotlinx/coroutines/flow/Flow;I)V
HSPLkotlinx/coroutines/flow/FlowKt__LimitKt;->take(Lkotlinx/coroutines/flow/Flow;I)Lkotlinx/coroutines/flow/Flow;
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2$1;-><init>(Lkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2;-><init>(Lkotlin/jvm/functions/Function2;Lkotlin/jvm/internal/Ref$ObjectRef;)V
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$$inlined$collectWhile$2;->emit(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt$first$3;-><init>(Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/FlowKt__ReduceKt;->first(Lkotlinx/coroutines/flow/Flow;Lkotlin/jvm/functions/Function2;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SafeFlow;-><init>(Lkotlin/jvm/functions/Function2;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl$collect$1;-><init>(Lkotlinx/coroutines/flow/SharedFlowImpl;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl$collect$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;-><init>(IILkotlinx/coroutines/channels/BufferOverflow;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->access$tryPeekLocked(Lkotlinx/coroutines/flow/SharedFlowImpl;Lkotlinx/coroutines/flow/SharedFlowSlot;)J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->awaitValue(Lkotlinx/coroutines/flow/SharedFlowSlot;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->cleanupTailLocked()V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->collect(Lkotlinx/coroutines/flow/FlowCollector;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlot()Lkotlinx/coroutines/flow/SharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlot()Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/SharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->emit(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->enqueueLocked(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->findSlotsToResumeLocked([Lkotlin/coroutines/Continuation;)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getBufferEndIndex()J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getHead()J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getPeekedValueLockedAt(J)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getQueueEndIndex()J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getReplaySize()I
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->getTotalSize()I
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->growBuffer([Ljava/lang/Object;II)[Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryEmit(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryEmitLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryEmitNoCollectorsLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryPeekLocked(Lkotlinx/coroutines/flow/SharedFlowSlot;)J
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->tryTakeValue(Lkotlinx/coroutines/flow/SharedFlowSlot;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->updateBufferLocked(JJJJ)V
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->updateCollectorIndexLocked$kotlinx_coroutines_core(J)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/SharedFlowImpl;->updateNewCollectorIndexLocked$kotlinx_coroutines_core()J
HSPLkotlinx/coroutines/flow/SharedFlowKt;->MutableSharedFlow$default(IILkotlinx/coroutines/channels/BufferOverflow;ILjava/lang/Object;)Lkotlinx/coroutines/flow/MutableSharedFlow;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->MutableSharedFlow(IILkotlinx/coroutines/channels/BufferOverflow;)Lkotlinx/coroutines/flow/MutableSharedFlow;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->access$getBufferAt([Ljava/lang/Object;J)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->access$setBufferAt([Ljava/lang/Object;JLjava/lang/Object;)V
HSPLkotlinx/coroutines/flow/SharedFlowKt;->getBufferAt([Ljava/lang/Object;J)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/SharedFlowKt;->setBufferAt([Ljava/lang/Object;JLjava/lang/Object;)V
HSPLkotlinx/coroutines/flow/SharedFlowSlot;-><init>()V
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->allocateLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->allocateLocked(Lkotlinx/coroutines/flow/SharedFlowImpl;)Z
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->freeLocked(Ljava/lang/Object;)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/SharedFlowSlot;->freeLocked(Lkotlinx/coroutines/flow/SharedFlowImpl;)[Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/flow/StateFlowImpl$collect$1;-><init>(Lkotlinx/coroutines/flow/StateFlowImpl;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/flow/StateFlowImpl$collect$1;->invokeSuspend(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowImpl;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/flow/StateFlowImpl;->collect(Lkotlinx/coroutines/flow/FlowCollector;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->compareAndSet(Ljava/lang/Object;Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlot()Lkotlinx/coroutines/flow/StateFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlot()Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/StateFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->createSlotArray(I)[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->getValue()Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowImpl;->setValue(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/flow/StateFlowImpl;->updateState(Ljava/lang/Object;Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/StateFlowKt;->MutableStateFlow(Ljava/lang/Object;)Lkotlinx/coroutines/flow/MutableStateFlow;
HSPLkotlinx/coroutines/flow/StateFlowKt;->access$getNONE$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/flow/StateFlowKt;->access$getPENDING$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/flow/StateFlowSlot;-><init>()V
HSPLkotlinx/coroutines/flow/StateFlowSlot;->allocateLocked(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/flow/StateFlowSlot;->allocateLocked(Lkotlinx/coroutines/flow/StateFlowImpl;)Z
HSPLkotlinx/coroutines/flow/StateFlowSlot;->awaitPending(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/flow/StateFlowSlot;->makePending()V
HSPLkotlinx/coroutines/flow/StateFlowSlot;->takePending()Z
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;-><init>()V
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->access$getNCollectors$p(Lkotlinx/coroutines/flow/internal/AbstractSharedFlow;)I
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->access$getSlots$p(Lkotlinx/coroutines/flow/internal/AbstractSharedFlow;)[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->allocateSlot()Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->freeSlot(Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;)V
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->getNCollectors()I
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlow;->getSlots()[Lkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;
HSPLkotlinx/coroutines/flow/internal/AbstractSharedFlowSlot;-><init>()V
HSPLkotlinx/coroutines/internal/AtomicOp;-><init>()V
HSPLkotlinx/coroutines/internal/AtomicOp;->decide(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/AtomicOp;->perform(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ContextScope;-><init>(Lkotlin/coroutines/CoroutineContext;)V
HSPLkotlinx/coroutines/internal/ContextScope;->getCoroutineContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;-><init>(Lkotlinx/coroutines/CoroutineDispatcher;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->checkPostponedCancellation(Lkotlinx/coroutines/CancellableContinuation;)Ljava/lang/Throwable;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->claimReusableCancellableContinuation()Lkotlinx/coroutines/CancellableContinuationImpl;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->getContext()Lkotlin/coroutines/CoroutineContext;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->getDelegate$kotlinx_coroutines_core()Lkotlin/coroutines/Continuation;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->getReusableCancellableContinuation()Lkotlinx/coroutines/CancellableContinuationImpl;
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->isReusable(Lkotlinx/coroutines/CancellableContinuationImpl;)Z
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->postponeCancellation(Ljava/lang/Throwable;)Z
HSPLkotlinx/coroutines/internal/DispatchedContinuation;->takeState$kotlinx_coroutines_core()Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/DispatchedContinuationKt;->access$getUNDEFINED$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/internal/DispatchedContinuationKt;->resumeCancellableWith(Lkotlin/coroutines/Continuation;Ljava/lang/Object;Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListHead;-><init>()V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListHead;->isRemoved()Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListKt;->unwrap(Ljava/lang/Object;)Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;->complete(Ljava/lang/Object;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;->complete(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;-><init>()V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->access$finishAdd(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->addNext(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->addOneIfEmpty(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->correctPrev(Lkotlinx/coroutines/internal/OpDescriptor;)Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->finishAdd(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->getNext()Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->getNextNode()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->getPrevNode()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->isRemoved()Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->remove()Z
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->removeFirstOrNull()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->removeOrNext()Lkotlinx/coroutines/internal/LockFreeLinkedListNode;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->removed()Lkotlinx/coroutines/internal/Removed;
HSPLkotlinx/coroutines/internal/LockFreeLinkedListNode;->tryCondAddNext(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode$CondAddOp;)I
HSPLkotlinx/coroutines/internal/LockFreeTaskQueue;-><init>(Z)V
HSPLkotlinx/coroutines/internal/LockFreeTaskQueueCore$Companion;-><init>()V
HSPLkotlinx/coroutines/internal/LockFreeTaskQueueCore$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/internal/LockFreeTaskQueueCore;-><init>(IZ)V
HSPLkotlinx/coroutines/internal/OpDescriptor;-><init>()V
HSPLkotlinx/coroutines/internal/Removed;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)V
HSPLkotlinx/coroutines/internal/ScopeCoroutine;-><init>(Lkotlin/coroutines/CoroutineContext;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/internal/ScopeCoroutine;->afterResume(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/ScopeCoroutine;->isScopedCoroutine()Z
HSPLkotlinx/coroutines/internal/Symbol;-><init>(Ljava/lang/String;)V
HSPLkotlinx/coroutines/internal/SystemPropsKt;->getAVAILABLE_PROCESSORS()I
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp$default(Ljava/lang/String;IIIILjava/lang/Object;)I
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp$default(Ljava/lang/String;JJJILjava/lang/Object;)J
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp(Ljava/lang/String;)Ljava/lang/String;
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp(Ljava/lang/String;III)I
HSPLkotlinx/coroutines/internal/SystemPropsKt;->systemProp(Ljava/lang/String;JJJ)J
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemPropsKt;->getAVAILABLE_PROCESSORS()I
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemPropsKt;->systemProp(Ljava/lang/String;)Ljava/lang/String;
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp$default(Ljava/lang/String;IIIILjava/lang/Object;)I
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp$default(Ljava/lang/String;JJJILjava/lang/Object;)J
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp(Ljava/lang/String;III)I
HSPLkotlinx/coroutines/internal/SystemPropsKt__SystemProps_commonKt;->systemProp(Ljava/lang/String;JJJ)J
HSPLkotlinx/coroutines/internal/ThreadContextKt$countAll$1;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadContextKt$countAll$1;->invoke(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadContextKt$countAll$1;->invoke(Ljava/lang/Object;Lkotlin/coroutines/CoroutineContext$Element;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadContextKt$findOne$1;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadContextKt$updateState$1;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadContextKt;->restoreThreadContext(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/internal/ThreadContextKt;->threadContextElements(Lkotlin/coroutines/CoroutineContext;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadContextKt;->updateThreadContext(Lkotlin/coroutines/CoroutineContext;Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;-><init>()V
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->addImpl(Lkotlinx/coroutines/internal/ThreadSafeHeapNode;)V
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->firstImpl()Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->getSize()I
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->isEmpty()Z
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->peek()Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->realloc()[Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->removeAtImpl(I)Lkotlinx/coroutines/internal/ThreadSafeHeapNode;
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->setSize(I)V
HSPLkotlinx/coroutines/internal/ThreadSafeHeap;->siftUpFrom(I)V
HSPLkotlinx/coroutines/intrinsics/CancellableKt;->startCoroutineCancellable$default(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/intrinsics/CancellableKt;->startCoroutineCancellable(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;Lkotlin/jvm/functions/Function1;)V
HSPLkotlinx/coroutines/intrinsics/UndispatchedKt;->startCoroutineUndispatched(Lkotlin/jvm/functions/Function2;Ljava/lang/Object;Lkotlin/coroutines/Continuation;)V
HSPLkotlinx/coroutines/intrinsics/UndispatchedKt;->startUndispatchedOrReturn(Lkotlinx/coroutines/internal/ScopeCoroutine;Ljava/lang/Object;Lkotlin/jvm/functions/Function2;)Ljava/lang/Object;
HSPLkotlinx/coroutines/scheduling/CoroutineScheduler$Companion;-><init>()V
HSPLkotlinx/coroutines/scheduling/CoroutineScheduler$Companion;-><init>(Lkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/scheduling/CoroutineScheduler;-><init>(IIJLjava/lang/String;)V
HSPLkotlinx/coroutines/scheduling/DefaultScheduler;-><init>()V
HSPLkotlinx/coroutines/scheduling/DefaultScheduler;->getIO()Lkotlinx/coroutines/CoroutineDispatcher;
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;-><init>(IIJLjava/lang/String;)V
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;-><init>(IILjava/lang/String;)V
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;-><init>(IILjava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
HSPLkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;->createScheduler()Lkotlinx/coroutines/scheduling/CoroutineScheduler;
HSPLkotlinx/coroutines/scheduling/GlobalQueue;-><init>()V
HSPLkotlinx/coroutines/scheduling/LimitingDispatcher;-><init>(Lkotlinx/coroutines/scheduling/ExperimentalCoroutineDispatcher;ILjava/lang/String;I)V
HSPLkotlinx/coroutines/scheduling/NanoTimeSource;-><init>()V
HSPLkotlinx/coroutines/scheduling/NonBlockingContext;-><init>()V
HSPLkotlinx/coroutines/scheduling/NonBlockingContext;->afterTask()V
HSPLkotlinx/coroutines/scheduling/SchedulerTimeSource;-><init>()V
HSPLkotlinx/coroutines/scheduling/Task;-><init>()V
HSPLkotlinx/coroutines/scheduling/Task;-><init>(JLkotlinx/coroutines/scheduling/TaskContext;)V
HSPLkotlinx/coroutines/sync/Empty;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/Mutex$DefaultImpls;->lock$default(Lkotlinx/coroutines/sync/Mutex;Ljava/lang/Object;Lkotlin/coroutines/Continuation;ILjava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/Mutex$DefaultImpls;->tryLock$default(Lkotlinx/coroutines/sync/Mutex;Ljava/lang/Object;ILjava/lang/Object;)Z
HSPLkotlinx/coroutines/sync/Mutex$DefaultImpls;->unlock$default(Lkotlinx/coroutines/sync/Mutex;Ljava/lang/Object;ILjava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont$tryResumeLockWaiter$1;-><init>(Lkotlinx/coroutines/sync/MutexImpl$LockCont;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont;-><init>(Lkotlinx/coroutines/sync/MutexImpl;Ljava/lang/Object;Lkotlinx/coroutines/CancellableContinuation;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont;->completeResumeLockWaiter(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockCont;->tryResumeLockWaiter()Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl$LockWaiter;-><init>(Lkotlinx/coroutines/sync/MutexImpl;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$LockedQueue;-><init>(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$lockSuspend$$inlined$suspendCancellableCoroutineReusable$lambda$2;-><init>(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Lkotlinx/coroutines/internal/LockFreeLinkedListNode;Ljava/lang/Object;Lkotlinx/coroutines/CancellableContinuation;Lkotlinx/coroutines/sync/MutexImpl$LockCont;Lkotlinx/coroutines/sync/MutexImpl;Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexImpl$lockSuspend$$inlined$suspendCancellableCoroutineReusable$lambda$2;->prepare(Ljava/lang/Object;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl$lockSuspend$$inlined$suspendCancellableCoroutineReusable$lambda$2;->prepare(Lkotlinx/coroutines/internal/LockFreeLinkedListNode;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl;-><init>(Z)V
HSPLkotlinx/coroutines/sync/MutexImpl;->lock(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl;->lockSuspend(Ljava/lang/Object;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;
HSPLkotlinx/coroutines/sync/MutexImpl;->tryLock(Ljava/lang/Object;)Z
HSPLkotlinx/coroutines/sync/MutexImpl;->unlock(Ljava/lang/Object;)V
HSPLkotlinx/coroutines/sync/MutexKt;->Mutex$default(ZILjava/lang/Object;)Lkotlinx/coroutines/sync/Mutex;
HSPLkotlinx/coroutines/sync/MutexKt;->Mutex(Z)Lkotlinx/coroutines/sync/Mutex;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getEMPTY_LOCKED$p()Lkotlinx/coroutines/sync/Empty;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getEMPTY_UNLOCKED$p()Lkotlinx/coroutines/sync/Empty;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getLOCKED$p()Lkotlinx/coroutines/internal/Symbol;
HSPLkotlinx/coroutines/sync/MutexKt;->access$getUNLOCKED$p()Lkotlinx/coroutines/internal/Symbol;

# Baseline Profile Rules for androidx.startup

Landroidx/startup/AppInitializer;
HSPLandroidx/startup/AppInitializer;->**(**)**
