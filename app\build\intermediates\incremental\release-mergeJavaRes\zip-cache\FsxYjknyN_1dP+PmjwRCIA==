[{"key": "androidx/compose/foundation/AbstractClickableNode$InteractionData.class", "name": "androidx/compose/foundation/AbstractClickableNode$InteractionData.class", "size": 2882, "crc": 1519002382}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$1.class", "size": 4260, "crc": 1453668673}, {"key": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2$1.class", "name": "androidx/compose/foundation/AbstractClickableNode$onKeyEvent$2$1.class", "size": 4440, "crc": 1319291231}, {"key": "androidx/compose/foundation/AbstractClickableNode.class", "name": "androidx/compose/foundation/AbstractClickableNode.class", "size": 10343, "crc": 1539584966}, {"key": "androidx/compose/foundation/AbstractClickablePointerInputNode$delayPressInteraction$1.class", "name": "androidx/compose/foundation/AbstractClickablePointerInputNode$delayPressInteraction$1.class", "size": 2367, "crc": -936447925}, {"key": "androidx/compose/foundation/AbstractClickablePointerInputNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/AbstractClickablePointerInputNode$pointerInputNode$1.class", "size": 4048, "crc": 1516107594}, {"key": "androidx/compose/foundation/AbstractClickablePointerInputNode.class", "name": "androidx/compose/foundation/AbstractClickablePointerInputNode.class", "size": 8255, "crc": -905861202}, {"key": "androidx/compose/foundation/ActualJvm_jvmKt.class", "name": "androidx/compose/foundation/ActualJvm_jvmKt.class", "size": 524, "crc": -524622498}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$applyToFling$1.class", "size": 1981, "crc": 1463754013}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1$1.class", "size": 8861, "crc": 1778446367}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$effectModifier$1.class", "size": 4107, "crc": -302102332}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$onNewSize$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$onNewSize$1.class", "size": 2910, "crc": 618612786}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$special$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect$special$$inlined$debugInspectorInfo$1.class", "size": 2910, "crc": -614159722}, {"key": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "name": "androidx/compose/foundation/AndroidEdgeEffectOverscrollEffect.class", "size": 25599, "crc": -1190626537}, {"key": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidEmbeddedExternalSurfaceState.class", "size": 3786, "crc": 1611471967}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceScope.class", "size": 1276, "crc": -660810491}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceState.class", "size": 2694, "crc": -40251785}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder$Companion.class", "size": 1491, "crc": 524373564}, {"key": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "name": "androidx/compose/foundation/AndroidExternalSurfaceZOrder.class", "size": 2956, "crc": -950089192}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$1.class", "size": 2860, "crc": 108884641}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$2.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$2.class", "size": 1658, "crc": -493199266}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$3.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$3.class", "size": 3938, "crc": 588304791}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$4.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidEmbeddedExternalSurface$4.class", "size": 2488, "crc": 184620733}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$1.class", "size": 2663, "crc": 652444996}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$2.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$2.class", "size": 1634, "crc": 1845103671}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$3$1.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$3$1.class", "size": 2914, "crc": 1337166964}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$4.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt$AndroidExternalSurface$4.class", "size": 2506, "crc": 2143200317}, {"key": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "name": "androidx/compose/foundation/AndroidExternalSurface_androidKt.class", "size": 15399, "crc": 239119840}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1$1.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1$1.class", "size": 2337, "crc": 1122546007}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$1.class", "size": 4180, "crc": 807349418}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2$1.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2$1.class", "size": 2136, "crc": -219466651}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt$StretchOverscrollNonClippingLayer$2.class", "size": 4135, "crc": 973225853}, {"key": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "name": "androidx/compose/foundation/AndroidOverscroll_androidKt.class", "size": 6418, "crc": 642039477}, {"key": "androidx/compose/foundation/Api31Impl.class", "name": "androidx/compose/foundation/Api31Impl.class", "size": 2185, "crc": 1791244307}, {"key": "androidx/compose/foundation/BackgroundElement.class", "name": "androidx/compose/foundation/BackgroundElement.class", "size": 5360, "crc": 865713921}, {"key": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background$$inlined$debugInspectorInfo$1.class", "size": 3207, "crc": -321268469}, {"key": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/BackgroundKt$background-bw27NRU$$inlined$debugInspectorInfo$1.class", "size": 3172, "crc": 2019513172}, {"key": "androidx/compose/foundation/BackgroundKt.class", "name": "androidx/compose/foundation/BackgroundKt.class", "size": 4212, "crc": -432923194}, {"key": "androidx/compose/foundation/BackgroundNode.class", "name": "androidx/compose/foundation/BackgroundNode.class", "size": 6998, "crc": 339156585}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1$receiver$1.class", "size": 2962, "crc": 57543580}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState$dispatchSurfaceCreated$1.class", "size": 5130, "crc": 808103324}, {"key": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "name": "androidx/compose/foundation/BaseAndroidExternalSurfaceState.class", "size": 6161, "crc": -631620396}, {"key": "androidx/compose/foundation/BasicMarqueeKt.class", "name": "androidx/compose/foundation/BasicMarqueeKt.class", "size": 8292, "crc": -1253687878}, {"key": "androidx/compose/foundation/BasicTooltipDefaults.class", "name": "androidx/compose/foundation/BasicTooltipDefaults.class", "size": 1332, "crc": -2041012928}, {"key": "androidx/compose/foundation/BasicTooltipKt.class", "name": "androidx/compose/foundation/BasicTooltipKt.class", "size": 4786, "crc": 1038195707}, {"key": "androidx/compose/foundation/BasicTooltipState.class", "name": "androidx/compose/foundation/BasicTooltipState.class", "size": 1806, "crc": -1756084476}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2$1.class", "size": 3559, "crc": -144924195}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$2.class", "size": 4076, "crc": -1130171016}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl$show$cancellableShow$1.class", "size": 5252, "crc": -349807576}, {"key": "androidx/compose/foundation/BasicTooltipStateImpl.class", "name": "androidx/compose/foundation/BasicTooltipStateImpl.class", "size": 5596, "crc": -592612415}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1$invoke$$inlined$onDispose$1.class", "size": 2207, "crc": 1554571231}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$2$1.class", "size": 3069, "crc": -1609585812}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$3.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$BasicTooltipBox$3.class", "size": 3053, "crc": 776253563}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1$1.class", "size": 3431, "crc": -1838241593}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$1.class", "size": 2148, "crc": -1614295383}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2$1$1.class", "size": 2175, "crc": -1098822942}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$2.class", "size": 10716, "crc": -1142671927}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$3.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$TooltipPopup$3.class", "size": 2751, "crc": -527860862}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$WrappedAnchor$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$WrappedAnchor$2.class", "size": 2536, "crc": -2029302659}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1$1.class", "size": 3801, "crc": -2012617400}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1$1.class", "size": 2035, "crc": 863423654}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$anchorSemantics$1.class", "size": 2458, "crc": 417688036}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$1.class", "size": 4233, "crc": 977006554}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1$2.class", "size": 3904, "crc": 1355069524}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1$1.class", "size": 6914, "crc": 67331013}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1$1.class", "size": 4184, "crc": 1617888817}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$1.class", "size": 4092, "crc": 290201015}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1$1.class", "size": 3904, "crc": 1361073214}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1$1.class", "size": 5961, "crc": -716321717}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2$1.class", "size": 4137, "crc": -393592871}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt$handleGestures$2.class", "size": 4092, "crc": -1133949413}, {"key": "androidx/compose/foundation/BasicTooltip_androidKt.class", "name": "androidx/compose/foundation/BasicTooltip_androidKt.class", "size": 23334, "crc": -1225661383}, {"key": "androidx/compose/foundation/BorderCache.class", "name": "androidx/compose/foundation/BorderCache.class", "size": 12443, "crc": 105675607}, {"key": "androidx/compose/foundation/BorderKt$drawContentWithoutBorder$1.class", "name": "androidx/compose/foundation/BorderKt$drawContentWithoutBorder$1.class", "size": 1679, "crc": -541562974}, {"key": "androidx/compose/foundation/BorderKt$drawRectBorder$1.class", "name": "androidx/compose/foundation/BorderKt$drawRectBorder$1.class", "size": 2341, "crc": 1968402651}, {"key": "androidx/compose/foundation/BorderKt.class", "name": "androidx/compose/foundation/BorderKt.class", "size": 8517, "crc": -1961539915}, {"key": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$1.class", "size": 2488, "crc": 2061419500}, {"key": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$3.class", "name": "androidx/compose/foundation/BorderModifierNode$drawGenericBorder$3.class", "size": 4575, "crc": -1446155981}, {"key": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$1.class", "size": 5843, "crc": -1717754574}, {"key": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$2.class", "name": "androidx/compose/foundation/BorderModifierNode$drawRoundRectBorder$2.class", "size": 2427, "crc": -655723162}, {"key": "androidx/compose/foundation/BorderModifierNode$drawWithCacheModifierNode$1.class", "name": "androidx/compose/foundation/BorderModifierNode$drawWithCacheModifierNode$1.class", "size": 4666, "crc": 1901858518}, {"key": "androidx/compose/foundation/BorderModifierNode.class", "name": "androidx/compose/foundation/BorderModifierNode.class", "size": 20721, "crc": 936901127}, {"key": "androidx/compose/foundation/BorderModifierNodeElement.class", "name": "androidx/compose/foundation/BorderModifierNodeElement.class", "size": 6412, "crc": -532579354}, {"key": "androidx/compose/foundation/BorderStroke.class", "name": "androidx/compose/foundation/BorderStroke.class", "size": 3297, "crc": -1015812754}, {"key": "androidx/compose/foundation/BorderStrokeKt.class", "name": "androidx/compose/foundation/BorderStrokeKt.class", "size": 1197, "crc": -680781903}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$1.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$1.class", "size": 2066, "crc": 550992917}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$2$1.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$2$1.class", "size": 1824, "crc": 631026012}, {"key": "androidx/compose/foundation/CanvasKt$Canvas$3.class", "name": "androidx/compose/foundation/CanvasKt$Canvas$3.class", "size": 2201, "crc": -1104273044}, {"key": "androidx/compose/foundation/CanvasKt.class", "name": "androidx/compose/foundation/CanvasKt.class", "size": 6018, "crc": -135790980}, {"key": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "name": "androidx/compose/foundation/CheckScrollableContainerConstraintsKt.class", "size": 2910, "crc": 2033075093}, {"key": "androidx/compose/foundation/ClickableElement.class", "name": "androidx/compose/foundation/ClickableElement.class", "size": 4891, "crc": -141068724}, {"key": "androidx/compose/foundation/ClickableKt$clickable$2.class", "name": "androidx/compose/foundation/ClickableKt$clickable$2.class", "size": 6261, "crc": 1749571531}, {"key": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "size": 3874, "crc": 500640096}, {"key": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$clickable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3376, "crc": -126014729}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable$2.class", "size": 6894, "crc": -1393962428}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-XVZzFYc$$inlined$debugInspectorInfo$1.class", "size": 4345, "crc": 1850970030}, {"key": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ClickableKt$combinedClickable-cJG_KMw$$inlined$debugInspectorInfo$1.class", "size": 3847, "crc": -994392288}, {"key": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$1.class", "name": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$1.class", "size": 4366, "crc": -1247596630}, {"key": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$2$1.class", "name": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1$2$1.class", "size": 4546, "crc": -776847838}, {"key": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1.class", "name": "androidx/compose/foundation/ClickableKt$genericClickableWithoutGesture$detectPressAndClickFromKey$1.class", "size": 5369, "crc": 137417206}, {"key": "androidx/compose/foundation/ClickableKt$handlePressInteraction$2$delayJob$1.class", "name": "androidx/compose/foundation/ClickableKt$handlePressInteraction$2$delayJob$1.class", "size": 5377, "crc": 672119409}, {"key": "androidx/compose/foundation/ClickableKt$handlePressInteraction$2.class", "name": "androidx/compose/foundation/ClickableKt$handlePressInteraction$2.class", "size": 7972, "crc": 1966057506}, {"key": "androidx/compose/foundation/ClickableKt.class", "name": "androidx/compose/foundation/ClickableKt.class", "size": 20643, "crc": 2130813475}, {"key": "androidx/compose/foundation/ClickableNode.class", "name": "androidx/compose/foundation/ClickableNode.class", "size": 4482, "crc": -106198788}, {"key": "androidx/compose/foundation/ClickablePointerInputNode$pointerInput$2.class", "name": "androidx/compose/foundation/ClickablePointerInputNode$pointerInput$2.class", "size": 3808, "crc": -1242695432}, {"key": "androidx/compose/foundation/ClickablePointerInputNode$pointerInput$3.class", "name": "androidx/compose/foundation/ClickablePointerInputNode$pointerInput$3.class", "size": 1827, "crc": 1322730872}, {"key": "androidx/compose/foundation/ClickablePointerInputNode.class", "name": "androidx/compose/foundation/ClickablePointerInputNode.class", "size": 5450, "crc": -1164003125}, {"key": "androidx/compose/foundation/ClickableSemanticsElement.class", "name": "androidx/compose/foundation/ClickableSemanticsElement.class", "size": 5101, "crc": 1599488539}, {"key": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$1.class", "name": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$1.class", "size": 1564, "crc": -2006901737}, {"key": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$2.class", "name": "androidx/compose/foundation/ClickableSemanticsNode$applySemantics$2.class", "size": 1605, "crc": 1621775051}, {"key": "androidx/compose/foundation/ClickableSemanticsNode.class", "name": "androidx/compose/foundation/ClickableSemanticsNode.class", "size": 4472, "crc": -640008919}, {"key": "androidx/compose/foundation/Clickable_androidKt.class", "name": "androidx/compose/foundation/Clickable_androidKt.class", "size": 3290, "crc": 234540933}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt$HorizontalScrollableClipModifier$1.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt$HorizontalScrollableClipModifier$1.class", "size": 2846, "crc": 633531353}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt$VerticalScrollableClipModifier$1.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt$VerticalScrollableClipModifier$1.class", "size": 2836, "crc": -765856742}, {"key": "androidx/compose/foundation/ClipScrollableContainerKt.class", "name": "androidx/compose/foundation/ClipScrollableContainerKt.class", "size": 3164, "crc": -378027029}, {"key": "androidx/compose/foundation/CombinedClickableElement.class", "name": "androidx/compose/foundation/CombinedClickableElement.class", "size": 6583, "crc": 1513492319}, {"key": "androidx/compose/foundation/CombinedClickableNode.class", "name": "androidx/compose/foundation/CombinedClickableNode.class", "size": 1898, "crc": 775088003}, {"key": "androidx/compose/foundation/CombinedClickableNodeImpl.class", "name": "androidx/compose/foundation/CombinedClickableNodeImpl.class", "size": 6168, "crc": 289017247}, {"key": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$2.class", "name": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$2.class", "size": 1918, "crc": 1327782154}, {"key": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$3.class", "name": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$3.class", "size": 1916, "crc": 374435011}, {"key": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$4.class", "name": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$4.class", "size": 3872, "crc": -345368948}, {"key": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$5.class", "name": "androidx/compose/foundation/CombinedClickablePointerInputNode$pointerInput$5.class", "size": 1867, "crc": 1570733747}, {"key": "androidx/compose/foundation/CombinedClickablePointerInputNode.class", "name": "androidx/compose/foundation/CombinedClickablePointerInputNode.class", "size": 7321, "crc": -1216391906}, {"key": "androidx/compose/foundation/DarkThemeKt.class", "name": "androidx/compose/foundation/DarkThemeKt.class", "size": 1453, "crc": -2031846601}, {"key": "androidx/compose/foundation/DarkTheme_androidKt.class", "name": "androidx/compose/foundation/DarkTheme_androidKt.class", "size": 2798, "crc": 823871462}, {"key": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "name": "androidx/compose/foundation/DefaultDebugIndication$DefaultDebugIndicationInstance.class", "size": 2952, "crc": -564756139}, {"key": "androidx/compose/foundation/DefaultDebugIndication.class", "name": "androidx/compose/foundation/DefaultDebugIndication.class", "size": 4878, "crc": -357529968}, {"key": "androidx/compose/foundation/DrawOverscrollModifier.class", "name": "androidx/compose/foundation/DrawOverscrollModifier.class", "size": 3190, "crc": -25361606}, {"key": "androidx/compose/foundation/EdgeEffectCompat.class", "name": "androidx/compose/foundation/EdgeEffectCompat.class", "size": 3001, "crc": 1572625238}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureElement.class", "size": 4011, "crc": -606255442}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureKt.class", "size": 1784, "crc": -1864381013}, {"key": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "name": "androidx/compose/foundation/ExcludeFromSystemGestureNode.class", "size": 3674, "crc": 1178044001}, {"key": "androidx/compose/foundation/ExperimentalFoundationApi.class", "name": "androidx/compose/foundation/ExperimentalFoundationApi.class", "size": 833, "crc": 1841402606}, {"key": "androidx/compose/foundation/FixedMotionDurationScale.class", "name": "androidx/compose/foundation/FixedMotionDurationScale.class", "size": 3079, "crc": -163744511}, {"key": "androidx/compose/foundation/FocusableElement.class", "name": "androidx/compose/foundation/FocusableElement.class", "size": 3287, "crc": -226913052}, {"key": "androidx/compose/foundation/FocusableInNonTouchMode.class", "name": "androidx/compose/foundation/FocusableInNonTouchMode.class", "size": 2631, "crc": -833166412}, {"key": "androidx/compose/foundation/FocusableInteractionNode$emitWithFallback$1.class", "name": "androidx/compose/foundation/FocusableInteractionNode$emitWithFallback$1.class", "size": 4054, "crc": 784563692}, {"key": "androidx/compose/foundation/FocusableInteractionNode.class", "name": "androidx/compose/foundation/FocusableInteractionNode.class", "size": 4230, "crc": -1079171847}, {"key": "androidx/compose/foundation/FocusableKt$FocusableInNonTouchModeElement$1.class", "name": "androidx/compose/foundation/FocusableKt$FocusableInNonTouchModeElement$1.class", "size": 2671, "crc": -1504727472}, {"key": "androidx/compose/foundation/FocusableKt$focusGroup$1.class", "name": "androidx/compose/foundation/FocusableKt$focusGroup$1.class", "size": 1577, "crc": -350097652}, {"key": "androidx/compose/foundation/FocusableKt$focusableInNonTouchMode$1.class", "name": "androidx/compose/foundation/FocusableKt$focusableInNonTouchMode$1.class", "size": 2200, "crc": 702588434}, {"key": "androidx/compose/foundation/FocusableKt$special$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/FocusableKt$special$$inlined$debugInspectorInfo$1.class", "size": 2464, "crc": 1798679264}, {"key": "androidx/compose/foundation/FocusableKt.class", "name": "androidx/compose/foundation/FocusableKt.class", "size": 5022, "crc": -1849896016}, {"key": "androidx/compose/foundation/FocusableNode$onFocusEvent$1.class", "name": "androidx/compose/foundation/FocusableNode$onFocusEvent$1.class", "size": 3835, "crc": 124675809}, {"key": "androidx/compose/foundation/FocusableNode.class", "name": "androidx/compose/foundation/FocusableNode.class", "size": 6190, "crc": -358555777}, {"key": "androidx/compose/foundation/FocusablePinnableContainerNode$retrievePinnableContainer$1.class", "name": "androidx/compose/foundation/FocusablePinnableContainerNode$retrievePinnableContainer$1.class", "size": 2369, "crc": -1978232739}, {"key": "androidx/compose/foundation/FocusablePinnableContainerNode.class", "name": "androidx/compose/foundation/FocusablePinnableContainerNode.class", "size": 2987, "crc": 1890956155}, {"key": "androidx/compose/foundation/FocusableSemanticsNode$applySemantics$1.class", "name": "androidx/compose/foundation/FocusableSemanticsNode$applySemantics$1.class", "size": 1632, "crc": 912323782}, {"key": "androidx/compose/foundation/FocusableSemanticsNode.class", "name": "androidx/compose/foundation/FocusableSemanticsNode.class", "size": 2258, "crc": 1360333619}, {"key": "androidx/compose/foundation/FocusedBoundsKt$ModifierLocalFocusedBoundsObserver$1.class", "name": "androidx/compose/foundation/FocusedBoundsKt$ModifierLocalFocusedBoundsObserver$1.class", "size": 1503, "crc": 1627955255}, {"key": "androidx/compose/foundation/FocusedBoundsKt.class", "name": "androidx/compose/foundation/FocusedBoundsKt.class", "size": 2608, "crc": 2068127548}, {"key": "androidx/compose/foundation/FocusedBoundsNode.class", "name": "androidx/compose/foundation/FocusedBoundsNode.class", "size": 3052, "crc": 1633217690}, {"key": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "name": "androidx/compose/foundation/FocusedBoundsObserverElement.class", "size": 3724, "crc": -2126487607}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode$focusBoundsObserver$1.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode$focusBoundsObserver$1.class", "size": 1983, "crc": -1325925062}, {"key": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "name": "androidx/compose/foundation/FocusedBoundsObserverNode.class", "size": 3656, "crc": 1842153159}, {"key": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "name": "androidx/compose/foundation/GlowEdgeEffectCompat.class", "size": 3180, "crc": -1435960762}, {"key": "androidx/compose/foundation/HoverableElement.class", "name": "androidx/compose/foundation/HoverableElement.class", "size": 3117, "crc": -201117576}, {"key": "androidx/compose/foundation/HoverableKt.class", "name": "androidx/compose/foundation/HoverableKt.class", "size": 1740, "crc": -1317596534}, {"key": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "name": "androidx/compose/foundation/HoverableNode$emitEnter$1.class", "size": 1732, "crc": -383440433}, {"key": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "name": "androidx/compose/foundation/HoverableNode$emitExit$1.class", "size": 1691, "crc": -156397706}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$1.class", "size": 3556, "crc": -688710561}, {"key": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "name": "androidx/compose/foundation/HoverableNode$onPointerEvent$2.class", "size": 3555, "crc": 1287378926}, {"key": "androidx/compose/foundation/HoverableNode.class", "name": "androidx/compose/foundation/HoverableNode.class", "size": 6466, "crc": 1592102031}, {"key": "androidx/compose/foundation/ImageKt$Image$$inlined$Layout$1.class", "name": "androidx/compose/foundation/ImageKt$Image$$inlined$Layout$1.class", "size": 2233, "crc": 1854082180}, {"key": "androidx/compose/foundation/ImageKt$Image$1$1.class", "name": "androidx/compose/foundation/ImageKt$Image$1$1.class", "size": 1676, "crc": -1151459110}, {"key": "androidx/compose/foundation/ImageKt$Image$1.class", "name": "androidx/compose/foundation/ImageKt$Image$1.class", "size": 2322, "crc": 576216377}, {"key": "androidx/compose/foundation/ImageKt$Image$2.class", "name": "androidx/compose/foundation/ImageKt$Image$2.class", "size": 2582, "crc": 1872540779}, {"key": "androidx/compose/foundation/ImageKt$Image$semantics$1$1.class", "name": "androidx/compose/foundation/ImageKt$Image$semantics$1$1.class", "size": 2281, "crc": -1169720008}, {"key": "androidx/compose/foundation/ImageKt.class", "name": "androidx/compose/foundation/ImageKt.class", "size": 15541, "crc": 511859443}, {"key": "androidx/compose/foundation/Indication.class", "name": "androidx/compose/foundation/Indication.class", "size": 1034, "crc": -824797540}, {"key": "androidx/compose/foundation/IndicationInstance.class", "name": "androidx/compose/foundation/IndicationInstance.class", "size": 682, "crc": -525169678}, {"key": "androidx/compose/foundation/IndicationKt$LocalIndication$1.class", "name": "androidx/compose/foundation/IndicationKt$LocalIndication$1.class", "size": 1409, "crc": -1431039057}, {"key": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/IndicationKt$indication$$inlined$debugInspectorInfo$1.class", "size": 3167, "crc": -1212779388}, {"key": "androidx/compose/foundation/IndicationKt$indication$2.class", "name": "androidx/compose/foundation/IndicationKt$indication$2.class", "size": 5198, "crc": -1247211037}, {"key": "androidx/compose/foundation/IndicationKt.class", "name": "androidx/compose/foundation/IndicationKt.class", "size": 3540, "crc": 1155830833}, {"key": "androidx/compose/foundation/IndicationModifier.class", "name": "androidx/compose/foundation/IndicationModifier.class", "size": 1584, "crc": -2011858532}, {"key": "androidx/compose/foundation/InternalFoundationApi.class", "name": "androidx/compose/foundation/InternalFoundationApi.class", "size": 1046, "crc": -1667127780}, {"key": "androidx/compose/foundation/MagnifierElement.class", "name": "androidx/compose/foundation/MagnifierElement.class", "size": 7881, "crc": -1394533533}, {"key": "androidx/compose/foundation/MagnifierNode$applySemantics$1.class", "name": "androidx/compose/foundation/MagnifierNode$applySemantics$1.class", "size": 1492, "crc": -541017483}, {"key": "androidx/compose/foundation/MagnifierNode$draw$1$1.class", "name": "androidx/compose/foundation/MagnifierNode$draw$1$1.class", "size": 1294, "crc": -1432893417}, {"key": "androidx/compose/foundation/MagnifierNode$draw$1.class", "name": "androidx/compose/foundation/MagnifierNode$draw$1.class", "size": 3960, "crc": 1862030227}, {"key": "androidx/compose/foundation/MagnifierNode$onObservedReadsChanged$1.class", "name": "androidx/compose/foundation/MagnifierNode$onObservedReadsChanged$1.class", "size": 3719, "crc": -833355430}, {"key": "androidx/compose/foundation/MagnifierNode.class", "name": "androidx/compose/foundation/MagnifierNode.class", "size": 18641, "crc": 1976570015}, {"key": "androidx/compose/foundation/Magnifier_androidKt$magnifier-jPUL71Q$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/Magnifier_androidKt$magnifier-jPUL71Q$$inlined$debugInspectorInfo$1.class", "size": 3957, "crc": 1456262674}, {"key": "androidx/compose/foundation/Magnifier_androidKt.class", "name": "androidx/compose/foundation/Magnifier_androidKt.class", "size": 8691, "crc": 1432121104}, {"key": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "name": "androidx/compose/foundation/MarqueeAnimationMode$Companion.class", "size": 1621, "crc": 462217963}, {"key": "androidx/compose/foundation/MarqueeAnimationMode.class", "name": "androidx/compose/foundation/MarqueeAnimationMode.class", "size": 2975, "crc": -1871725793}, {"key": "androidx/compose/foundation/MarqueeModifierElement.class", "name": "androidx/compose/foundation/MarqueeModifierElement.class", "size": 6730, "crc": 287368410}, {"key": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "name": "androidx/compose/foundation/MarqueeModifierNode$WhenMappings.class", "size": 819, "crc": -898092723}, {"key": "androidx/compose/foundation/MarqueeModifierNode$measure$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$measure$1.class", "size": 2575, "crc": -609809306}, {"key": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$restartAnimation$1.class", "size": 3862, "crc": 353419688}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$1.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$1.class", "size": 2123, "crc": 1114370906}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2$2.class", "size": 5486, "crc": 1751722633}, {"key": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$runAnimation$2.class", "size": 4071, "crc": 1327691948}, {"key": "androidx/compose/foundation/MarqueeModifierNode$spacingPx$2.class", "name": "androidx/compose/foundation/MarqueeModifierNode$spacingPx$2.class", "size": 2105, "crc": -461463381}, {"key": "androidx/compose/foundation/MarqueeModifierNode.class", "name": "androidx/compose/foundation/MarqueeModifierNode.class", "size": 19601, "crc": 1487810529}, {"key": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "name": "androidx/compose/foundation/MarqueeSpacing$Companion.class", "size": 1828, "crc": 1805385899}, {"key": "androidx/compose/foundation/MarqueeSpacing.class", "name": "androidx/compose/foundation/MarqueeSpacing.class", "size": 1125, "crc": 64044543}, {"key": "androidx/compose/foundation/MutatePriority.class", "name": "androidx/compose/foundation/MutatePriority.class", "size": 1487, "crc": -1686970598}, {"key": "androidx/compose/foundation/MutationInterruptedException.class", "name": "androidx/compose/foundation/MutationInterruptedException.class", "size": 1828, "crc": -715809018}, {"key": "androidx/compose/foundation/MutatorMutex$Mutator.class", "name": "androidx/compose/foundation/MutatorMutex$Mutator.class", "size": 2022, "crc": -775743984}, {"key": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutate$2.class", "size": 7172, "crc": 159053670}, {"key": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "name": "androidx/compose/foundation/MutatorMutex$mutateWith$2.class", "size": 7346, "crc": 1364222332}, {"key": "androidx/compose/foundation/MutatorMutex.class", "name": "androidx/compose/foundation/MutatorMutex.class", "size": 7010, "crc": -604549572}, {"key": "androidx/compose/foundation/NoIndication$NoIndicationInstance.class", "name": "androidx/compose/foundation/NoIndication$NoIndicationInstance.class", "size": 1353, "crc": -574727284}, {"key": "androidx/compose/foundation/NoIndication.class", "name": "androidx/compose/foundation/NoIndication.class", "size": 2300, "crc": 810895585}, {"key": "androidx/compose/foundation/NoOpOverscrollEffect.class", "name": "androidx/compose/foundation/NoOpOverscrollEffect.class", "size": 3580, "crc": 2130624397}, {"key": "androidx/compose/foundation/OverscrollConfiguration.class", "name": "androidx/compose/foundation/OverscrollConfiguration.class", "size": 3673, "crc": 2054975289}, {"key": "androidx/compose/foundation/OverscrollConfiguration_androidKt$LocalOverscrollConfiguration$1.class", "name": "androidx/compose/foundation/OverscrollConfiguration_androidKt$LocalOverscrollConfiguration$1.class", "size": 1572, "crc": -36390396}, {"key": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "name": "androidx/compose/foundation/OverscrollConfiguration_androidKt.class", "size": 1859, "crc": 1304032914}, {"key": "androidx/compose/foundation/OverscrollEffect.class", "name": "androidx/compose/foundation/OverscrollEffect.class", "size": 2060, "crc": -766034288}, {"key": "androidx/compose/foundation/OverscrollKt.class", "name": "androidx/compose/foundation/OverscrollKt.class", "size": 1161, "crc": 1689337302}, {"key": "androidx/compose/foundation/PlatformMagnifier.class", "name": "androidx/compose/foundation/PlatformMagnifier.class", "size": 856, "crc": 201609145}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory$Companion.class", "size": 1878, "crc": -1832853889}, {"key": "androidx/compose/foundation/PlatformMagnifierFactory.class", "name": "androidx/compose/foundation/PlatformMagnifierFactory.class", "size": 1634, "crc": -556149441}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl$PlatformMagnifierImpl.class", "size": 2493, "crc": -704032866}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi28Impl.class", "size": 2716, "crc": -1214052802}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl$PlatformMagnifierImpl.class", "size": 2236, "crc": 1110139081}, {"key": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "name": "androidx/compose/foundation/PlatformMagnifierFactoryApi29Impl.class", "size": 5205, "crc": 1652960532}, {"key": "androidx/compose/foundation/PreferKeepClearElement.class", "name": "androidx/compose/foundation/PreferKeepClearElement.class", "size": 3903, "crc": -1806916663}, {"key": "androidx/compose/foundation/PreferKeepClearNode.class", "name": "androidx/compose/foundation/PreferKeepClearNode.class", "size": 3557, "crc": -332929720}, {"key": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "name": "androidx/compose/foundation/PreferKeepClear_androidKt.class", "size": 2971, "crc": -1982531190}, {"key": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$1.class", "name": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$1.class", "size": 2584, "crc": -689570618}, {"key": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$2.class", "name": "androidx/compose/foundation/ProgressSemanticsKt$progressSemantics$2.class", "size": 2167, "crc": -2115936275}, {"key": "androidx/compose/foundation/ProgressSemanticsKt.class", "name": "androidx/compose/foundation/ProgressSemanticsKt.class", "size": 2335, "crc": 468284899}, {"key": "androidx/compose/foundation/RectListNode.class", "name": "androidx/compose/foundation/RectListNode.class", "size": 7299, "crc": -1131596945}, {"key": "androidx/compose/foundation/ScrollKt$rememberScrollState$1$1.class", "name": "androidx/compose/foundation/ScrollKt$rememberScrollState$1$1.class", "size": 1364, "crc": 35460379}, {"key": "androidx/compose/foundation/ScrollKt$scroll$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/ScrollKt$scroll$$inlined$debugInspectorInfo$1.class", "size": 3434, "crc": 1707410419}, {"key": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$1$1.class", "name": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$1$1.class", "size": 4306, "crc": -336724059}, {"key": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$1.class", "name": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$1.class", "size": 2304, "crc": -455267244}, {"key": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$accessibilityScrollState$1.class", "name": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$accessibilityScrollState$1.class", "size": 1545, "crc": -2074822161}, {"key": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$accessibilityScrollState$2.class", "name": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1$accessibilityScrollState$2.class", "size": 1548, "crc": -1425247767}, {"key": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1.class", "name": "androidx/compose/foundation/ScrollKt$scroll$2$semantics$1.class", "size": 3352, "crc": -1823496036}, {"key": "androidx/compose/foundation/ScrollKt$scroll$2.class", "name": "androidx/compose/foundation/ScrollKt$scroll$2.class", "size": 10007, "crc": 1957290923}, {"key": "androidx/compose/foundation/ScrollKt.class", "name": "androidx/compose/foundation/ScrollKt.class", "size": 6528, "crc": -1404902171}, {"key": "androidx/compose/foundation/ScrollState$Companion$Saver$1.class", "name": "androidx/compose/foundation/ScrollState$Companion$Saver$1.class", "size": 1839, "crc": 493826736}, {"key": "androidx/compose/foundation/ScrollState$Companion$Saver$2.class", "name": "androidx/compose/foundation/ScrollState$Companion$Saver$2.class", "size": 1423, "crc": 106364205}, {"key": "androidx/compose/foundation/ScrollState$Companion.class", "name": "androidx/compose/foundation/ScrollState$Companion.class", "size": 1315, "crc": 1444328541}, {"key": "androidx/compose/foundation/ScrollState$canScrollBackward$2.class", "name": "androidx/compose/foundation/ScrollState$canScrollBackward$2.class", "size": 1351, "crc": -613229794}, {"key": "androidx/compose/foundation/ScrollState$canScrollForward$2.class", "name": "androidx/compose/foundation/ScrollState$canScrollForward$2.class", "size": 1380, "crc": -1567019009}, {"key": "androidx/compose/foundation/ScrollState$scrollableState$1.class", "name": "androidx/compose/foundation/ScrollState$scrollableState$1.class", "size": 2127, "crc": 1735470909}, {"key": "androidx/compose/foundation/ScrollState.class", "name": "androidx/compose/foundation/ScrollState.class", "size": 12120, "crc": -2011899750}, {"key": "androidx/compose/foundation/ScrollingLayoutElement.class", "name": "androidx/compose/foundation/ScrollingLayoutElement.class", "size": 4090, "crc": 953076832}, {"key": "androidx/compose/foundation/ScrollingLayoutNode$measure$1.class", "name": "androidx/compose/foundation/ScrollingLayoutNode$measure$1.class", "size": 2656, "crc": -1117926161}, {"key": "androidx/compose/foundation/ScrollingLayoutNode.class", "name": "androidx/compose/foundation/ScrollingLayoutNode.class", "size": 5971, "crc": 79094684}, {"key": "androidx/compose/foundation/SurfaceCoroutineScope.class", "name": "androidx/compose/foundation/SurfaceCoroutineScope.class", "size": 615, "crc": 1085527667}, {"key": "androidx/compose/foundation/SurfaceScope.class", "name": "androidx/compose/foundation/SurfaceScope.class", "size": 1306, "crc": 353967741}, {"key": "androidx/compose/foundation/SystemGestureExclusionKt.class", "name": "androidx/compose/foundation/SystemGestureExclusionKt.class", "size": 3114, "crc": 148415522}, {"key": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "name": "androidx/compose/foundation/draganddrop/AndroidDragAndDropSource_androidKt.class", "size": 1729, "crc": 927163310}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$1.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback$cachePicture$1$1.class", "size": 5597, "crc": -259200490}, {"key": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "name": "androidx/compose/foundation/draganddrop/CacheDrawScopeDragShadowCallback.class", "size": 4379, "crc": -1872197916}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceElement.class", "size": 6969, "crc": 1527214902}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceKt.class", "size": 2041, "crc": -1408378110}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1$1.class", "size": 7040, "crc": -1108337304}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode$1.class", "size": 4327, "crc": 603333593}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceNode.class", "size": 5221, "crc": -58935308}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceScope.class", "size": 959, "crc": 306374842}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropSourceWithDefaultShadowElement.class", "size": 4468, "crc": 1029825293}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetKt.class", "size": 1829, "crc": 1802241320}, {"key": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "name": "androidx/compose/foundation/draganddrop/DragAndDropTargetNode.class", "size": 3049, "crc": -2079125338}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$1.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$1.class", "size": 1882, "crc": -1181981984}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$2.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$2.class", "size": 3899, "crc": 613003361}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter$cacheDrawScopeDragShadowCallback$1$1.class", "size": 1894, "crc": -121621532}, {"key": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "name": "androidx/compose/foundation/draganddrop/DragSourceNodeWithDefaultPainter.class", "size": 3721, "crc": 823101806}, {"key": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "name": "androidx/compose/foundation/draganddrop/DropTargetElement.class", "size": 4700, "crc": -1986776752}, {"key": "androidx/compose/foundation/gestures/AbstractDragScope.class", "name": "androidx/compose/foundation/gestures/AbstractDragScope.class", "size": 586, "crc": 349556938}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$_canDrag$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$_canDrag$1.class", "size": 1976, "crc": -53670857}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$_startDragImmediately$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$_startDragImmediately$1.class", "size": 1645, "crc": -1379101357}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1$1$1$1$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1$1$1$1$1.class", "size": 2518, "crc": -1504477685}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1$1$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1$1$1.class", "size": 9863, "crc": -747151082}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1$1.class", "size": 4629, "crc": 1641966273}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$pointerInputNode$1.class", "size": 4389, "crc": 1944343507}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$processDragCancel$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$processDragCancel$1.class", "size": 2138, "crc": -437477643}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$processDragStart$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$processDragStart$1.class", "size": 2494, "crc": -1985797547}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$processDragStop$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$processDragStop$1.class", "size": 2416, "crc": -955053322}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$startListeningForEvents$1$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$startListeningForEvents$1$1.class", "size": 6273, "crc": -1763784895}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode$startListeningForEvents$1.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode$startListeningForEvents$1.class", "size": 6906, "crc": 329633797}, {"key": "androidx/compose/foundation/gestures/AbstractDraggableNode.class", "name": "androidx/compose/foundation/gestures/AbstractDraggableNode.class", "size": 21329, "crc": -1070208517}, {"key": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "name": "androidx/compose/foundation/gestures/AnchoredDragFinishedSignal.class", "size": 1855, "crc": 2053742346}, {"key": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "name": "androidx/compose/foundation/gestures/AnchoredDragScope.class", "size": 1062, "crc": 1397061634}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$anchoredDraggable$1$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$anchoredDraggable$1$1.class", "size": 3813, "crc": -180040485}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$anchoredDraggable$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$anchoredDraggable$1.class", "size": 4216, "crc": 383660493}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2$1.class", "size": 1924, "crc": -1656816440}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$animateTo$2.class", "size": 5367, "crc": 947070930}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$1.class", "size": 1721, "crc": -383547364}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$2.class", "size": 4310, "crc": 39519515}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1$emit$1.class", "size": 2190, "crc": -1286767481}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2$1.class", "size": 4599, "crc": 331643096}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$restartable$2.class", "size": 4722, "crc": -447346305}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt$snapTo$2.class", "size": 4055, "crc": 337010259}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableKt.class", "size": 9132, "crc": 1527311686}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$1.class", "size": 1636, "crc": -714215842}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$2.class", "size": 1691, "crc": -589587496}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$1.class", "size": 1917, "crc": 633041095}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$2.class", "size": 2368, "crc": -1492211418}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion$Saver$3.class", "size": 3153, "crc": 2137704014}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$Companion.class", "size": 3715, "crc": -105229233}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$1.class", "size": 2019, "crc": 1139293327}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$1.class", "size": 1873, "crc": -210788308}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2$2.class", "size": 4725, "crc": 573733351}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$2.class", "size": 4659, "crc": 48514969}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$3.class", "size": 2038, "crc": 2141964655}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$1.class", "size": 2091, "crc": -840425180}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4$2.class", "size": 4944, "crc": 2108229216}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDrag$4.class", "size": 4907, "crc": 1051017347}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$anchoredDragScope$1.class", "size": 1718, "crc": -1695854697}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$closestValue$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$closestValue$2.class", "size": 2127, "crc": 113434812}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$draggableState$1$drag$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$draggableState$1$drag$2.class", "size": 5695, "crc": 1982573864}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$draggableState$1$dragScope$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$draggableState$1$dragScope$1.class", "size": 2068, "crc": 381420242}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$draggableState$1.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$draggableState$1.class", "size": 3967, "crc": -264623411}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$progress$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$progress$2.class", "size": 2344, "crc": -974822622}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState$targetValue$2.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState$targetValue$2.class", "size": 2109, "crc": -398645822}, {"key": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "name": "androidx/compose/foundation/gestures/AnchoredDraggableState.class", "size": 25847, "crc": -1218390649}, {"key": "androidx/compose/foundation/gestures/AndroidConfig.class", "name": "androidx/compose/foundation/gestures/AndroidConfig.class", "size": 4109, "crc": 457578599}, {"key": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "name": "androidx/compose/foundation/gestures/AndroidScrollable_androidKt.class", "size": 1106, "crc": -699221717}, {"key": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue$enqueue$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue$enqueue$1.class", "size": 2282, "crc": 1409664706}, {"key": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "name": "androidx/compose/foundation/gestures/BringIntoViewRequestPriorityQueue.class", "size": 9621, "crc": -215323827}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion$DefaultBringIntoViewSpec$1.class", "size": 2025, "crc": 1851357693}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec$Companion.class", "size": 2040, "crc": 343247699}, {"key": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "name": "androidx/compose/foundation/gestures/BringIntoViewSpec.class", "size": 1485, "crc": 1632603387}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$Request.class", "size": 4362, "crc": 156636608}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$WhenMappings.class", "size": 874, "crc": 1923690120}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$1.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$1.class", "size": 2829, "crc": 1027561334}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$2.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1$2.class", "size": 5490, "crc": 796019102}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2$1.class", "size": 4857, "crc": 1043389008}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode$launchAnimation$2.class", "size": 5504, "crc": -1093960353}, {"key": "androidx/compose/foundation/gestures/ContentInViewNode.class", "name": "androidx/compose/foundation/gestures/ContentInViewNode.class", "size": 17429, "crc": -370428906}, {"key": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "name": "androidx/compose/foundation/gestures/ContentInViewNodeKt.class", "size": 675, "crc": 1624562014}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag$2.class", "size": 4749, "crc": 2143265286}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState$drag2DScope$1.class", "size": 1560, "crc": 1205503360}, {"key": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggable2DState.class", "size": 4494, "crc": 213737402}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$drag$2.class", "size": 4719, "crc": 19249849}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState$dragScope$1.class", "size": 1410, "crc": 1395962755}, {"key": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "name": "androidx/compose/foundation/gestures/DefaultDraggableState.class", "size": 4256, "crc": -1497936842}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2$1.class", "size": 3050, "crc": -1883018662}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior$performFling$2.class", "size": 5695, "crc": -1104605359}, {"key": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "name": "androidx/compose/foundation/gestures/DefaultFlingBehavior.class", "size": 3983, "crc": -164036246}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2$1.class", "size": 4562, "crc": 197523454}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scroll$2.class", "size": 4995, "crc": -27078895}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState$scrollScope$1.class", "size": 1545, "crc": -1463441011}, {"key": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "name": "androidx/compose/foundation/gestures/DefaultScrollableState.class", "size": 5273, "crc": -972992657}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2$1.class", "size": 4634, "crc": -765469724}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transform$2.class", "size": 5070, "crc": 2095679008}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState$transformScope$1.class", "size": 1808, "crc": -153171153}, {"key": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "name": "androidx/compose/foundation/gestures/DefaultTransformableState.class", "size": 5444, "crc": 693112793}, {"key": "androidx/compose/foundation/gestures/Drag2DScope.class", "name": "androidx/compose/foundation/gestures/Drag2DScope.class", "size": 676, "crc": 448036047}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragCancelled.class", "size": 1025, "crc": -1887696627}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragDelta.class", "size": 1369, "crc": 1746312998}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStarted.class", "size": 1385, "crc": -802165228}, {"key": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "name": "androidx/compose/foundation/gestures/DragEvent$DragStopped.class", "size": 1379, "crc": -2085666268}, {"key": "androidx/compose/foundation/gestures/DragEvent.class", "name": "androidx/compose/foundation/gestures/DragEvent.class", "size": 1582, "crc": -728500016}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$BidirectionalPointerDirectionConfig$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$BidirectionalPointerDirectionConfig$1.class", "size": 1614, "crc": -158917548}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$HorizontalPointerDirectionConfig$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$HorizontalPointerDirectionConfig$1.class", "size": 1688, "crc": 2093799306}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$VerticalPointerDirectionConfig$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$VerticalPointerDirectionConfig$1.class", "size": 1684, "crc": 1386071166}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitDragOrCancellation$1.class", "size": 1771, "crc": 1670120271}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalDragOrCancellation$1.class", "size": 1811, "crc": 202003180}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalPointerSlopOrCancellation$1.class", "size": 2136, "crc": -1409520582}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitHorizontalTouchSlopOrCancellation$1.class", "size": 2124, "crc": -1284872558}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$1.class", "size": 1768, "crc": 573608425}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitLongPressOrCancellation$2.class", "size": 10989, "crc": 433687837}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitPointerSlopOrCancellation$1.class", "size": 2657, "crc": 1328221388}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitTouchSlopOrCancellation$1.class", "size": 2084, "crc": 938247430}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalDragOrCancellation$1.class", "size": 1803, "crc": 530400768}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalPointerSlopOrCancellation$1.class", "size": 2126, "crc": -1613271949}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$awaitVerticalTouchSlopOrCancellation$1.class", "size": 2116, "crc": -830171458}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$2.class", "size": 1728, "crc": -1169904557}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$3.class", "size": 1450, "crc": 385952459}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$4.class", "size": 1450, "crc": -655350463}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5$2.class", "size": 2372, "crc": -569792902}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGestures$5.class", "size": 14642, "crc": -770435815}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$2.class", "size": 1770, "crc": 2079130005}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$3.class", "size": 1492, "crc": 957296047}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$4.class", "size": 1492, "crc": -1871075399}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5$1.class", "size": 2414, "crc": 859066464}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectDragGesturesAfterLongPress$5.class", "size": 8573, "crc": 984751137}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$2.class", "size": 1758, "crc": -81720670}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$3.class", "size": 1480, "crc": 329738006}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$4.class", "size": 1480, "crc": -1987504124}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$1.class", "size": 2404, "crc": 2111260627}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5$drag$1.class", "size": 2058, "crc": -1204073170}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectHorizontalDragGestures$5.class", "size": 7096, "crc": 601431394}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$2.class", "size": 1752, "crc": 454128679}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$3.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$3.class", "size": 1474, "crc": -44281183}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$4.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$4.class", "size": 1474, "crc": 391428382}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$1.class", "size": 2396, "crc": 349889243}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5$drag$1.class", "size": 2052, "crc": 1938061119}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$detectVerticalDragGestures$5.class", "size": 7064, "crc": 339846921}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$1.class", "size": 1712, "crc": 1345007517}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$drag$2.class", "size": 2368, "crc": 647611134}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$horizontalDrag$1.class", "size": 1856, "crc": -27569115}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt$verticalDrag$1.class", "size": 1848, "crc": -277843701}, {"key": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/DragGestureDetectorKt.class", "size": 76620, "crc": -1134087115}, {"key": "androidx/compose/foundation/gestures/DragScope.class", "name": "androidx/compose/foundation/gestures/DragScope.class", "size": 486, "crc": -433894205}, {"key": "androidx/compose/foundation/gestures/Draggable2DElement.class", "name": "androidx/compose/foundation/gestures/Draggable2DElement.class", "size": 7116, "crc": 676416226}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpDrag2DScope$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$NoOpDrag2DScope$1.class", "size": 1030, "crc": -1419054321}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$1.class", "size": 3319, "crc": -1904753319}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$2.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$2.class", "size": 3313, "crc": -357315974}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$3.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$3.class", "size": 1898, "crc": 1690177104}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$4.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$draggable2D$4.class", "size": 1507, "crc": -648684920}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt$rememberDraggable2DState$1$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt$rememberDraggable2DState$1$1.class", "size": 2062, "crc": -438941238}, {"key": "androidx/compose/foundation/gestures/Draggable2DKt.class", "name": "androidx/compose/foundation/gestures/Draggable2DKt.class", "size": 8725, "crc": -1884966520}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$abstractDragScope$1.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$abstractDragScope$1.class", "size": 1648, "crc": 618514}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode$drag$2.class", "size": 4440, "crc": 1859879749}, {"key": "androidx/compose/foundation/gestures/Draggable2DNode.class", "name": "androidx/compose/foundation/gestures/Draggable2DNode.class", "size": 9018, "crc": 84146549}, {"key": "androidx/compose/foundation/gestures/Draggable2DState.class", "name": "androidx/compose/foundation/gestures/Draggable2DState.class", "size": 2232, "crc": 1232017529}, {"key": "androidx/compose/foundation/gestures/DraggableAnchors.class", "name": "androidx/compose/foundation/gestures/DraggableAnchors.class", "size": 1260, "crc": -552110863}, {"key": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "name": "androidx/compose/foundation/gestures/DraggableAnchorsConfig.class", "size": 1754, "crc": 1177089816}, {"key": "androidx/compose/foundation/gestures/DraggableElement.class", "name": "androidx/compose/foundation/gestures/DraggableElement.class", "size": 7488, "crc": 1070683124}, {"key": "androidx/compose/foundation/gestures/DraggableKt$NoOpDragScope$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$NoOpDragScope$1.class", "size": 925, "crc": 65058891}, {"key": "androidx/compose/foundation/gestures/DraggableKt$awaitDownAndSlop$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$awaitDownAndSlop$1.class", "size": 2405, "crc": -1844797432}, {"key": "androidx/compose/foundation/gestures/DraggableKt$awaitDownAndSlop$postPointerSlop$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$awaitDownAndSlop$postPointerSlop$1.class", "size": 2723, "crc": -281332883}, {"key": "androidx/compose/foundation/gestures/DraggableKt$awaitDrag$2.class", "name": "androidx/compose/foundation/gestures/DraggableKt$awaitDrag$2.class", "size": 3387, "crc": 118028825}, {"key": "androidx/compose/foundation/gestures/DraggableKt$draggable$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$draggable$1.class", "size": 3345, "crc": 1546244043}, {"key": "androidx/compose/foundation/gestures/DraggableKt$draggable$2.class", "name": "androidx/compose/foundation/gestures/DraggableKt$draggable$2.class", "size": 3248, "crc": 388149968}, {"key": "androidx/compose/foundation/gestures/DraggableKt$draggable$3.class", "name": "androidx/compose/foundation/gestures/DraggableKt$draggable$3.class", "size": 1932, "crc": 304957449}, {"key": "androidx/compose/foundation/gestures/DraggableKt$draggable$4.class", "name": "androidx/compose/foundation/gestures/DraggableKt$draggable$4.class", "size": 1541, "crc": 694134693}, {"key": "androidx/compose/foundation/gestures/DraggableKt$draggable$5.class", "name": "androidx/compose/foundation/gestures/DraggableKt$draggable$5.class", "size": 4374, "crc": 2079898645}, {"key": "androidx/compose/foundation/gestures/DraggableKt$onDragOrUp$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$onDragOrUp$1.class", "size": 1882, "crc": 509616636}, {"key": "androidx/compose/foundation/gestures/DraggableKt$rememberDraggableState$1$1.class", "name": "androidx/compose/foundation/gestures/DraggableKt$rememberDraggableState$1$1.class", "size": 1904, "crc": -2089488039}, {"key": "androidx/compose/foundation/gestures/DraggableKt.class", "name": "androidx/compose/foundation/gestures/DraggableKt.class", "size": 29150, "crc": -488067519}, {"key": "androidx/compose/foundation/gestures/DraggableNode$abstractDragScope$1.class", "name": "androidx/compose/foundation/gestures/DraggableNode$abstractDragScope$1.class", "size": 1984, "crc": -569398375}, {"key": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "name": "androidx/compose/foundation/gestures/DraggableNode$drag$2.class", "size": 4402, "crc": 1017525827}, {"key": "androidx/compose/foundation/gestures/DraggableNode.class", "name": "androidx/compose/foundation/gestures/DraggableNode.class", "size": 9593, "crc": 312583627}, {"key": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/DraggableState$DefaultImpls.class", "size": 685, "crc": -2041439001}, {"key": "androidx/compose/foundation/gestures/DraggableState.class", "name": "androidx/compose/foundation/gestures/DraggableState.class", "size": 2173, "crc": -1883534758}, {"key": "androidx/compose/foundation/gestures/FlingBehavior.class", "name": "androidx/compose/foundation/gestures/FlingBehavior.class", "size": 1051, "crc": -1167153858}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$2.class", "size": 3590, "crc": 1541647111}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitAllPointersUp$3.class", "size": 1666, "crc": 1022330374}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$awaitEachGesture$2.class", "size": 4962, "crc": 2141287905}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt$forEachGesture$1.class", "size": 1815, "crc": 374180803}, {"key": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "name": "androidx/compose/foundation/gestures/ForEachGestureKt.class", "size": 9537, "crc": -25241836}, {"key": "androidx/compose/foundation/gestures/GestureCancellationException.class", "name": "androidx/compose/foundation/gestures/GestureCancellationException.class", "size": 1297, "crc": **********}, {"key": "androidx/compose/foundation/gestures/MapDraggableAnchors.class", "name": "androidx/compose/foundation/gestures/MapDraggableAnchors.class", "size": 5486, "crc": -270862009}, {"key": "androidx/compose/foundation/gestures/ModifierLocalScrollableContainerProvider.class", "name": "androidx/compose/foundation/gestures/ModifierLocalScrollableContainerProvider.class", "size": 2169, "crc": **********}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1$1$2$1$1$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1$1$2$1$1$1.class", "size": 4167, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1$1$2$1$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1$1$2$1$1.class", "size": 4161, "crc": 871502257}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1$1.class", "size": 7869, "crc": -935602037}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollNode$1.class", "size": 3874, "crc": **********}, {"key": "androidx/compose/foundation/gestures/MouseWheelScrollNode.class", "name": "androidx/compose/foundation/gestures/MouseWheelScrollNode.class", "size": 2834, "crc": 869452651}, {"key": "androidx/compose/foundation/gestures/Orientation.class", "name": "androidx/compose/foundation/gestures/Orientation.class", "size": 1454, "crc": 366668660}, {"key": "androidx/compose/foundation/gestures/PointerDirectionConfig.class", "name": "androidx/compose/foundation/gestures/PointerDirectionConfig.class", "size": 798, "crc": 195352597}, {"key": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/PressGestureScope$DefaultImpls.class", "size": 3551, "crc": 2063149455}, {"key": "androidx/compose/foundation/gestures/PressGestureScope.class", "name": "androidx/compose/foundation/gestures/PressGestureScope.class", "size": 3573, "crc": 58449426}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$awaitRelease$1.class", "size": 1829, "crc": 1820260649}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$reset$1.class", "size": 1824, "crc": 927675584}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl$tryAwaitRelease$1.class", "size": 1864, "crc": -1830334123}, {"key": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "name": "androidx/compose/foundation/gestures/PressGestureScopeImpl.class", "size": 7374, "crc": -500540330}, {"key": "androidx/compose/foundation/gestures/ScrollConfig.class", "name": "androidx/compose/foundation/gestures/ScrollConfig.class", "size": 960, "crc": -1007127235}, {"key": "androidx/compose/foundation/gestures/ScrollDraggableState$drag$2.class", "name": "androidx/compose/foundation/gestures/ScrollDraggableState$drag$2.class", "size": 4251, "crc": 520590468}, {"key": "androidx/compose/foundation/gestures/ScrollDraggableState.class", "name": "androidx/compose/foundation/gestures/ScrollDraggableState.class", "size": 5228, "crc": -615212681}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$1.class", "size": 1701, "crc": 608429192}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2$1.class", "size": 1939, "crc": -1674227060}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$animateScrollBy$2.class", "size": 4597, "crc": 203709669}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$1.class", "size": 1620, "crc": 1734718849}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$scrollBy$2.class", "size": 3739, "crc": -957754157}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt$stopScroll$2.class", "size": 3378, "crc": -293877511}, {"key": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "name": "androidx/compose/foundation/gestures/ScrollExtensionsKt.class", "size": 5920, "crc": -912955328}, {"key": "androidx/compose/foundation/gestures/ScrollScope.class", "name": "androidx/compose/foundation/gestures/ScrollScope.class", "size": 489, "crc": -1294474486}, {"key": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "name": "androidx/compose/foundation/gestures/ScrollableDefaults.class", "size": 6286, "crc": -1243251252}, {"key": "androidx/compose/foundation/gestures/ScrollableElement.class", "name": "androidx/compose/foundation/gestures/ScrollableElement.class", "size": 6629, "crc": -2120689934}, {"key": "androidx/compose/foundation/gestures/ScrollableGesturesNode$onDragStopped$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableGesturesNode$onDragStopped$1$1.class", "size": 3875, "crc": 1253522681}, {"key": "androidx/compose/foundation/gestures/ScrollableGesturesNode$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/ScrollableGesturesNode$onDragStopped$1.class", "size": 4375, "crc": -425656416}, {"key": "androidx/compose/foundation/gestures/ScrollableGesturesNode$startDragImmediately$1.class", "name": "androidx/compose/foundation/gestures/ScrollableGesturesNode$startDragImmediately$1.class", "size": 1830, "crc": -2066393515}, {"key": "androidx/compose/foundation/gestures/ScrollableGesturesNode.class", "name": "androidx/compose/foundation/gestures/ScrollableGesturesNode.class", "size": 6163, "crc": -167456308}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$CanDragCalculation$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$CanDragCalculation$1.class", "size": 1983, "crc": -257821338}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$DefaultScrollMotionDurationScale$1.class", "size": 3125, "crc": -1138923438}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$ModifierLocalScrollableContainer$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$ModifierLocalScrollableContainer$1.class", "size": 1295, "crc": -819848263}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$NoOpFlingBehavior$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$NoOpFlingBehavior$1.class", "size": 1631, "crc": 1297868101}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$NoOpOnDragStarted$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$NoOpOnDragStarted$1.class", "size": 3038, "crc": 1741667113}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$NoOpScrollScope$1.class", "size": 931, "crc": 1970951629}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$UnityDensity$1.class", "size": 1005, "crc": 171834693}, {"key": "androidx/compose/foundation/gestures/ScrollableKt$awaitScrollEvent$1.class", "name": "androidx/compose/foundation/gestures/ScrollableKt$awaitScrollEvent$1.class", "size": 1663, "crc": -1021745018}, {"key": "androidx/compose/foundation/gestures/ScrollableKt.class", "name": "androidx/compose/foundation/gestures/ScrollableKt.class", "size": 10952, "crc": -1516562327}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection$onPostFling$1.class", "size": 1979, "crc": -1296565709}, {"key": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "name": "androidx/compose/foundation/gestures/ScrollableNestedScrollConnection.class", "size": 4848, "crc": -1888572345}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$1.class", "size": 2099, "crc": 1081141013}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onAttach$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onAttach$1.class", "size": 1716, "crc": 872390537}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1$1.class", "size": 4014, "crc": 1724933891}, {"key": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableNode$onKeyEvent$1$1.class", "size": 4120, "crc": 599095814}, {"key": "androidx/compose/foundation/gestures/ScrollableNode.class", "name": "androidx/compose/foundation/gestures/ScrollableNode.class", "size": 13847, "crc": -2025809826}, {"key": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/ScrollableState$DefaultImpls.class", "size": 1271, "crc": 1091425682}, {"key": "androidx/compose/foundation/gestures/ScrollableState.class", "name": "androidx/compose/foundation/gestures/ScrollableState.class", "size": 2832, "crc": -1921258779}, {"key": "androidx/compose/foundation/gestures/ScrollableStateKt$rememberScrollableState$1$1.class", "name": "androidx/compose/foundation/gestures/ScrollableStateKt$rememberScrollableState$1$1.class", "size": 1957, "crc": -723378636}, {"key": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "name": "androidx/compose/foundation/gestures/ScrollableStateKt.class", "size": 4677, "crc": 950377354}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$dispatchScroll$performScroll$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$dispatchScroll$performScroll$1.class", "size": 2835, "crc": -1932841970}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$1.class", "size": 1814, "crc": -1951882235}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$outerScopeScroll$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$outerScopeScroll$1.class", "size": 2245, "crc": 15428429}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$scope$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2$scope$1.class", "size": 1989, "crc": -1479692194}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$doFlingAnimation$2.class", "size": 5776, "crc": 992493852}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$onDragStopped$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$onDragStopped$1.class", "size": 1819, "crc": 1769938292}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic$onDragStopped$performFling$1.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic$onDragStopped$performFling$1.class", "size": 4794, "crc": 1431510026}, {"key": "androidx/compose/foundation/gestures/ScrollingLogic.class", "name": "androidx/compose/foundation/gestures/ScrollingLogic.class", "size": 11932, "crc": -1642938658}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$NoPressGesture$1.class", "size": 3179, "crc": -777316503}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitFirstDown$2.class", "size": 1802, "crc": 488381669}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$awaitSecondDown$2.class", "size": 4697, "crc": -552532910}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$consumeUntilUp$1.class", "size": 1699, "crc": -556834551}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$1.class", "size": 3761, "crc": -1860561541}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$2.class", "size": 4688, "crc": -1570790772}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$3.class", "size": 3643, "crc": 875903059}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$4.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1$4.class", "size": 3644, "crc": -1435202014}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2$1.class", "size": 7716, "crc": -1889236860}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapAndPress$2.class", "size": 5357, "crc": 414728506}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$1.class", "size": 3761, "crc": -1874218879}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$10.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$10.class", "size": 3648, "crc": 790113240}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$2.class", "size": 4688, "crc": -1889776436}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$3.class", "size": 4020, "crc": 803760244}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$4.class", "size": 3643, "crc": -1987469371}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$5.class", "size": 3644, "crc": 703489138}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$6.class", "size": 3644, "crc": 5698670}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$7.class", "size": 3761, "crc": -1865118222}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$8.class", "size": 4694, "crc": -774504881}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$1.class", "size": 3747, "crc": -1272124644}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9$2.class", "size": 3746, "crc": -1322190499}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1$9.class", "size": 6290, "crc": 1332392298}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2$1.class", "size": 10766, "crc": -601858681}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$detectTapGestures$2.class", "size": 5873, "crc": 802288505}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt$waitForUpOrCancellation$2.class", "size": 1824, "crc": 851772179}, {"key": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TapGestureDetectorKt.class", "size": 18225, "crc": 323363483}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformDelta.class", "size": 1810, "crc": 213064140}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStarted.class", "size": 1058, "crc": 372971666}, {"key": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "name": "androidx/compose/foundation/gestures/TransformEvent$TransformStopped.class", "size": 1058, "crc": -1153256848}, {"key": "androidx/compose/foundation/gestures/TransformEvent.class", "name": "androidx/compose/foundation/gestures/TransformEvent.class", "size": 1328, "crc": 546933377}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt$detectTransformGestures$2.class", "size": 12037, "crc": 1188519758}, {"key": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "name": "androidx/compose/foundation/gestures/TransformGestureDetectorKt.class", "size": 9476, "crc": -1987691504}, {"key": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformScope$DefaultImpls.class", "size": 588, "crc": 1665136784}, {"key": "androidx/compose/foundation/gestures/TransformScope.class", "name": "androidx/compose/foundation/gestures/TransformScope.class", "size": 1477, "crc": -373435282}, {"key": "androidx/compose/foundation/gestures/TransformableElement.class", "name": "androidx/compose/foundation/gestures/TransformableElement.class", "size": 4261, "crc": 2041895965}, {"key": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$detectZoom$1.class", "size": 2306, "crc": 1821738948}, {"key": "androidx/compose/foundation/gestures/TransformableKt$transformable$1.class", "name": "androidx/compose/foundation/gestures/TransformableKt$transformable$1.class", "size": 1680, "crc": -854331944}, {"key": "androidx/compose/foundation/gestures/TransformableKt.class", "name": "androidx/compose/foundation/gestures/TransformableKt.class", "size": 15350, "crc": -1418495159}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1$1.class", "size": 5465, "crc": -1994122285}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$1.class", "size": 5565, "crc": 446016535}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1$2.class", "size": 5443, "crc": 1087571471}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1$1.class", "size": 4860, "crc": -634348075}, {"key": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$pointerInputNode$1.class", "size": 4313, "crc": 1572158104}, {"key": "androidx/compose/foundation/gestures/TransformableNode$updatedCanPan$1.class", "name": "androidx/compose/foundation/gestures/TransformableNode$updatedCanPan$1.class", "size": 1893, "crc": 692119046}, {"key": "androidx/compose/foundation/gestures/TransformableNode.class", "name": "androidx/compose/foundation/gestures/TransformableNode.class", "size": 4895, "crc": -1191545521}, {"key": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "name": "androidx/compose/foundation/gestures/TransformableState$DefaultImpls.class", "size": 711, "crc": -775838245}, {"key": "androidx/compose/foundation/gestures/TransformableState.class", "name": "androidx/compose/foundation/gestures/TransformableState.class", "size": 2228, "crc": 320793560}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2$1.class", "size": 2660, "crc": 1760516858}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animatePanBy$2.class", "size": 5486, "crc": 1359415728}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2$1.class", "size": 2538, "crc": 383830624}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateRotateBy$2.class", "size": 5028, "crc": -1481081016}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3$1.class", "size": 2593, "crc": -1967825283}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$animateZoomBy$3.class", "size": 5019, "crc": -1572747165}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$panBy$2.class", "size": 3525, "crc": 493947770}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$rememberTransformableState$1$1.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$rememberTransformableState$1$1.class", "size": 2470, "crc": -182979771}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$rotateBy$2.class", "size": 3744, "crc": 1966445364}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$stopTransformation$2.class", "size": 3448, "crc": -505836622}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt$zoomBy$2.class", "size": 3737, "crc": 1694497556}, {"key": "androidx/compose/foundation/gestures/TransformableStateKt.class", "name": "androidx/compose/foundation/gestures/TransformableStateKt.class", "size": 12556, "crc": -1966751903}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$Companion.class", "size": 1540, "crc": -997892259}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$1.class", "size": 2085, "crc": -1690823926}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$4.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$4.class", "size": 4336, "crc": -1964749964}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$5.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState$animateToZero$5.class", "size": 2104, "crc": 1266870831}, {"key": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "name": "androidx/compose/foundation/gestures/UpdatableAnimationState.class", "size": 8918, "crc": -1972225889}, {"key": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "name": "androidx/compose/foundation/gestures/snapping/AnimationResult.class", "size": 2027, "crc": -127713980}, {"key": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/ApproachAnimation.class", "size": 1685, "crc": -1882358589}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem$Companion.class", "size": 1543, "crc": 886595567}, {"key": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "name": "androidx/compose/foundation/gestures/snapping/FinalSnappingItem.class", "size": 2920, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/HighVelocityApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/HighVelocityApproachAnimation.class", "size": 4233, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 9939, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyGridSnapLayoutInfoProviderKt.class", "size": 3925, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt$SnapLayoutInfoProvider$1.class", "size": 7509, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "name": "androidx/compose/foundation/gestures/snapping/LazyListSnapLayoutInfoProviderKt.class", "size": 7647, "crc": -847147043}, {"key": "androidx/compose/foundation/gestures/snapping/LowVelocityApproachAnimation.class", "name": "androidx/compose/foundation/gestures/snapping/LowVelocityApproachAnimation.class", "size": 4354, "crc": -**********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$1.class", "size": 2204, "crc": -751896704}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$2.class", "size": 1987, "crc": -815319551}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$animationState$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1$animationState$1.class", "size": 2017, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$fling$result$1.class", "size": 8267, "crc": 331586539}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$2.class", "size": 1464, "crc": -1181402141}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$3.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$performFling$3.class", "size": 1954, "crc": 1770428395}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior$tryApproach$1.class", "size": 2182, "crc": -1403265977}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehavior.class", "size": 15707, "crc": -765297395}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$1.class", "size": 1932, "crc": -1725801735}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateDecay$2.class", "size": 3500, "crc": 933455849}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$1.class", "size": 1997, "crc": -1692458805}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$2.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt$animateWithTarget$2.class", "size": 3458, "crc": 739379463}, {"key": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapFlingBehaviorKt.class", "size": 18982, "crc": 594260606}, {"key": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "name": "androidx/compose/foundation/gestures/snapping/SnapLayoutInfoProvider.class", "size": 736, "crc": **********}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPositionInLayout$Companion.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPositionInLayout$Companion.class", "size": 1826, "crc": 220709585}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPositionInLayout.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPositionInLayout.class", "size": 1116, "crc": -485379045}, {"key": "androidx/compose/foundation/gestures/snapping/SnapPositionInLayoutKt.class", "name": "androidx/compose/foundation/gestures/snapping/SnapPositionInLayoutKt.class", "size": 1449, "crc": 679291344}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Cancel.class", "size": 1475, "crc": 124627009}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Start.class", "size": 911, "crc": 1273615433}, {"key": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "name": "androidx/compose/foundation/interaction/DragInteraction$Stop.class", "size": 1469, "crc": 487504463}, {"key": "androidx/compose/foundation/interaction/DragInteraction.class", "name": "androidx/compose/foundation/interaction/DragInteraction.class", "size": 831, "crc": 544097825}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1$1.class", "size": 3409, "crc": -1263747935}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt$collectIsDraggedAsState$1$1.class", "size": 4752, "crc": 333396828}, {"key": "androidx/compose/foundation/interaction/DragInteractionKt.class", "name": "androidx/compose/foundation/interaction/DragInteractionKt.class", "size": 5137, "crc": -2113185407}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Focus.class", "size": 916, "crc": 69191497}, {"key": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "name": "androidx/compose/foundation/interaction/FocusInteraction$Unfocus.class", "size": 1487, "crc": 1743268778}, {"key": "androidx/compose/foundation/interaction/FocusInteraction.class", "name": "androidx/compose/foundation/interaction/FocusInteraction.class", "size": 753, "crc": 503779535}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1$1.class", "size": 3304, "crc": -902746663}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt$collectIsFocusedAsState$1$1.class", "size": 4762, "crc": -2122649114}, {"key": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "name": "androidx/compose/foundation/interaction/FocusInteractionKt.class", "size": 5150, "crc": 1354821958}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Enter.class", "size": 916, "crc": -1681335327}, {"key": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "name": "androidx/compose/foundation/interaction/HoverInteraction$Exit.class", "size": 1478, "crc": -274023303}, {"key": "androidx/compose/foundation/interaction/HoverInteraction.class", "name": "androidx/compose/foundation/interaction/HoverInteraction.class", "size": 747, "crc": -1207777466}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1$1.class", "size": 3298, "crc": 1087413719}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt$collectIsHoveredAsState$1$1.class", "size": 4762, "crc": 195535250}, {"key": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "name": "androidx/compose/foundation/interaction/HoverInteractionKt.class", "size": 5150, "crc": 897748604}, {"key": "androidx/compose/foundation/interaction/Interaction.class", "name": "androidx/compose/foundation/interaction/Interaction.class", "size": 421, "crc": 1008777738}, {"key": "androidx/compose/foundation/interaction/InteractionSource.class", "name": "androidx/compose/foundation/interaction/InteractionSource.class", "size": 897, "crc": 669095953}, {"key": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "name": "androidx/compose/foundation/interaction/InteractionSourceKt.class", "size": 850, "crc": -773399264}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSource.class", "size": 1318, "crc": -54852513}, {"key": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "name": "androidx/compose/foundation/interaction/MutableInteractionSourceImpl.class", "size": 3025, "crc": 1425643249}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Cancel.class", "size": 1484, "crc": -1050996633}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Press.class", "size": 1391, "crc": 2049772587}, {"key": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "name": "androidx/compose/foundation/interaction/PressInteraction$Release.class", "size": 1487, "crc": -599226296}, {"key": "androidx/compose/foundation/interaction/PressInteraction.class", "name": "androidx/compose/foundation/interaction/PressInteraction.class", "size": 843, "crc": -418253205}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1$1.class", "size": 3427, "crc": -2065083571}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt$collectIsPressedAsState$1$1.class", "size": 4762, "crc": -1059950307}, {"key": "androidx/compose/foundation/interaction/PressInteractionKt.class", "name": "androidx/compose/foundation/interaction/PressInteractionKt.class", "size": 5162, "crc": 1696438963}, {"key": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "name": "androidx/compose/foundation/internal/JvmDefaultWithCompatibility_jvmKt.class", "size": 566, "crc": -2034863898}, {"key": "androidx/compose/foundation/lazy/AnimateItemElement.class", "name": "androidx/compose/foundation/lazy/AnimateItemElement.class", "size": 5855, "crc": -2118454231}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$1.class", "size": 3796, "crc": 918473189}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyColumn$2.class", "size": 3734, "crc": -1063965043}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$1.class", "size": 3787, "crc": 1306188420}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$LazyRow$2.class", "size": 3725, "crc": -1252155378}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$1.class", "size": 1746, "crc": 891666688}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$2.class", "size": 2199, "crc": -773519769}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$3.class", "size": 2210, "crc": 1952942956}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$4.class", "size": 3964, "crc": 58777295}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$5.class", "size": 1749, "crc": -639283587}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$6.class", "size": 2133, "crc": 1597704791}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$7.class", "size": 2144, "crc": -1524761162}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$items$8.class", "size": 3875, "crc": 912791803}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$1.class", "size": 2423, "crc": 1670380660}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$$inlined$itemsIndexed$default$2.class", "size": 2395, "crc": 1552251410}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$1.class", "size": 1931, "crc": -1137804971}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$2.class", "size": 2440, "crc": 1600972355}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$3.class", "size": 2451, "crc": 42595072}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$4.class", "size": 4204, "crc": 1433050853}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$5.class", "size": 1934, "crc": -856163982}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$6.class", "size": 2374, "crc": 1419014729}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$7.class", "size": 2385, "crc": -6618295}, {"key": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/LazyDslKt$itemsIndexed$8.class", "size": 4115, "crc": -236778676}, {"key": "androidx/compose/foundation/lazy/LazyDslKt.class", "name": "androidx/compose/foundation/lazy/LazyDslKt.class", "size": 33266, "crc": -14558356}, {"key": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyItemScope$DefaultImpls.class", "size": 1081, "crc": -79484857}, {"key": "androidx/compose/foundation/lazy/LazyItemScope.class", "name": "androidx/compose/foundation/lazy/LazyItemScope.class", "size": 3804, "crc": 714409988}, {"key": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/LazyItemScopeImpl.class", "size": 3842, "crc": 184899059}, {"key": "androidx/compose/foundation/lazy/LazyItemScopeImplKt.class", "name": "androidx/compose/foundation/lazy/LazyItemScopeImplKt.class", "size": 2920, "crc": -1412291620}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 3705, "crc": -1603310947}, {"key": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/lazy/LazyLayoutSemanticStateKt.class", "size": 1229, "crc": -577767507}, {"key": "androidx/compose/foundation/lazy/LazyListAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/LazyListAnimateScrollScope.class", "size": 7326, "crc": -1127368622}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsModifierKt.class", "size": 4179, "crc": -396310265}, {"key": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/LazyListBeyondBoundsState.class", "size": 3106, "crc": 1068924529}, {"key": "androidx/compose/foundation/lazy/LazyListHeadersKt.class", "name": "androidx/compose/foundation/lazy/LazyListHeadersKt.class", "size": 4643, "crc": 70421611}, {"key": "androidx/compose/foundation/lazy/LazyListInterval.class", "name": "androidx/compose/foundation/lazy/LazyListInterval.class", "size": 3327, "crc": 1525696912}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$1.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$1.class", "size": 1432, "crc": -852152874}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$2.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$2.class", "size": 1436, "crc": 155946549}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent$item$3.class", "size": 3451, "crc": -1064758453}, {"key": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "name": "androidx/compose/foundation/lazy/LazyListIntervalContent.class", "size": 6542, "crc": 1493015169}, {"key": "androidx/compose/foundation/lazy/LazyListItemAnimator$ItemInfo.class", "name": "androidx/compose/foundation/lazy/LazyListItemAnimator$ItemInfo.class", "size": 3406, "crc": -2035023818}, {"key": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortBy$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortBy$1.class", "size": 2866, "crc": -1039932636}, {"key": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortBy$2.class", "name": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortBy$2.class", "size": 2856, "crc": -805340859}, {"key": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortByDescending$1.class", "size": 2936, "crc": -1401686681}, {"key": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "name": "androidx/compose/foundation/lazy/LazyListItemAnimator$onMeasured$$inlined$sortByDescending$2.class", "size": 2926, "crc": 157269459}, {"key": "androidx/compose/foundation/lazy/LazyListItemAnimator.class", "name": "androidx/compose/foundation/lazy/LazyListItemAnimator.class", "size": 19207, "crc": -344133037}, {"key": "androidx/compose/foundation/lazy/LazyListItemAnimatorKt.class", "name": "androidx/compose/foundation/lazy/LazyListItemAnimatorKt.class", "size": 2294, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "name": "androidx/compose/foundation/lazy/LazyListItemInfo.class", "size": 990, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListItemProvider.class", "size": 1329, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$1.class", "size": 4898, "crc": 402083213}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$2.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl$Item$2.class", "size": 1898, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderImpl.class", "size": 6092, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$1.class", "size": 1320, "crc": -64421505}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$intervalContentState$1.class", "size": 2163, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt$rememberLazyListItemProviderLambda$1$itemProviderState$1.class", "size": 3373, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "name": "androidx/compose/foundation/lazy/LazyListItemProviderKt.class", "size": 5707, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$LazyList$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$LazyList$1.class", "size": 4619, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$3.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$3.class", "size": 3225, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 4305, "crc": -664947982}, {"key": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListKt$rememberLazyListMeasurePolicy$1$1.class", "size": 14937, "crc": 72149117}, {"key": "androidx/compose/foundation/lazy/LazyListKt.class", "name": "androidx/compose/foundation/lazy/LazyListKt.class", "size": 20170, "crc": -304544483}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo$DefaultImpls.class", "size": 1887, "crc": 926350357}, {"key": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "name": "androidx/compose/foundation/lazy/LazyListLayoutInfo.class", "size": 3406, "crc": 842021838}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$3.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$3.class", "size": 2384, "crc": 2011637309}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$7.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt$measureLazyList$7.class", "size": 4725, "crc": 1266656654}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureKt.class", "size": 26271, "crc": 420658092}, {"key": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "name": "androidx/compose/foundation/lazy/LazyListMeasureResult.class", "size": 8843, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItem.class", "size": 15301, "crc": 954208822}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemKt.class", "size": 422, "crc": 432183704}, {"key": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/LazyListMeasuredItemProvider.class", "size": 3949, "crc": -952913457}, {"key": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "name": "androidx/compose/foundation/lazy/LazyListScope$DefaultImpls.class", "size": 3666, "crc": **********}, {"key": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "name": "androidx/compose/foundation/lazy/LazyListScope$items$1.class", "size": 1390, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListScope$items$2.class", "name": "androidx/compose/foundation/lazy/LazyListScope$items$2.class", "size": 1408, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListScope.class", "name": "androidx/compose/foundation/lazy/LazyListScope.class", "size": 6305, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPosition.class", "size": 7046, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/LazyListScrollPositionKt.class", "size": 523, "crc": -1333513848}, {"key": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "name": "androidx/compose/foundation/lazy/LazyListSemanticsKt.class", "size": 4049, "crc": -1501803570}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$1.class", "size": 2197, "crc": -385637525}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion$Saver$2.class", "size": 1829, "crc": -276690502}, {"key": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "name": "androidx/compose/foundation/lazy/LazyListState$Companion.class", "size": 1357, "crc": 1373229676}, {"key": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$remeasurementModifier$1.class", "size": 1458, "crc": -1902026917}, {"key": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$scroll$1.class", "size": 1884, "crc": -59911726}, {"key": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$scrollToItem$2.class", "size": 3635, "crc": -1155789420}, {"key": "androidx/compose/foundation/lazy/LazyListState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$scrollableState$1.class", "size": 1519, "crc": 2025883355}, {"key": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$1.class", "name": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$1.class", "size": 4335, "crc": -484005006}, {"key": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$2.class", "name": "androidx/compose/foundation/lazy/LazyListState$updateScrollDeltaForPostLookahead$2$2.class", "size": 4335, "crc": 1897578585}, {"key": "androidx/compose/foundation/lazy/LazyListState.class", "name": "androidx/compose/foundation/lazy/LazyListState.class", "size": 29268, "crc": -108002323}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$EmptyLazyListMeasureResult$1.class", "size": 1883, "crc": -85532080}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$1$1.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt$rememberLazyListState$1$1.class", "size": 1571, "crc": -1524524304}, {"key": "androidx/compose/foundation/lazy/LazyListStateKt.class", "name": "androidx/compose/foundation/lazy/LazyListStateKt.class", "size": 5626, "crc": 465494924}, {"key": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "name": "androidx/compose/foundation/lazy/LazyScopeMarker.class", "size": 611, "crc": -401847391}, {"key": "androidx/compose/foundation/lazy/ParentSizeElement.class", "name": "androidx/compose/foundation/lazy/ParentSizeElement.class", "size": 4801, "crc": -528689102}, {"key": "androidx/compose/foundation/lazy/ParentSizeNode$measure$1.class", "name": "androidx/compose/foundation/lazy/ParentSizeNode$measure$1.class", "size": 1942, "crc": -1591140378}, {"key": "androidx/compose/foundation/lazy/ParentSizeNode.class", "name": "androidx/compose/foundation/lazy/ParentSizeNode.class", "size": 5121, "crc": 1524910465}, {"key": "androidx/compose/foundation/lazy/grid/AnimateItemElement.class", "name": "androidx/compose/foundation/lazy/grid/AnimateItemElement.class", "size": 4896, "crc": 214004540}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Adaptive.class", "size": 4035, "crc": 921927324}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$Fixed.class", "size": 2944, "crc": 243176409}, {"key": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/grid/GridCells$FixedSize.class", "size": 4410, "crc": 2037127145}, {"key": "androidx/compose/foundation/lazy/grid/GridCells.class", "name": "androidx/compose/foundation/lazy/grid/GridCells.class", "size": 1199, "crc": -811209345}, {"key": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "name": "androidx/compose/foundation/lazy/grid/GridItemSpan.class", "size": 2339, "crc": 1931504447}, {"key": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "name": "androidx/compose/foundation/lazy/grid/GridSlotCache.class", "size": 3107, "crc": -542052624}, {"key": "androidx/compose/foundation/lazy/grid/ItemInfo.class", "name": "androidx/compose/foundation/lazy/grid/ItemInfo.class", "size": 3840, "crc": 1432098706}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope$calculateLineAverageMainAxisSize$lineOf$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope$calculateLineAverageMainAxisSize$lineOf$1.class", "size": 2067, "crc": -2080748067}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridAnimateScrollScope.class", "size": 8580, "crc": -896720841}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsModifierKt.class", "size": 4009, "crc": 1998181621}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridBeyondBoundsState.class", "size": 2855, "crc": -829744732}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyHorizontalGrid$1.class", "size": 4178, "crc": -52184082}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$LazyVerticalGrid$1.class", "size": 4175, "crc": 1901137562}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$1.class", "size": 1848, "crc": 380929835}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$10.class", "size": 4048, "crc": 312582372}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$2.class", "size": 2301, "crc": 699822647}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$3.class", "size": 3147, "crc": 826873797}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$4.class", "size": 2312, "crc": 146168054}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$5.class", "size": 4134, "crc": 968030044}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$6.class", "size": 1851, "crc": 304080295}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$7.class", "size": 2235, "crc": 1476536780}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$8.class", "size": 3057, "crc": -1689673141}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$items$9.class", "size": 2246, "crc": -1324098108}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$1.class", "size": 2033, "crc": -533549720}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$10.class", "size": 4288, "crc": 231681186}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$2.class", "size": 2542, "crc": 1683686635}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$3.class", "size": 3406, "crc": 1228246998}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$4.class", "size": 2553, "crc": 17795978}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$5.class", "size": 4374, "crc": 1820830248}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$6.class", "size": 2036, "crc": 308163383}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$7.class", "size": 2476, "crc": -778525303}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$8.class", "size": 3316, "crc": 525292874}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$itemsIndexed$9.class", "size": 2487, "crc": -1775123790}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberColumnWidthSums$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberColumnWidthSums$1$1.class", "size": 5519, "crc": -13991026}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberRowHeightSums$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt$rememberRowHeightSums$1$1.class", "size": 5254, "crc": 1221652189}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridDslKt.class", "size": 30223, "crc": 767901303}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridInterval.class", "size": 4337, "crc": 1477063597}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion$DefaultSpan$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion$DefaultSpan$1.class", "size": 2073, "crc": -71063406}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$Companion.class", "size": 1608, "crc": 492696748}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$1$1.class", "size": 1476, "crc": 1406304703}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$2$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$2$1.class", "size": 2540, "crc": 805155295}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$3.class", "size": 1483, "crc": 1357066185}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent$item$4.class", "size": 3557, "crc": 41190201}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridIntervalContent.class", "size": 8930, "crc": 846595293}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo$Companion.class", "size": 916, "crc": -1698854434}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemInfo.class", "size": 1618, "crc": -1006141871}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortBy$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortBy$1.class", "size": 3042, "crc": -1613314066}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortBy$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortBy$2.class", "size": 3210, "crc": -2110923524}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$1.class", "size": 3112, "crc": -2011539374}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$2.class", "size": 3279, "crc": -63880932}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimator.class", "size": 23245, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimatorKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemPlacementAnimatorKt.class", "size": 2427, "crc": 809786620}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProvider.class", "size": 1189, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$1.class", "size": 5018, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl$Item$2.class", "size": 1923, "crc": -82015998}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderImpl.class", "size": 5913, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$1.class", "size": 1340, "crc": -600139782}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$intervalContentState$1.class", "size": 2213, "crc": 278673061}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt$rememberLazyGridItemProviderLambda$1$itemProviderState$1.class", "size": 3202, "crc": -313534840}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemProviderKt.class", "size": 5652, "crc": 219472702}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScope.class", "size": 2573, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemScopeImpl.class", "size": 1996, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridItemSpanScope.class", "size": 782, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$LazyGrid$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$LazyGrid$1.class", "size": 4297, "crc": 529547241}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$1.class", "size": 4998, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$3.class", "size": 3245, "crc": -553608254}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredItemProvider$1.class", "size": 3752, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1$measuredLineProvider$1.class", "size": 3512, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt$rememberLazyGridMeasurePolicy$1$1.class", "size": 15460, "crc": 997036968}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridKt.class", "size": 20288, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridLayoutInfo.class", "size": 1834, "crc": 60877100}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$3.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$3.class", "size": 2485, "crc": 453043848}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$5.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt$measureLazyGrid$5.class", "size": 4495, "crc": -1300606320}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureKt.class", "size": 24763, "crc": -406838048}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasureResult.class", "size": 9164, "crc": 182030415}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItem.class", "size": 13943, "crc": 816239228}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemKt.class", "size": 427, "crc": 645791865}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredItemProvider.class", "size": 4831, "crc": -862018992}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLine.class", "size": 5738, "crc": 891292188}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridMeasuredLineProvider.class", "size": 6565, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope$items$1.class", "size": 1446, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScope.class", "size": 3952, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScopeMarker.class", "size": 633, "crc": 35944344}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPosition.class", "size": 7324, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridScrollPositionKt.class", "size": 528, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlots.class", "size": 1237, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSlotsProvider.class", "size": 952, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanKt.class", "size": 1574, "crc": 790201991}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$Bucket.class", "size": 1258, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LazyGridItemSpanScopeImpl.class", "size": 1610, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$LineConfiguration.class", "size": 1802, "crc": 61144763}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$getLineIndexOfItem$lowerBoundBucket$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider$getLineIndexOfItem$lowerBoundBucket$1.class", "size": 1858, "crc": -611515845}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridSpanLayoutProvider.class", "size": 10729, "crc": 472747613}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$1.class", "size": 2232, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion$Saver$2.class", "size": 1864, "crc": 852257805}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$Companion.class", "size": 1382, "crc": -252324388}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchInfoRetriever$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$prefetchInfoRetriever$2.class", "size": 1733, "crc": **********}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$remeasurementModifier$1.class", "size": 1397, "crc": -2145700481}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scroll$1.class", "size": 1924, "crc": 753343762}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollToItem$2.class", "size": 3675, "crc": 1227063161}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState$scrollableState$1.class", "size": 1544, "crc": -2139649772}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridState.class", "size": 29730, "crc": -1500327132}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$EmptyLazyGridLayoutInfo$1.class", "size": 1892, "crc": -1624565186}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt$rememberLazyGridState$1$1.class", "size": 1611, "crc": -668702814}, {"key": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "name": "androidx/compose/foundation/lazy/grid/LazyGridStateKt.class", "size": 5122, "crc": -812573463}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt$rememberLazyGridSemanticState$1$1.class", "size": 3667, "crc": 1401188278}, {"key": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "name": "androidx/compose/foundation/lazy/grid/LazySemanticsKt.class", "size": 4025, "crc": -1037066228}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier$waitForFirstLayout$1.class", "size": 1971, "crc": -61276475}, {"key": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "name": "androidx/compose/foundation/lazy/layout/AwaitFirstLayoutModifier.class", "size": 4265, "crc": -1028501851}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion$CREATOR$1.class", "size": 1934, "crc": 2095606017}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey$Companion.class", "size": 1105, "crc": 2049243120}, {"key": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "name": "androidx/compose/foundation/lazy/layout/DefaultLazyKey.class", "size": 3239, "crc": -457458608}, {"key": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "name": "androidx/compose/foundation/lazy/layout/DummyHandle.class", "size": 1175, "crc": 381009635}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList$Interval.class", "size": 2645, "crc": 1451465478}, {"key": "androidx/compose/foundation/lazy/layout/IntervalList.class", "name": "androidx/compose/foundation/lazy/layout/IntervalList.class", "size": 2074, "crc": -1943820696}, {"key": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "name": "androidx/compose/foundation/lazy/layout/IntervalListKt.class", "size": 2718, "crc": -1379491333}, {"key": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "name": "androidx/compose/foundation/lazy/layout/ItemFoundInScroll.class", "size": 1891, "crc": 1011860413}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$3.class", "size": 7246, "crc": 1164026533}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$5.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2$5.class", "size": 3908, "crc": -1875452103}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt$animateScrollToItem$2.class", "size": 12811, "crc": 2017573675}, {"key": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyAnimateScrollKt.class", "size": 4444, "crc": -270802653}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimateScrollScope.class", "size": 2104, "crc": 1168842111}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$Companion.class", "size": 1160, "crc": 676761212}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animateAppearance$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animateAppearance$1$1.class", "size": 2254, "crc": -285722493}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animateAppearance$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animateAppearance$1.class", "size": 5067, "crc": -1819143920}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animatePlacementDelta$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animatePlacementDelta$1$1.class", "size": 3501, "crc": 332961794}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animatePlacementDelta$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$animatePlacementDelta$1.class", "size": 6960, "crc": -1156893788}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$cancelPlacementAnimation$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$cancelPlacementAnimation$1.class", "size": 4411, "crc": -14435595}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$layerBlock$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$layerBlock$1.class", "size": 1765, "crc": -1217627141}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$stopAnimations$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$stopAnimations$1.class", "size": 3805, "crc": 1703673052}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$stopAnimations$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation$stopAnimations$2.class", "size": 3801, "crc": -1426231791}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimation.class", "size": 13912, "crc": 741621541}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationKt$DefaultLayerBlock$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationKt$DefaultLayerBlock$1.class", "size": 1559, "crc": -613439601}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationKt.class", "size": 2365, "crc": -66570683}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutAnimationSpecsNode.class", "size": 3292, "crc": -2020120554}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo$Interval.class", "size": 3742, "crc": 2016628209}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsInfo.class", "size": 5146, "crc": -1498973536}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion$emptyBeyondBoundsScope$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion$emptyBeyondBoundsScope$1.class", "size": 1214, "crc": -1212966918}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$Companion.class", "size": 1249, "crc": 540410822}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$WhenMappings.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$WhenMappings.class", "size": 898, "crc": 366673890}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$layout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal$layout$2.class", "size": 2560, "crc": 665617769}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocal.class", "size": 9363, "crc": 1653640249}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsModifierLocalKt.class", "size": 5937, "crc": -1182230970}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsState.class", "size": 848, "crc": 2105268269}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutBeyondBoundsStateKt.class", "size": 4671, "crc": 571810513}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval$type$1.class", "size": 1544, "crc": 1015694520}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent$Interval.class", "size": 1595, "crc": 436457302}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutIntervalContent.class", "size": 4788, "crc": 1887734788}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2$invoke$$inlined$onDispose$1.class", "size": 2725, "crc": 1148541068}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1$2.class", "size": 3549, "crc": -1182891612}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent$createContentLambda$1.class", "size": 6814, "crc": 1698523407}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory$CachedItemContent.class", "size": 4275, "crc": -521263644}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactory.class", "size": 4577, "crc": 1858800360}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$1.class", "size": 2850, "crc": -1377558013}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt$SkippableItem$2.class", "size": 2190, "crc": 889728585}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemContentFactoryKt.class", "size": 3745, "crc": 143650486}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProvider.class", "size": 1581, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemProviderKt.class", "size": 1453, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutItemReusePolicy.class", "size": 3105, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap$Empty.class", "size": 1456, "crc": -332671389}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKeyIndexMap.class", "size": 1083, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$1$1.class", "size": 1622, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$2.class", "size": 2985, "crc": -1271732666}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$2$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$2$1.class", "size": 3283, "crc": -135177078}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$itemContentFactory$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3$itemContentFactory$1$1.class", "size": 1987, "crc": -831077463}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$3.class", "size": 9184, "crc": -614429298}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$4.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt$LazyLayout$4.class", "size": 3033, "crc": -659422560}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutKt.class", "size": 9267, "crc": -708741879}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScope.class", "size": 5680, "crc": -1313070816}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutMeasureScopeImpl.class", "size": 10159, "crc": -540873942}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState$Companion.class", "size": 1723, "crc": 1422957684}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutNearestRangeState.class", "size": 4073, "crc": -1050185932}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItem.class", "size": 9249, "crc": -512772368}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1$invoke$$inlined$onDispose$1.class", "size": 2394, "crc": -1242179592}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$1$1.class", "size": 3215, "crc": 1032024090}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt$LazyLayoutPinnableItem$2.class", "size": 2523, "crc": 1529945750}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnableItemKt.class", "size": 7543, "crc": 473739409}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList$PinnedItem.class", "size": 981, "crc": -727417793}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPinnedItemList.class", "size": 8003, "crc": -1583419057}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$PrefetchHandle.class", "size": 807, "crc": 1360976039}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$Prefetcher.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState$Prefetcher.class", "size": 1160, "crc": -1914765132}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetchState.class", "size": 2403, "crc": 511022373}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher$Companion.class", "size": 1879, "crc": -2093251470}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher$PrefetchRequest.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher$PrefetchRequest.class", "size": 3168, "crc": 1726802464}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher.class", "size": 11901, "crc": 1869327339}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher_androidKt$LazyLayoutPrefetcher$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher_androidKt$LazyLayoutPrefetcher$2.class", "size": 2433, "crc": 526710932}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutPrefetcher_androidKt.class", "size": 6233, "crc": -786703538}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticState.class", "size": 1928, "crc": -1344184768}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$1.class", "size": 4173, "crc": 1511411484}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$accessibilityScrollState$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$accessibilityScrollState$1.class", "size": 1839, "crc": 937151146}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$accessibilityScrollState$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$accessibilityScrollState$2.class", "size": 1842, "crc": 579599917}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$indexForKeyMapping$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$indexForKeyMapping$1.class", "size": 2672, "crc": -1148204452}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollByAction$1$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollByAction$1$1.class", "size": 3891, "crc": -2092137566}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollByAction$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollByAction$1.class", "size": 2725, "crc": 994609134}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollToIndexAction$1$2.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollToIndexAction$1$2.class", "size": 3908, "crc": 285730993}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollToIndexAction$1.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt$lazyLayoutSemantics$1$scrollToIndexAction$1.class", "size": 3907, "crc": -684106323}, {"key": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "name": "androidx/compose/foundation/lazy/layout/LazyLayoutSemanticsKt.class", "size": 9943, "crc": -799990852}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$1.class", "size": 1784, "crc": 2027616107}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$1.class", "size": 3190, "crc": 1248444120}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$2.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion$saver$2.class", "size": 2305, "crc": 768113554}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$Companion.class", "size": 2456, "crc": -809325115}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2$invoke$$inlined$onDispose$1.class", "size": 2634, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$2.class", "size": 3423, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$3.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder$SaveableStateProvider$3.class", "size": 2394, "crc": 249305517}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolder.class", "size": 9679, "crc": 657878181}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$1.class", "size": 3652, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$2.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$2.class", "size": 2194, "crc": -673347721}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$holder$1.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt$LazySaveableStateHolderProvider$holder$1.class", "size": 1885, "crc": 419396745}, {"key": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "name": "androidx/compose/foundation/lazy/layout/LazySaveableStateHolderKt.class", "size": 6227, "crc": -424025551}, {"key": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "name": "androidx/compose/foundation/lazy/layout/Lazy_androidKt.class", "size": 778, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "name": "androidx/compose/foundation/lazy/layout/MutableIntervalList.class", "size": 6744, "crc": **********}, {"key": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap$2$1.class", "name": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap$2$1.class", "size": 3893, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "name": "androidx/compose/foundation/lazy/layout/NearestRangeKeyIndexMap.class", "size": 5830, "crc": -630218564}, {"key": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "name": "androidx/compose/foundation/lazy/layout/ObservableScopeInvalidator.class", "size": 4450, "crc": 342876798}, {"key": "androidx/compose/foundation/lazy/layout/StableValue.class", "name": "androidx/compose/foundation/lazy/layout/StableValue.class", "size": 2678, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/AnimateItemElement.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/AnimateItemElement.class", "size": 4955, "crc": -957682182}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/ItemInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/ItemInfo.class", "size": 4158, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyGridStaggeredGridSlotsProvider.class", "size": 1041, "crc": -518423380}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridAnimateScrollScope$getVisibleItemScrollOffset$searchedIndex$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridAnimateScrollScope$getVisibleItemScrollOffset$searchedIndex$1.class", "size": 1915, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridAnimateScrollScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridAnimateScrollScope.class", "size": 8223, "crc": -267989027}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsModifierKt.class", "size": 4322, "crc": -387459682}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridBeyondBoundsState.class", "size": 3041, "crc": -1533604883}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridCellsKt.class", "size": 1031, "crc": 1632789830}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyHorizontalStaggeredGrid$1.class", "size": 4140, "crc": -1260309082}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$LazyVerticalStaggeredGrid$1.class", "size": 4149, "crc": -1386322532}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$1.class", "size": 2001, "crc": -904678547}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$10.class", "size": 4335, "crc": -1956713728}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$2$1.class", "size": 2462, "crc": -519120483}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$3.class", "size": 2465, "crc": -777202750}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$4$1.class", "size": 2843, "crc": -1098810792}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$5.class", "size": 4421, "crc": -453983610}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$6.class", "size": 2004, "crc": 1669927736}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$7$1.class", "size": 2396, "crc": -1514402818}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$8.class", "size": 2399, "crc": -1273548020}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$items$9$1.class", "size": 2753, "crc": 256694748}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$1.class", "size": 2186, "crc": -504408410}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$10.class", "size": 4575, "crc": 211404462}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$2$1.class", "size": 2703, "crc": -1971404120}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$3.class", "size": 2706, "crc": 1854220053}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$4$1.class", "size": 3084, "crc": -1753944810}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$5.class", "size": 4661, "crc": 1229229302}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$6.class", "size": 2189, "crc": -297533699}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$7$1.class", "size": 2637, "crc": -1848885490}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$8.class", "size": 2640, "crc": -1821208201}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$itemsIndexed$9$1.class", "size": 2994, "crc": 845449682}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberColumnSlots$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberColumnSlots$1$1.class", "size": 5743, "crc": 383960977}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberRowSlots$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt$rememberRowSlots$1$1.class", "size": 5463, "crc": 654727330}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridDslKt.class", "size": 31987, "crc": -1318736273}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridInterval.class", "size": 4208, "crc": 1523415264}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$1$1.class", "size": 1577, "crc": 1492376557}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$2.class", "size": 1584, "crc": 1025995097}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$3$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$3$1.class", "size": 1806, "crc": 1653781878}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent$item$4.class", "size": 3798, "crc": -387174154}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridIntervalContent.class", "size": 7965, "crc": -1636257897}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemInfo.class", "size": 1245, "crc": 1048291406}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortBy$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortBy$1.class", "size": 3143, "crc": 1294805360}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortBy$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortBy$2.class", "size": 3365, "crc": -1223548209}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$1.class", "size": 3213, "crc": -177469081}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator$onMeasured$$inlined$sortByDescending$2.class", "size": 3434, "crc": 1730381714}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimator.class", "size": 22950, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimatorKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemPlacementAnimatorKt.class", "size": 2580, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProvider.class", "size": 1176, "crc": 522051470}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$1.class", "size": 5396, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl$Item$2.class", "size": 2022, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderImpl.class", "size": 6145, "crc": 90929909}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$1.class", "size": 1436, "crc": -569034214}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$intervalContentState$1.class", "size": 2417, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt$rememberStaggeredGridItemProviderLambda$1$itemProviderState$1.class", "size": 3550, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemProviderKt.class", "size": 6028, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScope.class", "size": 2672, "crc": 662360583}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridItemScopeImpl.class", "size": 2082, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt$LazyStaggeredGrid$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt$LazyStaggeredGrid$1.class", "size": 4107, "crc": 731330953}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridKt.class", "size": 16229, "crc": -413245833}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$Companion.class", "size": 1051, "crc": 1747845717}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$SpannedItem.class", "size": 1382, "crc": 510706949}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$getGaps$$inlined$binarySearchBy$default$1.class", "size": 3076, "crc": 592922069}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo$setGaps$$inlined$binarySearchBy$default$1.class", "size": 3077, "crc": 771971440}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLaneInfo.class", "size": 8451, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridLayoutInfo.class", "size": 1845, "crc": 565989488}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext$measuredItemProvider$1.class", "size": 4148, "crc": 241899027}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureContext.class", "size": 11390, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$1.class", "size": 1854, "crc": -688604602}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$29.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt$measure$1$29.class", "size": 4626, "crc": 238911542}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureKt.class", "size": 58025, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$WhenMappings.class", "size": 937, "crc": 792739390}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt$rememberStaggeredGridMeasurePolicy$1$1.class", "size": 11045, "crc": 908427864}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasurePolicyKt.class", "size": 9415, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureProvider.class", "size": 6372, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResult.class", "size": 9463, "crc": 171697189}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$EmptyLazyStaggeredGridLayoutInfo$1.class", "size": 2005, "crc": 827563902}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$findVisibleItem$index$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt$findVisibleItem$index$1.class", "size": 2005, "crc": 67296491}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasureResultKt.class", "size": 3231, "crc": 1525810253}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridMeasuredItem.class", "size": 16152, "crc": -1024633197}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope$items$1.class", "size": 1527, "crc": 1274394504}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScope.class", "size": 4028, "crc": -1992121889}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScopeMarker.class", "size": 670, "crc": 138975410}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPosition.class", "size": 13592, "crc": 1642466894}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridScrollPositionKt.class", "size": 555, "crc": -326638121}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt$rememberLazyStaggeredGridSemanticState$1$1.class", "size": 3863, "crc": 1849598977}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSemanticsKt.class", "size": 4411, "crc": -168421114}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlotCache.class", "size": 3320, "crc": -**********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSlots.class", "size": 1282, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridSpanProvider.class", "size": 3378, "crc": -303319307}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$1.class", "size": 2506, "crc": -188762276}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion$Saver$2.class", "size": 1955, "crc": 105507820}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$Companion.class", "size": 1500, "crc": **********}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$remeasurementModifier$1.class", "size": 1607, "crc": -1848923555}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scroll$1.class", "size": 2077, "crc": 2021844108}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollPosition$1.class", "size": 1629, "crc": -2080992530}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollToItem$2.class", "size": 3938, "crc": -1364449699}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollableState$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState$scrollableState$1.class", "size": 1704, "crc": 566738300}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridState.class", "size": 32582, "crc": 1389514591}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt$rememberLazyStaggeredGridState$1$1.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt$rememberLazyStaggeredGridState$1$1.class", "size": 1791, "crc": -900347785}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/LazyStaggeredGridStateKt.class", "size": 4376, "crc": 267471065}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/SpanRange.class", "size": 4249, "crc": -992899926}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Adaptive.class", "size": 3872, "crc": -722184899}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$Fixed.class", "size": 2809, "crc": -281085687}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells$FixedSize.class", "size": 2542, "crc": 1149681465}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridCells.class", "size": 1185, "crc": 506159751}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan$Companion.class", "size": 1477, "crc": -348015975}, {"key": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "name": "androidx/compose/foundation/lazy/staggeredgrid/StaggeredGridItemSpan.class", "size": 1672, "crc": -682576533}, {"key": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "name": "androidx/compose/foundation/pager/DefaultPagerNestedScrollConnection.class", "size": 5392, "crc": -218068920}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$2.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$2.class", "size": 5168, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$measurePolicy$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$measurePolicy$1$1.class", "size": 2149, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$pagerItemProvider$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$Pager$pagerItemProvider$1$1.class", "size": 2157, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1$1.class", "size": 7469, "crc": -**********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1$1.class", "size": 4114, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$dragDirectionDetector$1.class", "size": 4129, "crc": 152731028}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$1.class", "size": 1356, "crc": -387606798}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$intervalContentState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$intervalContentState$1.class", "size": 3047, "crc": 913807382}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$itemProviderState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt$rememberPagerItemProviderLambda$1$itemProviderState$1.class", "size": 3182, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutPagerKt.class", "size": 23345, "crc": **********}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt$LazyLayoutSemanticState$1.class", "size": 3823, "crc": 348790645}, {"key": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "name": "androidx/compose/foundation/pager/LazyLayoutSemanticStateKt.class", "size": 1225, "crc": -**********}, {"key": "androidx/compose/foundation/pager/MeasuredPage.class", "name": "androidx/compose/foundation/pager/MeasuredPage.class", "size": 10897, "crc": **********}, {"key": "androidx/compose/foundation/pager/MeasuredPageKt.class", "name": "androidx/compose/foundation/pager/MeasuredPageKt.class", "size": 407, "crc": -79171969}, {"key": "androidx/compose/foundation/pager/PageInfo.class", "name": "androidx/compose/foundation/pager/PageInfo.class", "size": 698, "crc": -140791838}, {"key": "androidx/compose/foundation/pager/PageSize$Fill.class", "name": "androidx/compose/foundation/pager/PageSize$Fill.class", "size": 1382, "crc": 821678038}, {"key": "androidx/compose/foundation/pager/PageSize$Fixed.class", "name": "androidx/compose/foundation/pager/PageSize$Fixed.class", "size": 2353, "crc": -1739421206}, {"key": "androidx/compose/foundation/pager/PageSize.class", "name": "androidx/compose/foundation/pager/PageSize.class", "size": 1005, "crc": -340230370}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsModifierKt.class", "size": 4119, "crc": -335469825}, {"key": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "name": "androidx/compose/foundation/pager/PagerBeyondBoundsState.class", "size": 2847, "crc": 1771452454}, {"key": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "name": "androidx/compose/foundation/pager/PagerBringIntoViewSpec.class", "size": 2350, "crc": 215902039}, {"key": "androidx/compose/foundation/pager/PagerDefaults.class", "name": "androidx/compose/foundation/pager/PagerDefaults.class", "size": 12024, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerIntervalContent.class", "size": 2952, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$2.class", "name": "androidx/compose/foundation/pager/PagerKt$HorizontalPager$2.class", "size": 4603, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$SnapLayoutInfoProvider$1.class", "name": "androidx/compose/foundation/pager/PagerKt$SnapLayoutInfoProvider$1.class", "size": 10322, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$VerticalPager$2.class", "name": "androidx/compose/foundation/pager/PagerKt$VerticalPager$2.class", "size": 4611, "crc": -656500431}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$1.class", "size": 1692, "crc": 918556269}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$2.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$2.class", "size": 1691, "crc": 147893438}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$3.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$3.class", "size": 1692, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$4.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1$4.class", "size": 1691, "crc": -790822036}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$1.class", "size": 2818, "crc": -125985905}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performBackwardPaging$1.class", "size": 3741, "crc": 877175863}, {"key": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "name": "androidx/compose/foundation/pager/PagerKt$pagerSemantics$performForwardPaging$1.class", "size": 3732, "crc": 2131094027}, {"key": "androidx/compose/foundation/pager/PagerKt.class", "name": "androidx/compose/foundation/pager/PagerKt.class", "size": 24544, "crc": -746104629}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfo.class", "size": 2017, "crc": -501524683}, {"key": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "name": "androidx/compose/foundation/pager/PagerLayoutInfoKt.class", "size": 1514, "crc": -1374519517}, {"key": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "name": "androidx/compose/foundation/pager/PagerLayoutIntervalContent.class", "size": 4177, "crc": -1778752061}, {"key": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt$PagerLazyAnimateScrollScope$1.class", "name": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt$PagerLazyAnimateScrollScope$1.class", "size": 6716, "crc": -**********}, {"key": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt.class", "name": "androidx/compose/foundation/pager/PagerLazyAnimateScrollScopeKt.class", "size": 1261, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$1.class", "size": 4995, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$2.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider$Item$2.class", "size": 1913, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "name": "androidx/compose/foundation/pager/PagerLazyLayoutItemProvider.class", "size": 6254, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$4.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$4.class", "size": 2266, "crc": 374621563}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$9.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$9.class", "size": 4169, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesAfter$1.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesAfter$1.class", "size": 3757, "crc": **********}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesBefore$1.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt$measurePager$extraPagesBefore$1.class", "size": 3759, "crc": -940648560}, {"key": "androidx/compose/foundation/pager/PagerMeasureKt.class", "name": "androidx/compose/foundation/pager/PagerMeasureKt.class", "size": 26458, "crc": 403849225}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1$2.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1$2.class", "size": 3257, "crc": 1342842608}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt$rememberPagerMeasurePolicy$1$1.class", "size": 13174, "crc": 1857156274}, {"key": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "name": "androidx/compose/foundation/pager/PagerMeasurePolicyKt.class", "size": 9935, "crc": 95171600}, {"key": "androidx/compose/foundation/pager/PagerMeasureResult.class", "name": "androidx/compose/foundation/pager/PagerMeasureResult.class", "size": 9153, "crc": 975342516}, {"key": "androidx/compose/foundation/pager/PagerScope.class", "name": "androidx/compose/foundation/pager/PagerScope.class", "size": 571, "crc": 1545279960}, {"key": "androidx/compose/foundation/pager/PagerScopeImpl.class", "name": "androidx/compose/foundation/pager/PagerScopeImpl.class", "size": 917, "crc": -2134330509}, {"key": "androidx/compose/foundation/pager/PagerScrollPosition.class", "name": "androidx/compose/foundation/pager/PagerScrollPosition.class", "size": 7957, "crc": -603779106}, {"key": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "name": "androidx/compose/foundation/pager/PagerScrollPositionKt.class", "size": 970, "crc": -165960811}, {"key": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "name": "androidx/compose/foundation/pager/PagerSemanticsKt.class", "size": 4116, "crc": 551003559}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance$Companion.class", "size": 1697, "crc": -2065587938}, {"key": "androidx/compose/foundation/pager/PagerSnapDistance.class", "name": "androidx/compose/foundation/pager/PagerSnapDistance.class", "size": 1088, "crc": -531765891}, {"key": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "name": "androidx/compose/foundation/pager/PagerSnapDistanceMaxPages.class", "size": 2524, "crc": -1411537122}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$1.class", "size": 1937, "crc": -1421643800}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3$1$3.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3$1$3.class", "size": 2839, "crc": -1403313243}, {"key": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3$1.class", "name": "androidx/compose/foundation/pager/PagerState$animateScrollToPage$3$1.class", "size": 7192, "crc": 1308741805}, {"key": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "name": "androidx/compose/foundation/pager/PagerState$remeasurementModifier$1.class", "size": 1441, "crc": 1330188640}, {"key": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "name": "androidx/compose/foundation/pager/PagerState$scroll$1.class", "size": 1940, "crc": -1865699276}, {"key": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$scrollToPage$2.class", "size": 5309, "crc": -1357073210}, {"key": "androidx/compose/foundation/pager/PagerState$scrollableState$1.class", "name": "androidx/compose/foundation/pager/PagerState$scrollableState$1.class", "size": 1543, "crc": -700233278}, {"key": "androidx/compose/foundation/pager/PagerState$settledPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$settledPage$2.class", "size": 1539, "crc": -436679811}, {"key": "androidx/compose/foundation/pager/PagerState$targetPage$2.class", "name": "androidx/compose/foundation/pager/PagerState$targetPage$2.class", "size": 2350, "crc": 128658356}, {"key": "androidx/compose/foundation/pager/PagerState.class", "name": "androidx/compose/foundation/pager/PagerState.class", "size": 40160, "crc": -142883161}, {"key": "androidx/compose/foundation/pager/PagerStateImpl$Companion$Saver$1.class", "name": "androidx/compose/foundation/pager/PagerStateImpl$Companion$Saver$1.class", "size": 2331, "crc": -807716895}, {"key": "androidx/compose/foundation/pager/PagerStateImpl$Companion$Saver$2$1.class", "name": "androidx/compose/foundation/pager/PagerStateImpl$Companion$Saver$2$1.class", "size": 1635, "crc": -55271653}, {"key": "androidx/compose/foundation/pager/PagerStateImpl$Companion$Saver$2.class", "name": "androidx/compose/foundation/pager/PagerStateImpl$Companion$Saver$2.class", "size": 2289, "crc": 2133408744}, {"key": "androidx/compose/foundation/pager/PagerStateImpl$Companion.class", "name": "androidx/compose/foundation/pager/PagerStateImpl$Companion.class", "size": 1364, "crc": 729444209}, {"key": "androidx/compose/foundation/pager/PagerStateImpl.class", "name": "androidx/compose/foundation/pager/PagerStateImpl.class", "size": 3836, "crc": 388470410}, {"key": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$EmptyLayoutInfo$1.class", "size": 1856, "crc": -89612651}, {"key": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$UnitDensity$1.class", "size": 1064, "crc": -1734928635}, {"key": "androidx/compose/foundation/pager/PagerStateKt$rememberPagerState$1$1.class", "name": "androidx/compose/foundation/pager/PagerStateKt$rememberPagerState$1$1.class", "size": 1822, "crc": -511977130}, {"key": "androidx/compose/foundation/pager/PagerStateKt.class", "name": "androidx/compose/foundation/pager/PagerStateKt.class", "size": 10744, "crc": 210258061}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$1.class", "size": 1877, "crc": -1255485247}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$2$1.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior$performFling$2$1.class", "size": 1768, "crc": 1831807904}, {"key": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "name": "androidx/compose/foundation/pager/PagerWrapperFlingBehavior.class", "size": 4060, "crc": -1601164835}, {"key": "androidx/compose/foundation/relocation/BringIntoViewChildNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewChildNode.class", "size": 3782, "crc": 563647026}, {"key": "androidx/compose/foundation/relocation/BringIntoViewKt$ModifierLocalBringIntoViewParent$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewKt$ModifierLocalBringIntoViewParent$1.class", "size": 1381, "crc": -2097144038}, {"key": "androidx/compose/foundation/relocation/BringIntoViewKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewKt.class", "size": 1601, "crc": 806359947}, {"key": "androidx/compose/foundation/relocation/BringIntoViewParent.class", "name": "androidx/compose/foundation/relocation/BringIntoViewParent.class", "size": 1228, "crc": -369544973}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequester.class", "size": 1637, "crc": 325197110}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterElement.class", "size": 3348, "crc": -2012555560}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl$bringIntoView$1.class", "size": 2058, "crc": 935337593}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterImpl.class", "size": 5222, "crc": -663626738}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterKt.class", "size": 1609, "crc": 899897853}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode$bringIntoView$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode$bringIntoView$2.class", "size": 2058, "crc": 35765177}, {"key": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewRequesterNode.class", "size": 4943, "crc": -2000623285}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder.class", "size": 1279, "crc": 1263128077}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderElement.class", "size": 3348, "crc": -1946541718}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderKt.class", "size": 2657, "crc": 1912710955}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1$1.class", "size": 2786, "crc": -1336779652}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$1.class", "size": 4747, "crc": 467432527}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2$2.class", "size": 4434, "crc": -562493690}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$2.class", "size": 5244, "crc": 1788976696}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$parentRect$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode$bringChildIntoView$parentRect$1.class", "size": 3472, "crc": -2018357453}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponderNode.class", "size": 6465, "crc": 980537667}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt$defaultBringIntoViewParent$1.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt$defaultBringIntoViewParent$1.class", "size": 3445, "crc": 1509751465}, {"key": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt.class", "name": "androidx/compose/foundation/relocation/BringIntoViewResponder_androidKt.class", "size": 1801, "crc": -429126388}, {"key": "androidx/compose/foundation/selection/SelectableGroupKt$selectableGroup$1.class", "name": "androidx/compose/foundation/selection/SelectableGroupKt$selectableGroup$1.class", "size": 1733, "crc": -946980790}, {"key": "androidx/compose/foundation/selection/SelectableGroupKt.class", "name": "androidx/compose/foundation/selection/SelectableGroupKt.class", "size": 1239, "crc": -1917644351}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable$2.class", "size": 6249, "crc": 1747908225}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable$4$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable$4$1.class", "size": 1942, "crc": -18402583}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "size": 3878, "crc": 789929400}, {"key": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/SelectableKt$selectable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3380, "crc": 553602387}, {"key": "androidx/compose/foundation/selection/SelectableKt.class", "name": "androidx/compose/foundation/selection/SelectableKt.class", "size": 6494, "crc": -1077438411}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable$2.class", "size": 6295, "crc": 102401106}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable$4$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable$4$1.class", "size": 1858, "crc": -1221025708}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "size": 3876, "crc": 185973136}, {"key": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$toggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3378, "crc": 219273644}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$2.class", "size": 6604, "crc": -1152161368}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$4$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable$4$1.class", "size": 2138, "crc": 2076822933}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-O2vRcR0$$inlined$debugInspectorInfo$1.class", "size": 4047, "crc": 2005534320}, {"key": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/selection/ToggleableKt$triStateToggleable-XHw0xAI$$inlined$debugInspectorInfo$1.class", "size": 3551, "crc": 617258202}, {"key": "androidx/compose/foundation/selection/ToggleableKt.class", "name": "androidx/compose/foundation/selection/ToggleableKt.class", "size": 10670, "crc": 302144562}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShape.class", "size": 5407, "crc": -2126291256}, {"key": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteCutCornerShapeKt.class", "size": 4801, "crc": -1039873108}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShape.class", "size": 5290, "crc": -1643823939}, {"key": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/AbsoluteRoundedCornerShapeKt.class", "size": 4905, "crc": 1667286297}, {"key": "androidx/compose/foundation/shape/CornerBasedShape.class", "name": "androidx/compose/foundation/shape/CornerBasedShape.class", "size": 5396, "crc": -1120922100}, {"key": "androidx/compose/foundation/shape/CornerSize.class", "name": "androidx/compose/foundation/shape/CornerSize.class", "size": 839, "crc": 655596887}, {"key": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "name": "androidx/compose/foundation/shape/CornerSizeKt$ZeroCornerSize$1.class", "size": 1735, "crc": -1393355637}, {"key": "androidx/compose/foundation/shape/CornerSizeKt.class", "name": "androidx/compose/foundation/shape/CornerSizeKt.class", "size": 2027, "crc": -464488047}, {"key": "androidx/compose/foundation/shape/CutCornerShape.class", "name": "androidx/compose/foundation/shape/CutCornerShape.class", "size": 5449, "crc": -260874621}, {"key": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "name": "androidx/compose/foundation/shape/CutCornerShapeKt.class", "size": 4589, "crc": 581671918}, {"key": "androidx/compose/foundation/shape/DpCornerSize.class", "name": "androidx/compose/foundation/shape/DpCornerSize.class", "size": 3873, "crc": 1293079385}, {"key": "androidx/compose/foundation/shape/GenericShape.class", "name": "androidx/compose/foundation/shape/GenericShape.class", "size": 3622, "crc": -1474629526}, {"key": "androidx/compose/foundation/shape/PercentCornerSize.class", "name": "androidx/compose/foundation/shape/PercentCornerSize.class", "size": 3271, "crc": 2130740982}, {"key": "androidx/compose/foundation/shape/PxCornerSize.class", "name": "androidx/compose/foundation/shape/PxCornerSize.class", "size": 2806, "crc": -1714102328}, {"key": "androidx/compose/foundation/shape/RoundedCornerShape.class", "name": "androidx/compose/foundation/shape/RoundedCornerShape.class", "size": 5332, "crc": 1624363461}, {"key": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "name": "androidx/compose/foundation/shape/RoundedCornerShapeKt.class", "size": 4966, "crc": -2120916431}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$1$1.class", "size": 1114, "crc": -400082725}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$2.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$2.class", "size": 3282, "crc": 398317128}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$3.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$CursorHandle$3.class", "size": 2265, "crc": 1636848168}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$DefaultCursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$DefaultCursorHandle$1.class", "size": 1807, "crc": 1497756452}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1$1.class", "size": 5106, "crc": 1830281206}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1$1$1.class", "size": 2920, "crc": -1117151886}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt$drawCursorHandle$1.class", "size": 5819, "crc": 1184766252}, {"key": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "name": "androidx/compose/foundation/text/AndroidCursorHandle_androidKt.class", "size": 8550, "crc": 626392369}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2$1.class", "size": 3646, "crc": 493341946}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$1$2.class", "size": 4506, "crc": -1290650786}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$2.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt$InlineChildren$2.class", "size": 2518, "crc": -479507874}, {"key": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "name": "androidx/compose/foundation/text/AnnotatedStringResolveInlineContentKt.class", "size": 12862, "crc": -1872743395}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$1.class", "size": 1895, "crc": -2145195325}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$10.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$10.class", "size": 1896, "crc": 1536066977}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$12.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$12.class", "size": 4938, "crc": -1768071005}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$13.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$13.class", "size": 1925, "crc": -1266319068}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$15.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$15.class", "size": 5112, "crc": 1088909800}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$3$1.class", "size": 2805, "crc": 1102881150}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$4$1.class", "size": 3451, "crc": -1915867644}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$5.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$5.class", "size": 4989, "crc": -304067304}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$6.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$6.class", "size": 1924, "crc": 1876570562}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$8$1.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$8$1.class", "size": 2600, "crc": 1159089190}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$9.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt$BasicTextField$9.class", "size": 5163, "crc": -577704834}, {"key": "androidx/compose/foundation/text/BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/BasicTextFieldKt.class", "size": 35576, "crc": 23923491}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$1.class", "size": 2912, "crc": -993483591}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$3$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$3$1.class", "size": 2166, "crc": 1198076033}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$4$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$4$1.class", "size": 1952, "crc": -1287545709}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$5.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$5.class", "size": 3296, "crc": -1817365553}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$6.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$6.class", "size": 2633, "crc": 909385535}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$7.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$7.class", "size": 3017, "crc": 212038413}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$8.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$8.class", "size": 2686, "crc": -1470568601}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$9.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$9.class", "size": 3070, "crc": -851113015}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$1.class", "size": 1726, "crc": -929816540}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$2.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText$selectionController$selectableId$2.class", "size": 1765, "crc": 1247318103}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText-RWo7tUw$$inlined$Layout$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText-RWo7tUw$$inlined$Layout$1.class", "size": 2290, "crc": -492741829}, {"key": "androidx/compose/foundation/text/BasicTextKt$BasicText-VhcvRP8$$inlined$Layout$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$BasicText-VhcvRP8$$inlined$Layout$1.class", "size": 2251, "crc": -473883037}, {"key": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$1.class", "name": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$1.class", "size": 2189, "crc": -749984345}, {"key": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$2.class", "name": "androidx/compose/foundation/text/BasicTextKt$selectionIdSaver$2.class", "size": 1507, "crc": 223703758}, {"key": "androidx/compose/foundation/text/BasicTextKt.class", "name": "androidx/compose/foundation/text/BasicTextKt.class", "size": 37969, "crc": -782042833}, {"key": "androidx/compose/foundation/text/BasicText_androidKt.class", "name": "androidx/compose/foundation/text/BasicText_androidKt.class", "size": 1423, "crc": -159853231}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$1.class", "size": 1630, "crc": 421170003}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$2$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$2$1.class", "size": 2417, "crc": 764893729}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$3.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$3.class", "size": 3014, "crc": -1594042799}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$4.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$4.class", "size": 1662, "crc": 233277509}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$5$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$5$1.class", "size": 2449, "crc": -1675209422}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$6.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$6.class", "size": 3202, "crc": -1599379358}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1$1.class", "size": 2896, "crc": 512838821}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$1.class", "size": 4986, "crc": -794247771}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$2.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1$2.class", "size": 2271, "crc": 402895877}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pointerInputModifier$1.class", "size": 5763, "crc": -562926390}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1$1.class", "size": 2507, "crc": -1751332169}, {"key": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "name": "androidx/compose/foundation/text/ClickableTextKt$ClickableText$pressIndicator$1$1.class", "size": 4832, "crc": 401766412}, {"key": "androidx/compose/foundation/text/ClickableTextKt.class", "name": "androidx/compose/foundation/text/ClickableTextKt.class", "size": 18280, "crc": -180796763}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-1$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-1$1.class", "size": 3178, "crc": 1527318929}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-2$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-2$1.class", "size": 3179, "crc": 1663762420}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-3$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-3$1.class", "size": 3179, "crc": 1642273287}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-4$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt$lambda-4$1.class", "size": 3179, "crc": -1122009511}, {"key": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$BasicTextFieldKt.class", "size": 2849, "crc": 2099330323}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda-1$1.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt$lambda-1$1.class", "size": 3172, "crc": -171958636}, {"key": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/ComposableSingletons$CoreTextFieldKt.class", "size": 1749, "crc": 1837027147}, {"key": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "name": "androidx/compose/foundation/text/ContextMenu_androidKt.class", "size": 2800, "crc": 1441077528}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$1.class", "size": 1910, "crc": -388568146}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$1.class", "size": 1653, "crc": 1349183026}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2$2.class", "size": 3249, "crc": 1182505464}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$2.class", "size": 6079, "crc": 660993200}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3$invoke$$inlined$onDispose$1.class", "size": 2265, "crc": -39824458}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$3.class", "size": 3300, "crc": -1265692204}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4$invoke$$inlined$onDispose$1.class", "size": 1942, "crc": -1091221929}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$4.class", "size": 4717, "crc": -756257316}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2$measure$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2$measure$2.class", "size": 2021, "crc": -595844921}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1$2.class", "size": 9452, "crc": -707865514}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$1.class", "size": 10398, "crc": 940832069}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$coreTextFieldModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1$coreTextFieldModifier$1.class", "size": 1659, "crc": 155109154}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5$1.class", "size": 7846, "crc": 491383232}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$5.class", "size": 7361, "crc": 931151681}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$6.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$6.class", "size": 5121, "crc": -384077698}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$decorationBoxModifier$1.class", "size": 2428, "crc": -308021207}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$drawModifier$1.class", "size": 4849, "crc": 278026413}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1$1$1.class", "size": 5106, "crc": 1376378309}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$focusModifier$1.class", "size": 5335, "crc": -1261576679}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$onPositionedModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$onPositionedModifier$1.class", "size": 5767, "crc": -1238666744}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$1.class", "size": 2010, "crc": -1615787923}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$pointerModifier$2.class", "size": 4864, "crc": -907480943}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$scrollerPosition$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$scrollerPosition$1$1.class", "size": 2142, "crc": -1334209833}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$1.class", "size": 2351, "crc": 395792972}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$10.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$10.class", "size": 1637, "crc": -1375074003}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$2.class", "size": 4045, "crc": 1071143098}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$3.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$3.class", "size": 4655, "crc": 1362355105}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$4.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$4.class", "size": 3987, "crc": 1208459109}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$5.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$5.class", "size": 2040, "crc": 992129538}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$6.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$6.class", "size": 1918, "crc": -390343657}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$7.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$7.class", "size": 1749, "crc": 131944652}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$8.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$8.class", "size": 1735, "crc": 1790805642}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$9.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1$9.class", "size": 1633, "crc": -2047888624}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextField$semanticsModifier$1.class", "size": 7201, "crc": 1186628521}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextFieldRootBox$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$CoreTextFieldRootBox$2.class", "size": 2528, "crc": -1347280357}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$SelectionToolbarAndHandles$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$SelectionToolbarAndHandles$2.class", "size": 1983, "crc": -393815423}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1$1.class", "size": 4060, "crc": -1768027863}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1$2$1.class", "size": 1902, "crc": -1137691050}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1$2.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1$2.class", "size": 4427, "crc": -1784180338}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1$1.class", "size": 4861, "crc": -46681289}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$1.class", "size": 4468, "crc": 668668593}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$2$1.class", "size": 2489, "crc": 1566353946}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$3.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$TextFieldCursorHandle$3.class", "size": 1876, "crc": 823143653}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt$previewKeyEventToDeselectOnBack$1.class", "size": 2883, "crc": 1094070816}, {"key": "androidx/compose/foundation/text/CoreTextFieldKt.class", "name": "androidx/compose/foundation/text/CoreTextFieldKt.class", "size": 63154, "crc": -1119897126}, {"key": "androidx/compose/foundation/text/DeadKeyCombiner.class", "name": "androidx/compose/foundation/text/DeadKeyCombiner.class", "size": 2458, "crc": 1462998632}, {"key": "androidx/compose/foundation/text/EmptyMeasurePolicy$placementBlock$1.class", "name": "androidx/compose/foundation/text/EmptyMeasurePolicy$placementBlock$1.class", "size": 1596, "crc": -21472003}, {"key": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "name": "androidx/compose/foundation/text/EmptyMeasurePolicy.class", "size": 2736, "crc": 821965509}, {"key": "androidx/compose/foundation/text/FixedMotionDurationScale.class", "name": "androidx/compose/foundation/text/FixedMotionDurationScale.class", "size": 3092, "crc": 1014837052}, {"key": "androidx/compose/foundation/text/Handle.class", "name": "androidx/compose/foundation/text/Handle.class", "size": 1470, "crc": 1571815104}, {"key": "androidx/compose/foundation/text/HandleState.class", "name": "androidx/compose/foundation/text/HandleState.class", "size": 1487, "crc": -541826596}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$$inlined$debugInspectorInfo$1.class", "size": 3249, "crc": -647981552}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt$heightInLines$2.class", "size": 13001, "crc": 1955305396}, {"key": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "name": "androidx/compose/foundation/text/HeightInLinesModifierKt.class", "size": 3925, "crc": 1253726740}, {"key": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier$measure$1.class", "name": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier$measure$1.class", "size": 4274, "crc": 1254815517}, {"key": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/HorizontalScrollLayoutModifier.class", "size": 7288, "crc": 247059333}, {"key": "androidx/compose/foundation/text/InlineTextContent.class", "name": "androidx/compose/foundation/text/InlineTextContent.class", "size": 1996, "crc": -200937882}, {"key": "androidx/compose/foundation/text/InlineTextContentKt.class", "name": "androidx/compose/foundation/text/InlineTextContentKt.class", "size": 1998, "crc": -268152536}, {"key": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "name": "androidx/compose/foundation/text/InternalFoundationTextApi.class", "size": 1163, "crc": -1035015864}, {"key": "androidx/compose/foundation/text/KeyCommand.class", "name": "androidx/compose/foundation/text/KeyCommand.class", "size": 4793, "crc": 1929223742}, {"key": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "name": "androidx/compose/foundation/text/KeyEventHelpers_androidKt.class", "size": 1392, "crc": -851180252}, {"key": "androidx/compose/foundation/text/KeyMapping.class", "name": "androidx/compose/foundation/text/KeyMapping.class", "size": 848, "crc": 1673313972}, {"key": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$commonKeyMapping$1.class", "size": 4741, "crc": 433378948}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$1.class", "size": 1409, "crc": 2120127964}, {"key": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "name": "androidx/compose/foundation/text/KeyMappingKt$defaultKeyMapping$2$1.class", "size": 3286, "crc": -1397966996}, {"key": "androidx/compose/foundation/text/KeyMappingKt.class", "name": "androidx/compose/foundation/text/KeyMappingKt.class", "size": 2026, "crc": 1915098089}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt$platformDefaultKeyMapping$1.class", "size": 2612, "crc": -820882214}, {"key": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "name": "androidx/compose/foundation/text/KeyMapping_androidKt.class", "size": 968, "crc": 1770674040}, {"key": "androidx/compose/foundation/text/KeyboardActionRunner.class", "name": "androidx/compose/foundation/text/KeyboardActionRunner.class", "size": 4707, "crc": 1616293432}, {"key": "androidx/compose/foundation/text/KeyboardActionScope.class", "name": "androidx/compose/foundation/text/KeyboardActionScope.class", "size": 626, "crc": 649920333}, {"key": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardActions$Companion.class", "size": 1338, "crc": -302763869}, {"key": "androidx/compose/foundation/text/KeyboardActions.class", "name": "androidx/compose/foundation/text/KeyboardActions.class", "size": 5452, "crc": 255322477}, {"key": "androidx/compose/foundation/text/KeyboardActionsKt.class", "name": "androidx/compose/foundation/text/KeyboardActionsKt.class", "size": 1452, "crc": 165450875}, {"key": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "name": "androidx/compose/foundation/text/KeyboardOptions$Companion.class", "size": 1338, "crc": -1488773156}, {"key": "androidx/compose/foundation/text/KeyboardOptions.class", "name": "androidx/compose/foundation/text/KeyboardOptions.class", "size": 8699, "crc": 2034702710}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$1.class", "size": 4155, "crc": -1897753269}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2$2.class", "size": 4152, "crc": 2049367686}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDownAndDragGesturesWithObserver$2.class", "size": 4712, "crc": -1621994748}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$2.class", "size": 1885, "crc": -1494423069}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$3.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$3.class", "size": 1599, "crc": -233424335}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$4.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$4.class", "size": 1601, "crc": 1036111358}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$5.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesAfterLongPressWithObserver$5.class", "size": 2294, "crc": 757417382}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$2.class", "size": 1843, "crc": -1991008537}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$3.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$3.class", "size": 1557, "crc": -1244337127}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$4.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$4.class", "size": 1559, "crc": -1770940966}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$5.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectDragGesturesWithObserver$5.class", "size": 2252, "crc": -1306979310}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt$detectPreDragGesturesWithObserver$2.class", "size": 7287, "crc": 996770345}, {"key": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "name": "androidx/compose/foundation/text/LongPressTextDragObserverKt.class", "size": 5063, "crc": -968341614}, {"key": "androidx/compose/foundation/text/MappedKeys.class", "name": "androidx/compose/foundation/text/MappedKeys.class", "size": 4712, "crc": -1000557039}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2$1.class", "size": 6888, "crc": -615670272}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt$detectMoves$2.class", "size": 5621, "crc": 1327377946}, {"key": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "name": "androidx/compose/foundation/text/PointerMoveDetectorKt.class", "size": 2665, "crc": 2013361639}, {"key": "androidx/compose/foundation/text/StringHelpersKt.class", "name": "androidx/compose/foundation/text/StringHelpersKt.class", "size": 1466, "crc": -148040455}, {"key": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "name": "androidx/compose/foundation/text/StringHelpers_androidKt.class", "size": 3020, "crc": -1560933637}, {"key": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "name": "androidx/compose/foundation/text/StringHelpers_jvmKt.class", "size": 913, "crc": -1354221363}, {"key": "androidx/compose/foundation/text/TextDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextDelegate$Companion.class", "size": 1417, "crc": -188128216}, {"key": "androidx/compose/foundation/text/TextDelegate.class", "name": "androidx/compose/foundation/text/TextDelegate.class", "size": 13343, "crc": 1227812948}, {"key": "androidx/compose/foundation/text/TextDelegateKt.class", "name": "androidx/compose/foundation/text/TextDelegateKt.class", "size": 4542, "crc": 885154660}, {"key": "androidx/compose/foundation/text/TextDragObserver.class", "name": "androidx/compose/foundation/text/TextDragObserver.class", "size": 859, "crc": -260211268}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1$1.class", "size": 4373, "crc": 907203431}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$1.class", "size": 4140, "crc": 1546652493}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$2.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1$2.class", "size": 4885, "crc": -563788257}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursor$1.class", "size": 7378, "crc": -1888592231}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt$cursorAnimationSpec$1.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt$cursorAnimationSpec$1.class", "size": 2175, "crc": 1711397507}, {"key": "androidx/compose/foundation/text/TextFieldCursorKt.class", "name": "androidx/compose/foundation/text/TextFieldCursorKt.class", "size": 4189, "crc": 1736918630}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion$restartInput$1.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion$restartInput$1.class", "size": 3217, "crc": -23791042}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion$updateTextLayoutResult$1$1$1.class", "size": 2310, "crc": -1818148440}, {"key": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "name": "androidx/compose/foundation/text/TextFieldDelegate$Companion.class", "size": 17931, "crc": -781014126}, {"key": "androidx/compose/foundation/text/TextFieldDelegate.class", "name": "androidx/compose/foundation/text/TextFieldDelegate.class", "size": 6949, "crc": 60399787}, {"key": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "name": "androidx/compose/foundation/text/TextFieldDelegateKt.class", "size": 3163, "crc": -1258189123}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt$interceptDPadAndMoveFocus$1.class", "size": 3678, "crc": 2119687735}, {"key": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldFocusModifier_androidKt.class", "size": 2245, "crc": 1444288063}, {"key": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "name": "androidx/compose/foundation/text/TextFieldGestureModifiersKt.class", "size": 2239, "crc": 1159978686}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$1.class", "size": 1999, "crc": -932111950}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$1.class", "size": 1731, "crc": 1234926106}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$2.class", "size": 1733, "crc": -1690909803}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$3.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$3.class", "size": 2186, "crc": -1994280769}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$4.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$4.class", "size": 2263, "crc": 108793900}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$5.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$5.class", "size": 2395, "crc": -320813630}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$6.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$6.class", "size": 2391, "crc": 1971019431}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$7.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$7.class", "size": 2394, "crc": 1423010913}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$8.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$8.class", "size": 2392, "crc": 1656133632}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2$WhenMappings.class", "size": 3290, "crc": 1340380974}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput$process$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput$process$2.class", "size": 8388, "crc": 1553701708}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput.class", "size": 13504, "crc": 1803381750}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$1.class", "size": 1942, "crc": -24094204}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2$1.class", "size": 1917, "crc": 481054819}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt$textFieldKeyInput$2.class", "size": 8094, "crc": -926362663}, {"key": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInputKt.class", "size": 3840, "crc": -710764762}, {"key": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "name": "androidx/compose/foundation/text/TextFieldKeyInput_androidKt.class", "size": 948, "crc": 1591826758}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1$invoke$$inlined$onDispose$1.class", "size": 3454, "crc": 1956347873}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$1$1.class", "size": 3902, "crc": -22817151}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$1.class", "size": 5725, "crc": -1917920339}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1$2.class", "size": 5662, "crc": -519643664}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$1.class", "size": 5792, "crc": -1263859223}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2$2.class", "size": 2121, "crc": 1135589773}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1$2.class", "size": 5744, "crc": 60538721}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt$tapPressTextFieldModifier$1.class", "size": 9836, "crc": -22806584}, {"key": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "name": "androidx/compose/foundation/text/TextFieldPressGestureFilterKt.class", "size": 2399, "crc": 1094592915}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$WhenMappings.class", "size": 864, "crc": 470325334}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$$inlined$debugInspectorInfo$1.class", "size": 3533, "crc": 1370357218}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$scrollableState$1$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$scrollableState$1$1.class", "size": 2001, "crc": 1171154149}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollBackward$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollBackward$2.class", "size": 1863, "crc": 977121718}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollForward$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1$canScrollForward$2.class", "size": 1890, "crc": -304203415}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2$wrappedScrollableState$1$1.class", "size": 5018, "crc": 687243942}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt$textFieldScrollable$2.class", "size": 8532, "crc": -1816054524}, {"key": "androidx/compose/foundation/text/TextFieldScrollKt.class", "name": "androidx/compose/foundation/text/TextFieldScrollKt.class", "size": 8532, "crc": -1507691308}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$1.class", "size": 2616, "crc": 3238806}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$2.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion$Saver$2.class", "size": 2480, "crc": -976523900}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition$Companion.class", "size": 1438, "crc": -1587895236}, {"key": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "name": "androidx/compose/foundation/text/TextFieldScrollerPosition.class", "size": 8775, "crc": 1782502601}, {"key": "androidx/compose/foundation/text/TextFieldSize.class", "name": "androidx/compose/foundation/text/TextFieldSize.class", "size": 4422, "crc": 60909145}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1$1.class", "size": 2083, "crc": -1368248103}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1$1.class", "size": 3819, "crc": 2072142619}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt$textFieldMinSize$1.class", "size": 10594, "crc": 759994839}, {"key": "androidx/compose/foundation/text/TextFieldSizeKt.class", "name": "androidx/compose/foundation/text/TextFieldSizeKt.class", "size": 1335, "crc": 771083769}, {"key": "androidx/compose/foundation/text/TextFieldState$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text/TextFieldState$onImeActionPerformed$1.class", "size": 1912, "crc": -702310097}, {"key": "androidx/compose/foundation/text/TextFieldState$onValueChange$1.class", "name": "androidx/compose/foundation/text/TextFieldState$onValueChange$1.class", "size": 2658, "crc": 1159163646}, {"key": "androidx/compose/foundation/text/TextFieldState$onValueChangeOriginal$1.class", "name": "androidx/compose/foundation/text/TextFieldState$onValueChangeOriginal$1.class", "size": 1669, "crc": 2112856623}, {"key": "androidx/compose/foundation/text/TextFieldState.class", "name": "androidx/compose/foundation/text/TextFieldState.class", "size": 17383, "crc": 1352502002}, {"key": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "name": "androidx/compose/foundation/text/TextLayoutHelperKt.class", "size": 4556, "crc": -288950234}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxy.class", "size": 7283, "crc": -1879529417}, {"key": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "name": "androidx/compose/foundation/text/TextLayoutResultProxyKt.class", "size": 1345, "crc": 1532909031}, {"key": "androidx/compose/foundation/text/TextMeasurePolicy$measure$1.class", "name": "androidx/compose/foundation/text/TextMeasurePolicy$measure$1.class", "size": 3558, "crc": -2017740626}, {"key": "androidx/compose/foundation/text/TextMeasurePolicy.class", "name": "androidx/compose/foundation/text/TextMeasurePolicy.class", "size": 5758, "crc": -150476272}, {"key": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "name": "androidx/compose/foundation/text/TextPointerIcon_androidKt.class", "size": 910, "crc": 40501581}, {"key": "androidx/compose/foundation/text/TouchMode_androidKt.class", "name": "androidx/compose/foundation/text/TouchMode_androidKt.class", "size": 789, "crc": 1838541796}, {"key": "androidx/compose/foundation/text/UndoManager$Entry.class", "name": "androidx/compose/foundation/text/UndoManager$Entry.class", "size": 2044, "crc": 838734806}, {"key": "androidx/compose/foundation/text/UndoManager.class", "name": "androidx/compose/foundation/text/UndoManager.class", "size": 4781, "crc": -1593314768}, {"key": "androidx/compose/foundation/text/UndoManagerKt.class", "name": "androidx/compose/foundation/text/UndoManagerKt.class", "size": 584, "crc": -937151512}, {"key": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "name": "androidx/compose/foundation/text/UndoManager_jvmKt.class", "size": 496, "crc": 110414890}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMapping.class", "size": 2679, "crc": 331959468}, {"key": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "name": "androidx/compose/foundation/text/ValidatingOffsetMappingKt.class", "size": 2474, "crc": 741993049}, {"key": "androidx/compose/foundation/text/VerticalScrollLayoutModifier$measure$1.class", "name": "androidx/compose/foundation/text/VerticalScrollLayoutModifier$measure$1.class", "size": 3989, "crc": 655894893}, {"key": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "name": "androidx/compose/foundation/text/VerticalScrollLayoutModifier.class", "size": 7147, "crc": 231048513}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity$Companion.class", "size": 1158, "crc": 587607233}, {"key": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "name": "androidx/compose/foundation/text/modifiers/InlineDensity.class", "size": 4785, "crc": 1585167979}, {"key": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/LayoutUtilsKt.class", "size": 3325, "crc": -1928995422}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer$Companion.class", "size": 3915, "crc": 2128950906}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainer.class", "size": 5680, "crc": -1445803488}, {"key": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "name": "androidx/compose/foundation/text/modifiers/MinLinesConstrainerKt.class", "size": 1311, "crc": -1201005953}, {"key": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "name": "androidx/compose/foundation/text/modifiers/ModifierUtilsKt.class", "size": 1363, "crc": -953450038}, {"key": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/MultiParagraphLayoutCache.class", "size": 14413, "crc": -1208390548}, {"key": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "name": "androidx/compose/foundation/text/modifiers/ParagraphLayoutCache.class", "size": 15276, "crc": -233133262}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringElement.class", "size": 14209, "crc": 1184218163}, {"key": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/SelectableTextAnnotatedStringNode.class", "size": 11050, "crc": -1146653848}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$modifier$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$modifier$1.class", "size": 1851, "crc": 1826738369}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$1.class", "size": 1739, "crc": -1092309683}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$2.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController$onRemembered$2.class", "size": 1729, "crc": 2079288813}, {"key": "androidx/compose/foundation/text/modifiers/SelectionController.class", "name": "androidx/compose/foundation/text/modifiers/SelectionController.class", "size": 10835, "crc": 484627901}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$longPressDragObserver$1.class", "size": 5318, "crc": 1717868565}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt$makeSelectionModifier$mouseSelectionObserver$1.class", "size": 5812, "crc": 689177019}, {"key": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "name": "androidx/compose/foundation/text/modifiers/SelectionControllerKt.class", "size": 2638, "crc": 954799116}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams$Companion.class", "size": 1302, "crc": 813632573}, {"key": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "name": "androidx/compose/foundation/text/modifiers/StaticTextSelectionParams.class", "size": 4216, "crc": -65334619}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringElement.class", "size": 9452, "crc": -1037863524}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$TextSubstitutionValue.class", "size": 5652, "crc": -2088228019}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$1.class", "size": 5528, "crc": 1307110311}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$2.class", "size": 2214, "crc": -4850945}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$3.class", "size": 2668, "crc": -407324744}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$applySemantics$4.class", "size": 2153, "crc": -221878717}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$measure$1.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode$measure$1.class", "size": 2005, "crc": 1110425246}, {"key": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "name": "androidx/compose/foundation/text/modifiers/TextAnnotatedStringNode.class", "size": 30064, "crc": -97948131}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleElement.class", "size": 6195, "crc": 2136090242}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$TextSubstitutionValue.class", "size": 5271, "crc": -445128439}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$1.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$1.class", "size": 4152, "crc": 1007944944}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$2.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$2.class", "size": 2215, "crc": -1854031425}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$3.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$3.class", "size": 2641, "crc": 738686149}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$4.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$applySemantics$4.class", "size": 2135, "crc": -1532555105}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$measure$1.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode$measure$1.class", "size": 1993, "crc": -2019857361}, {"key": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "name": "androidx/compose/foundation/text/modifiers/TextStringSimpleNode.class", "size": 24457, "crc": 130333855}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$HandlePopup$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$HandlePopup$1.class", "size": 2685, "crc": 838810642}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$1.class", "size": 3337, "crc": 1023019807}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$2.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1$2.class", "size": 1829, "crc": 1800164371}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1$1.class", "size": 3734, "crc": -758900819}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$1.class", "size": 4075, "crc": 806332287}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$2.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandle$2.class", "size": 2411, "crc": -1416583443}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandleIcon$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$SelectionHandleIcon$1.class", "size": 2221, "crc": 98076334}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1$1.class", "size": 5793, "crc": -1480099549}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1$1$1.class", "size": 3328, "crc": -1324133410}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt$drawSelectionHandle$1.class", "size": 6271, "crc": -1487414577}, {"key": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "name": "androidx/compose/foundation/text/selection/AndroidSelectionHandles_androidKt.class", "size": 18609, "crc": 1298734616}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection$Companion.class", "size": 976, "crc": 667194799}, {"key": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/BaseTextPreparedSelection.class", "size": 26148, "crc": 791896488}, {"key": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "name": "androidx/compose/foundation/text/selection/BoundaryFunction.class", "size": 851, "crc": -2107479053}, {"key": "androidx/compose/foundation/text/selection/ClicksCounter.class", "name": "androidx/compose/foundation/text/selection/ClicksCounter.class", "size": 3103, "crc": 588688124}, {"key": "androidx/compose/foundation/text/selection/CrossStatus.class", "name": "androidx/compose/foundation/text/selection/CrossStatus.class", "size": 1557, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/Direction.class", "name": "androidx/compose/foundation/text/selection/Direction.class", "size": 1531, "crc": 638033327}, {"key": "androidx/compose/foundation/text/selection/HandleImageCache.class", "name": "androidx/compose/foundation/text/selection/HandleImageCache.class", "size": 2166, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/HandlePositionProvider$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/HandlePositionProvider$WhenMappings.class", "size": 1001, "crc": -959807309}, {"key": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "name": "androidx/compose/foundation/text/selection/HandlePositionProvider.class", "size": 4974, "crc": -93396968}, {"key": "androidx/compose/foundation/text/selection/HandleReferencePoint.class", "name": "androidx/compose/foundation/text/selection/HandleReferencePoint.class", "size": 1624, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "name": "androidx/compose/foundation/text/selection/MouseSelectionObserver.class", "size": 1212, "crc": 772692383}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout$WhenMappings.class", "size": 957, "crc": 425878060}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout$createSubSelections$2$1.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout$createSubSelections$2$1.class", "size": 2685, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/MultiSelectionLayout.class", "size": 14267, "crc": -369446908}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegate.class", "size": 9911, "crc": 893778906}, {"key": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/MultiWidgetSelectionDelegateKt.class", "size": 8688, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/OffsetProvider.class", "name": "androidx/compose/foundation/text/selection/OffsetProvider.class", "size": 567, "crc": -582957961}, {"key": "androidx/compose/foundation/text/selection/Selectable.class", "name": "androidx/compose/foundation/text/selection/Selectable.class", "size": 2243, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectableInfo.class", "name": "androidx/compose/foundation/text/selection/SelectableInfo.class", "size": 5861, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "name": "androidx/compose/foundation/text/selection/Selection$AnchorInfo.class", "size": 3613, "crc": -90193809}, {"key": "androidx/compose/foundation/text/selection/Selection.class", "name": "androidx/compose/foundation/text/selection/Selection.class", "size": 4883, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Paragraph$1$1.class", "size": 1679, "crc": -338039184}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion$Word$1$1.class", "size": 1642, "crc": -1458849618}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment$Companion.class", "size": 6295, "crc": 1303738863}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustment.class", "size": 1213, "crc": -346189254}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$anchorSnappedToWordBoundary$2.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$anchorSnappedToWordBoundary$2.class", "size": 3352, "crc": -726325880}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$currentRawLine$2.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt$updateSelectionBoundary$currentRawLine$2.class", "size": 2166, "crc": 818413045}, {"key": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "name": "androidx/compose/foundation/text/selection/SelectionAdjustmentKt.class", "size": 11406, "crc": -1662977764}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$DisableSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$DisableSelection$1.class", "size": 2027, "crc": -2119484539}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$1$1.class", "size": 2159, "crc": 1496121375}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$2.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$2.class", "size": 2258, "crc": 59755097}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$1.class", "size": 4149, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$1.class", "size": 2040, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1$1$1$positionProvider$1$2.class", "size": 2038, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1$1.class", "size": 9596, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$3$1.class", "size": 4142, "crc": 665620204}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4$invoke$$inlined$onDispose$1.class", "size": 2380, "crc": 146826065}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$4.class", "size": 3156, "crc": 192413611}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$5.class", "size": 2901, "crc": -192906268}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$registrarImpl$1.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$SelectionContainer$registrarImpl$1.class", "size": 1730, "crc": 18177994}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt$sam$androidx_compose_foundation_text_selection_OffsetProvider$0.class", "size": 1876, "crc": -**********}, {"key": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionContainerKt.class", "size": 15846, "crc": 311358175}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$awaitDown$1.class", "size": 1693, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$1.class", "size": 1959, "crc": 828187008}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$1.class", "size": 2250, "crc": **********}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$mouseSelection$shouldConsumeUp$2.class", "size": 2499, "crc": 1846821484}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1$1.class", "size": 7545, "crc": -437197801}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$selectionGestureInput$1.class", "size": 5039, "crc": -2083492710}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$1.class", "size": 1919, "crc": -300264561}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$2.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$touchSelection$2.class", "size": 2165, "crc": 304021470}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1$1.class", "size": 4701, "crc": -293047590}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt$updateSelectionTouchMode$1.class", "size": 4108, "crc": -1591693923}, {"key": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionGesturesKt.class", "size": 16278, "crc": -2111142178}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleAnchor.class", "size": 1606, "crc": 114001788}, {"key": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "name": "androidx/compose/foundation/text/selection/SelectionHandleInfo.class", "size": 4842, "crc": -117940202}, {"key": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "name": "androidx/compose/foundation/text/selection/SelectionHandlesKt.class", "size": 3057, "crc": 2017541519}, {"key": "androidx/compose/foundation/text/selection/SelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SelectionLayout.class", "size": 2594, "crc": -1420301855}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder$WhenMappings.class", "size": 941, "crc": -1137309090}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutBuilder.class", "size": 8016, "crc": -727354154}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt$WhenMappings.class", "size": 931, "crc": 800183188}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt$isCollapsed$1.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt$isCollapsed$1.class", "size": 2056, "crc": 1782379126}, {"key": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SelectionLayoutKt.class", "size": 6471, "crc": -545296248}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$1.class", "size": 2046, "crc": 12623910}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$2.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$UnspecifiedSafeOffsetVectorConverter$2.class", "size": 1881, "crc": 1910300747}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1$1$1.class", "size": 1781, "crc": 262962070}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$animatedSelectionMagnifier$1.class", "size": 6363, "crc": -562252887}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$1.class", "size": 1847, "crc": -770969838}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$2$1.class", "size": 4716, "crc": -1469219777}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1$2.class", "size": 3516, "crc": 2030297439}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt$rememberAnimatedMagnifierPosition$1.class", "size": 5078, "crc": 399570252}, {"key": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "name": "androidx/compose/foundation/text/selection/SelectionMagnifierKt.class", "size": 10449, "crc": -1974437262}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$1.class", "size": 2078, "crc": 1321726022}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$2.class", "size": 4110, "crc": 1080377652}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$3.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$3.class", "size": 3110, "crc": 406785671}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$4.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$4.class", "size": 3351, "crc": 1046656943}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$5.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$5.class", "size": 1698, "crc": 962173032}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$6.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$6.class", "size": 2112, "crc": -2005324920}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$7.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$7.class", "size": 2751, "crc": -645121144}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$detectNonConsumingTap$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$detectNonConsumingTap$2.class", "size": 4706, "crc": 901456846}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$getSelectionLayout-Wko1d7g$$inlined$compareBy$1.class", "size": 2670, "crc": 1585361328}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$handleDragObserver$1.class", "size": 7458, "crc": 610220410}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$1.class", "size": 1333, "crc": 1852127434}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$2.class", "size": 1729, "crc": -1603533148}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$3.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$3.class", "size": 1842, "crc": -205069162}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$4.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$4.class", "size": 1514, "crc": -939194086}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$modifier$5.class", "size": 2064, "crc": -1985657509}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1$1.class", "size": 1745, "crc": 30442570}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onClearSelectionRequested$1.class", "size": 4490, "crc": 149591096}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$onSelectionChange$1.class", "size": 1653, "crc": 1994838066}, {"key": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager$updateSelectionToolbar$1.class", "size": 1374, "crc": -2038704551}, {"key": "androidx/compose/foundation/text/selection/SelectionManager.class", "name": "androidx/compose/foundation/text/selection/SelectionManager.class", "size": 43909, "crc": -464404610}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt$WhenMappings.class", "size": 914, "crc": -221529368}, {"key": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManagerKt.class", "size": 11234, "crc": 916152819}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$1.class", "size": 2238, "crc": -933643291}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$1.class", "size": 2205, "crc": 1200736116}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1$2.class", "size": 2560, "crc": 1903768965}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1$2$1.class", "size": 3487, "crc": -1655728377}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt$selectionMagnifier$1.class", "size": 8925, "crc": -1330903675}, {"key": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/SelectionManager_androidKt.class", "size": 2419, "crc": -976394499}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Horizontal.class", "size": 1805, "crc": 1893454301}, {"key": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "name": "androidx/compose/foundation/text/selection/SelectionMode$Vertical.class", "size": 1800, "crc": 1084505332}, {"key": "androidx/compose/foundation/text/selection/SelectionMode.class", "name": "androidx/compose/foundation/text/selection/SelectionMode.class", "size": 3393, "crc": -816596557}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar$Companion.class", "size": 897, "crc": 667046121}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrar.class", "size": 2855, "crc": 1240927425}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$1.class", "size": 2189, "crc": -725086908}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$2.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion$Saver$2.class", "size": 1652, "crc": -1588023290}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$Companion.class", "size": 1485, "crc": -907549714}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$sort$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl$sort$1.class", "size": 3038, "crc": -1954320336}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarImpl.class", "size": 18452, "crc": -519771916}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarKt$LocalSelectionRegistrar$1.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarKt$LocalSelectionRegistrar$1.class", "size": 1404, "crc": 2081298615}, {"key": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "name": "androidx/compose/foundation/text/selection/SelectionRegistrarKt.class", "size": 2345, "crc": -1117908739}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1$1.class", "size": 3343, "crc": -1539585603}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$1.class", "size": 5778, "crc": 1611622224}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$2.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt$SimpleLayout$2.class", "size": 2216, "crc": -2037504347}, {"key": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "name": "androidx/compose/foundation/text/selection/SimpleLayoutKt.class", "size": 7618, "crc": -290158556}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout$Companion.class", "size": 1010, "crc": -1484980655}, {"key": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "name": "androidx/compose/foundation/text/selection/SingleSelectionLayout.class", "size": 6279, "crc": 35501788}, {"key": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextFieldPreparedSelection.class", "size": 10243, "crc": 154123358}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$cursorDragObserver$1.class", "size": 6300, "crc": 1667710148}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$handleDragObserver$1.class", "size": 5317, "crc": -1086273277}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$mouseSelectionObserver$1.class", "size": 4633, "crc": -1638070150}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$onValueChange$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$onValueChange$1.class", "size": 1629, "crc": 1880037704}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$copy$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$copy$1.class", "size": 1602, "crc": 1375508049}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$cut$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$cut$1.class", "size": 1492, "crc": -1096506886}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$paste$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$paste$1.class", "size": 1498, "crc": -1071330510}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$selectAll$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$showSelectionToolbar$selectAll$1.class", "size": 1447, "crc": 1882524447}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager$touchSelectionObserver$1.class", "size": 9067, "crc": -2138674629}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager.class", "size": 34424, "crc": -1043642115}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$1.class", "size": 1535, "crc": -1322364115}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$2.class", "size": 4194, "crc": 258551935}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$3.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$TextFieldSelectionHandle$3.class", "size": 2239, "crc": -134901695}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt$WhenMappings.class", "size": 941, "crc": -125497243}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManagerKt.class", "size": 11073, "crc": 1111542725}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$1.class", "size": 2319, "crc": 1136004119}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$1.class", "size": 2250, "crc": 762944048}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$2.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1$2.class", "size": 2614, "crc": 792030090}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1$2$1.class", "size": 3537, "crc": 1414939929}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt$textFieldMagnifier$1.class", "size": 9141, "crc": 1106320907}, {"key": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "name": "androidx/compose/foundation/text/selection/TextFieldSelectionManager_androidKt.class", "size": 2003, "crc": 1450522975}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelection.class", "size": 3041, "crc": 1685848883}, {"key": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "name": "androidx/compose/foundation/text/selection/TextPreparedSelectionState.class", "size": 1333, "crc": 1680326195}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColors.class", "size": 2568, "crc": 467663212}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColorsKt$LocalTextSelectionColors$1.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColorsKt$LocalTextSelectionColors$1.class", "size": 1463, "crc": -2064065517}, {"key": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionColorsKt.class", "size": 2580, "crc": -2023159876}, {"key": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "name": "androidx/compose/foundation/text/selection/TextSelectionDelegateKt.class", "size": 2341, "crc": 277292183}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$1$1.class", "size": 3154, "crc": 378515592}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$2.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$2.class", "size": 4885, "crc": -1692584531}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$3$1$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$3$1$1.class", "size": 2032, "crc": -1025853007}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$3.class", "size": 9165, "crc": 1456196121}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$4.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$4.class", "size": 4741, "crc": -1386173725}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$1.class", "size": 1477, "crc": 756193733}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$2.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1$2.class", "size": 1477, "crc": -1856337522}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$BasicSecureTextField$secureTextFieldModifier$1.class", "size": 3013, "crc": 1439139874}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$1$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$1$1.class", "size": 2276, "crc": 907891365}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$1.class", "size": 9722, "crc": -402058569}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$2.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$2.class", "size": 2031, "crc": -190722333}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$DisableCutCopy$copyDisabledToolbar$1$1.class", "size": 2498, "crc": -374533908}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$1.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$1.class", "size": 2217, "crc": 1590164492}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$2.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$2.class", "size": 2215, "crc": 782303776}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$3.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$3.class", "size": 2217, "crc": -437623497}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$4.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$4.class", "size": 2221, "crc": -540867373}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$5.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$5.class", "size": 2219, "crc": 1268367868}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$6.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt$KeyboardActions$6.class", "size": 2217, "crc": -231996455}, {"key": "androidx/compose/foundation/text2/BasicSecureTextFieldKt.class", "name": "androidx/compose/foundation/text2/BasicSecureTextFieldKt.class", "size": 32041, "crc": -1785240500}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$1$1.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$1$1.class", "size": 3251, "crc": 1933955844}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$2.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$2.class", "size": 5582, "crc": -862687580}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$3.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$3.class", "size": 3026, "crc": 1284008168}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$4$invoke$$inlined$onDispose$1.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$4$invoke$$inlined$onDispose$1.class", "size": 2323, "crc": 145542321}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$4.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$4.class", "size": 3570, "crc": 1579422883}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$5$1.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$5$1.class", "size": 15123, "crc": 762451152}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$6.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$BasicTextField2$6.class", "size": 5438, "crc": 2144157583}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$DefaultTextFieldDecorator$1$Decoration$1.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$DefaultTextFieldDecorator$1$Decoration$1.class", "size": 2353, "crc": -430772472}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$DefaultTextFieldDecorator$1.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$DefaultTextFieldDecorator$1.class", "size": 3192, "crc": -755924790}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldCursorHandle$1$1.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldCursorHandle$1$1.class", "size": 2809, "crc": 177949739}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldCursorHandle$2.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldCursorHandle$2.class", "size": 4689, "crc": -1602542424}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldCursorHandle$3.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldCursorHandle$3.class", "size": 1936, "crc": -86812483}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$1.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$1.class", "size": 1598, "crc": 1061384513}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$2.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$2.class", "size": 4834, "crc": -414744945}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$3.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$3.class", "size": 1596, "crc": -1993823566}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$4.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$4.class", "size": 4834, "crc": 1136722377}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$5.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt$TextFieldSelectionHandles$5.class", "size": 1948, "crc": -1565795304}, {"key": "androidx/compose/foundation/text2/BasicTextField2Kt.class", "name": "androidx/compose/foundation/text2/BasicTextField2Kt.class", "size": 45312, "crc": 1459819578}, {"key": "androidx/compose/foundation/text2/PasswordRevealFilter.class", "name": "androidx/compose/foundation/text2/PasswordRevealFilter.class", "size": 4744, "crc": -73159875}, {"key": "androidx/compose/foundation/text2/SecureTextFieldController$1$1.class", "name": "androidx/compose/foundation/text2/SecureTextFieldController$1$1.class", "size": 3693, "crc": -1347858512}, {"key": "androidx/compose/foundation/text2/SecureTextFieldController$1.class", "name": "androidx/compose/foundation/text2/SecureTextFieldController$1.class", "size": 4069, "crc": 185769216}, {"key": "androidx/compose/foundation/text2/SecureTextFieldController$focusChangeModifier$1.class", "name": "androidx/compose/foundation/text2/SecureTextFieldController$focusChangeModifier$1.class", "size": 1921, "crc": 1858599205}, {"key": "androidx/compose/foundation/text2/SecureTextFieldController$passwordRevealFilter$1.class", "name": "androidx/compose/foundation/text2/SecureTextFieldController$passwordRevealFilter$1.class", "size": 1393, "crc": -561612858}, {"key": "androidx/compose/foundation/text2/SecureTextFieldController.class", "name": "androidx/compose/foundation/text2/SecureTextFieldController.class", "size": 5327, "crc": -1222784738}, {"key": "androidx/compose/foundation/text2/TextFieldDecorator.class", "name": "androidx/compose/foundation/text2/TextFieldDecorator.class", "size": 1094, "crc": 2093725057}, {"key": "androidx/compose/foundation/text2/input/AllCapsTransformation.class", "name": "androidx/compose/foundation/text2/input/AllCapsTransformation.class", "size": 5982, "crc": 368179600}, {"key": "androidx/compose/foundation/text2/input/AllCapsTransformationKt.class", "name": "androidx/compose/foundation/text2/input/AllCapsTransformationKt.class", "size": 1468, "crc": 1137594727}, {"key": "androidx/compose/foundation/text2/input/CodepointTransformation$Companion.class", "name": "androidx/compose/foundation/text2/input/CodepointTransformation$Companion.class", "size": 809, "crc": -1105750764}, {"key": "androidx/compose/foundation/text2/input/CodepointTransformation.class", "name": "androidx/compose/foundation/text2/input/CodepointTransformation.class", "size": 1060, "crc": 1909820721}, {"key": "androidx/compose/foundation/text2/input/CodepointTransformationKt.class", "name": "androidx/compose/foundation/text2/input/CodepointTransformationKt.class", "size": 3701, "crc": -1041956376}, {"key": "androidx/compose/foundation/text2/input/EmptyChangeList.class", "name": "androidx/compose/foundation/text2/input/EmptyChangeList.class", "size": 1568, "crc": -697439085}, {"key": "androidx/compose/foundation/text2/input/FilterChain.class", "name": "androidx/compose/foundation/text2/input/FilterChain.class", "size": 3122, "crc": -1609027294}, {"key": "androidx/compose/foundation/text2/input/ImeActionHandler.class", "name": "androidx/compose/foundation/text2/input/ImeActionHandler.class", "size": 756, "crc": -1634130892}, {"key": "androidx/compose/foundation/text2/input/InputTransformation$Companion.class", "name": "androidx/compose/foundation/text2/input/InputTransformation$Companion.class", "size": 1836, "crc": 863368456}, {"key": "androidx/compose/foundation/text2/input/InputTransformation.class", "name": "androidx/compose/foundation/text2/input/InputTransformation.class", "size": 1721, "crc": -634960092}, {"key": "androidx/compose/foundation/text2/input/InputTransformationByValue.class", "name": "androidx/compose/foundation/text2/input/InputTransformationByValue.class", "size": 4768, "crc": -1904062500}, {"key": "androidx/compose/foundation/text2/input/InputTransformationKt.class", "name": "androidx/compose/foundation/text2/input/InputTransformationKt.class", "size": 1608, "crc": -1939675987}, {"key": "androidx/compose/foundation/text2/input/MaskCodepointTransformation.class", "name": "androidx/compose/foundation/text2/input/MaskCodepointTransformation.class", "size": 2478, "crc": -2036541661}, {"key": "androidx/compose/foundation/text2/input/MaxLengthFilter.class", "name": "androidx/compose/foundation/text2/input/MaxLengthFilter.class", "size": 4097, "crc": 543161739}, {"key": "androidx/compose/foundation/text2/input/MaxLengthTransformationKt.class", "name": "androidx/compose/foundation/text2/input/MaxLengthTransformationKt.class", "size": 1571, "crc": 459908508}, {"key": "androidx/compose/foundation/text2/input/SingleLineCodepointTransformation.class", "name": "androidx/compose/foundation/text2/input/SingleLineCodepointTransformation.class", "size": 1664, "crc": 1972555605}, {"key": "androidx/compose/foundation/text2/input/TextFieldBuffer$ChangeList.class", "name": "androidx/compose/foundation/text2/input/TextFieldBuffer$ChangeList.class", "size": 1009, "crc": 590191863}, {"key": "androidx/compose/foundation/text2/input/TextFieldBuffer.class", "name": "androidx/compose/foundation/text2/input/TextFieldBuffer.class", "size": 15084, "crc": -810259703}, {"key": "androidx/compose/foundation/text2/input/TextFieldBufferKt.class", "name": "androidx/compose/foundation/text2/input/TextFieldBufferKt.class", "size": 5169, "crc": 574921659}, {"key": "androidx/compose/foundation/text2/input/TextFieldCharSequence.class", "name": "androidx/compose/foundation/text2/input/TextFieldCharSequence.class", "size": 1426, "crc": 444847457}, {"key": "androidx/compose/foundation/text2/input/TextFieldCharSequenceKt.class", "name": "androidx/compose/foundation/text2/input/TextFieldCharSequenceKt.class", "size": 4273, "crc": -2139771599}, {"key": "androidx/compose/foundation/text2/input/TextFieldCharSequenceWrapper.class", "name": "androidx/compose/foundation/text2/input/TextFieldCharSequenceWrapper.class", "size": 4537, "crc": -1582470125}, {"key": "androidx/compose/foundation/text2/input/TextFieldLineLimits$Companion.class", "name": "androidx/compose/foundation/text2/input/TextFieldLineLimits$Companion.class", "size": 1346, "crc": 2071549549}, {"key": "androidx/compose/foundation/text2/input/TextFieldLineLimits$MultiLine.class", "name": "androidx/compose/foundation/text2/input/TextFieldLineLimits$MultiLine.class", "size": 2896, "crc": -997391259}, {"key": "androidx/compose/foundation/text2/input/TextFieldLineLimits$SingleLine.class", "name": "androidx/compose/foundation/text2/input/TextFieldLineLimits$SingleLine.class", "size": 1044, "crc": -1715842051}, {"key": "androidx/compose/foundation/text2/input/TextFieldLineLimits.class", "name": "androidx/compose/foundation/text2/input/TextFieldLineLimits.class", "size": 1306, "crc": 308513303}, {"key": "androidx/compose/foundation/text2/input/TextFieldState$NotifyImeListener.class", "name": "androidx/compose/foundation/text2/input/TextFieldState$NotifyImeListener.class", "size": 972, "crc": 573265937}, {"key": "androidx/compose/foundation/text2/input/TextFieldState$Saver.class", "name": "androidx/compose/foundation/text2/input/TextFieldState$Saver.class", "size": 4652, "crc": -1825286840}, {"key": "androidx/compose/foundation/text2/input/TextFieldState$WhenMappings.class", "name": "androidx/compose/foundation/text2/input/TextFieldState$WhenMappings.class", "size": 1023, "crc": 374544166}, {"key": "androidx/compose/foundation/text2/input/TextFieldState.class", "name": "androidx/compose/foundation/text2/input/TextFieldState.class", "size": 19410, "crc": 247447368}, {"key": "androidx/compose/foundation/text2/input/TextFieldStateKt$forEachTextValue$1.class", "name": "androidx/compose/foundation/text2/input/TextFieldStateKt$forEachTextValue$1.class", "size": 1654, "crc": -1100820297}, {"key": "androidx/compose/foundation/text2/input/TextFieldStateKt$rememberTextFieldState$1$1.class", "name": "androidx/compose/foundation/text2/input/TextFieldStateKt$rememberTextFieldState$1$1.class", "size": 1733, "crc": 873908818}, {"key": "androidx/compose/foundation/text2/input/TextFieldStateKt$textAsFlow$1.class", "name": "androidx/compose/foundation/text2/input/TextFieldStateKt$textAsFlow$1.class", "size": 1637, "crc": -359188830}, {"key": "androidx/compose/foundation/text2/input/TextFieldStateKt.class", "name": "androidx/compose/foundation/text2/input/TextFieldStateKt.class", "size": 9964, "crc": 1954578812}, {"key": "androidx/compose/foundation/text2/input/TextObfuscationMode$Companion.class", "name": "androidx/compose/foundation/text2/input/TextObfuscationMode$Companion.class", "size": 1507, "crc": -693686893}, {"key": "androidx/compose/foundation/text2/input/TextObfuscationMode.class", "name": "androidx/compose/foundation/text2/input/TextObfuscationMode.class", "size": 3029, "crc": 511382566}, {"key": "androidx/compose/foundation/text2/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "name": "androidx/compose/foundation/text2/input/TextUndoManager$Companion$Saver$special$$inlined$createSaver$1.class", "size": 7110, "crc": -1544738561}, {"key": "androidx/compose/foundation/text2/input/TextUndoManager$Companion$Saver.class", "name": "androidx/compose/foundation/text2/input/TextUndoManager$Companion$Saver.class", "size": 6656, "crc": -1985232904}, {"key": "androidx/compose/foundation/text2/input/TextUndoManager$Companion.class", "name": "androidx/compose/foundation/text2/input/TextUndoManager$Companion.class", "size": 979, "crc": 2114972487}, {"key": "androidx/compose/foundation/text2/input/TextUndoManager.class", "name": "androidx/compose/foundation/text2/input/TextUndoManager.class", "size": 9361, "crc": -1634859620}, {"key": "androidx/compose/foundation/text2/input/TextUndoManagerKt.class", "name": "androidx/compose/foundation/text2/input/TextUndoManagerKt.class", "size": 5544, "crc": -422236642}, {"key": "androidx/compose/foundation/text2/input/UndoState.class", "name": "androidx/compose/foundation/text2/input/UndoState.class", "size": 2075, "crc": 1748064197}, {"key": "androidx/compose/foundation/text2/input/internal/AndroidTextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text2/input/internal/AndroidTextFieldKeyEventHandler.class", "size": 3629, "crc": -1351588610}, {"key": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "name": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$1.class", "size": 1985, "crc": 199575572}, {"key": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2$1.class", "size": 7215, "crc": -2072432895}, {"key": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2$2$1.class", "size": 1990, "crc": 140694072}, {"key": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2$2$textInputSession$1.class", "name": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2$2$textInputSession$1.class", "size": 7717, "crc": -2060481158}, {"key": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "name": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt$platformSpecificTextInputSession$2.class", "size": 8960, "crc": 2026962752}, {"key": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt.class", "name": "androidx/compose/foundation/text2/input/internal/AndroidTextInputSession_androidKt.class", "size": 8563, "crc": -247051574}, {"key": "androidx/compose/foundation/text2/input/internal/ChangeTracker$Change.class", "name": "androidx/compose/foundation/text2/input/internal/ChangeTracker$Change.class", "size": 3721, "crc": 1035212118}, {"key": "androidx/compose/foundation/text2/input/internal/ChangeTracker.class", "name": "androidx/compose/foundation/text2/input/internal/ChangeTracker.class", "size": 8845, "crc": -69173565}, {"key": "androidx/compose/foundation/text2/input/internal/CodepointHelpers_jvmKt.class", "name": "androidx/compose/foundation/text2/input/internal/CodepointHelpers_jvmKt.class", "size": 1148, "crc": 1014913664}, {"key": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManager.class", "name": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManager.class", "size": 1203, "crc": -2003800627}, {"key": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManagerImpl.class", "name": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManagerImpl.class", "size": 4074, "crc": -216067091}, {"key": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManagerImplApi21.class", "name": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManagerImplApi21.class", "size": 2357, "crc": -113546001}, {"key": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManagerImplApi24.class", "name": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManagerImplApi24.class", "size": 1539, "crc": -1376454455}, {"key": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManager_androidKt$ComposeInputMethodManagerFactory$1.class", "name": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManager_androidKt$ComposeInputMethodManagerFactory$1.class", "size": 2233, "crc": 1761228652}, {"key": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManager_androidKt.class", "name": "androidx/compose/foundation/text2/input/internal/ComposeInputMethodManager_androidKt.class", "size": 2426, "crc": 1822218734}, {"key": "androidx/compose/foundation/text2/input/internal/EditCommandKt.class", "name": "androidx/compose/foundation/text2/input/internal/EditCommandKt.class", "size": 7367, "crc": 528621176}, {"key": "androidx/compose/foundation/text2/input/internal/EditingBuffer$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/EditingBuffer$Companion.class", "size": 941, "crc": -63523074}, {"key": "androidx/compose/foundation/text2/input/internal/EditingBuffer.class", "name": "androidx/compose/foundation/text2/input/internal/EditingBuffer.class", "size": 9480, "crc": 2041766038}, {"key": "androidx/compose/foundation/text2/input/internal/EditingBufferKt.class", "name": "androidx/compose/foundation/text2/input/internal/EditingBufferKt.class", "size": 1227, "crc": 2076659345}, {"key": "androidx/compose/foundation/text2/input/internal/FixedMotionDurationScale.class", "name": "androidx/compose/foundation/text2/input/internal/FixedMotionDurationScale.class", "size": 3130, "crc": -659097216}, {"key": "androidx/compose/foundation/text2/input/internal/GapBuffer.class", "name": "androidx/compose/foundation/text2/input/internal/GapBuffer.class", "size": 4411, "crc": -682439159}, {"key": "androidx/compose/foundation/text2/input/internal/MathUtilsKt.class", "name": "androidx/compose/foundation/text2/input/internal/MathUtilsKt.class", "size": 1408, "crc": -2071934883}, {"key": "androidx/compose/foundation/text2/input/internal/OffsetMappingCalculator.class", "name": "androidx/compose/foundation/text2/input/internal/OffsetMappingCalculator.class", "size": 5734, "crc": -183384672}, {"key": "androidx/compose/foundation/text2/input/internal/OpArray$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/OpArray$Companion.class", "size": 937, "crc": -190054755}, {"key": "androidx/compose/foundation/text2/input/internal/OpArray.class", "name": "androidx/compose/foundation/text2/input/internal/OpArray.class", "size": 5273, "crc": 773512343}, {"key": "androidx/compose/foundation/text2/input/internal/PartialGapBuffer$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/PartialGapBuffer$Companion.class", "size": 1021, "crc": -1390055308}, {"key": "androidx/compose/foundation/text2/input/internal/PartialGapBuffer.class", "name": "androidx/compose/foundation/text2/input/internal/PartialGapBuffer.class", "size": 6099, "crc": 819405290}, {"key": "androidx/compose/foundation/text2/input/internal/StateSyncingModifier.class", "name": "androidx/compose/foundation/text2/input/internal/StateSyncingModifier.class", "size": 3851, "crc": -1956676800}, {"key": "androidx/compose/foundation/text2/input/internal/StateSyncingModifierKt.class", "name": "androidx/compose/foundation/text2/input/internal/StateSyncingModifierKt.class", "size": 1991, "crc": 532353385}, {"key": "androidx/compose/foundation/text2/input/internal/StateSyncingModifierNode$observeTextState$1.class", "name": "androidx/compose/foundation/text2/input/internal/StateSyncingModifierNode$observeTextState$1.class", "size": 2249, "crc": -1381582448}, {"key": "androidx/compose/foundation/text2/input/internal/StateSyncingModifierNode.class", "name": "androidx/compose/foundation/text2/input/internal/StateSyncingModifierNode.class", "size": 7762, "crc": -1534517670}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$commitText$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$commitText$1.class", "size": 2066, "crc": 714402507}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$deleteSurroundingText$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$deleteSurroundingText$1.class", "size": 1925, "crc": 283539470}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$deleteSurroundingTextInCodePoints$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$deleteSurroundingTextInCodePoints$1.class", "size": 1961, "crc": 2025248342}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$endBatchEditInternal$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$endBatchEditInternal$1.class", "size": 3702, "crc": 734303449}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$finishComposingText$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$finishComposingText$1.class", "size": 1810, "crc": -679713635}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$performContextMenuAction$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$performContextMenuAction$1.class", "size": 2187, "crc": 2044083032}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$setComposingRegion$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$setComposingRegion$1.class", "size": 1901, "crc": 1264767161}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$setComposingText$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$setComposingText$1.class", "size": 2084, "crc": -980247447}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$setSelection$1.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection$setSelection$1.class", "size": 1743, "crc": -406711407}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection.class", "size": 14929, "crc": 595185114}, {"key": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection_androidKt.class", "name": "androidx/compose/foundation/text2/input/internal/StatelessInputConnection_androidKt.class", "size": 2205, "crc": 2059087402}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifier.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifier.class", "size": 8351, "crc": 378074958}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierKt$cursorAnimationSpec$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierKt$cursorAnimationSpec$1.class", "size": 2247, "crc": 1243318320}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierKt.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierKt.class", "size": 6154, "crc": -71419073}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$measureHorizontalScroll$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$measureHorizontalScroll$1.class", "size": 5229, "crc": -919967214}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$measureVerticalScroll$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$measureVerticalScroll$1.class", "size": 5244, "crc": -2133777159}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$1.class", "size": 4376, "crc": -1419472025}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2$1$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2$1$1.class", "size": 1830, "crc": -851836928}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2$1$2.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2$1$2.class", "size": 4878, "crc": -1227218511}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2$1.class", "size": 4728, "crc": -1764804375}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateNode$2.class", "size": 4785, "crc": 1861473461}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode$updateScrollState$1.class", "size": 4298, "crc": -866090299}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldCoreModifierNode.class", "size": 20893, "crc": 1367934763}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifier.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifier.class", "size": 8906, "crc": 179815197}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierKt.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierKt.class", "size": 2384, "crc": 1699209742}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$1.class", "size": 3104, "crc": 773037709}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$10.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$10.class", "size": 1838, "crc": 1616418626}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$2.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$2.class", "size": 2306, "crc": -1440095946}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$3.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$3.class", "size": 3734, "crc": -1470466748}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$4.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$4.class", "size": 2504, "crc": 1993289307}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$5.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$5.class", "size": 2156, "crc": -1101000469}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$6.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$6.class", "size": 2291, "crc": 1103315212}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$7.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$7.class", "size": 2489, "crc": 652106028}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$8.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$8.class", "size": 1950, "crc": 1334827554}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$9.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$applySemantics$9.class", "size": 1834, "crc": -532039481}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$keyboardActionScope$1.class", "size": 3844, "crc": -1646353376}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$onImeActionPerformed$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$onImeActionPerformed$1.class", "size": 3780, "crc": 442781102}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$onKeyEvent$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$onKeyEvent$1.class", "size": 1989, "crc": -1989835169}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$onObservedReadsChanged$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$onObservedReadsChanged$1.class", "size": 2214, "crc": -682329792}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$1.class", "size": 2059, "crc": 543264955}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$2.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1$1$2.class", "size": 1840, "crc": 176182048}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$pointerInputNode$1.class", "size": 5554, "crc": 1754097483}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1$1.class", "size": 4220, "crc": 1316222374}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$startInputSession$1$1.class", "size": 5624, "crc": 1892389512}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode$startInputSession$1.class", "size": 4213, "crc": -283093850}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldDecoratorModifierNode.class", "size": 22130, "crc": -1695559242}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler$WhenMappings.class", "size": 3315, "crc": -891131555}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$1.class", "size": 2141, "crc": -2061652842}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$2.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler$onKeyEvent$2$2.class", "size": 2143, "crc": 1989113871}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler.class", "size": 22870, "crc": 1902666700}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler_androidKt.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldKeyEventHandler_androidKt.class", "size": 1656, "crc": -1446617771}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$CacheRecord.class", "size": 6558, "crc": -1635262717}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion$mutationPolicy$1.class", "size": 2660, "crc": 1866538053}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$MeasureInputs$Companion.class", "size": 1731, "crc": 2057420831}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$MeasureInputs.class", "size": 4775, "crc": 1536829323}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion$mutationPolicy$1.class", "size": 2412, "crc": 1976784330}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$NonMeasureInputs$Companion.class", "size": 1749, "crc": -1220943599}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache$NonMeasureInputs.class", "size": 3601, "crc": 779097902}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldLayoutStateCache.class", "size": 18703, "crc": 2031280526}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldPreparedSelection$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldPreparedSelection$Companion.class", "size": 997, "crc": -671269268}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldPreparedSelection.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldPreparedSelection.class", "size": 26315, "crc": -1852823150}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldPreparedSelectionState.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldPreparedSelectionState.class", "size": 1206, "crc": 1407649691}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldTextLayoutModifier.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldTextLayoutModifier.class", "size": 8209, "crc": -1366610114}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldTextLayoutModifierNode$measure$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldTextLayoutModifierNode$measure$1.class", "size": 2051, "crc": 2055375903}, {"key": "androidx/compose/foundation/text2/input/internal/TextFieldTextLayoutModifierNode.class", "name": "androidx/compose/foundation/text2/input/internal/TextFieldTextLayoutModifierNode.class", "size": 8762, "crc": **********}, {"key": "androidx/compose/foundation/text2/input/internal/TextInputSession.class", "name": "androidx/compose/foundation/text2/input/internal/TextInputSession.class", "size": 1504, "crc": **********}, {"key": "androidx/compose/foundation/text2/input/internal/TextLayoutState$layoutWithNewMeasureInputs$1$textLayoutProvider$1.class", "name": "androidx/compose/foundation/text2/input/internal/TextLayoutState$layoutWithNewMeasureInputs$1$textLayoutProvider$1.class", "size": 2125, "crc": 383015583}, {"key": "androidx/compose/foundation/text2/input/internal/TextLayoutState.class", "name": "androidx/compose/foundation/text2/input/internal/TextLayoutState.class", "size": 12284, "crc": **********}, {"key": "androidx/compose/foundation/text2/input/internal/TextLayoutStateKt.class", "name": "androidx/compose/foundation/text2/input/internal/TextLayoutStateKt.class", "size": 4032, "crc": 972515997}, {"key": "androidx/compose/foundation/text2/input/internal/ToCharArray_androidKt.class", "name": "androidx/compose/foundation/text2/input/internal/ToCharArray_androidKt.class", "size": 1236, "crc": -381114651}, {"key": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$Companion.class", "size": 6042, "crc": -**********}, {"key": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$TransformedText.class", "name": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$TransformedText.class", "size": 3769, "crc": 1923258280}, {"key": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "name": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$collectImeNotifications$1.class", "size": 2315, "crc": 2020810518}, {"key": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$collectImeNotifications$2$1.class", "size": 2506, "crc": 1232610555}, {"key": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$transformedText$1$1.class", "name": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState$transformedText$1$1.class", "size": 3073, "crc": -1858677884}, {"key": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState.class", "name": "androidx/compose/foundation/text2/input/internal/TransformedTextFieldState.class", "size": 23165, "crc": -262971976}, {"key": "androidx/compose/foundation/text2/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/AndroidTextFieldMagnifier_androidKt$textFieldMagnifierNode$1.class", "size": 2218, "crc": 2105888299}, {"key": "androidx/compose/foundation/text2/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "name": "androidx/compose/foundation/text2/input/internal/selection/AndroidTextFieldMagnifier_androidKt.class", "size": 2468, "crc": 551452592}, {"key": "androidx/compose/foundation/text2/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/PressDownGestureKt$detectPressDownGesture$2.class", "size": 7670, "crc": 899890777}, {"key": "androidx/compose/foundation/text2/input/internal/selection/PressDownGestureKt.class", "name": "androidx/compose/foundation/text2/input/internal/selection/PressDownGestureKt.class", "size": 2703, "crc": 2090884577}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$awaitSecondDown$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$awaitSecondDown$2.class", "size": 4894, "crc": -701504182}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$consumeUntilUp$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$consumeUntilUp$1.class", "size": 1833, "crc": 20867662}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2$1$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2$1$1.class", "size": 4204, "crc": -1328320556}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2$1$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2$1$2.class", "size": 4736, "crc": -974339624}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2$1.class", "size": 7003, "crc": 2080404017}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt$detectTapAndDoubleTap$2.class", "size": 4967, "crc": 1919086149}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapAndDoubleTapGestureKt.class", "size": 8146, "crc": 1266870073}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TapOnPosition.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TapOnPosition.class", "size": 626, "crc": -1870571741}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldHandleState$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldHandleState$Companion.class", "size": 1361, "crc": -1949945085}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldHandleState.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldHandleState.class", "size": 5285, "crc": -1681142002}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierKt$WhenMappings.class", "size": 952, "crc": -1859244790}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierKt.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierKt.class", "size": 5881, "crc": -769809913}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNode.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNode.class", "size": 2760, "crc": 2104347461}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$1.class", "size": 2430, "crc": 1198818680}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$magnifierNode$2.class", "size": 2982, "crc": 33445742}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$1.class", "size": 2953, "crc": 955806623}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2$1.class", "size": 4967, "crc": -699970087}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1$2.class", "size": 3660, "crc": 1281166270}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28$restartAnimationJob$1.class", "size": 4784, "crc": -1132353485}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldMagnifierNodeImpl28.class", "size": 11994, "crc": 913711322}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandle$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandle$2.class", "size": 3821, "crc": -238077282}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$1.class", "size": 4294, "crc": -1793026942}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$2.class", "size": 4309, "crc": -1347842988}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3$1.class", "size": 2635, "crc": -722024511}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2$3.class", "size": 4610, "crc": 785633952}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleGestures$2.class", "size": 5113, "crc": 1149675647}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleInBounds$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorHandleInBounds$2.class", "size": 4819, "crc": -1858296972}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorRect$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$cursorRect$2.class", "size": 5206, "crc": 1101645807}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$1.class", "size": 2494, "crc": -378620075}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$2.class", "size": 3198, "crc": -1554562626}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$3.class", "size": 1928, "crc": -2099862784}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$4.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$4.class", "size": 1928, "crc": 1273780019}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$5.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectCursorHandleDragGestures$5.class", "size": 4925, "crc": -761489665}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$1.class", "size": 2548, "crc": -1900789008}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$2.class", "size": 3148, "crc": -1785856586}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$3.class", "size": 1947, "crc": -2136803471}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$4.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$4.class", "size": 1947, "crc": -1477217255}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$5.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$5.class", "size": 5267, "crc": 1206145142}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$6.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectSelectionHandleDragGestures$6.class", "size": 2149, "crc": 1282251642}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$2$1.class", "size": 1719, "crc": -1260405165}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$2.class", "size": 7629, "crc": 1144139585}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$3.class", "size": 2173, "crc": -570950099}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$4.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$4.class", "size": 2173, "crc": -247387388}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$5$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$5$1.class", "size": 1779, "crc": -791798968}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$5.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldLongPressAndAfterDrag$5.class", "size": 8146, "crc": 1216081513}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2$1.class", "size": 1408, "crc": 1354926924}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$2.class", "size": 4123, "crc": 1131275834}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3$1.class", "size": 1414, "crc": -132861898}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTextFieldTapGestures$3.class", "size": 4495, "crc": 1852388717}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$detectTouchMode$2.class", "size": 4887, "crc": -1601675919}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$endSelectionHandle$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$endSelectionHandle$2.class", "size": 2056, "crc": 1366497276}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$1.class", "size": 2057, "crc": 362003129}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$2$1.class", "size": 3982, "crc": -975192131}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$2$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$2$2.class", "size": 3992, "crc": 238047599}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeChanges$2.class", "size": 4334, "crc": 55526604}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextChanges$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextChanges$2.class", "size": 1985, "crc": 1113480259}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextChanges$3.class", "size": 2116, "crc": 2003444353}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextChanges$4.class", "size": 2693, "crc": -850233453}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$2.class", "size": 5504, "crc": 1849702264}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$observeTextToolbarVisibility$3.class", "size": 2711, "crc": 954363624}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$1.class", "size": 4312, "crc": -267493748}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$1.class", "size": 2462, "crc": -638400382}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2$2.class", "size": 1768, "crc": -1382436664}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$2.class", "size": 4985, "crc": 1686047568}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2$3.class", "size": 4396, "crc": 1902938292}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$selectionHandleGestures$2.class", "size": 5391, "crc": -102751272}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$copy$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$copy$1.class", "size": 1919, "crc": -782490841}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$cut$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$cut$1.class", "size": 1795, "crc": -319889646}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$paste$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$paste$1.class", "size": 1801, "crc": -404411520}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$selectAll$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$showTextToolbar$selectAll$1.class", "size": 2105, "crc": 103833800}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$startSelectionHandle$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$startSelectionHandle$2.class", "size": 2060, "crc": 792781077}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2$1.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2$1.class", "size": 4276, "crc": -1300350115}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2$2.class", "size": 4726, "crc": 2057764123}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2$3.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2$3.class", "size": 4559, "crc": -2024421986}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState$textFieldGestures$2.class", "size": 5741, "crc": -987807660}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionState.class", "size": 50774, "crc": -728496819}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionStateKt.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextFieldSelectionStateKt.class", "size": 1588, "crc": -1550440456}, {"key": "androidx/compose/foundation/text2/input/internal/selection/TextToolbarState.class", "name": "androidx/compose/foundation/text2/input/internal/selection/TextToolbarState.class", "size": 1683, "crc": 1646551854}, {"key": "androidx/compose/foundation/text2/input/internal/undo/TextDeleteType.class", "name": "androidx/compose/foundation/text2/input/internal/undo/TextDeleteType.class", "size": 1680, "crc": -1809844370}, {"key": "androidx/compose/foundation/text2/input/internal/undo/TextEditType.class", "name": "androidx/compose/foundation/text2/input/internal/undo/TextEditType.class", "size": 1615, "crc": 1724620465}, {"key": "androidx/compose/foundation/text2/input/internal/undo/TextFieldEditUndoBehavior.class", "name": "androidx/compose/foundation/text2/input/internal/undo/TextFieldEditUndoBehavior.class", "size": 1719, "crc": -1308856049}, {"key": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "name": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperation$Companion$Saver$1.class", "size": 3817, "crc": -1567405077}, {"key": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperation$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperation$Companion.class", "size": 1505, "crc": 654450932}, {"key": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperation.class", "name": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperation.class", "size": 5556, "crc": 1008485758}, {"key": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperationKt.class", "name": "androidx/compose/foundation/text2/input/internal/undo/TextUndoOperationKt.class", "size": 4701, "crc": -221994787}, {"key": "androidx/compose/foundation/text2/input/internal/undo/UndoManager$Companion$createSaver$1.class", "name": "androidx/compose/foundation/text2/input/internal/undo/UndoManager$Companion$createSaver$1.class", "size": 6838, "crc": -1296529310}, {"key": "androidx/compose/foundation/text2/input/internal/undo/UndoManager$Companion.class", "name": "androidx/compose/foundation/text2/input/internal/undo/UndoManager$Companion.class", "size": 1860, "crc": 1005837737}, {"key": "androidx/compose/foundation/text2/input/internal/undo/UndoManager.class", "name": "androidx/compose/foundation/text2/input/internal/undo/UndoManager.class", "size": 5703, "crc": 360944093}, {"key": "META-INF/androidx.compose.foundation_foundation.version", "name": "META-INF/androidx.compose.foundation_foundation.version", "size": 6, "crc": -1055996171}, {"key": "META-INF/foundation_release.kotlin_module", "name": "META-INF/foundation_release.kotlin_module", "size": 5880, "crc": -1483515140}]