[{"key": "androidx/compose/ui/tooling/data/CallGroup.class", "name": "androidx/compose/ui/tooling/data/CallGroup.class", "size": 2786, "crc": 639207631}, {"key": "androidx/compose/ui/tooling/data/CompositionCallStack.class", "name": "androidx/compose/ui/tooling/data/CompositionCallStack.class", "size": 10986, "crc": 429092694}, {"key": "androidx/compose/ui/tooling/data/ContextCache.class", "name": "androidx/compose/ui/tooling/data/ContextCache.class", "size": 1409, "crc": -1839465611}, {"key": "androidx/compose/ui/tooling/data/EmptyGroup.class", "name": "androidx/compose/ui/tooling/data/EmptyGroup.class", "size": 1352, "crc": -113940935}, {"key": "androidx/compose/ui/tooling/data/Group.class", "name": "androidx/compose/ui/tooling/data/Group.class", "size": 4692, "crc": -1007206581}, {"key": "androidx/compose/ui/tooling/data/JoinedKey.class", "name": "androidx/compose/ui/tooling/data/JoinedKey.class", "size": 2960, "crc": -1787288633}, {"key": "androidx/compose/ui/tooling/data/NodeGroup.class", "name": "androidx/compose/ui/tooling/data/NodeGroup.class", "size": 2556, "crc": -357223975}, {"key": "androidx/compose/ui/tooling/data/ParameterInformation.class", "name": "androidx/compose/ui/tooling/data/ParameterInformation.class", "size": 4987, "crc": -1129715898}, {"key": "androidx/compose/ui/tooling/data/SlotTreeKt$extractFromIndyLambdaFields$$inlined$sortedBy$1.class", "name": "androidx/compose/ui/tooling/data/SlotTreeKt$extractFromIndyLambdaFields$$inlined$sortedBy$1.class", "size": 2826, "crc": 1996606127}, {"key": "androidx/compose/ui/tooling/data/SlotTreeKt.class", "name": "androidx/compose/ui/tooling/data/SlotTreeKt.class", "size": 28563, "crc": -1249072646}, {"key": "androidx/compose/ui/tooling/data/SourceContext.class", "name": "androidx/compose/ui/tooling/data/SourceContext.class", "size": 1565, "crc": -1646521451}, {"key": "androidx/compose/ui/tooling/data/SourceInformationContext.class", "name": "androidx/compose/ui/tooling/data/SourceInformationContext.class", "size": 4794, "crc": 571200667}, {"key": "androidx/compose/ui/tooling/data/SourceLocation.class", "name": "androidx/compose/ui/tooling/data/SourceLocation.class", "size": 3955, "crc": 1209288191}, {"key": "androidx/compose/ui/tooling/data/UiToolingDataApi.class", "name": "androidx/compose/ui/tooling/data/UiToolingDataApi.class", "size": 796, "crc": -2076709776}, {"key": "META-INF/androidx.compose.ui_ui-tooling-data.version", "name": "META-INF/androidx.compose.ui_ui-tooling-data.version", "size": 6, "crc": -2139691038}, {"key": "META-INF/ui-tooling-data.kotlin_module", "name": "META-INF/ui-tooling-data.kotlin_module", "size": 72, "crc": -1779386426}]